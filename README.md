## ijiwei-dc2

运行方式：
命令行方式：
python3 dc/tests/baidu_remote_webdriver.py

运行 server:
通过 pycharm 配置 flash 启动
或命令行
```
export FLASK_APP=dc/app.py
export FLASK_RUN_HOST=0.0.0.0
export FLASK_ENV=development
export FLASK_DEBUG=1
cd /Users/<USER>/code/pythton/ijiwei-dc2
python3 -m flask run --host=0.0.0.0

```

### windows 运行方式
```
set FLASK_APP=dc/app.py
set FLASK_RUN_HOST=0.0.0.0
set FLASK_ENV=development
set FLASK_DEBUG=1

python.exe -m flask run --host=0.0.0.0
```

## uwsgi 运行方式
cd /data/ijiwei-dc2
uwsgi dc/app.ini

uwsgi --http :9090 --wsgi-file server.py

# 启动uwsgi服务, 使用-d参数可以放到后台运行
cd /data/ijiwei-dc2
uwsgi -d --ini dc/app.ini   


## 涉及框架
flash
https://flask.palletsprojects.com/en/2.2.x/

flask-sqlalchemy
https://flask-sqlalchemy.palletsprojects.com/

SQLAlchemy
https://www.sqlalchemy.org/


## 安装依赖
python3 -m pip install -r requirements.txt


## 可能用的到包
chromedriver              
elastic-transport         
elasticsearch             
Faker                     
Flask                     
Flask-Migrate             
Flask-Script              
Flask-SQLAlchemy          
geckodriver-autoinstaller 
mysql-connector           
mysqlclient               
pymongo                   
PyMySQL                   
redis                     
requests                  
requests-file             
Scrapy                    
scrapy-selenium           
selenium                  
selenium-browser          
selenium-chrome           
selenium-firefox          
service-identity          
SQLAlchemy                
sqlparse                  



## Python项目目录结构
为项目设置目录结构是为了将功能类似的文件放置在同一目录内，增强项目的可读性和可维护性。如果一个python项目功能单一，代码量很小，那就没必要设置的这么复杂。

1，bin目录：是整个应用程序的执行文件目录，其中start.py文件是启动入口

2，conf目录：是整个应用程序的配置文件目录，config.yaml是其中一个配置文件

3，core目录：是整个应用程序的核心模块，core.py是核心业务逻辑脚本文件

4，db目录：是整个应用程序的数据库文件目录

5，lib目录：是整个应用程序的通用功能脚本和第三方应用文件存放目录

6，log目录：是整个应用程序的日志文件目录

7，res目录：是整个应用程序的图标、图片、ui等目录

8，tests目录：是整个应用程序的测试文件目录

9，venv-win32目录：是整个应用程序的32位虚拟环境目录，用于运行和打包32应用程序

10，venv-win64目录：是整个应用程序的64位虚拟环境目录，用于运行和打包64应用程序

11，readme.txt：项目说明文档

12，requirements.txt：用于存放整个应用依赖的外部Python包列表
