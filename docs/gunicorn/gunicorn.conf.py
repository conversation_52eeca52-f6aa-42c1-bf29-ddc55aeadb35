# 监听的 IP 和端口，通常用于开发环境。在生产环境中可以使用 Unix 套接字。
bind = '0.0.0.0:8000'  # 或者使用: bind = 'unix:/path/to/your/project/gunicorn.sock'

# workers 的数量是 Gunicorn 性能调优的关键。通常建议使用 CPU 核心数的 2 倍加 1。
# 例如，如果你的服务器有 4 核 CPU，workers 设置为 9。
workers = 9  # workers = 2 * CPU 核心数 + 1

# 使用的 worker 类。根据应用的特性选择：
# - 'sync'：适合轻负载应用
# - 'gevent'：适合高并发、IO 密集型应用，如网络或数据库操作频繁
worker_class = 'gevent'  # 适合高并发应用

# 每个 worker 处理的最大请求数，建议设置此值避免内存泄漏。
# 设置为 1000 表示每个 worker 在处理 1000 个请求后会自动重启，释放内存。
max_requests = 1000
max_requests_jitter = 50  # 在 max_requests 上增加随机量，避免所有 worker 同时重启

# 请求超时时间，单位为秒。可以根据你的应用处理复杂度进行调整。
# 如果你的应用有一些长时间运行的任务，可以适当增加该值。
timeout = 30  # 默认超时 30 秒

# 优雅超时时间，即当 worker 在关闭时还可以处理请求的时间。
# 这个值通常和 `timeout` 保持一致或稍大。
graceful_timeout = 30

# 保持连接时间，单位为秒。keepalive 可用于控制 HTTP 保持连接的时间，建议设置为 2-5 秒。
keepalive = 5  # 如果你的服务器负载较高，可将 keepalive 设置为 5 秒

# 日志文件路径
# 访问日志文件，用于记录所有的 HTTP 请求。
accesslog = '/var/log/gunicorn/access.log'
# 错误日志文件，用于记录所有错误消息。
errorlog = '/var/log/gunicorn/error.log'

# 日志级别。推荐使用 'info'，在调试时可以设置为 'debug'。
loglevel = 'info'  # 可选择 'debug'、'info'、'warning'、'error'

# 调试模式。可以用于开发环境，但不要在生产环境中启用。
# reload = True  # 仅在开发环境中启用，以自动重启代码变更的进程

# 预加载应用程序。启用该选项可以减少内存使用，特别适合多 worker 的环境。
# 但是，如果应用程序初始化较慢或依赖数据库连接，可能会导致进程竞争。
preload_app = True  # 在进程启动前预加载应用

# 降低线程并发压力，推荐启用。
# 这个选项可以避免一些线程切换引起的问题，提升高并发性能。
threads = 3  # 根据需要调整，适合多线程的应用程序

# 设定进程的优先级。低优先级的进程可以避免 CPU 高负载情况下的竞争。
# 此项用于优化系统负载较重时的资源分配。
worker_tmp_dir = '/dev/shm'  # 使用共享内存提升性能

# 限制 Gunicorn 使用的文件描述符数量，以控制应用资源的分配，适用于大规模并发场景。
limit_request_line = 8190  # 限制 HTTP 请求行的最大长度
limit_request_fields = 100  # 限制 HTTP 请求头的最大字段数
limit_request_field_size = 8190  # 限制 HTTP 请求头字段的最大大小

# 启用日志记录缓冲。可以提升日志写入性能，适合高日志吞吐量的应用。
logconfig_dict = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    },
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',
            'formatter': 'standard',
            'filename': '/var/log/gunicorn/custom_error.log',
            'level': 'ERROR',
        },
    },
    'loggers': {
        'gunicorn.error': {
            'handlers': ['file'],
            'level': 'ERROR',
            'propagate': True,
        },
    },
}
