在 CentOS 上使用 Gunicorn 运行 Python 应用程序需要进行以下步骤：

1. **安装必要的软件包**:
   确保系统已经安装了 `python3` 和 `pip3`，如果没有安装，可以使用以下命令进行安装：
   ```bash
   sudo yum update -y
   sudo yum install python3 -y
   sudo yum install python3-pip -y
   ```

2. **安装 Gunicorn**:
   使用 `pip3` 安装 Gunicorn：
   ```bash
   pip3 install gunicorn
   ```

3. **编写 Flask 应用**:
   创建一个简单的 Flask 应用来测试 Gunicorn。比如，在你的项目目录中创建一个 `app.py` 文件：
   ```python
   from flask import Flask
   app = Flask(__name__)

   @app.route('/')
   def hello():
       return "Hello, World!"

   if __name__ == "__main__":
       app.run()
   ```

4. **运行 Gunicorn**:
   在项目目录中，使用 Gunicorn 运行 Flask 应用。假设你的应用程序文件名是 `app.py`，你可以使用以下命令：
   ```bash
   gunicorn --bind 0.0.0.0:8000 app:app
   ```
   这会启动一个 Gunicorn 进程，监听在端口 8000 上。

5. **配置系统服务**:
   为了在系统重启后自动启动 Gunicorn，可以配置一个 systemd 服务。创建一个名为 `myapp.service` 的文件（例如在 `/etc/systemd/system/` 目录下）：
   ```ini
   [Unit]
   Description=Gunicorn instance to serve myapp
   After=network.target

   [Service]
   User=yourusername
   Group=nginx
   WorkingDirectory=/path/to/your/app
   ExecStart=/usr/local/bin/gunicorn --workers 3 --bind unix:myapp.sock -m 007 wsgi:app

   [Install]
   WantedBy=multi-user.target
   ```
   请根据实际情况修改 `User`、`WorkingDirectory` 和 `ExecStart` 路径。

6. **启动并启用服务**:
   ```bash
   sudo systemctl start myapp
   sudo systemctl enable myapp
   ```

7. **配置 Nginx（可选）**:
   如果需要通过 Nginx 代理访问你的应用，可以按以下步骤配置：

   安装 Nginx：
   ```bash
   sudo yum install nginx -y
   ```

   编辑 Nginx 配置文件（如 `/etc/nginx/conf.d/myapp.conf`）：
   ```nginx
   server {
       listen 80;
       server_name your_domain_or_IP;

       location / {
           include proxy_params;
           proxy_pass http://unix:/path/to/your/app/myapp.sock;
       }
   }
   ```

   启动并启用 Nginx：
   ```bash
   sudo systemctl start nginx
   sudo systemctl enable nginx
   ```

按照这些步骤，你应该可以在 CentOS 上使用 Gunicorn 运行你的 Python 应用程序。