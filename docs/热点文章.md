

# 文章权重分数计算 每1小时一次
0 */1 * * * root cd /data/ijiwei-dc2 && python3 ./dc/commands/article_hot_update.py >> /var/log/article_hot_update.log 2>&1

# 更新集微关键词权重 每2小时一次
0 */2 * * * root cd /data/ijiwei-dc2 && python3 ./dc/commands/article_jiwei_keyword.py >> /var/log/article_jiwei_keyword.log 2>&1

# 7天权重衰减 每天一次  0:0 执行
0 0 * * * root cd /data/ijiwei-dc2 && python3 ./dc/commands/hot_decay.py >> /var/log/hot_decay.log 2>&1
     