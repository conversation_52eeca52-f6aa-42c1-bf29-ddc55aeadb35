# mysql 数据结构

## mysql 数据结构
```sql
CREATE TABLE `ELK_Error` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `index` varchar(100) DEFAULT NULL COMMENT '索引',
  `esid` varchar(100) DEFAULT NULL COMMENT 'es id',
  `host` varchar(100) DEFAULT NULL COMMENT '服务器',
  `env` varchar(100) DEFAULT NULL COMMENT '环境',
  `service` varchar(100) DEFAULT NULL COMMENT '服务(系统)',
  `log_time` timestamp NULL DEFAULT NULL COMMENT '采集时间',
  `message` text COMMENT '消息体',
  `message_func` varchar(100) DEFAULT NULL COMMENT '函数',
  `message_lineno` int(11) DEFAULT NULL COMMENT '行号',
  `message_pathname` varchar(255) DEFAULT NULL COMMENT '路径',
  `message_time` datetime DEFAULT NULL COMMENT '时间',
  `message_levelno` int(11) DEFAULT NULL COMMENT '日志级别',
  `message_levelname` varchar(255) DEFAULT NULL COMMENT '级别名称',
  `message_process` int(11) DEFAULT NULL COMMENT '进程',
  `message_message` text COMMENT '消息内容',
  `error_type` int(4) DEFAULT 0 COMMENT '错误类型',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `levelname` (`message_levelname`) USING BTREE,
  KEY `host` (`host`) USING BTREE,
  KEY `env` (`env`) USING BTREE,
  KEY `service` (`service`) USING BTREE,
  KEY `index` (`index`) USING BTREE,
  KEY `message_lineno` (`message_lineno`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4;
```

## 错误类型
error_type  错误类型
```python
patterns = [
    (1, r'org\.openqa\.selenium\.NoSuchSessionException'),
    (2, r'raise Exception\(\'response is null\'\)'),
    (3, r'task is failed after \d+ times'),
    (4, r'TimeoutError: timed out'),
    (5, r'greenstalk\.NotFoundError'),
    (6, r'Read timed out'),
    (7, r'net::ERR_CONNECTION_TIMED_OUT'),
    (8, r'Max retries exceeded with url'),
    (9, r'打开网页失败, 失败原因'),
    (10, r'Message: Could not start a new session'),
    (11, r'Incorrect string value')
]
```