# 数据结构

## 监控报警表 DM_MonitorAlert
```sql
CREATE TABLE `DM_MonitorAlert` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `alert_type` int NOT NULL COMMENT '报警类型 1 日志 2数据',
  `task_id` int DEFAULT NULL COMMENT '任务ID（来源于DM_AlertTasks表的id字段）',
  `log_id` int DEFAULT NULL COMMENT '任务日志ID',
  `alert_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报警名称',
  `execution_duration` int DEFAULT NULL COMMENT '执行时长（毫秒）',
  `status` tinyint NOT NULL COMMENT '执行状态:1-成功，2-失败，3-执行中',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '执行结果',
  `createdUserId` int DEFAULT NULL COMMENT '操作人ID',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '任务开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '任务结束时间',
  `alert_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报警信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='监控报警表';


CREATE TABLE `DM_MonitorAlertDetail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `alert_id` int NOT NULL COMMENT '报警 id',
  `task_id` int DEFAULT NULL COMMENT '任务ID（来源于DM_AlertTasks表的id字段）',
  `service` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务（日志监控）',
  `error_type` tinyint DEFAULT '0' COMMENT '错误类型（日志监控）',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '统计结果',
  `total` int DEFAULT NULL COMMENT '统计数量',
  `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '执行状态:1-正常  2 删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='监控报警详情表';

```