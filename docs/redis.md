## redis 数据说明

### laravel_database_dc-main-task
```
2390046
2390045
```

### laravel_database_dc-site-task-analyse
来源  DCSiteList
```json
{
  "id": 5829766,
   "siteListId":  1375,
   "mainTaskId":  2389211,
   "baseUrl":  "https://www.jiemian.com",
   "url":  "https://www.jiemian.com/article/10765596.html",
   "title":  "\u8bda\u610f\u836f\u4e1a\uff1a\u76ee\u524d\u516c\u53f8\u751f\u4ea7\u7ecf\u8425\u4e00\u5207\u6b63\u5e38\uff0c\u5927\u5065\u5eb7\u4ea7\u54c1\u5df2\u9010\u6b65\u4e0a\u5e02",
   "taskStatus":  1,
   "taskAt":  "2024-02-02 09:10:48",
   "analysisStatus":  0,
   "httpStatusCode":  200,
   "mongoCollection":  "",
   "mongoKey":  "65bc4117f0617b7129354959",
   "retryTime":  1,
   "createdAt":  "2024-02-02 09:08:05",
   "updatedAt":  "2024-02-02 09:10:48",
   "esIndex":  "",
   "esId":  "",
   "ruleId":  0,
   "publicTime":  "",
   "coverImg":  "",
   "detailDcMethod":  2
}
```

## laravel_database_dc-site-list-hash
key: 1308

```json
{
  "sourceCategory": "dc_yq",
   "siteName":  "MEMS\u54a8\u8be2",
   "analysisMethod":  0,
   "isQuickShort":  2,
   "isKeyWord":  2,
   "detailDcMethod":  2,
   "sourceName":  "MEMS\u54a8\u8be2-\u5fae\u8bbf\u8c08",
   "ruleItem":  "{\"1284\": {\"5652\": {\"id\": 5652, \"siteListId\": null, \"siteRuleId\": 1284, \"columnTitle\": \"\\u6807\\u9898\", \"columnKey\": \"title\", \"crawlRuleType\": 1, \"crawlRule\": \"//*[@id=\\\"Article\\\"]/h1\", \"startFlag\": \"\", \"endFlag\": \"\", \"columnRegex\": \"(.*)(?=\\\\s\\\\d{4})\", \"columnDefault\": \"\", \"status\": 1, \"createdAt\": \"2023-12-04 10:09:07\", \"updatedAt\": \"2023-12-04 10:09:07\", \"analysisType\": 1, \"path\": \"\", \"pathLevel\": 0}, \"5653\": {\"id\": 5653, \"siteListId\": null, \"siteRuleId\": 1284, \"columnTitle\": \"\\u53d1\\u5e03\\u65e5\\u671f\", \"columnKey\": \"public_time\", \"crawlRuleType\": 1, \"crawlRule\": \"//div[@id=\\\"Article\\\"]/h1\", \"startFlag\": \"\", \"endFlag\": \"\", \"columnRegex\": \"\\\\d{4}-\\\\d{2}-\\\\d{2}\\\\s\\\\d{2}:\\\\d{2}:\\\\d{2}\", \"columnDefault\": \"\", \"status\": 1, \"createdAt\": \"2023-12-04 10:09:07\", \"updatedAt\": \"2023-12-04 10:09:07\", \"analysisType\": 1, \"path\": \"\", \"pathLevel\": 0}, \"5654\": {\"id\": 5654, \"siteListId\": null, \"siteRuleId\": 1284, \"columnTitle\": \"\\u6b63\\u6587\", \"columnKey\": \"text\", \"crawlRuleType\": 1, \"crawlRule\": \"//div[@class=\\\"content\\\"]\", \"startFlag\": \"\", \"endFlag\": \"\", \"columnRegex\": \"\", \"columnDefault\": \"\", \"status\": 1, \"createdAt\": \"2023-12-04 10:09:07\", \"updatedAt\": \"2023-12-04 10:09:07\", \"analysisType\": 1, \"path\": \"\", \"pathLevel\": 0}}}",
   "isHotModule":  2,
   "tags":  null
}
```