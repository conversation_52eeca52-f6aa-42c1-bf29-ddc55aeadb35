# elasticsearch 数据结构


## 创建索引模板
```
PUT _template/elk_template
{
  "index_patterns": [
    "elk_*"
  ],
  "mappings": {
    "dynamic_date_formats": ["yyyy-MM-dd HH:mm:ss Z||yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis||yyyy-MM-dd'T'HH:mm:ss.SSSX"],
    "dynamic_templates": [
      {
        "string_fields": {
          "match": "*",
          "match_mapping_type": "string",
          "mapping": {
            "type": "text",
            "analyzer": "ik_max_word",
            "search_analyzer": "ik_smart",
            "fields": {
              "keyword": {
                "type": "keyword",
                "ignore_above": 256
              }
            }
          }
        }
      }
    ]
  }
}

```

## 创建 elk_log_prod_python 索引
```
PUT elk_log_prod_python
{
  "settings": {
    "index": {
      "number_of_shards": "3",
      "number_of_replicas": "2",
      "refresh_interval": "30s",
      "max_result_window": "100000"
    }
  }
}

```

## 创建 elk_log_prod_gzh 索引
```
PUT elk_log_prod_gzh
{
  "settings": {
    "index": {
      "number_of_shards": "3",
      "number_of_replicas": "2",
      "refresh_interval": "30s",
      "max_result_window": "100000"
    }
  }
}


```

## 创建 elk_log_prod_php 索引
```
PUT elk_log_prod_php
{
  "settings": {
    "index": {
      "number_of_shards": "3",
      "number_of_replicas": "2",
      "refresh_interval": "30s",
      "max_result_window": "100000"
    }
  }
}

```

## 创建 elk_log_prod_ai 索引
```
PUT elk_log_prod_ai
{
  "settings": {
    "index": {
      "number_of_shards": "3",
      "number_of_replicas": "2",
      "refresh_interval": "30s",
      "max_result_window": "100000"
    }
  }
}

```

## 删除索引
```
DELETE elk_log_prod_python
DELETE elk_log_prod_gzh
DELETE elk_log_prod_php
DELETE elk_log_prod_ai
```

## 生成索引
```
PUT /elk_log_prod_python
PUT /elk_log_prod_gzh
PUT /elk_log_prod_ai
PUT /elk_log_prod_php
```