使用方法：复制配置文件.txt并重命名为config.ini，修改相应的字段，打开软件就可以操作接口了

只有当enableIntercept = false 时，才不会拦截公众号发文


wechatDir 微信路径(版本3.6.0.18)
port http服务端口

http接口文档（两个内容是一样的，哪个舒服看哪个）：
https://www.showdoc.cc/1965672579479613
https://www.eolink.com/share/index?shareCode=Ay4wG3

拦截到的数据(链接、标题和发布时间等)有四个保存的方式：mysql、redis、api(post提交数据)和csv文件
想保存到哪里就配置哪个的连接信息，优先顺序：mysql > redis > api > csv
如果没有配置前三个或者配置了但是连接信息错误的话，都会保存到csv文件里

软件支持控制多个微信的, 只需要启动多个微信，用不同的wxpid即可


配套的微信下载地址：
https://www.123pan.com/s/ihEKVv-sv2x 提取码:W0Ns

链接: https://pan.baidu.com/s/1X7Vde-uwg0GgdA1qfpi2Qg?pwd=66p5 提取码: 66p5 



C:\Windows\System32\drivers\etc\hosts 在这个hosts文件里增加两条记录 
127.0.0.1 dldir1.qq.com 
127.0.0.1  dl.softmgr.qq.com
作用是防止微信强制更新，这个必须做。


配置文件中的sendmsg，一般情况下是用不到的。如果没有出现拦截不到消息的情况，不需要管。
测试的时候有些小号会出现公众号消息不同步的情况，就是明明公众号已经发文，但是微信客户端并
没有显示，当有微信好友给你发消息的时候，公众号的消息才全部同步过来，这个时候就需要
配置sendmsg，意思是每隔5分钟往配置的服务号发送随机消息，这样公众号的消息就会被同步过来

注意：sendmsg前面的键名随便写，值必须是服务号的公众号id，不能是订阅号和好友

服务号是指和好友显示在同一栏的公众号，订阅号则统一归到了订阅号里，

配置的服务号需要是已经关注的

示例的配置文件中的服务号是：WxPusher消息推送平台、方糖 （需要的话，搜索这两个公众号并关注下）




