<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0,viewport-fit=cover"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />

    <title></title>

    <link rel="dns-prefetch" href="//res.wx.qq.com" />
    <link rel="dns-prefetch" href="//mmbiz.qpic.cn" />
    <link rel="shortcut icon" type="image/x-icon" href="//res.wx.qq.com/wxawap/zh_CN/favicon.ico" />
    <link rel="icon" type="image/x-icon" href="//res.wx.qq.com/wxawap/zh_CN/favicon.ico" />
    <link
      rel="stylesheet"
      href="https://res.wx.qq.com/open/libs/weui/0.4.3/weui.min.css"
      media="all"
    />
    <style type="text/css">
      html {}
      body {
        line-height: 1.6;
        font-family: -apple-system-font, BlinkMacSystemFont, "Helvetica Neue",
          Helvetica, Arial, sans-serif;
        font-size: 17px;
        background-color: #ffffff;
      }
      body,
      h1,
      h2,
      h3,
      h4,
      h5,
      p,
      ul,
      ol,
      dl,
      dd,
      fieldset,
      textarea {
        margin: 0;
      }
      fieldset,
      legend,
      textarea,
      input,
      button {
        padding: 0;
      }
      button,
      input,
      select,
      textarea {
        font-family: inherit;
        font-size: 100%;
        margin: 0;
        *font-family: -apple-system-font, BlinkMacSystemFont, "Helvetica Neue",
          Helvetica, Arial, sans-serif;
      }
      ul,
      ol {
        padding-left: 0;
        list-style-type: none;
      }
      a img,
      fieldset {
        border: 0;
      }
      a {
        text-decoration: none;
      }
      .radius_avatar {
        display: inline-block;
        background-color: #fff;
        padding: 3px;
        border-radius: 50%;
        -moz-border-radius: 50%;
        -webkit-border-radius: 50%;
        overflow: hidden;
        vertical-align: middle;
      }
      .radius_avatar img {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        -moz-border-radius: 50%;
        -webkit-border-radius: 50%;
        background-color: #eee;
      }
      html {
        height: 100%;
        position: relative;
        background-color: #f7f7f7;
        color: rgba(0, 0, 0, 0.9);
      }
      body {
        min-height: 100%;
        position: relative;
      }
      a {
        color: #576b95;
      }
      h2 {
        font-size: 15px;
      }
      h3 {
        font-weight: 400;
      }
      .weui_msg_weapp_info {
        height: 100%;
        padding-top: 0;
        text-align: left;
      }
      .weui_msg_weapp_info .radius_avatar {
        width: 60px;
        height: 60px;
        padding: 0;
      }
      .weui_msg_weapp_info .weui_icon_area {
        margin-bottom: 10px;
      }
      .weui_msg_weapp_info .weui_extra_area {
        font-size: 13px;
      }
      .weui_msg_weapp_info .weui_text_area {
        padding: 0 15px;
        margin-bottom: 20px;
        padding: 32px 24px 45px 24px;
      }
      .weui_msg_weapp_info .weui_icon_area,
      .weui_msg_weapp_info .weui_msg_title,
      .weui_msg_weapp_info .weui_msg_desc {
        text-align: center;
      }
      .weui_msg_weapp_info .list_title {
        font-size: 17px;
        font-family: PingFangSC-Medium;
        color: rgba(0, 0, 0, 0.9);
      }
      .plugin_logo {
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='16' height='16' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M8 .7a7.5 7.5 0 110 15 7.5 7.5 0 010-15zm0 .9a6.6 6.6 0 100 13.2A6.6 6.6 0 008 1.6zm.45 5.1v5.25h-.9V6.7h.9zM8 4.45a.75.75 0 110 1.5.75.75 0 010-1.5z' fill='%23000' fill-rule='evenodd' fill-opacity='.3'/%3e%3c/svg%3e");
        height: 18px;
        width: 18px;
        background-repeat: no-repeat;
        background-position: center center;
        line-height: 1.6;
        position: relative;
        left: -2px;
        top: 3px;
      }
      /* .link-plugin{
        display: flex;
      } */
      .card-desc{
        overflow: hidden;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 19.6px;
        line-height: 1.4;
      }
      .card-desc div{
        padding-bottom: 8px;
      }
      .card-thum{
        margin-top: 16px;
        margin-bottom: 16px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 17px;
        overflow: hidden;
        line-height: 1.4;
      }
      .height-transition-enter-active, .height-transition-leave-active {
        transition: .3s cubic-bezier(0.215, 0.61, 0.355, 1);
        overflow: hidden;
      }
      .card{
        display: flex;
        flex-direction: column;
        line-height: 1.6;
      }
      .card::after{
        content: '';
        width: 100%;
        height: 1px;
        transform: scaleY(0.5);
        background: rgba(0, 0, 0, 0.1);
      }
      .card:nth-child(2)::before{
        content: '';
        width: 100%;
        height: 1px;
        transform: scaleY(0.5);
        background: rgba(0, 0, 0, 0.1);
      }
      .weui_msg_weapp_info .over_title {
        margin-top: 48px;
        display: flex;
        align-items: center;
      }
      .weui_msg_weapp_info .notice_info {
        margin-top: 12px;
        font-size: 17px;
        color: rgba(0, 0, 0, 0.9);
       line-height: 1.6;
      }
      .weui_msg_weapp_info .info_link li:nth-child(1) {
        margin-top: 12px;
      }
      .weui_msg_weapp_info .info_link li {
        font-size: 17px;
        color: #576b95;
       line-height: 1.6;
        margin-bottom: 4px;
      }
      .weui_msg_weapp_info .info_list {
        position: relative;
        padding-top: 12px;
      }
      .weui_msg_weapp_info .info_list + h2 {
        margin-top: 48px;
      }
      .spec-padding{
          margin-left: -11px;
          word-break: break-all;
      }
      .weui_msg_weapp_info .info_item {
        margin-bottom: 12px;
        word-wrap: break-word;
        -webkit-hyphens: auto;
        -ms-hyphens: auto;
        hyphens: auto;
        display: flex;
        align-items: flex-start;
      }
      .weui_msg_weapp_info .info_item_inner {
        display: flex;
        flex-direction: column;
      }
      .weui_msg_weapp_info .info_item_title {
        font-size: 17px;
        color: rgba(0, 0, 0, 0.5);
       line-height: 1.6;
        min-width: 109px;
        max-width: 109px;
      }
      .weui_msg_weapp_info .fold {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
      }
      .weui_msg_weapp_info .info_item_desc {
        font-size: 17px;
       line-height: 1.6;
        color: rgba(0, 0, 0, 0.9);
       line-height: 1.6;
        flex: 1;
      }
      .info_opr {
        margin-top: 48px;
        text-align: center;
        width: 100%;
        left: 0;
        color: #576b95;
        font-size: 15px;
        bottom: 0;
      }
      [class^="weui-icon-"],
      [class*=" weui-icon-"] {
        display: inline-block;
        vertical-align: middle;
        width: 24px;
        height: 24px;
        -webkit-mask-position: 50% 50%;
        mask-position: 50% 50%;
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        -webkit-mask-size: 100%;
        mask-size: 100%;
        background-color: rgba(0, 0, 0, 0.3);
      }
      .weui-icon-arrow {
        -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
        mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
      }
      .weui-icon-arrow.down {
        width: 12px;
        height: 12px;
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
      }
      .unfold-btn {
        display: inline-block;
      }

      #wxaproduct-toast {
        position: fixed;
        z-index: 50000;
        top: 180px;
        left: 50%;
        transform: translateX(-50%);
        background: #4c4c4c;
        border-radius: 8px;
        text-align: center;
      }
      #wxaproduct-toast p {
        margin: 12px 20px;
        font-size: 14px;
        color: #ffffff;
        opacity: 0.9;
      }
      .folder {
        display: flex;
        align-items: center;
      }
      .folder__header {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        word-break: break-all;
      }
      .arrow_logo {
        margin-left: 4px;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='12' height='7' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M1.484.645L.6 1.529l4.816 4.816a.83.83 0 001.177 0l4.816-4.816-.884-.884-4.52 4.52L1.483.646z' fill='%23000' fill-rule='evenodd' fill-opacity='.3'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: center center;
        min-height: 10px;
        min-width: 20px;
      }
      .expandBtn {
        display: flex;
        align-items: center;
      }
      .morebtn {
        font-size: 17px;
        color: rgba(0, 0, 0, 0.5);
       line-height: 1.6;
      }
      .link-spec{
        margin-left: 12px;
      }
      .weui-mask {
        position: fixed;
        z-index: 1000;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        background: rgba(0,0,0,0.6);
      }
      .weui-half-screen-dialog_show{
        transition: transform .3s,-webkit-transform .3s;
        transform: translateY(0);
      }
      .weui-half-screen-dialog_close{
        transition: transform .3s,-webkit-transform .3s;
        transform: translateY(100%);
      }
      .weui-half-screen-dialog {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        max-height: 75%;
        z-index: 5000;
        line-height: 1.4;
        background-color: #fff;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        overflow: hidden;
        padding: 0 24px;
        padding: 0 calc(24px + constant(safe-area-inset-right)) constant(safe-area-inset-bottom) calc(24px + constant(safe-area-inset-left));
        padding: 0 calc(24px + env(safe-area-inset-right)) env(safe-area-inset-bottom) calc(24px + env(safe-area-inset-left));
      }
      .close-wrap{
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .close-arrow{
        height: 16px;
        width: 40px;
        background: #F7F7F7;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .custom_bd{
        overflow-y: scroll!important;
        max-height: 60vh;
      }
      .custom_bd::-webkit-scrollbar{
        display: none;
      }
      @media only screen and (max-height:558px) {
        .weui-half-screen-dialog {
          max-height: none;
        }
      }

      .weui-half-screen-dialog__hd {
        font-size: 8px;
        height: 64px;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
      }

      .weui-half-screen-dialog__hd .weui-icon-btn {
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
      }

      .weui-half-screen-dialog__hd .weui-icon-btn:active {
        opacity: 0.5;
      }

      .weui-half-screen-dialog__hd__side {
        position: relative;
        left: -8px;
      }

      .weui-half-screen-dialog__hd__main {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
      }

      .weui-half-screen-dialog__hd__side+.weui-half-screen-dialog__hd__main {
        text-align: center;
        padding: 0 40px;
      }

      .weui-half-screen-dialog__hd__main+.weui-half-screen-dialog__hd__side {
        right: -8px;
        left: auto;
      }

      .weui-half-screen-dialog__hd__main+.weui-half-screen-dialog__hd__side .weui-icon-btn {
        right: 0;
      }

      .weui-half-screen-dialog__title {
        display: block;
        color: rgba(0,0,0,0.9);
        color: var(--weui-FG-0);
        font-weight: 700;
        font-size: 15px;
      }

      .weui-half-screen-dialog__subtitle {
        display: block;
        color: rgba(0,0,0,0.5);
        color: var(--weui-FG-1);
        font-size: 10px;
      }

      .weui-half-screen-dialog__bd {
        word-wrap: break-word;
        -webkit-hyphens: auto;
        hyphens: auto;
        overflow-y: auto;
        padding-bottom: 56px;
        font-size: 14px;
        color: rgba(0,0,0,0.9);
        color: var(--weui-FG-0);
      }

      .weui-half-screen-dialog__desc {
        font-size: 17px;
        font-weight: 700;
        color: rgba(0,0,0,0.9);
        color: var(--weui-FG-0);
        line-height: 1.4;
      }

      .weui-half-screen-dialog__tips {
        padding-top: 16px;
        font-size: 14px;
        color: rgba(0,0,0,0.3);
        color: var(--weui-FG-2);
        line-height: 1.4;
      }

      .weui-half-screen-dialog__ft {
        padding: 0 0 64px;
        text-align: center;
      }

      .weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2),.weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2)+.weui-btn {
        display: inline-block;
        vertical-align: top;
        margin: 0 8px;
        width: 120px;
      }

      .weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2):first-child,.weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2)+.weui-btn:first-child {
        margin-left: 0;
      }

      .weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2):last-child,.weui-half-screen-dialog__ft .ƒ:nth-last-child(n+2)+.weui-btn:last-child {
        margin-right: 0;
      }

      .weui-half-screen-dialog__btn-area+.weui-half-screen-dialog__attachment-area {
        margin-top: 24px;
        margin-bottom: -44px;
      }
      .js_dialog{
        transition: all 0.3s;
      }

      .fade-enter-active, .fade-leave-active {
        opacity: 1;
        transition: opacity .3s;
      }
      .fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
        opacity: 0;
      }

      .slide-enter-active, .slide-leave-active {
        transform: translateY(0);
        transition: transform .3s;
      }
      .slide-enter, .slide-leave-to /* .fade-leave-active below version 2.1.8 */ {
        transform: translateY(100%);
        transition: transform .3s;
      }
      .dialog-title{
        margin-top: 16px;
        font-size: 17px;
        font-weight: 600;
        display: flex;
        justify-content: center;
        text-align: left;
        margin-bottom: 24px;
        line-height: 1.4;
      }
      @media (prefers-color-scheme: dark) {
        html {
          background-color: #191919;
          color: rgba(255, 255, 255, 0.8);
        }
        body {
            background-color: #191919;
        }
        a,
        .info_opr {
          color: #7d90a9;
        }
        .weui_msg_weapp_info .info_item_title {
          color: rgba(255, 255, 255, 0.5);
        }
        .weui_msg_weapp_info .info_item_desc {
          color: rgba(255, 255, 255, 0.8);
        }
        .weui_msg_weapp_info .list_title {
          font-size: 17px;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.8);
          font-family: PingFangSC-Medium;
        }
        .weui_msg_weapp_info .notice_info {
          margin-top: 12px;
          font-size: 17px;
          color: rgba(255, 255, 255, 0.8);
         line-height: 1.6;
        }
        .arrow_logo {
          margin-left: 4px;
          background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='12' height='7' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M1.484.645L.6 1.529l4.816 4.816a.83.83 0 001.177 0l4.816-4.816-.884-.884-4.52 4.52L1.483.646z' fill='%23FFF' fill-rule='evenodd' fill-opacity='.3'/%3e%3c/svg%3e");          background-repeat: no-repeat;
          background-position: center center;
          min-height: 10px;
          min-width: 20px;
        }
        .morebtn {
          font-size: 17px;
          color: rgba(255, 255, 255, 0.5);
         line-height: 1.6;
        }
        .plugin_logo {
          background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='18' height='18' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M9 .867a8.333 8.333 0 110 16.666A8.333 8.333 0 019 .867zm0 1a7.333 7.333 0 100 14.666A7.333 7.333 0 009 1.867zm.5 5.666v5.834h-1V7.533h1zm-.5-2.5A.833.833 0 119 6.7a.833.833 0 010-1.667z' fill='%23FFF' fill-rule='evenodd' fill-opacity='.3'/%3e%3c/svg%3e");          height: 18px;
          width: 18px;
          background-repeat: no-repeat;
          background-position: center center;
          line-height: 1.6;
          position: relative;
          left: -2px;
          top: 3px;
        }
        .weui-half-screen-dialog{
          background-color: #2c2c2c;
        }
        .close-arrow{
          background: #3b3b3b;
        }
        .card::after{
          content: '';
          background: rgba(255, 255, 255, 0.05);
        }
        .card:nth-child(2)::before{
          content: '';
          background: rgba(255, 255, 255, 0.05);
        }
      }
      .info_item_desc_wxverify {
        display: flex;
        align-items: center;
      }
      .info_item_desc_wxverify_inner {
        /* margin-left: 4px; */
        width: 20px;
        height: 20px;
        position: relative;
        top: 4px;
        display: inline-block;
      }
      .info_item_desc_wxverify_inner.yellow {
        background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGVsbGlwc2UgY3g9IjEwLjQxNjciIGN5PSI5Ljk5OTkyIiByeD0iNS40MTY2NyIgcnk9IjQuMTY2NjciIGZpbGw9IndoaXRlIi8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTMuNjQ5NSAyLjI2MTQ4TDE0Ljg0NSA1LjE0NzY4TDE3LjczMTYgNi4zNDM2NEMxOC4wMzEgNi40Njc2MyAxOC4xNzMxIDYuODEwODEgMTguMDQ5MSA3LjExMDE2TDE2Ljg1MzMgOS45OTYxMUwxOC4wNDkxIDEyLjg4MzJDMTguMTczMSAxMy4xODI1IDE4LjAzMSAxMy41MjU3IDE3LjczMTYgMTMuNjQ5N0wxNC44NDQ4IDE0Ljg0NDlMMTMuNjQ5NSAxNy43MzE5QzEzLjUyNTUgMTguMDMxMiAxMy4xODIzIDE4LjE3MzQgMTIuODgzIDE4LjA0OTRMOS45OTYzNiAxNi44NTMyTDcuMTA5OTEgMTguMDQ5NEM2LjgxMDU3IDE4LjE3MzQgNi40NjczOSAxOC4wMzEyIDYuMzQzNCAxNy43MzE5TDUuMTQ3NTQgMTQuODQ0OEwyLjI2MTI0IDEzLjY0OTdDMS45NjE4OSAxMy41MjU3IDEuODE5NzQgMTMuMTgyNSAxLjk0Mzc0IDEyLjg4MzJMMy4xMzkyNSA5Ljk5NjM0TDEuOTQzNzQgNy4xMTAxNkMxLjgxOTc0IDYuODEwODEgMS45NjE4OSA2LjQ2NzYzIDIuMjYxMjQgNi4zNDM2NEw1LjE0NzcgNS4xNDc1Mkw2LjM0MzQgMi4yNjE0OEM2LjQ2NzM5IDEuOTYyMTQgNi44MTA1NyAxLjgxOTk5IDcuMTA5OTEgMS45NDM5OEw5Ljk5NjEzIDMuMTM5MjNMMTIuODgzIDEuOTQzOThDMTMuMTgyMyAxLjgxOTk5IDEzLjUyNTUgMS45NjIxNCAxMy42NDk1IDIuMjYxNDhaTTkuMDE5MzMgMTEuNzgyOEwxMy42ODU0IDcuMTE2N0wxNC41NzA5IDguMDAyMTRMOS41ODcwNyAxMi45ODM5QzkuMjc0NTUgMTMuMjk2MyA4Ljc2Nzk1IDEzLjI5NjEgOC40NTU2MSAxMi45ODM1TDUuODMzMTMgMTAuMzU5Mkw2LjcxNDQxIDkuNDc3ODhMOS4wMTkzMyAxMS43ODI4WiIgZmlsbD0iI0ZGQzMwMCIvPgo8L3N2Zz4K);
      }
      .info_item_desc_wxverify_inner.blue {
        background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xMy42NjIxIDIuMjg3MzJMMTQuODQ4MiA1LjE1MDk2TDE3LjcxMjMgNi4zMzc1NEMxOC4wMjQxIDYuNDY2NyAxOC4xNzIyIDYuODI0MTggMTguMDQzIDcuMTM2TDE2Ljg1NjUgOS45OTk0TDE4LjA0MyAxMi44NjM5QzE4LjE3MjIgMTMuMTc1NyAxOC4wMjQxIDEzLjUzMzIgMTcuNzEyMyAxMy42NjIzTDE0Ljg0ODQgMTQuODQ3NUwxMy42NjIxIDE3LjcxMjVDMTMuNTMyOSAxOC4wMjQ0IDEzLjE3NTQgMTguMTcyNCAxMi44NjM2IDE4LjA0MzNMOS45OTg4NiAxNi44NTYyTDcuMTM1NzUgMTguMDQzM0M2LjgyMzk0IDE4LjE3MjQgNi40NjY0NiAxOC4wMjQ0IDYuMzM3MyAxNy43MTI1TDUuMTUwOCAxNC44NDgxTDIuMjg3MDggMTMuNjYyM0MxLjk3NTI2IDEzLjUzMzIgMS44MjcxOSAxMy4xNzU3IDEuOTU2MzUgMTIuODYzOUwzLjE0MjUxIDkuOTk5NjNMMS45NTYzNSA3LjEzNkMxLjgyNzE5IDYuODI0MTggMS45NzUyNiA2LjQ2NjcgMi4yODcwOCA2LjMzNzU0TDUuMTUxMjggNS4xNTAwM0w2LjMzNzMgMi4yODczMkM2LjQ2NjQ2IDEuOTc1NSA2LjgyMzk0IDEuODI3NDMgNy4xMzU3NSAxLjk1NjU5TDkuOTk4NjMgMy4xNDIxOUwxMi44NjM2IDEuOTU2NTlDMTMuMTc1NCAxLjgyNzQzIDEzLjUzMjkgMS45NzU1IDEzLjY2MjEgMi4yODczMlpNMTMuNjg1NCA3LjExNjdMOS4wMTkzMyAxMS43ODI4TDYuNzE0NDEgOS40Nzc4OEw1LjgzMzEzIDEwLjM1OTJMOC42Mjg0OCAxMy4xNTY1VjEzLjE1NjVMOC42OTczMyAxMy4yMTUzQzguOTE0NCAxMy4zNzE1IDkuMjE4ODUgMTMuMzUyIDkuNDE0MSAxMy4xNTY4VjEzLjE1NjhMMTQuNTcwOSA4LjAwMjE0TDEzLjY4NTQgNy4xMTY3WiIgZmlsbD0iIzE0ODVFRSIvPgo8L3N2Zz4K);
      }
    </style>
    <script type="text/javascript">
      // fontScale
      (function() {
        var fontScale = "" * 1;
        if (fontScale) {
          var lh = 16000 / fontScale + "";
          var lineHeight = "";
          if (lineHeight.length > 2) lineHeight += lh[0];
          lineHeight += "." + lh.substr(lh.length - 2, 2);
          document.style.cssText =
            "-webkit-text-size-adjust: " +
            fontScale +
            "; line-height: " +
            lineHeight;
        }
      })();
    </script>
  </head>
  <body class="zh_CN">
    <div class="container" id="js_container"></div>
    <script src="//res.wx.qq.com/wxawap/zh_CN/libs/vue-f29bb7.js"></script>
    <script type="text/javascript">
    function html_decode(str) {
        var s = "";
        if (str.length == 0) return "";
        s = str.replace(/&amp;/g, "&");
        s = s.replace(/&lt;/g, "<");
        s = s.replace(/&gt;/g, ">");
        s = s.replace(/&nbsp;/g, " ");
        s = s.replace(/&#39;/g, "\'");
        s = s.replace(/&quot;/g, "\"");
        s = s.replace(/<br\/>/g, "\n");
        return s;
    };

    window.cgiData = {
        app_update_time: "1726211174" * 1,
        auth_3rd_list: [
            		],
        auth_3rd_info_list: [
            		],
        ret: '0' * 1,
        category_list: {"cate":["休闲游戏"]},
        desc: "螺丝消除，启动！",
        headimg_url: "http://wx.qlogo.cn/mmhead/dibCvqHg4Wndflj4VWUicXFf6cJQuwlN8xHQNK4f7YWic8a9zC3wBrJyuIT8EribBbbKHaetOevf8as",
        icp_beian_id: "湘ICP备**********号-1X",
        name: "长沙指米网络科技有限公司岳麓区分公司",
        nickname: "关不住我吧",
        wxaproduct_qua_size: '' * 1,
        wxaproduct_frequency_limit: "",

        show_wxaproduct_merchant_license: "false",
        username: "gh_b3ab84198f95",
        verify_id: "",
        wxaproduct_type:  "false",
        wxaproduct_subject_type: "",
        wxaproduct_license_frequency_limit: "",

        privacy_category: "4" * 1,
        game_privacy_wording: "开发者严格按照&amp;lt;a&nbsp;href=&amp;quot;https://game.weixin.qq.com/cgi-bin/minigame/static/privacy/?appid=wx8ca72ead29d3a293&amp;quot;&amp;gt;《关不住我吧小游戏隐私保护指引》&amp;lt;/a&amp;gt;处理你的个人信息，如你发现开发者不当处理你的个人信息，可进行&amp;lt;a&nbsp;href=&amp;quot;https://mp.weixin.qq.com/mp/wacomplain?action=show&amp;amp;appid=wx8ca72ead29d3a293&amp;amp;from=6#wechat_redirect&amp;quot;&amp;gt;投诉&amp;lt;/a&amp;gt;。",
        realname_type: "1" * 1,
        nickname_mod_list: {
                    },

        subject_change_list: {

            records: [
                                {
                    change_time: "**********" * 1,
                    time_str: "2024年04月26日",
                    realname_info: {
                                                new_info: {
                                                        license_no: "91430104MAD3LXME2D",
                            realname: "长沙指米网络科技有限公司岳麓区分公司",
                            realname_type: "1" * 1,
                                                    },
                        old_info: {
                                                        license_no: "91430104MACME1JA0H",
                            realname: "长沙指米网络科技有限公司",
                            realname_type: "1" * 1,
                                                    }
                                            }
                },
                                {
                    change_time: "**********" * 1,
                    time_str: "2023年11月13日",
                    realname_info: {
                                                new_info: {
                                                        license_no: "91430104MACME1JA0H",
                            realname: "长沙指米网络科技有限公司",
                            realname_type: "1" * 1,
                                                    },
                        old_info: {
                                                        license_no: "91430104MACKUFHMXM",
                            realname: "长沙指色网络科技有限公司长沙岳麓区分公司",
                            realname_type: "1" * 1,
                                                    }
                                            }
                },
                                {
                    change_time: "**********" * 1,
                    time_str: "2023年09月19日",
                    realname_info: {
                                                new_info: {
                                                        license_no: "91430104MACKUFHMXM",
                            realname: "长沙指色网络科技有限公司长沙岳麓区分公司",
                            realname_type: "1" * 1,
                                                    },
                        old_info: {
                                                        license_no: "",
                            realname: "个人",
                            realname_type: "0" * 1,
                                                    }
                                            }
                },
                            ]

        },

        wxaproduct_frequency_limit: "",

        abbr_mod_list: {

        },

        plugins: [
            		],
        request_domain: {
                        item: [
                                "https://cocos.com",
                                                "https://external-app.zxmn2018.com",
                                                "https://gamesapi.zxmn2018.com",
                                                "https://gamesapi2.aslk2018.com",
                                                "https://h5.udrig.com",
                                                "https://mmocgame.qpic.cn",
                                                "https://moderate.aslk2018.com",
                                                "https://puffergames.com:8443",
                                                "https://puffergames.com:8889",
                                                "https://res.wqop2018.com",
                                                "https://thirdwx.qlogo.cn",
                                                "https://wscdn.wqop2018.com",
                                                "https://yr-game-api.feigo.fun",
                                            ]
                    },
        wxa_verify_info: {
            verify_status: "1",
            customer_type: "1",
            customer_type_text: "企业",
            single_verify_identity: "",
        },
        show_licenses: "",
    };
    window.cgiData.nickname = html_decode(window.cgiData.nickname); // 去掉里面的空格等
</script>
    <script src="//res.wx.qq.com/wxawap/zh_CN/js/wap/weappinfo_tmpl-896922.js"></script>
    <script type="text/javascript">
      document.addEventListener("touchstart", function() {}, false);
    </script>
  </body>
</html>