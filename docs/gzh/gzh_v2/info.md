
# 公众号搜索结果
```json
{
  "status": 200,
  "user": "kanadeblisst15",
  "result": {
    "next_params": "{\"BusinessType\":\"1\",\"CliVersion\":80010711,\"ExtReqParams\":[{\"key\":\"netType\",\"text_value\":\"wifi\"},{\"key\":\"subType\",\"text_value\":\"\",\"uint_value\":\"0\"},{\"key\":\"AdPassThroughInfo\",\"text_value\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090620) XWEB/6945 Flue\"},{\"key\":\"AdCommonDeviceId\",\"text_value\":\"\"},{\"key\":\"TemplateNightModeType\",\"uint_value\":\"0\"},{\"key\":\"currentPage\",\"uint_value\":2},{\"key\":\"requestId\",\"text_value\":\"879055c8-36d5-47cf-bec9-303e6d9b3c92\"},{\"key\":\"cookies\",\"text_value\":\"{\\\"box_offset\\\":0,\\\"businessType\\\":1,\\\"cookies_buffer\\\":\\\"Uh0IYhABGAEiBuaXpeaKpVABggEFEACiAQCaAwJCAA==\\\",\\\"doc_offset\\\":0,\\\"dup_bf\\\":\\\"\\\",\\\"isHomepage\\\":0,\\\"page_cnt\\\":1,\\\"query\\\":\\\"\\u65E5\\u62A5\\\",\\\"scene\\\":98}\\n\"},{\"key\":\"widgetVersion\",\"uint_value\":\"1023022\"},{\"key\":\"windowWidth\",\"uint_value\":\"1165\"}],\"IsHomePage\":0,\"Keyword\":\"\\u65E5\\u62A5\",\"Language\":\"zh_CN\",\"MatchUserList\":[],\"NumConditions\":[],\"Offset\":20,\"RequestId\":\"879055c8-36d5-47cf-bec9-303e6d9b3c92\",\"Scene\":98,\"SceneActionType\":7,\"SearchID\":\"5373008087384246431\",\"SessionID\":\"8510599356628226387\",\"SugID\":\"\",\"TagInfo\":{}}",
    "result": {
      "category": [
        {
          "type": 0,
          "word": "全部"
        },
        {
          "type": 7,
          "word": "视频号"
        },
        {
          "type": 2,
          "word": "文章"
        },
        {
          "selected": 1,
          "type": 1,
          "word": "公众号"
        },
        {
          "type": 262208,
          "word": "小程序"
        },
        {
          "type": 16777728,
          "word": "百科"
        },
        {
          "type": 16384,
          "word": "新闻"
        },
        {
          "type": 384,
          "word": "表情"
        },
        {
          "type": 8,
          "word": "朋友圈"
        }
      ],
      "continueFlag": 1,
      "cookies": "{\"box_offset\":0,\"businessType\":1,\"cookies_buffer\":\"Uh0IYhABGAEiBuaXpeaKpVABggEFEACiAQCaAwJCAA==\",\"doc_offset\":0,\"dup_bf\":\"\",\"isHomepage\":0,\"page_cnt\":1,\"query\":\"日报\",\"scene\":98}\n",
      "data": [
        {
          "subBoxes": [
            {
              "boxID": "split-0x1-0-2392014380",
              "boxMergeTag": 0,
              "boxMergeType": 109,
              "boxMergeValue": 3,
              "boxPos": 1,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "参与、沟通、记录时代。",
                  "docID": "2392014380",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/xrFYciaHL08D1hiaVU0brP9YwQH1wFm9D9AG7oAVaNE8RzGiaKekViaIovvgsjOicfxXcPUPiahpdZ1U1nndyYd4pP0w/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 493170387,
                      "reportId": "493170387-rselfmenu_0:menu_bizbutton:497962",
                      "title": "看报纸",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://www.peopleapp.com/"
                      }
                    },
                    {
                      "id": 493170387,
                      "reportId": "493170387-rselfmenu_1:menu_bizbutton:598008",
                      "title": "看视界",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://www.pdnews.cn/prod"
                      }
                    }
                  ],
                  "reportId": "2392014380:biz:181946",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "人民<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "rmrbwx",
                    "bizuin": "2392014380",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5MjAxNDM4MA==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"人民日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5MjAxNDM4MA==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5Dlw4H8vWoicXPXccEVkWYgrmSiavKwjaTkYuqswgXicqhA/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5Dlw4H8vWoicXPXccEVkWYgrmSiavKwjaTkYuqswgXicqhA/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/xrFYciaHL08D1hiaVU0brP9YwQH1wFm9D9AG7oAVaNE8RzGiaKekViaIovvgsjOicfxXcPUPiahpdZ1U1nndyYd4pP0w/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "人民日报",
                    "signature": "参与、沟通、记录时代。",
                    "userName": "gh_363b924965e9",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "人民日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "公众号",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": null
            },
            {
              "boxID": "split-0x1-0-2397343481",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 2,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "思想品格  人文情怀",
                  "docID": "2397343481",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/XlmGXYTkxQNUbQx8t90NqMWMcJvqtMTclrN2xmWibUKLNOtov0OMQavpSF96x310tYTUul4eOQ5uib7FPLCmUoUA/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 448686787,
                      "reportId": "448686787-rselfmenu_2:menu_bizbutton:413101",
                      "title": "文化遗存",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://mp.weixin.qq.com/mp/appmsgalbum?__biz=MjM5NzM0MzQ4MQ==&action=getalbum&album_id=2395946838257565698#wechat_redirect"
                      }
                    },
                    {
                      "id": 448686787,
                      "reportId": "448686787-rselfmenu_0:menu_bizbutton:901074",
                      "title": "APP下载",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://www.gmdaily.cn/k/"
                      }
                    }
                  ],
                  "reportId": "2397343481:biz:571176",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "光明<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "gmrb1949",
                    "bizuin": "2397343481",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5NzM0MzQ4MQ==#wechat_webview_type=1&wechat_redirect\"},{\"title\":\"查看地理位置\",\"title_key\":\"__mp_wording__brandinfo_location\",\"url\":\"http://3gimg.qq.com/lightmap/v1/wxmarker/index.html?marker=coord:39.89333,116.41505;title:%E5%85%89%E6%98%8E%E6%97%A5%E6%8A%A5;addr:%E4%B8%AD%E5%9B%BD%E5%8C%97%E4%BA%AC%E5%B8%82%E4%B8%9C%E5%9F%8E%E5%8C%BA%E7%8F%A0%E5%B8%82%E5%8F%A3%E4%B8%9C%E5%A4%A7%E8%A1%975%E5%8F%B7&referer=wexinmp_profile\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"光明日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5NzM0MzQ4MQ==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM70Lv4n9RSLNeff0Ew4s4Qq1q6WyUorgnCiby3qOTTWoVg/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM70Lv4n9RSLNeff0Ew4s4Qq1q6WyUorgnCiby3qOTTWoVg/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/XlmGXYTkxQNUbQx8t90NqMWMcJvqtMTclrN2xmWibUKLNOtov0OMQavpSF96x310tYTUul4eOQ5uib7FPLCmUoUA/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "光明日报",
                    "signature": "思想品格  人文情怀",
                    "userName": "gh_9a5523ee7d3d",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "光明日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-3017159050",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 3,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "CHINA DAILY（中国<em class=\"highlight\">日报</em>）官方中文公众号，解读中国万象，点评世界风云。",
                  "docID": "3017159050",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/8vd2Hk2TS2OBG0XWq30JqD3gp8eribBxXAOfu5KkVib8icvAkbt6NBVXNASCpRMIfwgnwyrK7lD3TCKqkAcdgcTFA/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 529846366,
                      "reportId": "529846366-rselfmenu_1:menu_bizbutton:534562",
                      "title": "我的商城",
                      "jumpInfo": {
                        "jumpType": 2,
                        "userName": "gh_c14a7f277482@app",
                        "weappUrl": "pages/home/<USER>/index.html"
                      }
                    },
                    {
                      "id": 529846366,
                      "reportId": "529846366-rselfmenu_2:menu_bizbutton:969886",
                      "title": "号内搜",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://data.newrank.cn/m/s.html?s=Ny0xOTI4KDA9"
                      }
                    }
                  ],
                  "reportId": "3017159050:biz:696558",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "中国<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "CHINADAILYWX",
                    "bizuin": "3017159050",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MzAxNzE1OTA1MA==#wechat_webview_type=1&wechat_redirect\"},{\"title\":\"查看地理位置\",\"title_key\":\"__mp_wording__brandinfo_location\",\"url\":\"http://3gimg.qq.com/lightmap/v1/wxmarker/index.html?marker=coord:39.98002,116.42415;title:%E4%B8%AD%E5%9B%BD%E6%97%A5%E6%8A%A5;addr:%E4%B8%AD%E5%9B%BD%E5%8C%97%E4%BA%AC%E5%B8%82%E5%8C%97%E4%BA%AC%E5%B8%82%E6%9C%9D%E9%98%B3%E5%8C%BA&referer=wexinmp_profile\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"中国日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MzAxNzE1OTA1MA==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7YqeKos37SyiaHJHUibsdNcPM0FU6nbQpHznib6xlicgvNqw/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7YqeKos37SyiaHJHUibsdNcPM0FU6nbQpHznib6xlicgvNqw/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/8vd2Hk2TS2OBG0XWq30JqD3gp8eribBxXAOfu5KkVib8icvAkbt6NBVXNASCpRMIfwgnwyrK7lD3TCKqkAcdgcTFA/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "中国日报",
                    "signature": "CHINA DAILY（中国日报）官方中文公众号，解读中国万象，点评世界风云。",
                    "userName": "gh_3707d9541c3f",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "中国日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-3274292289",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 4,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "《科技<em class=\"highlight\">日报</em>》是富有鲜明科技特色的综合性<em class=\"highlight\">日报</em>，是面向国内外公开发行的中央主流新闻媒体，是党和国家在科技领域的重要舆论前沿，是广大读者依靠科技创造财富、提升文明、刷新生活的服务平台，是中国科技界面向社会、连接世界的明亮窗口。",
                  "docID": "3274292289",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/mcrLqJvbDOo7mUkRjKpFqQNd2MaCErB3Hl4qhtBPULWQdTr8mziccXZRbPAHHDt4JrFjicyZLvkOeHc7YzcgLiaLg/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 424436605,
                      "reportId": "424436605-rselfmenu_0:menu_bizbutton:843923",
                      "title": "数字科报",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "http://digitalpaper.stdaily.com/"
                      }
                    },
                    {
                      "id": 424436605,
                      "reportId": "424436605-rselfmenu_2:menu_bizbutton:941421",
                      "title": "深瞳",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://mp.weixin.qq.com/mp/appmsgalbum?__biz=MzI3NDI5MjI4OQ==&action=getalbum&album_id=1670388376211914759#wechat_redirect"
                      }
                    }
                  ],
                  "reportId": "3274292289:biz:974631",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "科技<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "kjrbwx",
                    "bizuin": "3274292289",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MzI3NDI5MjI4OQ==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"科技日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MzI3NDI5MjI4OQ==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4QCiaZol5cnkf3mpzuAEuZ7dEB0UWfKkAQot9Da7lNnXQ/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4QCiaZol5cnkf3mpzuAEuZ7dEB0UWfKkAQot9Da7lNnXQ/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/mcrLqJvbDOo7mUkRjKpFqQNd2MaCErB3Hl4qhtBPULWQdTr8mziccXZRbPAHHDt4JrFjicyZLvkOeHc7YzcgLiaLg/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "科技日报",
                    "signature": "《科技日报》是富有鲜明科技特色的综合性日报，是面向国内外公开发行的中央主流新闻媒体，是党和国家在科技领域的重要舆论前沿，是广大读者依靠科技创造财富、提升文明、刷新生活的服务平台，是中国科技界面向社会、连接世界的明亮窗口。",
                    "userName": "gh_e829f4e7d3c7",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "科技日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2397274880",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 5,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "权威、实力，源自人民。",
                  "docID": "2397274880",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/ibQ2cXpBDzUN9R1aIvjLSz0K4MCxw7gfyhzqLxox7PrdBq0ShvZJZ1VV88cOEURqKU536A7qsvSwZQx6ia2rvQvw/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "2397274880:biz:532858",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "人民网",
                  "jumpInfo": {
                    "aliasName": "people_rmw",
                    "bizuin": "2397274880",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5NzI3NDg4MA==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"人民网股份有限公司\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5NzI3NDg4MA==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":2}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7fyU4GApMpvWanVpMHA15fwyziaEqORgdqOZWGR2UKIuw/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7fyU4GApMpvWanVpMHA15fwyziaEqORgdqOZWGR2UKIuw/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/ibQ2cXpBDzUN9R1aIvjLSz0K4MCxw7gfyhzqLxox7PrdBq0ShvZJZ1VV88cOEURqKU536A7qsvSwZQx6ia2rvQvw/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "人民网",
                    "signature": "权威、实力，源自人民。",
                    "userName": "gh_d62474859d61",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "人民网股份有限公司"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2393698380",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 6,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "知上海，观天下",
                  "docID": "2393698380",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/wm80Ec5NJpmqr7SyuJeevg3QwTcB3cZjfMRzKGxPzGuiah54sCKJDMJZeCrLhsU0xVhKjeCgcCicxyCVdtKw9ZjA/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "2393698380:biz:407205",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "解放<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "jiefangdaily",
                    "bizuin": "2393698380",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5MzY5ODM4MA==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"解放日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5MzY5ODM4MA==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7OqSLYuGVgPAD8Wiad1icQ9EGmLibTtM1soz4Qoz2icXcYBg/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7OqSLYuGVgPAD8Wiad1icQ9EGmLibTtM1soz4Qoz2icXcYBg/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/wm80Ec5NJpmqr7SyuJeevg3QwTcB3cZjfMRzKGxPzGuiah54sCKJDMJZeCrLhsU0xVhKjeCgcCicxyCVdtKw9ZjA/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "解放日报",
                    "signature": "知上海，观天下",
                    "userName": "gh_321d62483744",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "解放日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-1240574601",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 7,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "“央视新闻”是中央广播电视总台新闻新媒体旗舰账号，是重大新闻、突发事件和重要报道的总台首发平台。",
                  "docID": "1240574601",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/sz_mmbiz_png/oq1PymRl9D6qg8YXiaVFVOicJp5b3Hzhib1Ee28E8kYhmoYpzpUqbLNwxINyx7zTYW897sxC6Rphe9MCcpSPSUshQ/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 440310435,
                      "reportId": "440310435-rselfmenu_0:menu_bizbutton:498608",
                      "title": "文博日历",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://mp.weixin.qq.com/mp/appmsgalbum?__biz=MTI0MDU3NDYwMQ==&action=getalbum&album_id=2858552699561410561#wechat_redirect"
                      }
                    },
                    {
                      "id": 440310435,
                      "reportId": "440310435-rselfmenu_1:menu_bizbutton:419653",
                      "title": "夜读",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://mp.weixin.qq.com/mp/homepage?__biz=MTI0MDU3NDYwMQ==&hid=3&sn=7b9302ba2fa43d23b9e3f81c78e49a57&scene=18"
                      }
                    }
                  ],
                  "reportId": "1240574601:biz:862757",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "央视新闻",
                  "jumpInfo": {
                    "aliasName": "cctvnewscenter",
                    "bizuin": "1240574601",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MTI0MDU3NDYwMQ==#wechat_webview_type=1&wechat_redirect\"},{\"title\":\"查看地理位置\",\"title_key\":\"__mp_wording__brandinfo_location\",\"url\":\"http://3gimg.qq.com/lightmap/v1/wxmarker/index.html?marker=coord:39.915065,116.463726;title:%E5%A4%AE%E8%A7%86%E6%96%B0%E9%97%BB;addr:%E4%B8%AD%E5%9B%BD%E5%8C%97%E4%BA%AC%E5%B8%82%E5%8C%97%E4%BA%AC%E5%B8%82%E6%9C%9D%E9%98%B3%E5%8C%BA%E4%B8%9C%E4%B8%89%E7%8E%AF%E4%B8%AD%E8%B7%AF&referer=wexinmp_profile\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"中央广播电视总台\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MTI0MDU3NDYwMQ==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/1gvL9ficRs1Guiby55YQzbbgSqN0b78KoAXyw54L5iaweh4YH9TYFS7WTaxy9ictp3cFl5UiaDT9WCIg/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/1gvL9ficRs1Guiby55YQzbbgSqN0b78KoAXyw54L5iaweh4YH9TYFS7WTaxy9ictp3cFl5UiaDT9WCIg/132",
                    "iconUrl": "http://mmbiz.qpic.cn/sz_mmbiz_png/oq1PymRl9D6qg8YXiaVFVOicJp5b3Hzhib1Ee28E8kYhmoYpzpUqbLNwxINyx7zTYW897sxC6Rphe9MCcpSPSUshQ/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "央视新闻",
                    "signature": "“央视新闻”是中央广播电视总台新闻新媒体旗舰账号，是重大新闻、突发事件和重要报道的总台首发平台。",
                    "userName": "wxid_pzhf43hmwizd11",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "中央广播电视总台"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2398264303",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 8,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "时政 · 热点 · 话题 | 解放<em class=\"highlight\">日报</em>出品，高端资讯品牌",
                  "docID": "2398264303",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/QvZej5zN2tv2iasb0ur1aalV8GiaGUHt9HG3yJoBmicaC1DF4UicZ5IefMjuIY2JiazILIsCUic0RcqnnFlpKkS2sicFw/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 430679685,
                      "reportId": "430679685-rselfmenu_0:menu_bizbutton:998406",
                      "title": "小观精选",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://web.shobserver.com/"
                      }
                    },
                    {
                      "id": 430679685,
                      "reportId": "430679685-rselfmenu_1:menu_bizbutton:398309",
                      "title": "报料建言",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://web.shobserver.com/staticsg/res/html/web/userContribute/navi.html?channel=shgc"
                      }
                    }
                  ],
                  "reportId": "2398264303:biz:633314",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "上观新闻",
                  "jumpInfo": {
                    "aliasName": "shobserver",
                    "bizuin": "2398264303",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5ODI2NDMwMw==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"解放日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5ODI2NDMwMw==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4PJ2SUpMjRx42Gwsm9zWXTucicw1KNtNpcfXnYy0mhEJQ/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4PJ2SUpMjRx42Gwsm9zWXTucicw1KNtNpcfXnYy0mhEJQ/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/QvZej5zN2tv2iasb0ur1aalV8GiaGUHt9HG3yJoBmicaC1DF4UicZ5IefMjuIY2JiazILIsCUic0RcqnnFlpKkS2sicFw/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "上观新闻",
                    "signature": "时政 · 热点 · 话题 | 解放日报出品，高端资讯品牌",
                    "userName": "gh_da009608b19f",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "解放日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-3097079126",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 9,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "中央政法委机关报",
                  "docID": "3097079126",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/mHvPQibgPEl6ib5wqmexOhv0qCT9hAs5qzNgHicOx908qD4ZjYsJ6iazdBZQcmGHaKWhiblK8TtjW9udaPp7XyXViarA/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 447748564,
                      "reportId": "447748564-rselfmenu_0:menu_bizbutton:916794",
                      "title": "法律法规",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://flk.npc.gov.cn/"
                      }
                    },
                    {
                      "id": 447748564,
                      "reportId": "447748564-rselfmenu_1:menu_bizbutton:522002",
                      "title": "追剧普法",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://mp.weixin.qq.com/mp/appmsgalbum?action=getalbum&__biz=MzA5NzA3OTEyNg==&scene=1&album_id=1500502014030610432&count=3#wechat_redirect"
                      }
                    }
                  ],
                  "reportId": "3097079126:biz:552048",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "法治<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "fazhiribaoxinwen",
                    "bizuin": "3097079126",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MzA5NzA3OTEyNg==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"法制日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MzA5NzA3OTEyNg==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7gxPBhqJtibL85CeSdk4iakibf74Gz4Y2hK4mf0ic15OfiavA/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7gxPBhqJtibL85CeSdk4iakibf74Gz4Y2hK4mf0ic15OfiavA/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/mHvPQibgPEl6ib5wqmexOhv0qCT9hAs5qzNgHicOx908qD4ZjYsJ6iazdBZQcmGHaKWhiblK8TtjW9udaPp7XyXViarA/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "法治日报",
                    "signature": "中央政法委机关报",
                    "userName": "gh_dd0d9dcbc9aa",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "法制日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2399587742",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 10,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "“中华第一刊”《半月谈》官微，每天帮你分析政治、经济、商业大事，解读社会发展，品味人生百态，您的政经顾问，学习良师，生活益友。",
                  "docID": "2399587742",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/eiccNfic7OE9LuE1q00IWLmgGuLOwmHJZvo9RJLajAfOjRy1TmSVOL8mA4ABvtMy3LKmS22ZpLKw11YQwjK3UeMA/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "2399587742:biz:563889",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "半月谈",
                  "jumpInfo": {
                    "aliasName": "banyuetan-weixin",
                    "bizuin": "2399587742",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5OTU4Nzc0Mg==#wechat_webview_type=1&wechat_redirect\"},{\"title\":\"查看地理位置\",\"title_key\":\"__mp_wording__brandinfo_location\",\"url\":\"http://3gimg.qq.com/lightmap/v1/wxmarker/index.html?marker=coord:39.901365130165615,116.21262429356574;title:%E5%8D%8A%E6%9C%88%E8%B0%88;addr:%E4%B8%AD%E5%9B%BD%E5%8C%97%E4%BA%AC%E5%B8%82%E7%9F%B3%E6%99%AF%E5%B1%B1%E5%8C%BA%E4%BA%AC%E5%8E%9F%E8%B7%AF8%E5%8F%B7&referer=wexinmp_profile\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"半月谈杂志社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5OTU4Nzc0Mg==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7c2YVbFwQXTI1VM3ksDkQpeAKV1b52GIWCRDbsuh8dmg/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7c2YVbFwQXTI1VM3ksDkQpeAKV1b52GIWCRDbsuh8dmg/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/eiccNfic7OE9LuE1q00IWLmgGuLOwmHJZvo9RJLajAfOjRy1TmSVOL8mA4ABvtMy3LKmS22ZpLKw11YQwjK3UeMA/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "半月谈",
                    "signature": "“中华第一刊”《半月谈》官微，每天帮你分析政治、经济、商业大事，解读社会发展，品味人生百态，您的政经顾问，学习良师，生活益友。",
                    "userName": "gh_e3ae8ac3dd21",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "半月谈杂志社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "type": 10
                      },
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2396123612",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 11,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "中央党报，经济大报。主流，权威，公信力。",
                  "docID": "2396123612",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/3QdXlNYeWk3IB77uZ5ROIe5bZaUD5oSHfPWAoGgib2btrA3SicmQpor0BSZicwYGLkIsWfiaIrxUXzNWcqTian3TgjQ/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "2396123612:biz:250432",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "经济<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "jjrbwx",
                    "bizuin": "2396123612",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5NjEyMzYxMg==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"经济日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5NjEyMzYxMg==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4lQpicLicOQTU83Kq8iaXuiagcZvjjpcPA4Cc5pdgv6eceTg/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4lQpicLicOQTU83Kq8iaXuiagcZvjjpcPA4Cc5pdgv6eceTg/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/3QdXlNYeWk3IB77uZ5ROIe5bZaUD5oSHfPWAoGgib2btrA3SicmQpor0BSZicwYGLkIsWfiaIrxUXzNWcqTian3TgjQ/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "经济日报",
                    "signature": "中央党报，经济大报。主流，权威，公信力。",
                    "userName": "gh_a09deab6adec",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "经济日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2390380115",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 12,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "知乎官方订阅号，每日精选知乎热门、有趣内容",
                  "docID": "2390380115",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/sz_mmbiz_png/hFB4FUPIIlIJvoUAg5vRbgP6330DQUQFpziaMoXPEbPRUX9gVuxbhVVHgc1RLTVMmHvSFib8ibb5TIfibeIQZ73tZQ/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "2390380115:biz:252954",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "知乎<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "zhihuribao",
                    "bizuin": "2390380115",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5MDM4MDExNQ==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"北京智者天下科技有限公司\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5MDM4MDExNQ==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":1,\"VerifyCustomerType\":1}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5GuSMxbCEJwCxruzVGIBmcmQRS7ESK26FCEgficuQH5Vg/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5GuSMxbCEJwCxruzVGIBmcmQRS7ESK26FCEgficuQH5Vg/132",
                    "iconUrl": "http://mmbiz.qpic.cn/sz_mmbiz_png/hFB4FUPIIlIJvoUAg5vRbgP6330DQUQFpziaMoXPEbPRUX9gVuxbhVVHgc1RLTVMmHvSFib8ibb5TIfibeIQZ73tZQ/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "知乎日报",
                    "signature": "知乎官方订阅号，每日精选知乎热门、有趣内容",
                    "userName": "gh_e59ba9dfaa66",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "北京智者天下科技有限公司"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "type": 10
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2106247381",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 13,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "高度决定影响力",
                  "docID": "2106247381",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/NwTqsUH47xiasKia23by8jnkk9B1Z3LXhCibSEeh5RgjYJPYYQfLM5Lsv0fTuBZ6XACcaCOcDO1UQXWmYZ4icLkJEA/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 521616060,
                      "reportId": "521616060-rselfmenu_0_0:menu_bizbutton:478176",
                      "title": "今日报纸",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "http://epaper.southcn.com/"
                      }
                    },
                    {
                      "id": 521616060,
                      "reportId": "521616060-rselfmenu_0_1:menu_bizbutton:718807",
                      "title": "南方+",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "http://static.nfapp.southcn.com/nftouch/index.html"
                      }
                    }
                  ],
                  "reportId": "2106247381:biz:152459",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "南方<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "NF_Daily",
                    "bizuin": "2106247381",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjEwNjI0NzM4MQ==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"南方日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjEwNjI0NzM4MQ==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM6NVKiaPj7FLJJnoJtp2Qu2VIibajSiayTqg2G66AQFGH35w/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM6NVKiaPj7FLJJnoJtp2Qu2VIibajSiayTqg2G66AQFGH35w/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/NwTqsUH47xiasKia23by8jnkk9B1Z3LXhCibSEeh5RgjYJPYYQfLM5Lsv0fTuBZ6XACcaCOcDO1UQXWmYZ4icLkJEA/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "南方日报",
                    "signature": "高度决定影响力",
                    "userName": "wxid_2439084392221",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "南方日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2395387512",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 14,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "More than a newspaper.",
                  "docID": "2395387512",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/9QZkmZZ8vQBEFEZFtgVdIOlPqicHp7MHiamhYA64avqYIWs84riaomZEg3v0gnTPicEj3pQKB2K51Z5IC0IlQfDicXw/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 438393968,
                      "reportId": "438393968-rselfmenu_0:menu_bizbutton:115689",
                      "title": "Search",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://data.newrank.cn/m/s.html?s=OSsyOjNIPTI%3D"
                      }
                    },
                    {
                      "id": 438393968,
                      "reportId": "438393968-rselfmenu_2:menu_bizbutton:325296",
                      "title": "History",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz=MjM5NTM4NzUxMg==#wechat_redirect"
                      }
                    }
                  ],
                  "reportId": "2395387512:biz:406233",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "上海<em class=\"highlight\">日报</em>SHINE",
                  "jumpInfo": {
                    "aliasName": "SHDSHINE",
                    "bizuin": "2395387512",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5NTM4NzUxMg==#wechat_webview_type=1&wechat_redirect\"},{\"title\":\"查看地理位置\",\"title_key\":\"__mp_wording__brandinfo_location\",\"url\":\"http://3gimg.qq.com/lightmap/v1/wxmarker/index.html?marker=coord:31.225672056780002,121.45728507041932;title:%E4%B8%8A%E6%B5%B7%E6%97%A5%E6%8A%A5Shdaily;addr:%E4%B8%AD%E5%9B%BD%E4%B8%8A%E6%B5%B7%E5%B8%82%E9%9D%99%E5%AE%89%E5%8C%BA%E5%A8%81%E6%B5%B7%E8%B7%AF755%E5%8F%B7&referer=wexinmp_profile\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"上海日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5NTM4NzUxMg==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM6qNKhHBvyVlXRezqCA9xiczxRKK4ib8iaBSicbw3Ar0I3yWg/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM6qNKhHBvyVlXRezqCA9xiczxRKK4ib8iaBSicbw3Ar0I3yWg/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/9QZkmZZ8vQBEFEZFtgVdIOlPqicHp7MHiamhYA64avqYIWs84riaomZEg3v0gnTPicEj3pQKB2K51Z5IC0IlQfDicXw/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "上海日报SHINE",
                    "signature": "More than a newspaper.",
                    "userName": "gh_780834872487",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "上海日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-3085157003",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 15,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "面向企业，面向工会，为亿万职工服务，宣传工人阶级创造性劳动和先进人物光辉思想。",
                  "docID": "3085157003",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/gMrUGSf38XAJ4WBSMsftH0OyfyuPyXiboFiaic3q9TKYM1WACXsp6t4CGK8DdJd7MvKYjDFZtdlmSSsaoKmgibb13A/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "menus": [
                    {
                      "id": 420800020,
                      "reportId": "420800020-rselfmenu_0_0:menu_bizbutton:317803",
                      "title": "中工网",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "http://www.workercn.cn/"
                      }
                    },
                    {
                      "id": 420800020,
                      "reportId": "420800020-rselfmenu_0_1:menu_bizbutton:544141",
                      "title": "工人日报微博",
                      "jumpInfo": {
                        "jumpType": 3,
                        "jumpUrl": "http://weibo.com/grrb"
                      }
                    }
                  ],
                  "reportId": "3085157003:biz:163049",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "工人<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "grrbwx",
                    "bizuin": "3085157003",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MzA4NTE1NzAwMw==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"工人日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MzA4NTE1NzAwMw==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7okibLXicKaBUc77JcYQX1Tv70blQzfkBDDOAfN7PicRKaQ/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7okibLXicKaBUc77JcYQX1Tv70blQzfkBDDOAfN7PicRKaQ/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/gMrUGSf38XAJ4WBSMsftH0OyfyuPyXiboFiaic3q9TKYM1WACXsp6t4CGK8DdJd7MvKYjDFZtdlmSSsaoKmgibb13A/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "工人日报",
                    "signature": "面向企业，面向工会，为亿万职工服务，宣传工人阶级创造性劳动和先进人物光辉思想。",
                    "userName": "gh_34b6ff304bfa",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "工人日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2395124745",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 16,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "《农民<em class=\"highlight\">日报</em>》创刊于1980年4月6日，是我国历史上第一张面向全国农村发行的报纸，农民<em class=\"highlight\">日报</em>是一份中央级、综合性大报。邓小平同志亲笔题写报名。",
                  "docID": "2395124745",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/sz_mmbiz_png/P0Dx5mRA64ibMXS9nz0fEDSBB9VZnlVbcdsI9XjUDAVXOZ6gnWQp6ovKIriaptmBCVZUWiccw5jdiab2ia0PicRJiatvw/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "2395124745:biz:645238",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "农民<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "farmersdaily",
                    "bizuin": "2395124745",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5NTEyNDc0NQ==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"农民日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5NTEyNDc0NQ==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/ufVMVgCvafKKOPEdCFicoow1eF3VFoJazyCEFEsx3OxbLOsBZGmokKKrAnWbFMS2NiczMJ0yPAeYM/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/ufVMVgCvafKKOPEdCFicoow1eF3VFoJazyCEFEsx3OxbLOsBZGmokKKrAnWbFMS2NiczMJ0yPAeYM/132",
                    "iconUrl": "http://mmbiz.qpic.cn/sz_mmbiz_png/P0Dx5mRA64ibMXS9nz0fEDSBB9VZnlVbcdsI9XjUDAVXOZ6gnWQp6ovKIriaptmBCVZUWiccw5jdiab2ia0PicRJiatvw/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "农民日报",
                    "signature": "《农民日报》创刊于1980年4月6日，是我国历史上第一张面向全国农村发行的报纸，农民日报是一份中央级、综合性大报。邓小平同志亲笔题写报名。",
                    "userName": "gh_1cb5f98db386",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "农民日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-3089222529",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 17,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "人民<em class=\"highlight\">日报</em>评论部微信公共账号。秉承党报评论的厚土，我们向新媒体平台伸出小小一枝，期待与您一起见证复杂而深刻的转型中国。",
                  "docID": "3089222529",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/3qGBpicMFJ40h1wXibl6VlHribduU4DhIRzWq5ZPADKPTpicRL7sjweyEvcqDYsYhllkTASqjtzxdp8tWzVQPOo7SQ/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "3089222529:biz:607432",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "人民<em class=\"highlight\">日报</em>评论",
                  "jumpInfo": {
                    "aliasName": "rmrbpl",
                    "bizuin": "3089222529",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MzA4OTIyMjUyOQ==#wechat_webview_type=1&wechat_redirect\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"人民日报社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MzA4OTIyMjUyOQ==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5f5VHK824nX8LX0GD51hRoszPG4Td4gibBJhPthiceOMqQ/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5f5VHK824nX8LX0GD51hRoszPG4Td4gibBJhPthiceOMqQ/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/3qGBpicMFJ40h1wXibl6VlHribduU4DhIRzWq5ZPADKPTpicRL7sjweyEvcqDYsYhllkTASqjtzxdp8tWzVQPOo7SQ/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "人民日报评论",
                    "signature": "人民日报评论部微信公共账号。秉承党报评论的厚土，我们向新媒体平台伸出小小一枝，期待与您一起见证复杂而深刻的转型中国。",
                    "userName": "gh_4a4914660bcd",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "人民日报社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-2399593722",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 18,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "教育部主管的全国性<em class=\"highlight\">日报</em>。主流教育媒体，权威行业大报！",
                  "docID": "2399593722",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/N2CRD1bp3XY9grhZTrTBEG15g3Kow47IBoN9u9LHs1MO6kDMibVsI1nsbrPhPuknXEVItP1kNibtwrgf4bzMYDMQ/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "2399593722:biz:969591",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "中国教育报",
                  "jumpInfo": {
                    "aliasName": "Zhongguojiaoyubao",
                    "bizuin": "2399593722",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MjM5OTU5MzcyMg==#wechat_webview_type=1&wechat_redirect\"},{\"title\":\"查看地理位置\",\"title_key\":\"__mp_wording__brandinfo_location\",\"url\":\"http://3gimg.qq.com/lightmap/v1/wxmarker/index.html?marker=coord:39.956989,116.361816;title:%E4%B8%AD%E5%9B%BD%E6%95%99%E8%82%B2%E6%8A%A5;addr:%E4%B8%AD%E5%9B%BD%E5%8C%97%E4%BA%AC%E5%B8%82%E6%B5%B7%E6%B7%80%E5%8C%BA%E6%96%87%E6%85%A7%E5%9B%AD%E5%8C%97%E8%B7%AF10%E5%8F%B7&referer=wexinmp_profile\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"中国教育报刊社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MjM5OTU5MzcyMg==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4VxJpznCpRv8kWIN7BLBIw2eRQc49Hxku4NvYWUiama3A/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4VxJpznCpRv8kWIN7BLBIw2eRQc49Hxku4NvYWUiama3A/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/N2CRD1bp3XY9grhZTrTBEG15g3Kow47IBoN9u9LHs1MO6kDMibVsI1nsbrPhPuknXEVItP1kNibtwrgf4bzMYDMQ/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "中国教育报",
                    "signature": "教育部主管的全国性日报。主流教育媒体，权威行业大报！",
                    "userName": "gh_331bf3c4e2a8",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "中国教育报刊社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-3093130320",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 19,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "家事国事天下事，事事关心。这里有值得青少年关注的新闻，也有精彩纷呈的活动信息。希望通过我们的平台，提升你的媒介素养能力，让你的课余生活更丰富。",
                  "docID": "3093130320",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/sz_mmbiz_png/9LuiblKaqvT2t11Gs5BxoYfzMeRu0I0omlGRwWJbmvsgbaXXotClS8CGKX0c8mPzlJovcOoL7JLS7wweScfaebw/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "3093130320:biz:865998",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "少年<em class=\"highlight\">日报</em>",
                  "jumpInfo": {
                    "aliasName": "Shaonianribao",
                    "bizuin": "3093130320",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MzA5MzEzMDMyMA==#wechat_webview_type=1&wechat_redirect\"},{\"title\":\"查看地理位置\",\"title_key\":\"__mp_wording__brandinfo_location\",\"url\":\"http://3gimg.qq.com/lightmap/v1/wxmarker/index.html?marker=coord:31.192011,121.466187;title:%E5%B0%91%E5%B9%B4%E6%97%A5%E6%8A%A5;addr:%E4%B8%AD%E5%9B%BD%E4%B8%8A%E6%B5%B7%E5%B8%82%E4%B8%8A%E6%B5%B7%E5%B8%82%E5%BE%90%E6%B1%87%E5%8C%BA%E4%B8%AD%E5%B1%B1%E5%8D%97%E4%BA%8C%E8%B7%AF151%E5%8F%B7&referer=wexinmp_profile\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"上海教育报刊总社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MzA5MzEzMDMyMA==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4MgSzf1mRGt6YkaRxYd4suHsBgmdUID3KQIQeRxATIWQ/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4MgSzf1mRGt6YkaRxYd4suHsBgmdUID3KQIQeRxATIWQ/132",
                    "iconUrl": "http://mmbiz.qpic.cn/sz_mmbiz_png/9LuiblKaqvT2t11Gs5BxoYfzMeRu0I0omlGRwWJbmvsgbaXXotClS8CGKX0c8mPzlJovcOoL7JLS7wweScfaebw/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "少年日报",
                    "signature": "家事国事天下事，事事关心。这里有值得青少年关注的新闻，也有精彩纷呈的活动信息。希望通过我们的平台，提升你的媒介素养能力，让你的课余生活更丰富。",
                    "userName": "gh_ee489fcde80c",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "上海教育报刊总社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            },
            {
              "boxID": "split-0x1-0-3002114553",
              "boxMergeTag": 0,
              "boxMergeValue": 3,
              "boxPos": 20,
              "boxPosMerge": 1,
              "count": 1,
              "items": [
                {
                  "desc": "《学习时报》1999年9月创刊，中共中央党校（国家行政学院）主管主办，面向全国，服务全党，以各级党政干部和广大知识分子为主要对象，是国内外公开发行的全党唯一专门讲学习的报纸。",
                  "docID": "3002114553",
                  "foldHide": 0,
                  "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/6xz9HxNXZQRicH3x7W3KeBdKQxeq1nRhXuHTDnB9D56iadd2ld7eAUkqe5wQ2MBWNpYhSXL5kRSn9IhqHYosjcjQ/0?wx_fmt=png",
                  "isTwoLineExpt": false,
                  "reportId": "3002114553:biz:261203",
                  "report_extinfo_str": "%7B%22foldHide%22%3A0%7D%0A",
                  "style": 0,
                  "title": "学习时报",
                  "jumpInfo": {
                    "aliasName": "studytimes",
                    "bizuin": "3002114553",
                    "brandFlag": 2,
                    "brandInfo": "{\"urls\":[{\"title\":\"查看历史消息\",\"title_key\":\"__mp_wording__brandinfo_history_massmsg\",\"url\":\"http://mp.weixin.qq.com/mp/getmasssendmsg?__biz=MzAwMjExNDU1Mw==#wechat_webview_type=1&wechat_redirect\"},{\"title\":\"查看地理位置\",\"title_key\":\"__mp_wording__brandinfo_location\",\"url\":\"http://3gimg.qq.com/lightmap/v1/wxmarker/index.html?marker=coord:39.957895,116.300896;title:%E5%AD%A6%E4%B9%A0%E6%97%B6%E6%8A%A5;addr:%E4%B8%AD%E5%9B%BD%E5%8C%97%E4%BA%AC%E5%B8%82%E5%8C%97%E4%BA%AC%E5%B8%82%E6%B5%B7%E6%B7%80%E5%8C%BA%E9%95%BF%E6%98%A5%E6%A1%A5%E8%B7%AF&referer=wexinmp_profile\"}]}\n",
                    "externalInfo": "{\"VerifySource\":{\"Description\":\"中央党校(国家行政学院)报刊社\",\"IntroUrl\":\"http://mp.weixin.qq.com/mp/getverifyinfo?__biz=MzAwMjExNDU1Mw==#wechat_webview_type=1&wechat_redirect\",\"Type\":0,\"VerifyBizType\":2,\"VerifyCustomerType\":9}}\n",
                    "headHDImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5KuEtGic5FmMTWNfic1QoyNVaxPHAhRQq8kZGFs7xfrokA/0",
                    "headImgUrl": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5KuEtGic5FmMTWNfic1QoyNVaxPHAhRQq8kZGFs7xfrokA/132",
                    "iconUrl": "http://mmbiz.qpic.cn/mmbiz_png/6xz9HxNXZQRicH3x7W3KeBdKQxeq1nRhXuHTDnB9D56iadd2ld7eAUkqe5wQ2MBWNpYhSXL5kRSn9IhqHYosjcjQ/0?wx_fmt=png",
                    "jumpType": 1,
                    "nickName": "学习时报",
                    "signature": "《学习时报》1999年9月创刊，中共中央党校（国家行政学院）主管主办，面向全国，服务全党，以各级党政干部和广大知识分子为主要对象，是国内外公开发行的全党唯一专门讲学习的报纸。",
                    "userName": "gh_7a3f946ffd2e",
                    "verifyFlag": 24
                  },
                  "source": {
                    "auth": 1,
                    "title": "中央党校(国家行政学院)报刊社"
                  },
                  "titleTag": {
                    "append": [
                      {
                        "noBg": true,
                        "title": "媒体",
                        "type": 12
                      }
                    ]
                  }
                }
              ],
              "real_type": 1,
              "resultType": 0,
              "subType": 0,
              "title": "",
              "totalCount": 200,
              "type": 91,
              "boxExtraInfo": {
                "real_type": 1,
                "resultType": 0,
                "subType": 0,
                "title": ""
              }
            }
          ],
          "type": 109,
          "boxExtraInfo": {
            "real_type": 1,
            "resultType": 0,
            "subType": 0,
            "title": "公众号"
          }
        }
      ],
      "direction": 2,
      "experiment": [
        {
          "key": "s1s_all_prefetch_next_page",
          "value": "2"
        }
      ],
      "isAutoPlayVideo": 1,
      "isDivide": 0,
      "isHomePage": 0,
      "lang": "zh_CN",
      "offset": 20,
      "pageNumber": 1,
      "query": "日报",
      "resultType": 0,
      "ret": 0,
      "searchID": "5373008087384246431",
      "timeStamp": 1726821351
    }
  }
}
```

# 解析
```python
import json
import pandas as pd

# 原始 JSON 数据
corrected_data = ''''''

# 加载 JSON 数据
json_data = json.loads(corrected_data)

# 提取需要的字段
extracted_data = []
for subbox in json_data['result']['result']['data'][0]['subBoxes']:
    for item in subbox['items']:
        jump_info = item.get('jumpInfo', {})
        extracted_data.append({
            'aliasName': jump_info.get('aliasName'),
            'nickName': jump_info.get('nickName'),
            'userName': jump_info.get('userName'),
            'signature': jump_info.get('signature')
        })

# 将数据转为 DataFrame 以便显示
df = pd.DataFrame(extracted_data)

# 输出 DataFrame
print(df)

```