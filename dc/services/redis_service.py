from typing import Callable, Union

import redis
from redis.typing import ExpiryT

from dc.conf.settings import get_settings


class RedisService:
    name = 'default'
    config = {}
    extra = {}
    client = None  # type: redis.client.Redis

    # redis_pool = redis.ConnectionPool(host='127.0.0.1', port= 6379, password= 'your pw', db= 0)
    # redis_conn = redis.Redis(connection_pool= redis_pool)

    def __init__(self, name: str = 'redis', run_model=None, **kwargs) -> None:
        super().__init__()
        self.name = name,
        self.config = get_settings(name, run_model=run_model)
        self.extra = kwargs

    def get_client(self) -> redis.client.Redis:
        """
        get redis client
        :return:
        """
        if not self.client:
            password = self.config.get('password')
            host = self.config.get('host')
            port = self.config.get('port')
            db = self.extra.get('db', 0)
            self.client = redis.Redis(host=host, port=port, password=password, db=db, decode_responses=True)

        return self.client

    def select(self, index: int, **kwargs) -> None:
        """
        select redis db
        :return:
        """
        if not self.client:
            self.client = self.get_client()

        return self.client.select(index, **kwargs)

    def get_client_pool(self) -> redis.client.Redis:
        """
        获取连接池
        :return: redis.client.Redis
        """
        password = self.config.get('password')
        host = self.config.get('host')
        port = self.config.get('port')
        db = self.extra.get('db', 0)
        redis_pool = redis.ConnectionPool(host=host, port=port, password=password, db=db, decode_responses=True)
        return redis.Redis(connection_pool=redis_pool)

    def get_length(self, key) -> int:
        """
        get key value length
        :author wjh
        :date 2023/9/13
        :param key:
        :return:
        """
        client = self.get_client()
        key_type = client.type(key)

        if key_type == 'string':
            return client.strlen(key)
        elif key_type == 'list':
            return client.llen(key)
        elif key_type == 'set':
            return client.scard(key)
        elif key_type == 'zset':
            return client.zcard(key)
        elif key_type == 'hash':
            return client.hlen(key)
        else:
            return 0

    def get(self, key: str,
            func: Callable = None,
            params: dict = None,
            ex: Union[ExpiryT, None] = None
            ):

        if not self.client:
            self.client = self.get_client()

        data = self.client.get(key)
        if data is None:
            print('not')
            data = func(params)
            result = self.client.set(key, data, ex=ex)
            assert result, 'redis set is fail'
            data = self.client.get(key)

        return data

    def close(self):
        if self.client:
            self.client.close()

    def quit(self):
        if self.client:
            self.client.quit()
