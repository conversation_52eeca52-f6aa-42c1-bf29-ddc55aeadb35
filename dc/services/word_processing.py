import os
import re
import jieba
import jieba.analyse
import codecs

# 加载停用词表
stopwords_path = os.path.dirname(os.path.realpath(__file__)) + '/../tests/ai/stopword.txt'
stopwords = codecs.open(stopwords_path, 'r', encoding='utf8').readlines()
stopwords = [w.strip() for w in stopwords]


def sep_and_del_stop_word(text):
    """
    拆词并去掉停用词
    """

    # 读取文本数据
    # text_path = 'content.txt'
    # text = codecs.open(text_path, 'r', encoding='utf8').read()

    # 分词并去除停用词
    words = jieba.cut(text)
    words = [w for w in words if w not in stopwords]
    print(words)

    res = "".join(words[0:])
    # 输出结果
    # print(res, len(text), len(res))
    return res


def html_special_chars(text):
    """
    删除所有 HTML 标签
    """
    article = re.sub(r'<.*?>', '', text)
    return article


def extract_key_sentences(text, topK=10):
    """
    使用TextRank算法抽取关键词
    """
    keywords = jieba.analyse.textrank(text, topK=topK)
    print(keywords, '-------------')
    key_sentences = []
    sentences = text.split('。')
    for sentence in sentences:
        for keyword in keywords:
            if keyword in sentence:
                key_sentences.append(sentence)
                break
    return '。'.join(key_sentences)


def replace_punctuation(text, change=0):
    """
    中英文标点符号转换
    """
    return text
    punctuation_dict = {
        '，': ',', '。': '.', '！': '!', '？': '?', '；': ';', '：': ':',
        '“': '"', '”': '"', '‘': "'", '’': "'", '【': '[', '】': ']',
        '（': '(', '）': ')', '《': '<', '》': '>', '——': '-', '…': '...'
    }
    for k, v in punctuation_dict.items():
        # 英文转中文
        if change:
            text = re.sub(re.escape(v), k, text)
        # 中文转英文
        else:
            text = re.sub(k, v, text)

    return text


def split_article(article):
    # 删除所有 HTML 标签

    # 将文章分解成段落
    paragraphs = article.split('\r\n')

    # 将段落按长度切分
    result = []
    current_paragraph = ''
    for p in paragraphs:
        if len(current_paragraph) + len(p) <= 500:
            current_paragraph += p
        else:
            result.append(current_paragraph)
            current_paragraph = p
    if current_paragraph:
        result.append(current_paragraph)

    return result


def split_text(text, max_length=500):
    """
    将输入的文本按照最大长度进行切分，返回切分后的文本列表。
    """
    result = []
    current_text = ''
    for word in text.split():
        if len(current_text) + len(word) + 1 <= max_length:
            current_text += ' ' + word
        else:
            result.append(current_text.strip())
            current_text = word
    if current_text:
        result.append(current_text.strip())

    return result


# 整体测试
# text_path = 'content.txt'
# text = codecs.open(text_path, 'r', encoding='utf8').read()
# res = extract_key_sentences(del_stop_word(text))
# res = replace_punctuation(res,0)
# print(res, len(res), len(text))

# 转中文标点测试
# text_path = '../tests/ai/content2.txt'
# text = codecs.open(text_path, 'r', encoding='utf8').read()
# res = replace_punctuation(text, 1)
# print(res)

# 切分段落测试
# text_path = 'content.txt'
# text = codecs.open(text_path, 'r', encoding='utf8').read()
# res = split_text(text, 500)
# print(len(res), res)
