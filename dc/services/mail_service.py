import json
import logging
import os
import traceback
from logging.handlers import TimedRotatingFileHandler
import yagmail
from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.models.model import DCSiteTask, DCSiteTaskHistory
from sqlalchemy.orm import declarative_base, relationship, backref, Session
from dc.services.logging_service import LoggingService


class MailService:
    """
    邮件发送服务
    :author wjh
    :date 2022-11-14
    """
    logger: LoggingService = None
    config = {}
    session: Session = None

    def __init__(self, **kwargs) -> None:
        super().__init__()
        self.config = get_settings('qianxun')
        self.logger = kwargs.get('logger') if kwargs.get('logger') is not None else LoggingService(logfile='mail.log')

    def send(self, email: dict) -> bool:
        """
        邮件发送
        :author wjh
        :date 2022-11-14
        :param email:
        :return:
        """
        try:
            self.logger.info(f'mail send: {email}')

            smtp_user = self.config.get('smtp_user')
            smtp_user_name = self.config.get('smtp_user_name')
            smtp_user = {smtp_user: smtp_user_name}
            smtp_password = self.config.get('smtp_password')
            smtp_host = self.config.get('smtp_host')
            smtp_port = self.config.get('smtp_port')

            # yagmail.SMTP(user='发件人邮箱账号', password='授权码', host='SMTP 服务器域名')
            yag = yagmail.SMTP(user=smtp_user, password=smtp_password, host=smtp_host, port=smtp_port, smtp_ssl=False, smtp_starttls=False)
            contents = email.get('mail_body')  # '邮件内容' + str(random.randint(1000, 9999))  # 邮件内容
            # contents['From'] = '小李'
            subject = email.get('mail_title')  # '第一封邮件2'  # 邮件主题
            receiver = email.get('mail_to')  # '<EMAIL>'  # 接收方邮箱账号
            self.logger.info(f"subject: {subject}")
            self.logger.info(f"receiver: {receiver}")
            # yag.user = {user:'dsfs'}
            result = yag.send(receiver, subject, contents)
            yag.close()

            # print('发送成功')
            self.logger.info(f'发送结果：{result}')

            return True

        except Exception as ex:
            print(ex.args)
            self.logger.info(f'发送错误：{traceback.format_exc()}')
            return False

