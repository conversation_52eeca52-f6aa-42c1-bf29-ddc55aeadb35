import datetime
import json
import random
import time
import traceback
import os
import sys
import openai
import base64
import requests
from sqlalchemy import create_engine, <PERSON><PERSON>n, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete

from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.models.model import DCGzhWechat
from urllib import parse as urlparse
from dc.common.func import md5
from dc.services.redis_service import RedisService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.word_processing import replace_punctuation
from dc.services.mysql_service import MySQLService
from dc.models.model import DCStockQA
from dc.services.redis_service import RedisService

mysql1 = MySQLService()
redis1 = RedisService()
usable_gpt_key_set = 'usable_gpt_key_set'


class ChatGPTService:
    name = 'default'
    config = {}
    extra = {}

    logger = None  # LoggingService

    def __init__(self, name: str = 'chatgpt', **kwargs) -> None:
        super().__init__()
        self.name = name,
        self.config = get_settings(name)
        self.extra = kwargs
        self.logger = kwargs.get('logger') if kwargs.get('logger') is not None else LoggingService(
            logfile='chatgpt.log')
        # config
        params: dict = self.config['params']
        api_key = params.get('api_key', False)
        if api_key:
            openai.api_key = api_key

        proxy = params.get('proxy', False)
        if proxy:
            openai.proxy = proxy

        api_base = params.get('api_base', False)
        if api_base:
            openai.api_base = api_base

    def _chat(self, prompt, max_tokens=2500):
        """
        chat 调用
        :param prompt:
        :return:
        """
        try:
            response = openai.Completion.create(
                model="text-davinci-003",
                prompt=prompt,
                temperature=0,
                max_tokens=max_tokens,
                top_p=1,
                frequency_penalty=0.0,
                presence_penalty=0.6,
                stop=[" Human:", " AI:"]
            )
            answer = response["choices"][0]["text"].strip()
            return answer
        except Exception as exc:
            # print(exc)  #如果需要打印出故障原因可以使用本行代码，如果想增强美感，就屏蔽它。
            return "broken"

    def _chat35(self, prompt):
        """
        chat 调用 version 3.5
        :param prompt:
        :return:
        """
        try:
            # response = openai.Completion.create(
            #     model="text-davinci-003",
            #     prompt=prompt,
            #     temperature=0.9,
            #     max_tokens=2500,
            #     top_p=1,
            #     frequency_penalty=0.0,
            #     presence_penalty=0.6,
            #     stop=[" Human:", " AI:"]
            # )

            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                # max_tokens=1024,
                # temperature=0.5
            )

            answer = response["choices"][0]["text"].strip()
            return answer
        except Exception as exc:
            # print(exc)  #如果需要打印出故障原因可以使用本行代码，如果想增强美感，就屏蔽它。
            return "broken"

    def chat(self, question, zh=True, max_tokens=2500):
        if zh:
            question = replace_punctuation(question)
        """
        chatgpt 调用 version 2
        :param question:
        :param zh: 默认是中文, 如果是中文, 则使用标点转换以减少token的使用
        :return: string, False if error
        """
        try:
            text = ""  # 设置一个字符串变量
            # question = '''
            # 请提取新闻标题，“尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢”，保留40个字以内
            # '''
            #
            # question = '''
            # 提取摘要，“尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢”，保留30个字以内
            # '''
            # 公司四川募投项目正在进行土建收尾工作和设备安装调试。请关注定期报告和临时公告，以获取项目正式投产时间
            # 公司四川募投项目正在土建收尾和设备安装调试，投产时间关注公司披露的报告与公告

            prompt = text + "\nHuman: " + question
            gptService = ChatGPTService()
            openai.api_key = gptService.get_gpt_key()
            result = self._chat(prompt, max_tokens=max_tokens)
            num = 0
            while result == "broken":  # 问不出结果会自动反复提交上一个问题，直到有结果为止。
                # print(f"please wait {num}...")
                result = self._chat(prompt, max_tokens=max_tokens)  # 重复提交问题
                num += 1
                if num > 3:
                    # print(f"break {num}...")
                    break

            # print(question)
            # print('-' * 50)
            # print(result)
            if zh:
                return replace_punctuation(result)
            return result
        except:
            self.logger.info(f"chat error: {traceback.format_exc()}")
            return False

    #  gpt 处理标题，摘要
    # def stock_chat(self, params):
    #     session = mysql1.create_session()
    #     ret = session.query(DCStockQA).filter(DCStockQA.id == params['id']).first()
    #     if not ret:
    #         return {"code": 801, "msg": "数据不存在", "data": ""}
    #
    #     title_origin = f"请提取新闻标题，“{params['answer']}”，保留25个字以内"
    #     title = self.chat(title_origin)
    #     if title == "broken":
    #         return {"code": 801, "msg": "处理失败", "data": ""}
    #
    #     title_arr = title.split('：')
    #     title = title if len(title_arr) == 1 else title_arr[1]
    #
    #     summary_origin = f"请提取文章摘要，“{params['answer']}”，保留90个字以内"
    #     summary = self.chat(summary_origin)
    #     if summary == "broken":
    #         return {"code": 801, "msg": "处理失败", "data": ""}
    #
    #     summary_arr = summary.split('：')
    #     summary = summary if len(summary_arr) == 1 else summary_arr[1]
    #
    #     title = ret.__dict__['stock_name'] + "：" + title
    #     up_sql = f"update DC_Stock_QA set title = '{title}', summary = '{summary}', is_chatgpt = 2 where id = {params['id']}"
    #     try:
    #         session.execute(up_sql)
    #         session.commit()
    #         session.close()
    #     except:
    #         if session is not None:
    #             session.close()
    #         return {"code": 801, "msg": "处理失败", "data": ""}
    #
    #     return {"code": 0, "msg": "成功", "data": title}

    def chat35(self, question):
        """
        chatgpt 调用 version 3.5
        :param question:
        :return:
        """
        try:
            text = ""  # 设置一个字符串变量
            # question = '''
            # 请提取新闻标题，“尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢”，保留40个字以内
            # '''
            #
            # question = '''
            # 提取摘要，“尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢”，保留30个字以内
            # '''
            # 公司四川募投项目正在进行土建收尾工作和设备安装调试。请关注定期报告和临时公告，以获取项目正式投产时间
            # 公司四川募投项目正在土建收尾和设备安装调试，投产时间关注公司披露的报告与公告

            prompt = text + "\nHuman: " + question
            gptService = ChatGPTService()
            openai.api_key = gptService.get_gpt_key()
            result = self._chat35(prompt)

            num = 0
            while result == "broken":  # 问不出结果会自动反复提交上一个问题，直到有结果为止。
                print(f"please wait {num}...")
                result = self._chat35(prompt)  # 重复提交问题
                num += 1
                if num > 3:
                    print(f"break {num}...")
                    break

            # print(result)
            return result

        except:
            self.logger.info(f"chat35 error: {traceback.format_exc()}")
            return False

    def chat4(self, prompt, params: dict = {}):
        """
        chat 调用 version 4
        :author wjh
        :date 2023-5-16
        :param params:
        :param prompt:
        :return:
        """
        try:

            openai.api_key = "***************************************************"  # api4
            # print(prompt)
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo-16k",
                messages=[{"role": "user", "content": prompt}],
                # max_tokens=params.get('temperature', 1024),
                temperature=params.get('temperature', 0),
                timeout=600
            )

            answer = response["choices"][0]['message']["content"].strip()
            # print("--" * 30)
            # print(answer)
            return answer
        except Exception as exc:
            # print(exc)  #如果需要打印出故障原因可以使用本行代码，如果想增强美感，就屏蔽它。
            return "broken"

    def speech_to_text(self, filename):
        """
        转换语音到文本
        :author wangfeng
        :param filename: 文件名称:
        :return: string , false if error
        """
        try:
            audio_file = open(filename, "rb")
            transcript = openai.Audio.transcribe("whisper-1", audio_file)
            data = json.loads(str(transcript))
            # print(data["text"])
            return data["text"]
        except:
            self.logger.info(f"speech_to_text error: {traceback.format_exc()}")
            return ''

    # def chat_once(self, question, zh=True, max_tokens=2500):
    #     if zh:
    #         question = replace_punctuation(question)
    #     """
    #     chatgpt 调用 version 2
    #     :param question:
    #     :param zh: 默认是中文, 如果是中文, 则使用标点转换以减少token的使用
    #     :return: string, False if error
    #     """
    #     try:
    #         text = ""  # 设置一个字符串变量
    #         # question = '''
    #         # 请提取新闻标题，“尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢”，保留40个字以内
    #         # '''
    #         #
    #         # question = '''
    #         # 提取摘要，“尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢”，保留30个字以内
    #         # '''
    #         # 公司四川募投项目正在进行土建收尾工作和设备安装调试。请关注定期报告和临时公告，以获取项目正式投产时间
    #         # 公司四川募投项目正在土建收尾和设备安装调试，投产时间关注公司披露的报告与公告
    #
    #         prompt = text + "\nHuman: " + question
    #         openai.api_key = random.choice(self.config['gpt_keys'])
    #         return self._chat(prompt, max_tokens=max_tokens)
    #     except:
    #         self.logger.info(f"chat error: {traceback.format_exc()}")
    #         return 'broken'

    def chat16k(self, question, return_type='normal', zh=True, max_tokens=2500):
        if zh:
            question = replace_punctuation(question)
        try:
            text = ""
            prompt = text + "\nHuman: " + question
            gptService = ChatGPTService()
            openai.api_key = gptService.get_gpt_key()
            params = {
                'model': 'gpt-3.5-turbo-16k',
                'messages': [{"role": "user", "content": prompt}]
            }
            retries = 3
            while retries > 0:
                response = openai.ChatCompletion.create(**params)
                answer = response["choices"][0].get('message', {}).get('content', '').strip()
                if answer:
                    break
                retries -= 1
            if zh:
                return replace_punctuation(answer)
            return answer

        except:
            self.logger.info(f"chat error: {traceback.format_exc()}")
            return 'broken'

    def chat16k_stream(self, question, zh=True, max_tokens=2500):
        if zh:
            question = replace_punctuation(question)
        try:
            text = ""
            prompt = text + "\nHuman: " + question
            gptService = ChatGPTService()
            openai.api_key = gptService.get_gpt_key()
            params = {
                'model': 'gpt-3.5-turbo-16k',
                'messages': [{"role": "user", "content": prompt}],
                'stream': True
            }
            retries = 3
            completion = openai.ChatCompletion.create(**params)
            for line in completion:
                if 'broken' in line:
                    retries -= 1
                    if retries > 0:
                        completion = openai.ChatCompletion.create(**params)
                        continue
                    else:
                        msg = "无法生成响应，请重试"
                        bytes_to_encode = msg.encode('utf-8')

                        # 使用base64进行编码
                        encoded_bytes = base64.b64encode(bytes_to_encode)

                        # 将编码后的字节转换为字符串
                        encoded_text = encoded_bytes.decode("utf-8")

                        yield 'data: %s\n\n' % encoded_text
                        return
                chunk = line['choices'][0].get('delta', {}).get('content', '')
                if chunk == '':
                    continue
                # 将字符串转换为字节
                bytes_to_encode = chunk.encode('utf-8')

                # 使用base64进行编码
                encoded_bytes = base64.b64encode(bytes_to_encode)

                # 将编码后的字节转换为字符串
                encoded_text = encoded_bytes.decode("utf-8")

                yield 'data: %s\n\n' % encoded_text
        except:
            self.logger.info(f"chat error: {traceback.format_exc()}")
            msg = "无法生成响应，请重试"
            bytes_to_encode = msg.encode('utf-8')

            # 使用base64进行编码
            encoded_bytes = base64.b64encode(bytes_to_encode)

            # 将编码后的字节转换为字符串
            encoded_text = encoded_bytes.decode("utf-8")

            yield 'data: %s\n\n' % encoded_text
            return

    def get_gpt_key(self):
        client1 = redis1.get_client()
        gpt_keys = client1.smembers(usable_gpt_key_set)
        if gpt_keys:
            api_key = random.choice(list(gpt_keys))
        else:
            api_key = random.choice(self.config['gpt_keys'])

        return api_key
