# -*- coding:utf-8 -*-
# @Function  : k8s 服务类
# <AUTHOR> wjh
# @Time      : 2025-01-03
# Version    : 1.0

import time
from datetime import datetime, timedelta

from dc.common.func import get_text_md5
from dc.common.functools_wraps import get_session, rate_limit
from dc.common.k8s_util import *
from dc.conf.settings import get_settings
from dc.models.model_k8s import K8sLiveness
from dc.services.logging_service import LoggingService


class K8sService:
    logger: LoggingService = None
    config = {}
    params = {}
    logfile = 'k8s_service.log'

    def __init__(self, name="k8s", **kwargs) -> None:
        super().__init__()
        self.logger = kwargs.get('logger', LoggingService(logfile=self.logfile))
        self.params = kwargs
        self.config = get_settings(name)

    @rate_limit(60)
    def liveness(self, **kwargs) -> None:
        try:
            depth = kwargs.get('depth', 4)
            process_info = self.get_process_info(depth=depth)
            self.logger.info(f"process_info: {process_info}")
            md5_str = "|".join([process_info['hostname'], process_info['script_path'], process_info['full_process_name']])
            md5 = get_text_md5(md5_str)

            with get_session() as session:
                liveness = session.query(K8sLiveness).filter_by(md5=md5).first()
                op = 'updated' if liveness else 'created'
                if not liveness:
                    liveness = K8sLiveness()
                    liveness.hostname = process_info['hostname']
                    liveness.script_name = process_info['script_name']
                    liveness.script_path = process_info['script_path']
                    liveness.process_name = process_info['process_name']
                    liveness.group_name = process_info['group_name']
                    liveness.full_process_name = process_info['full_process_name']
                    liveness.host_process_name = process_info['host_process_name']
                    # liveness.livenessAt = datetime.now()
                    # liveness.readinessAt = datetime.now()
                    liveness.md5 = md5
                    liveness.createdAt = datetime.now()

                liveness.livenessAt = datetime.now()
                liveness.updatedAt = datetime.now()

                session.add(liveness)
                session.commit()

                self.logger.info(f"liveness {op} id: {liveness.id}")

        except Exception as ex:
            self.logger.error(f"exception: {str(ex)}")
            # raise

    def get_process_info(self, depth=2) -> dict:
        """
        get process info
        :param depth:
        :return:
        """
        process_info = {}
        hostname = get_hostname()
        process_info['hostname'] = hostname
        # 调用最外层函数
        script_name, script_path = get_called_script_info(depth=depth)
        process_info.update(script_name=script_name, script_path=script_path)
        process_name = get_process_name(name='SUPERVISOR_PROCESS_NAME')
        process_info.update(process_name=process_name)
        group_name = get_process_name(name='SUPERVISOR_GROUP_NAME')
        process_info.update(group_name=group_name)
        full_process_name = f"{group_name}:{process_name}"
        process_info.update(full_process_name=full_process_name)
        host_process_name = f"{hostname}:{group_name}:{process_name}"
        process_info.update(host_process_name=host_process_name)
        self.logger.info(f"process_info: {process_info}")

        return process_info

    def livenessProbe(self, **kwargs) -> int:
        """
        健康检查探针，如果不健康，则会重新启动容器
        :param kwargs: 参数
        :return: 0健康 非 0不健康
        """
        # script_path = kwargs.get('script_path', None)
        script_name = kwargs.get('script_name', None)
        failed_percent = int(kwargs.get('failed_percent', 30))
        hours = int(kwargs.get('hours', 1))
        hostname = get_hostname()
        # hostname = 'dc-task-pull-6dc66468df-4sl4q'
        self.logger.info(f"kwargs: {kwargs}")

        current_failed_percent = 0
        with get_session() as session:
            data = session.query(K8sLiveness).filter_by(script_name=script_name, hostname=hostname).all()
            if not data:
                current_failed_percent = 0
            else:
                stop_time = datetime.now() - timedelta(hours=hours)
                failed_liveness = [liveness for liveness in data if liveness.livenessAt < stop_time]
                current_failed_percent = int((len(failed_liveness) / len(data)) * 100)

        self.logger.info(f"hostname:{hostname} script_name:{script_name} failed_percent: {failed_percent} current_failed_percent: {current_failed_percent}")
        return 0 if current_failed_percent <= failed_percent else -1

    def cleanup(self, **kwargs) -> int:
        """
        在 Pod 销毁前执行脚本
        :param kwargs: 参数
        :return: 0成功 非0失败
        """
        action = kwargs.get('action', None)
        message = kwargs.get('message', None)
        hostname = get_hostname()
        self.logger.info(f"kwargs: {kwargs}")

        match action:
            case "stop":    # pre stop
                with get_session() as session:
                    result = session.query(K8sLiveness).filter_by(hostname=hostname).delete()
                    self.logger.info(f"delete {result} rows by hostname: {hostname}")
            case _:
                self.logger.info("Default case")

        return 0
