from selenium import webdriver
from selenium.webdriver.common.by import By
import time


def GetNextPage(wb, conf, n):
    v = conf.ExtraConfig.get("NextPageFunc")
    if v is None:
        return NextPage(wb, conf)
    if v == "multi":
        return nextPageByLine(wb, conf)
    elif v == "number":
        return NextPageByNum(wb, conf, n)
    elif v == "redirect":
        return nextPageRedirect(wb, conf, n)
    else:
        return NextPage(wb, conf)


def NextPage(wb, conf):
    lp = True
    if conf.ListUrl == "":
        return False
    if "|" in conf.ListUrl:
        withTry = conf.ListUrl.split("|")
        for s in withTry:
            try:
                btn = wb.find_element(conf.GetSelector(), s)
                text = btn.text
                if nextPageJudge(text):
                    btn.click()
                else:
                    continue
            except:
                continue
    else:
        try:
            btn = wb.find_element(conf.GetSelector(), conf.ListUrl)
            btn.click()
        except:
            lp = False
    return lp


def NextPageByNum(wb, conf, n):
    lp = True

    def nextButton(wb):
        buttons = wb.find_elements(conf.GetSelector(), conf.ListUrl)
        return len(buttons) > 0

    try:
        wb.wait_with_timeout(nextButton, 30)
    except:
        return False
    r = wb.find_elements(conf.GetSelector(), conf.ListUrl)
    for btn in r:
        try:
            text = btn.text
            pn = int(text)
            if pn == n:
                btn.click()
                break
        except:
            continue
    return lp


def nextPageByLine(wb, conf):
    lp = True
    r = wb.find_elements(conf.GetSelector(), conf.ListUrl)
    for _ in range(int(conf.ListPagiNatePageOffset)):
        for btn in r:
            text = btn.text
            if nextPageJudge(text):
                try:
                    btn.click()
                    break
                except:
                    pass
            try:
                aLink = btn.find_element(By.TAG_NAME, "a")
                text = aLink.text
                if nextPageJudge(text):
                    try:
                        aLink.click()
                        break
                    except:
                        pass
            except:
                continue
        time.sleep(2)
    return lp


def nextPageJudge(t):
    np = ["下一页", "下页"]
    click = False
    for v in np:
        if v == t or v in t:
            click = True
    return click


def nextPageRedirect(wb, conf, n):
    lp = True
    url = conf.ListUrl % n
    try:
        wb.get(url)
    except:
        lp = False
    return lp