# -*- coding:utf-8 -*-
# @Function  : 七牛云服务
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0

import time
import requests

from dc.common.qiniu_utils import *
from dc.conf.settings import get_settings2
from dc.services.logging_service import LoggingService
from qiniu import Auth, BucketManager, put_file, put_data, etag


class QiniuBaseService:
    name: str = None
    config = {}

    logger = None  # LoggingService
    q: Auth = None
    bucket: BucketManager = None

    def __init__(self, name='bianjifagao', **kwargs) -> None:
        self.name = name
        self.config = get_settings2('qiniu', name, {})
        self.logger = kwargs.get('logger') if kwargs.get('logger') is not None else LoggingService(logfile='qiniu.log')

        access_key = self.config['access_key']
        secret_key = self.config['secret_key']
        # bucket_name = self.config['bucket']
        # bucket_domain = 'https://s.laoyaoba.com'

        q = Auth(access_key, secret_key)
        bucket = BucketManager(q)
        self.q = q
        self.bucket = bucket


    def get_save_path(self, path: str):
        """
        get save path
        :param path:
        :return:
        """
        return url_path_join(self.config["document_name"], path)

    def put_file(self, localfile: str, key: str):
        """
        上传文件
        :param localfile: 本地文件目录
        :param key: 上传后文件目录
        :return:
        """
        token = self.q.upload_token(self.config['bucket'], key, 3600)
        ret, info = put_file(token, key, localfile, version='v1')
        self.logger.info(f"上传文件结果：{ret}, {info}")
        return ret, info

    def put_file_v2(self, localfile: str, key: str):
        """
        上传文件(使用v2版本)
        :param localfile: 本地文件目录
        :param key: 上传后文件目录
        :return: 上传结果
        """
        token = self.q.upload_token(self.config['bucket'], key, 3600)
        # 使用v2版本更适合大文件和不稳定网络
        ret, info = put_file(token, key, localfile, version='v2')
        self.logger.info(f"上传文件结果：{info}")
        return ret, info

    def put_file_with_callback(self, localfile: str, key: str, callback_url: str, callback_body: str = None):
        """
        带回调的文件上传
        :param localfile: 本地文件路径
        :param key: 保存的文件名
        :param callback_url: 回调地址
        :param callback_body: 回调参数设置，默认为 key=$(key)&hash=$(etag)&bucket=$(bucket)&fsize=$(fsize)
        :return: 上传结果
        """
        if not callback_body:
            callback_body = 'key=$(key)&hash=$(etag)&bucket=$(bucket)&fsize=$(fsize)'
        
        policy = {
            'callbackUrl': callback_url,
            'callbackBody': callback_body,
            'callbackBodyType': 'application/x-www-form-urlencoded'
        }
        
        token = self.q.upload_token(self.config['bucket'], key, 3600, policy)
        ret, info = put_file(token, key, localfile)
        self.logger.info(f"带回调上传文件结果：{info}")
        return ret, info

    def put_data(self, data, key: str, mime_type=None):
        """
        上传二进制数据
        :param data: 二进制数据
        :param key: 上传后文件目录
        :param mime_type: 文件类型，如'image/jpeg'
        :return: 上传结果
        """
        token = self.q.upload_token(self.config['bucket'], key, 3600)
        ret, info = put_data(token, key, data, mime_type=mime_type)
        self.logger.info(f"上传数据结果：{info}")
        return ret, info

    def private_download(self, base_url: str, expires=3600):
        """
        获取私有空间文件
        :param base_url: 文件URL，比如 http://privte.laoyaoba.com/xxx
        :param expires: 链接有效期，默认1小时
        :return: 私有下载链接
        """
        # 生成私有下载链接
        private_url = self.q.private_download_url(base_url, expires=expires)
        self.logger.info(f"生成私有下载链接: {private_url}")
        
        # 请求下载链接
        # response = requests.get(private_url)
        return private_url

    def delete_file(self, key: str):
        """
        删除文件
        :param key: 文件路径
        :return: 删除结果
        """
        bucket_name = self.config['bucket']
        ret, info = self.bucket.delete(bucket_name, key)
        self.logger.info(f"删除文件结果：{info}")
        return ret, info

    def stat_file(self, key: str):
        """
        获取文件信息
        :param key: 文件路径
        :return: 文件信息
        """
        bucket_name = self.config['bucket']
        ret, info = self.bucket.stat(bucket_name, key)
        return ret, info

    def move_file(self, source_key: str, target_bucket: str, target_key: str):
        """
        移动或重命名文件
        :param source_key: 源文件路径
        :param target_bucket: 目标空间名
        :param target_key: 目标文件路径
        :return: 操作结果
        """
        source_bucket = self.config['bucket']
        ret, info = self.bucket.move(source_bucket, source_key, target_bucket, target_key)
        self.logger.info(f"移动文件结果：{info}")
        return ret, info

    def copy_file(self, source_key: str, target_bucket: str, target_key: str):
        """
        复制文件
        :param source_key: 源文件路径
        :param target_bucket: 目标空间名
        :param target_key: 目标文件路径
        :return: 操作结果
        """
        source_bucket = self.config['bucket']
        ret, info = self.bucket.copy(source_bucket, source_key, target_bucket, target_key)
        self.logger.info(f"复制文件结果：{info}")
        return ret, info

    def set_file_lifecycle(self, key: str, days: int):
        """
        设置文件生存时间
        :param key: 文件路径
        :param days: 有效期天数
        :return: 操作结果
        """
        bucket_name = self.config['bucket']
        ret, info = self.bucket.delete_after_days(bucket_name, key, days)
        self.logger.info(f"设置文件生存时间结果：{info}")
        return ret, info

    def list_files(self, prefix=None, limit=100):
        """
        获取文件列表
        :param prefix: 前缀
        :param limit: 每次返回的最大数量
        :return: 文件列表
        """
        bucket_name = self.config['bucket']
        ret, eof, info = self.bucket.list(bucket_name, prefix=prefix, limit=limit)
        return ret, eof, info

    def fetch_web_resource(self, url: str, key: str):
        """
        抓取网络资源到空间
        :param url: 网络资源URL
        :param key: 保存的文件名
        :return: 抓取结果
        """
        bucket_name = self.config['bucket']
        ret, info = self.bucket.fetch(url, bucket_name, key)
        self.logger.info(f"抓取网络资源结果：{info}")
        return ret, info
