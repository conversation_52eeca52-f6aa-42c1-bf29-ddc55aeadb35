# -*-coding:utf-8-*-

class PromptGeneratorService:

    def daily(self,article_content:str,style:str,format:str,size:int,**kwargs):
        info = []
        keys = ['user_setting','question_description','target_need','more_description']
        for key in keys:
            if  v := kwargs.get(key,""):
                info.append(v)
        s = '请以{}的文风书写,字数要求{}字左右，返回格式为{}格式'.format(style,size,format)
        info.append(s)
        info.append( "文章内容如下：" + article_content)
        prompt = '。'.join(info)
        return prompt
