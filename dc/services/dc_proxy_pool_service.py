import sys
import os
import time

from dc.tests.browser import Browser

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.conf.settings import get_settings


class DcProxyPoolService(object):
    name = 'default'
    config = {}

    def __init__(self, name: str = 'proxy', run_model=None) -> None:
        self.name = name
        self.config = get_settings(name, run_model=run_model)

    def get_proxy(self, proxy_id: str):
        """
        获取某个代理配置，并进行相关的配置检查
        """
        assert proxy_id in self.config
        proxy = self.config.get(proxy_id)
        _type = proxy.get('type')
        return proxy

    def get_requests_proxy(self, proxy_id: str, proxy_usage: str = "white", is_use_socks: bool = False) -> dict:
        """
        获取requests请求框架的代理参数, response = requests.get(target_url, proxies=proxies)
        :param proxy_id: 使用的代理编号
        :param proxy_usage: 代理使用方式: 用户名密码、ip白名单 （'user', 'white'）
        :param is_use_socks: 是否使用socks
        :return: 配置
        """
        proxy = self.get_proxy(proxy_id)
        assert proxy_usage in ['user', 'white']
        if is_use_socks:
            tunnel = proxy['params']['socks']['host'] + ":" + str(proxy['params']['socks']['port'])
            if proxy_usage == "user":
                return {
                    "http": "socks5h://%(user)s:%(pwd)s@%(proxy)s/" % {"user": proxy['params']['username'], "pwd": proxy['params']['password'],
                                                                       "proxy": tunnel},
                    "https": "socks5h://%(user)s:%(pwd)s@%(proxy)s/" % {"user": proxy['params']['username'], "pwd": proxy['params']['password'],
                                                                        "proxy": tunnel}
                }
            if proxy_usage == "white":
                return {
                    "http": "socks5h://%(proxy)s/" % {"proxy": tunnel},
                    "https": "socks5h://%(proxy)s/" % {"proxy": tunnel}
                }
        else:
            tunnel = proxy['params']['http']['host'] + ":" + str(proxy['params']['http']['port'])
            if proxy_usage == "user":
                return {
                    "http": "http://%(user)s:%(pwd)s@%(proxy)s/" % {"user": proxy['params']['username'], "pwd": proxy['params']['password'],
                                                                    "proxy": tunnel},
                    "https": "http://%(user)s:%(pwd)s@%(proxy)s/" % {"user": proxy['params']['username'], "pwd": proxy['params']['password'],
                                                                     "proxy": tunnel}
                }
            return {
                "http": "http://%(proxy)s/" % {"proxy": tunnel},
                "https": "http://%(proxy)s/" % {"proxy": tunnel}
            }

    def get_selenium_chrome_proxy(self, proxy_id: str, is_use_socks: bool = False) -> str:
        """
        获取selenium谷歌浏览器配置信息，driver = get_remote_webdriver('http://*************:4444', browser=Browser.Chrome,
        options={'arguments': [get_selenium_chrome_proxy()]})
        :param proxy_id: 使用的代理编号
        :param is_use_socks: 是否使用socks
        :return: 谷歌浏览器配置信息
        """
        proxy = self.get_proxy(proxy_id)
        if is_use_socks:
            tunnel = proxy['params']['socks']['host'] + ":" + str(proxy['params']['socks']['port'])
            return "--proxy-server=socks5h://" + tunnel
        tunnel = proxy['params']['http']['host'] + ":" + str(proxy['params']['http']['port'])
        return "--proxy-server=http://" + tunnel

    def get_selenium_firefox_proxy(self, proxy_id: str,
                                   ) -> dict:
        """
        获取selenium火狐浏览器配置信息 driver = get_remote_webdriver('http://*************:4444', browser=Browser.
        Firefox, options={'arguments': ['--no-sandbox'], "preferences":  get_selenium_firefox_proxy()})
        :param proxy_id: 使用的代理编号
        :return:
        """
        proxy = self.get_proxy(proxy_id)
        host = proxy['params']['http']['host']
        port = proxy['params']['http']['port']
        return {
            'network.proxy.type': 1,
            'network.proxy.http': host,
            'network.proxy.http_port': port,
            'network.proxy.ssl': host,
            'network.proxy.ssl_port': port,
        }
