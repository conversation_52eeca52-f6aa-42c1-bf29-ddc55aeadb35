from concurrent.futures import Future

from jwtools.func import *
from confluent_kafka import Consumer, Producer, TopicPartition, ConsumerGroupTopicPartitions
from confluent_kafka.admin import AdminClient, TopicMetadata, PartitionMetadata
from dc.conf.settings import get_settings
from dc.models.model import Base


class KafkaService:
    name = 'default'
    config = {}
    echo_set = False  # 输出配置

    def __init__(self, name: str = 'kafka', run_model=None, extra: dict = None) -> None:
        super().__init__()
        self.name = name,
        conf = get_settings(name, run_model=run_model)
        self.config = conf if conf else {}

    def get_consumer(self, extra: dict = None) -> Consumer:
        """
        get consumer with extra config
        example:
            consumer = kafka.get_consumer(extra={
                'group.id': 'elk-consumer-test',
                'auto.offset.reset': 'earliest',
                'error_cb': error_cb,
                'stats_cb': stats_cb,
                'on_commit': on_commit
            })
        :param extra: extra configs
        :return: Consumer
        """
        config = self.merge_config(extra)
        consumer = Consumer(config)
        return consumer

    def get_producer(self, extra: dict = None) -> Producer:
        """
        get consumer with extra config
        example:
            producer = kafka.get_producer()
        :param extra: extra configs
        :return: Consumer
        """
        config = self.merge_config(extra)
        producer = Producer(config)
        return producer

    def get_topics(self, extra: dict = None, params: dict = {}):
        """
        get list of topic name
        :param extra: other config
        :param params: other params , filter is support, {'filter': lambda x: x.startswith('elk_s125_')}
        :rtype: list(str)
        :return:
        """
        config = self.merge_config(extra)
        tps = []
        admin_client = AdminClient(config)
        topics = admin_client.list_topics(timeout=10)
        for topic_name, tm in topics.topics.items():  # type: str, TopicMetadata
            if topic_name.startswith('__'):
                continue

            tps.append(topic_name)

        # filter
        func = params.get('filter', None)
        if func and callable(func):
            tps = list(filter(func, tps))

        return tps

    def get_topic_partition(self, extra: dict = None, params: dict = None):
        """
        get list of TopicPartition
        :rtype: list(TopicPartition)
        :return:
        """
        tps = []  # type: list(TopicPartition)
        config = self.merge_config(extra)
        admin_client = AdminClient(config)
        topics = admin_client.list_topics(timeout=10)
        for topic_name, tm in topics.topics.items():  # type: str, TopicMetadata
            if topic_name.startswith('__'):
                continue

            for partition, pm in tm.partitions.items():  # type: str, PartitionMetadata
                tps.append(TopicPartition(topic_name, partition))

        return tps

    def list_consumer_group_offsets(self, extra: dict = None, params: dict = None):
        """
        get list of TopicPartition
        :rtype: list(TopicPartition)
        :return:
        """
        data = []
        tps = self.get_topic_partition(extra)
        config = self.merge_config(extra)
        admin_client = AdminClient(config)
        cc = ConsumerGroupTopicPartitions(config['group.id'], tps)
        result: dict[str, Future] = admin_client.list_consumer_group_offsets([cc])

        for group, metadata in result.items():
            tps: ConsumerGroupTopicPartitions = metadata.result()
            zz: list = tps.topic_partitions
            for tp2 in zz:  # type: TopicPartition
                offset = tp2.offset if tp2.offset > -1 else '-1'
                data.append({
                    'topic': tp2.topic,
                    'partition': tp2.partition,
                    'offset': offset,
                })
        return data

    def list_topics_watermark(self, extra: dict = None, params: dict = None):
        """
        get list of TopicPartition
        :rtype: list(TopicPartition)
        :return:
        """
        config = self.merge_config(extra)
        topic_info = []
        consumer = Consumer(config)
        topics = consumer.list_topics(timeout=10)
        for topic, tm in topics.topics.items():  # type: str,TopicMetadata
            if topic.startswith('__'):
                continue

            for key, pm in tm.partitions.items():  # type: str,PartitionMetadata
                tp = TopicPartition(topic, pm.id)
                # tps2 = consumer.position([tp])  # type: list(TopicPartition)
                (low, high) = consumer.get_watermark_offsets(tp)
                lag = high - low
                info = {
                    'topic': topic,
                    'partition': pm.id,
                    'low': low,
                    'high': high,
                    'lag': lag,
                    # 'offset': tps[0].offset,
                }
                topic_info.append(info)

        return topic_info

    def list_topics_offset_watermark(self, extra: dict = None, params: dict = None):
        """
        get list of TopicPartition
        :rtype: list(TopicPartition)
        :return:
        """
        data = self.list_consumer_group_offsets(extra=extra)
        data2 = self.list_topics_watermark(extra=extra)

        for d2 in data2:  # type: dict
            # print_line('999')
            # print(d2)
            one = [d for d in data if d['topic'] == d2['topic'] and d['partition'] == d2['partition']]
            one = one[0] if len(one) > 0 else {}
            # print(one)
            d2.setdefault('offset', one.get('offset', '-1'))
            # print(d2)

        return data2

    def merge_config(self, extra: dict = None) -> dict:
        """
        merge config
        :rtype: dict
        :return:
        """
        merge_config = self.config.copy()
        if extra:
            merge_config.update(extra)

        return merge_config
