import greenstalk
import pymongo
from pymongo.collection import Collection
from dc.conf.settings import get_settings
from elasticsearch import Elasticsearch


class BeanstalkService:
    name = 'default'
    config = {}

    def __init__(self, name='beanstalk', run_model=None) -> None:
        super().__init__()
        self.name = name
        self.config = get_settings(name, run_model=run_model)

    def get_client(self, **kwargs) -> greenstalk.Client:
        host = self.config.get('host')
        port = self.config.get('port')
        address = (host, port)
        encoding = self.config.get('encoding', 'utf-8')
        use = kwargs.get('use', self.config.get('use'))
        watch = kwargs.get('watch', self.config.get('watch'))

        client = greenstalk.Client(address=address, encoding=encoding, use=use, watch=watch)
        return client


