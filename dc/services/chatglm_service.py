import random
import os
import sys
from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService
from wudao.api_request import executeEngine, getToken
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))


class ChatGLMService:
    name = 'default'

    # 接口 API KEY
    API_KEY = "05afd3b4ac3047e99a23dd417df73463"
    # 公钥
    PUBLIC_KEY = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJBg/jeSpPV/dlYHtyvsSgLC2lv2Big3KOZcakqYr0ROoNu/fvE2ydH9dj+N/PTCKJ8Tfwfn9ZDAKbtVBWimfSkCAwEAAQ=="

    # 能力类型
    ability_type = "chatGLM"
    # 引擎类型
    engine_type = "chatGLM"

    config = {}
    extra = {}

    logger = None  # LoggingService

    def __init__(self, name: str = 'chatgpt', **kwargs) -> None:
        super().__init__()
        self.name = name,
        self.config = get_settings(name)
        self.extra = kwargs
        self.logger = kwargs.get('logger') if kwargs.get('logger') is not None else LoggingService(
            logfile='chatglm.log')

    def get_token(self):
        """
        chat 调用
        :param prompt:
        :return:
        """
        try:
            token_result = getToken(self.API_KEY, self.PUBLIC_KEY)
            if token_result and token_result["code"] == 200:
                token = token_result["data"]
            else:
                token = False

            return token
        except Exception as exc:
            return False

    def execute_engine(self, data: dict):
        """
        chat 调用 version 3.5
        :param data:
            data = {
                "top_p": 0.7,
                "temperature": 0.9,
                "prompt": "他之前担任什么职务？",
                "requestTaskNo": "1542097269879345154",
                "history": [
                    "清华大学校长是谁？",
                    "截止 2023 年，清华大学校长是王希勤"
                ]
            }
        :return:
        """
        try:
            data.setdefault("top_p", 0.7)
            data.setdefault("temperature", 0.9)
            data.setdefault("requestTaskNo", random.randint(100000, 999999))

            token = self.get_token()
            if not token:
                return False

            resp = executeEngine(self.ability_type,self.engine_type, token, data)
            # print(resp)
            assert resp['code'] == 200, 'code is not 200'
            return resp['data']

        except Exception as exc:
            return "execute exception: " + str(exc)

