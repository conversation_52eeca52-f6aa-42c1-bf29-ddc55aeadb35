import pymongo
from pymongo import MongoClient
from pymongo.collection import Collection
from dc.conf.settings import get_settings


class MongoService:
    name = 'default'
    config = {}

    def __init__(self, name='mongo', run_model=None) -> None:
        super().__init__()
        self.name = name
        self.config = get_settings(name, run_model=run_model)

    def get_collection(self, collection, database='yuqing') -> Collection:
        conn = pymongo.MongoClient(self.config.get('url'))
        database = conn.get_database(database)
        collection = database.get_collection(collection)
        return collection

    def get_client(self) -> MongoClient:
        client = pymongo.MongoClient(self.config.get('url'))
        return client
