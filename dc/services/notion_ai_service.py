import os
from notion_client import Client

notion = Client(auth="**************************************************")
did = "1843addc9bf7432d9e9239100665e06e"
# question = "请提取新闻标题，“尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢”，保留40个字以内"
# # Step 1: Create a new page in the database with the question
# new_page = {
#     "question": {
#         "title": [
#             {
#                 "text": {
#                     "content": question
#                 }
#             }]
#     }
# }
# print(new_page)
#
# notion.pages.create(parent={"database_id": did}, properties="new_page")
#
# # Step 2: Retrieve the answer from the database
# results = notion.databases.query(
#     **{
#         "database_id": did,
#         "filter": {
#             "property": question,
#             "title": {
#                 "equals": True
#             }
#         }
#     }
# ).get("results")
# print(results)
# if results:
#     answer = results[0].get("properties").get("答案").get("rich_text")[0].get("text").get("content")
# else:
#     answer = "抱歉，找不到您的答案。"
#
# print(answer)
#

import requests

url = "https://api.notion.com/v1/databases/" + did

headers = {
    "accept": "application/json",
    "Notion-Version": "2022-06-28"
}

response = requests.get(url, headers=headers)

print(response.text)
