# -*- coding:utf-8 -*-
# @Function  : 监控报警服务
# <AUTHOR>
# @Time      : 2024/8/27
# Version    : 1.0

import random
import smtplib
import time
from datetime import timedelta
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

import requests

from dc.conf.settings import *
from dc.models.model import *
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService


class MonitorAlertService:
    logger: LoggingService = None

    def __init__(self, config={}):
        default_config = {
            'debug': False,
            'version': '1.0'
        }
        self.config = {**default_config, **config}
        self.logger = config.get('logger') if config.get('logger') else LoggingService(logfile='monitor_alert_service.log')
        # self.logger = LoggingService(logfile='monitor_alert_service.log')

    def get_log_monitor_api(self, api_type: str):
        """
        获取 api 路径
        :param api_type: error_service_count, error_type_count
        :return:
        :rtype: str
        """
        api = get_settings2('elk', 'log_monitor_alert_api', {})
        assert api is not None, 'api is empty'

        service_api = api['host'].rstrip('/') + api[api_type]
        assert service_api is not None, 'api is empty'
        return service_api

    def fetch_json_data(self, url, method='GET', headers=None, params=None, data=None, json=None, timeout=10) -> dict:
        """
        发送HTTP请求并获取JSON格式的返回数据。

        :param url: 请求的URL
        :param method: HTTP方法，如 'GET', 'POST', 'PUT', 'DELETE'
        :param headers: 请求头字典
        :param params: URL参数字典
        :param data: 请求体数据（通常用于POST请求）
        :param json: 请求体JSON数据（通常用于POST请求）
        :param timeout: 请求超时时间（秒）
        :return: 返回解析后的JSON数据
        :raises: requests.exceptions.RequestException 请求失败时抛出的异常
        """
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                data=data,
                json=json,
                timeout=timeout
            )

            response.raise_for_status()  # 如果响应状态码不是200, 则抛出异常
            return response.json()  # 返回JSON格式的数据

        except requests.exceptions.RequestException as e:
            print(f"HTTP请求失败: {e}")
            return None

    def get_alert_message(self, alert_id):
        """
        get alert message
        :param alert_id:
        :return:
        """
        api = get_settings2('elk', 'log_monitor_alert_api', {})
        assert api is not None, 'api is empty'
        host = api['detail_host'].rstrip('/')
        # host = 'http://localhost'

        alert_message = f"""
报警时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
报警事件：日志监控
事件详情：日志监控
详情链接：{host}/elk/logMonitor/getAlertDetail?id={alert_id}
"""
        return alert_message

    def log_monitor_save(self):
        """
        日志监控报警数据存储
        :return:
        """
        self.logger.debug(f'enter -> log_monitor_save')

        mysql = MySQLService()
        session: Session = None
        try:

            session = mysql.create_session()

            # 日志监控
            alert = (
                session.query(DMMonitorAlert)
                .filter(DMMonitorAlert.alert_type == 1)
                .order_by(DMMonitorAlert.id.desc())
                .first()
            )
            last_time = alert.createdAt if alert else datetime(1970, 1, 1).strftime('%Y-%m-%d %H:%M:%S')
            # last_time = ''
            self.logger.info(f'last_time:{last_time}')

            # 统计数据
            service_count_api = self.get_log_monitor_api('error_service_count')
            service_count_result = self.fetch_json_data(service_count_api, params={'start_time': last_time, 'page': 1, 'perPage': 200})
            service_count_data = service_count_result.get('data', [])

            error_type_count_api = self.get_log_monitor_api('error_type_count')
            error_type_count_result = self.fetch_json_data(error_type_count_api, params={'start_time': last_time, 'page': 1, 'perPage': 200})
            error_type_count_data = error_type_count_result.get('data', [])

            # print(service_count_data)
            # print(error_type_count_data)

            # alert
            alert = DMMonitorAlert()
            alert.alert_type = 1
            alert.alert_name = "日志监控"
            alert.result = json.dumps({'service': service_count_data, 'error_type': error_type_count_data})
            alert.status = 3
            alert.start_time = datetime.now()
            session.add(alert)
            session.commit()
            self.logger.info('alert create')

            # error type count
            for item in error_type_count_data:
                print(item)
                detail = DMMonitorAlertDetail()
                detail.alert_id = alert.id
                detail.error_type = item.get('error_type', 0)
                detail.total = item.get('count', 0)
                detail.status = 1
                session.add(detail)
                session.commit()

            # service count
            for item in service_count_data:
                print(item)
                detail = DMMonitorAlertDetail()
                detail.alert_id = alert.id
                detail.service = item.get('service', None)
                if not detail.service:
                    continue

                detail.total = item.get('count', 0)
                detail.status = 1
                session.add(detail)
                session.commit()

            # alert update
            alert: DMMonitorAlert = session.query(DMMonitorAlert).get(alert.id)
            alert.alert_message = self.get_alert_message(alert.id)
            alert.end_time = datetime.now()
            alert.status = 1
            alert.total = sum(item.get('count', 0) for item in service_count_data)
            alert.execution_duration = (alert.end_time - alert.start_time).total_seconds() * 1000
            session.add(alert)
            session.commit()

            self.logger.info('alert update')

            # 触发报警
            self.logger.info('trigger_alert_log_monitor')
            self.trigger_alert_log_monitor(alert)

        except Exception as ex:
            session.rollback()
            self.logger.error(f'alert Exception {str(ex)}')

        finally:
            if session:
                session.close()

            self.logger.debug(f'finish -> log_monitor_save')

    def get_latest_task_logs(self):

        mysql = MySQLService()
        session: Session = None
        try:
            session = mysql.create_session()

            # 获取DMTaskLogs中最新的createdAt
            last_created_at = session.query(func.max(DMMonitorAlert.createdAt)).filter(DMMonitorAlert.alert_type == 2).scalar()

            # 如果数据库中没有记录，或这是第一次执行，使用一个很早的时间
            if not last_created_at:
                start_time = datetime(1970, 1, 1)
            else:
                start_time = last_created_at

            # 子查询获取每个 task_id 组中最大的 id
            subquery = session.query(
                DMTaskLogs.task_id,
                func.max(DMTaskLogs.id).label('max_id')
            ).filter(
                DMTaskLogs.start_time >= start_time,
                DMTaskLogs.status.in_([1, 2])
            ).group_by(DMTaskLogs.task_id).subquery()

            # 使用子查询过滤出最新的任务日志
            latest_logs = session.query(DMTaskLogs).join(
                subquery, DMTaskLogs.id == subquery.c.max_id
            ).all()

            return latest_logs

        except Exception as ex:
            session.rollback()
            self.logger.error(f'alert Exception {str(ex)}')
            return None

        finally:
            if session:
                session.close()

    def data_monitor_save(self):
        """
        数据监控报警数据存储
        :return:
        """
        self.logger.debug(f'enter -> data_monitor_save')

        mysql = MySQLService()
        session: Session = None
        try:
            session = mysql.create_session()
            # 使用示例
            latest_task_logs = self.get_latest_task_logs()
            for log in latest_task_logs:
                self.logger.debug(f'Task ID: {log.task_id}, Log ID: {log.id}, Start Time: {log.start_time}')
                # print(log.to_dict())
                task: DMAlertTasks = session.query(DMAlertTasks).get(log.task_id)
                if task:
                    self.logger.info(task.to_dict())
                    if not log.alert_message:
                        continue

                    # 触发报警
                    self.logger.info('trigger_alert')
                    self.trigger_alert(task, log)

                else:
                    self.logger.info('rule is empty')

                # print(log.task.rule)
                # continue
                alert = DMMonitorAlert()
                alert.alert_type = 2
                alert.task_id = log.task_id
                alert.log_id = log.id
                alert.alert_name = "数据监控"
                alert.alert_message = log.alert_message
                alert.start_time = log.start_time
                alert.end_time = log.start_time
                alert.execution_duration = log.execution_duration
                alert.result = log.result
                alert.status = 1
                session.add(alert)
                session.commit()

                self.logger.info('alert commit')
                time.sleep(5)

            return True

        except Exception as ex:
            session.rollback()
            self.logger.error(f'alert Exception {str(ex)}')
            return False

        finally:
            if session:
                session.close()

            self.logger.debug(f'finish -> data_monitor_save')

    def trigger_alert(self, task: DMAlertTasks, log: DMTaskLogs):
        """ 数据监控报警 """

        # 静默处理
        quiet_period = get_settings2('elk', 'quiet_period', [])
        is_quiet_period = self.is_quiet_period(quiet_period)
        self.logger.info(f'is_quiet_period:{is_quiet_period} quiet_period: {quiet_period}')
        if is_quiet_period:  # 静默处理
            self.logger.info('is quiet period --->>>')
            return True

        # 去重逻辑，根据 task_id 进行去重
        repeat_time = get_settings2('elk', 'repeat_time', 60 * 5)
        is_trigger_alert = self.can_trigger_alert(task.id, repeat_time)
        self.logger.debug(f'is_trigger_alert: {is_trigger_alert} repeat_time: {repeat_time}')
        if not is_trigger_alert:
            self.logger.debug(f'message is repeat ===>>>, {is_trigger_alert} return')
            return

        # 获取报警方式
        alert_methods = task.alert_method.split(',')
        message = log.alert_message
        log_data = {'alert_message': message}
        # log_task_execution(log_data, log_id)

        alert_methods = list(set(get_settings3('elk', 'alert_channel', 'data').keys()).intersection(alert_methods))
        errors = []

        # 发送微信报警
        if '1' in alert_methods:
            try:
                recipients = task.wechat_recipients.split(',')
                self.send_wechat_alert(recipients, message)
            except Exception as e:
                self.logger.error(f'微信报警失败: {str(e)}')

        # 发送钉钉报警
        if '2' in alert_methods:
            try:
                recipients = task.dingtalk_recipients.split(',')
                self.send_dingtalk_alert(recipients, message)
            except Exception as e:
                self.logger.error(f'钉钉报警失败: {str(e)}')

        # 发送邮件报警
        if '3' in alert_methods:
            try:
                recipients = task.email_recipients.split(',')
                self.send_email_alert(recipients, message)
            except Exception as e:
                self.logger.error(f'邮件报警失败: {str(e)}')

        # 如果有错误信息，处理错误
        if errors:
            raise Exception(json.dumps(errors, ensure_ascii=False))

    def trigger_alert_log_monitor(self, alert: DMMonitorAlert):

        # 静默处理
        quiet_period = get_settings2('elk', 'quiet_period', [])
        is_quiet_period = self.is_quiet_period(quiet_period)
        self.logger.info(f'is_quiet_period:{is_quiet_period} quiet_period: {quiet_period}')
        if is_quiet_period:  # 静默处理
            self.logger.info('is quiet period --->>>')
            return True

        # 获取报警方式
        alert_channel = get_settings3('elk', 'alert_channel', 'log', {})
        alert_methods = alert_channel.keys()
        message = f"{alert.alert_message} 共 {alert.total} 条"
        log_data = {'alert_message': message}
        # log_task_execution(log_data, log_id)

        errors = []

        # 发送微信报警
        if '1' in alert_methods:
            try:
                recipients = alert_channel.get('1', '').split(',')
                self.send_wechat_alert(recipients, message)
            except Exception as e:
                self.logger.error(f'微信报警失败: {str(e)}')

        # 发送钉钉报警
        if '2' in alert_methods:
            try:
                recipients = alert_channel.get('2', '').split(',')
                self.send_dingtalk_alert(recipients, message)
            except Exception as e:
                self.logger.error(f'钉钉报警失败: {str(e)}')

        # 发送邮件报警
        if '3' in alert_methods:
            try:
                recipients = alert_channel.get('3', '').split(',')
                self.send_email_alert(recipients, message)
            except Exception as e:
                self.logger.error(f'邮件报警失败: {str(e)}')

        # 如果有错误信息，处理错误
        if errors:
            raise Exception(json.dumps(errors, ensure_ascii=False))

    def send_wechat_alert(self, recipients, message):
        url = 'http://data.ijiwei.com/dc/qianxun/send_text2'
        for recipient in recipients:
            try:
                post_data = {
                    'channel': 'wechat',
                    # 'type': hashlib.md5(message.encode()).hexdigest()[:8],
                    'type': random.randint(100000, 999999),
                    'wxid': recipient,
                    'msg': message,
                    'mail_title': '微信监控报警',
                    'mail_body': message,
                }

                response = requests.post(url, data=post_data)
                # 检查响应状态码
                if response.status_code == 200:
                    self.logger.info(f"请求成功:{response.text}")
                else:
                    self.logger.error(f"请求失败，状态码: {response.status_code}, 响应内容: {response.text}")
            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常: {str(e)}")

    def send_dingtalk_alert(self, recipients, message):
        message = f"【钉钉监控报警】\n{message}"
        for access_token in recipients:
            webhook_url = f"https://oapi.dingtalk.com/robot/send?access_token={access_token.strip()}"
            post_data = {
                'msgtype': 'text',
                'text': {
                    'content': message,
                },
            }

            headers = {'Content-Type': 'application/json; charset=utf-8'}
            response = requests.post(webhook_url, headers=headers, json=post_data)

            if response.status_code != 200:
                self.logger.error(f'cURL 请求失败: {response.text}')

            response_decoded = response.json()
            if response_decoded.get('errcode') != 0:
                self.logger.error(f'请求失败: {response_decoded.get("errmsg", "未知错误")}')

    def send_email_alert(self, recipients, message, subject='邮件监控报警'):
        # subject = '邮件报警'
        for recipient in recipients:
            self.send_email(subject, message, recipient)

    def send_email(self, subject, message, recipient):
        # 定义发件人和收件人的邮箱
        mail_config = get_settings2('elk', 'mail', {})

        # sender_email = "<EMAIL>"
        sender_email = mail_config.get('mail_from_address')
        recipient_email = recipient

        # 创建MIMEText对象，表示电子邮件的正文
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg['Subject'] = subject

        # 添加邮件正文
        msg.attach(MIMEText(message, 'plain'))
        msg.attach(MIMEText(message, 'plain', 'utf-8'))

        # 连接到SMTP服务器并发送邮件
        try:
            # 使用SMTP_SSL进行SSL加密连接
            server = smtplib.SMTP_SSL(mail_config.get('mail_host'), int(mail_config.get('mail_port')))  # 使用正确的SMTP服务器和SSL端口
            server.login(sender_email, mail_config.get('mail_password'))  # 登录SMTP服务器
            server.sendmail(sender_email, recipient_email.split(','), msg.as_string())  # 发送邮件
            server.quit()
            self.logger.info(f"邮件已发送到 {recipient_email}")
        except Exception as e:
            self.logger.error(f"无法发送邮件到 {recipient_email}: {str(e)}")

    def is_quiet_period(self, quiet_period):
        """
        判断当前时间是否静默时间
        :param quiet_period:
        :return:
        :rtype: bool
        """
        # 获取当前时间
        current_time = datetime.now().time()

        # 遍历每一个时间段
        for period in quiet_period:
            # 将字符串时间转换为 time 对象
            start_time = datetime.strptime(period['start'], '%H:%M:%S').time()
            stop_time = datetime.strptime(period['stop'], '%H:%M:%S').time()

            # 判断当前时间是否在该时间段内
            if start_time <= current_time <= stop_time:
                return True

        return False

    def can_trigger_alert(self, task_id, window_seconds=20):
        """
        检查是否可以触发报警任务，根据 task_id 在指定时间窗口内（默认 20 秒）进行去重。

        参数:
        task_id (int): 报警任务的唯一标识 ID。
        window_seconds (int): 时间窗口，单位为秒，默认是 20 秒。

        返回值:
        bool: 如果可以触发报警返回 True，否则返回 False。
        """

        # 允许触发报警
        return True

        from dc.services.redis_service import RedisService
        rs = RedisService()
        redis_client = rs.get_client()

        # 定义 Redis key，使用 task_id 作为唯一标识
        redis_key = f"alert:{task_id}"

        # 获取当前时间
        current_time = datetime.now()

        # 从 Redis 获取上次触发时间
        last_trigger_time = redis_client.get(redis_key)

        if last_trigger_time:
            # 将 Redis 中存储的时间转换为 datetime 对象
            last_trigger_time = datetime.strptime(str(last_trigger_time), "%Y-%m-%d %H:%M:%S")

            # 检查是否已经超过指定的时间窗口
            if current_time - last_trigger_time < timedelta(seconds=window_seconds):
                # 如果未超过时间窗口，不允许触发报警
                return False

        # 更新 Redis 中的触发时间
        redis_client.set(redis_key, current_time.strftime("%Y-%m-%d %H:%M:%S"))

        redis_client.close()

        # 允许触发报警
        return True

    def log_test_save(self, msg):
        self.logger.info(time.time())
        self.logger.info(msg)
