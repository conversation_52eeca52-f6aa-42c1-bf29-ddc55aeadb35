# -*-coding:utf-8-*-
import json
import os
import re
import sys
import traceback
import time
from typing import List

import parsel
import requests
import chardet
import hashlib
import random

from dc.common.functools_wraps import deprecated
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from urllib.parse import urlparse, urlunparse
from bs4 import BeautifulSoup
from datetime import datetime

from selenium.webdriver.remote.webelement import WebElement

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from dc.services.mysql_service import MySQLService
from dc.models.model import DCSiteListRule, DCSiteList, DCIntellectualProperty
from dc.common.alchemy import query2dict
from dc.services.qiniu_service import QiniuService
from dc.common.qiniu_utils import *

log_service = LoggingService('dc_check_analysis.log')
redis_service = RedisService('redis')
client1 = redis_service.get_client()

mysql = MySQLService()
session = mysql.create_session()
save_path = "/uploads/chanquan/"


class CheckAnalysis:

    @staticmethod
    def check_analysis(params):
        """ 验证提取规则 """

        ret = '【%s】参数不能为空，请确认'
        if 'detailUrl' not in params or not params['detailUrl']:
            return {'code': 801, 'msg': ret % 'detailUrl', 'data': []}

        if 'siteRuleId' not in params or not params['siteRuleId']:
            return {'code': 801, 'msg': ret % 'siteRuleId', 'data': []}

        if 'ruleItems' not in params:
            return {'code': 801, 'msg': ret % 'ruleItems', 'data': []}

        check_method = CheckAnalysis.checkDetailMethod(params['siteRuleId'])
        detail_method = int(check_method[0])
        analysis_method = 0 if check_method[1] == '' else check_method[1]

        items = json.dumps({9999999: json.loads(params['ruleItems'])})
        if detail_method == 1:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
            }
            r = requests.get(f"{params['detailUrl']}", headers=headers)
            r.encoding = "UTF-8"

            if r.status_code != 200:
                return {'code': 801, 'msg': '当前页面无法打开，请稍后重试', 'data': []}

            info_encode = chardet.detect(r.content).get('encoding', 'UTF-8')
            html = r.content.decode(info_encode, 'ignore')
            task_info = {'siteListId': 0, 'url': params['detailUrl']}
            ret = CheckAnalysis.ordinary_analysis(items, html, task_info)
        else:
            ym = time.strftime('%Y%m', time.localtime(time.time())) + "/"
            if analysis_method == 1:
                opt = webdriver.FirefoxOptions()
                opt.add_argument("--headless")
                # opt.add_argument('--disable-gpu')
                driver = webdriver.Firefox(options=opt)
            else:
                chrome_options = Options()
                chrome_options.add_argument('--headless')
                chrome_options.add_argument('--no-sandbox')
                # chrome_options.add_argument('--disable-gpu')
                prefs = {'profile.default_content_settings.popups': 0,
                         'download.default_directory': "/data" + save_path + ym}
                # prefs = {'profile.default_content_settings.popups': 0,
                #          'download.default_directory': "D:\\"}
                chrome_options.add_experimental_option('prefs', prefs)
                driver = webdriver.Chrome(options=chrome_options)
                driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                    'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'})

            driver.implicitly_wait(10)
            driver.get(params['detailUrl'])

            task_info = {'siteListId': 0, 'url': params['detailUrl']}
            site_info = {'ruleItem': items, 'sourceCategory': ''}
            ret = CheckAnalysis.browser_analysis(driver, task_info, site_info, driver.page_source)

        if ret['analysis_status'] == 1:
            msg = '成功'
        elif ret['analysis_status'] == 2:
            msg = '失败'
        else:
            msg = '部分成功'
        return {'code': 0, 'msg': msg, 'data': ret['info']}

    # 验证参数
    @staticmethod
    def check_params(params):
        if len(params) == 0:
            return {'code': 801, 'msg': '参数为空，请确认参数', 'data': []}

        ret = '【%s】参数不能为空，请确认'
        if 'detailUrl' not in params or not params['detailUrl']:
            return {'code': 801, 'msg': ret % 'detailUrl', 'data': []}

        if 'crawlRuleType' not in params or not params['crawlRuleType'] or params['crawlRuleType'] \
                not in ['1', '2', '3', '4', '5', '6']:
            return {'code': 801, 'msg': ret % 'crawlRuleType', 'data': []}

        if ('crawlRule' not in params or not params['crawlRule']) and params['crawlRuleType'] not in ['4', '5', '6']:
            return {'code': 801, 'msg': ret % 'crawlRule', 'data': []}

        if ('startFlag' not in params or 'endFlag' not in params or not params['startFlag'] or not params['endFlag']) \
                and params['crawlRuleType'] == '4':
            return {'code': 801, 'msg': ret % 'startFlag/endFlag', 'data': []}

        if 'siteRuleId' not in params or not params['siteRuleId']:
            return {'code': 801, 'msg': ret % 'siteRuleId', 'data': []}

        return {'code': 0, 'msg': '成功'}

    # 验证规则
    @staticmethod
    @deprecated(message='该方法已废弃')
    def check_analysis2(params):
        ret = CheckAnalysis.check_params(params)
        if ret['code'] == 801:
            return ret

        ret1 = {'code': 801, 'msg': '当前规则未能提取到信息', 'data': []}

        # 判断当前详情页是普通还是高级
        check_method = CheckAnalysis.checkDetailMethod(params['siteRuleId'])
        str1 = ''
        if check_method[0] == 1:
            # html = client1.get(f"{params['detailUrl']}_1")
            html = False
            if not html:
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
                }
                r = requests.get(f"{params['detailUrl']}", headers=headers)
                r.encoding = "UTF-8"

                if r.status_code != 200:
                    return {'code': 801, 'msg': '当前页面无法打开，请稍后重试', 'data': []}

                info_encode = chardet.detect(r.content).get('encoding', 'UTF-8')
                html = r.content.decode(info_encode, 'ignore')

                html = CheckAnalysis.html_replace_str(html)
                if html:
                    client1.set(f"{params['detailUrl']}_1", html, 1200)

            if params['crawlRuleType'] == '1':
                str1 = CheckAnalysis.selector_xpath(html, f"{params['crawlRule']}")
            if params['crawlRuleType'] == '2':
                str1 = CheckAnalysis.selector_css(html, f"{params['crawlRule']}")
            # if params['crawlRuleType'] == '5':
            #     str1 = CheckAnalysis.selector_xpath_attr(html, f"{params['crawlRule']}")
            # if params['crawlRuleType'] == '6':
            #     str1 = CheckAnalysis.selector_css_attr(html, f"{params['crawlRule']}")
        else:
            asd = int(check_method[1]) if check_method[1] else 0
            if asd == 1:
                opt = webdriver.FirefoxOptions()
                opt.add_argument("--headless")
                opt.add_argument('--disable-gpu')
                driver = webdriver.Firefox(options=opt)
            else:
                chrome_options = Options()
                chrome_options.add_argument('--headless')
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-gpu')
                prefs = {'profile.default_content_settings.popups': 0, 'download.default_directory': save_path}
                chrome_options.add_experimental_option('prefs', prefs)
                driver = webdriver.Chrome(options=chrome_options)
                driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                    'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
                })
            driver.implicitly_wait(10)

            try:
                # html = client1.get(f"{params['detailUrl']}_2")
                html = False
                if not html:
                    driver.get(f"{params['detailUrl']}")
                    driver.implicitly_wait(10)
                    if params['crawlRuleType'] == '1':
                        driver.find_elements('xpath', params['crawlRule'])
                        driver.implicitly_wait(10)
                    if params['crawlRuleType'] == '2':
                        driver.find_elements('css selector', params['crawlRule'])
                        driver.implicitly_wait(10)
                    # re_encoding1 = CheckAnalysis.get_char_set(driver)
                    html = driver.page_source

                    # client1.set(f"{params['detailUrl']}_2", html, 1200)
                    # client1.set(f"{params['detailUrl']}_2_encoding", re_encoding1, 1200)
                # else:
                # re_encoding1 = client1.get(f"{params['detailUrl']}_2_encoding")
                # re_encoding2 = re_encoding1 if re_encoding1 else 'utf-8'
                # f = open(f"./tmp_html.html", 'a', encoding=f"{re_encoding2}", errors='ignore')
                # f.write(html)
                # f.close()
                # driver.get('file:///' + os.path.abspath(f"tmp_html.html"))
            except:
                log_service.dcPullLog(f"详情地址：{params['detailUrl']}；打开网页失败，失败原因：{traceback.format_exc()}")
                driver.close()
                CheckAnalysis.del_tmp_html()

                return {'code': 801, 'msg': '当前页面无法打开，请稍后重试', 'data': []}

            # css、xpath
            try:
                if params['crawlRuleType'] == '1':
                    xpath = driver.find_elements('xpath', params['crawlRule'])
                    str1 = CheckAnalysis.deal_text(xpath)
                elif params['crawlRuleType'] == '2':
                    css = driver.find_elements('css selector', params['crawlRule'])
                    str1 = CheckAnalysis.deal_text(css)
                elif params['crawlRuleType'] == '5':
                    str1 = CheckAnalysis.getFilesV2(driver, params['detailUrl'], params['crawlRule'], params['columnRegex'], 1)
                elif params['crawlRuleType'] == '6':
                    str1 = CheckAnalysis.getImages(driver, params['detailUrl'], params['crawlRule'], params['columnRegex'], 1)

                CheckAnalysis.del_tmp_html()
                driver.close()
            except:
                log_service.dcPullLog(f"解析异常：{params['crawlRule']}；失败原因：{traceback.format_exc()}")
                CheckAnalysis.del_tmp_html()
                driver.close()
                return ret1

        # 截取
        if params['crawlRuleType'] == '4':
            begin = html.find(params['startFlag'])
            end = html.rfind(params['endFlag'])
            str1 = html[begin:end]

        # 正则
        if params['crawlRuleType'] == '3':
            arr = re.search(params['crawlRule'], html)
            if not arr:
                return ret1
            else:
                str1 = arr.group()

        if params['crawlRuleType'] not in ['5', '6']:
            if not str1 or not str1.strip():
                return ret1

            str1 = str1.strip()
            str1 = str1.strip("/n")

            if 'columnRegex' in params and params['columnRegex']:
                arr = re.findall(f"{params['columnRegex']}", str1)
                str1 = arr[0] if arr else ''

        return {'code': 0, 'msg': '验证成功', 'data': [str1]}

    @staticmethod
    def checkDetailMethod(rule_id):
        try:
            ret = session.query(DCSiteListRule).filter(DCSiteListRule.id == rule_id).first()
            dc_rule_info = query2dict(ret)
            site_id = dc_rule_info['siteListId']

            ret_site = session.query(DCSiteList).filter(DCSiteList.id == site_id).first()
            dc_site_info = query2dict(ret_site)

            analysis_method = ''
            if dc_site_info['extra']:
                dc_extra = json.loads(dc_site_info['extra'])
                if 'analysisMethod' in dc_extra:
                    analysis_method = dc_extra['analysisMethod']

            detail_method = dc_site_info['detailDcMethod']

            return [detail_method, analysis_method]
        except:
            return [2, '']  # 返回高级模式

    @staticmethod
    def get_char_set(driver):
        try:
            html_encoding = driver.find_element('xpath', '/html/head/meta[1]')
        except:
            return 'utf-8'

        html_encoding1 = html_encoding.get_attribute('content')
        if html_encoding1:
            re_encoding = "(?<=charset=)(.+)"
            arr = re.findall(f"{re_encoding}", html_encoding1)
            re_encoding1 = arr[0] if arr else 'utf-8'
        else:
            re_encoding1 = html_encoding.get_attribute('charset')

        return re_encoding1

    @staticmethod
    def deal_text(obj):
        split = "/n"
        tmp_list = [x.text for x in obj if x.text.strip()]
        str1 = split.join(tmp_list)

        return str1

    @staticmethod
    def deal_attr(obj, attr_value='value'):
        split = "/n"
        tmp_list = [x.get_attribute(attr_value) for x in obj if x.get_attribute(attr_value).strip()]
        str1 = split.join(tmp_list)

        return str1

    @staticmethod
    def del_tmp_html():
        file_list = os.listdir('./')
        for file_name in file_list:
            if file_name == 'tmp_html.html':
                os.remove(os.path.abspath(f"tmp_html.html"))

    @staticmethod
    def selector_xpath(html, craw_rule):
        selector = parsel.Selector(text=html)
        xpath_obj = selector.xpath(f"{craw_rule}")
        craw_arr = re.findall(f"text\(\)", f"{craw_rule}")
        if craw_arr:
            str_arr = xpath_obj.getall()
        else:
            str_arr = xpath_obj.xpath(".//text()").getall()

        deal_arr = [tmp_str for tmp_str in str_arr if tmp_str.strip()]

        flag = "/n"
        str1 = flag.join(deal_arr)

        return str1

    @staticmethod
    def selector_css(html, craw_rule):
        selector = parsel.Selector(html)
        css_obj = selector.css(f"{craw_rule}")
        craw_arr = re.findall(f"text\(\)", f"{craw_rule}")
        if craw_arr:
            str_arr = css_obj.getall()
        else:
            str_arr = css_obj.xpath(".//text()").getall()

        deal_arr = [tmp_str for tmp_str in str_arr if tmp_str.strip()]

        flag = "/n"
        str1 = flag.join(deal_arr)

        return str1

    @staticmethod
    def html_replace_str(html):
        html = html.replace("\n", '')
        html = html.replace("\r", '')
        html = html.replace("\t", '')
        html = html.replace("<br>", '')

        return html

    @staticmethod
    def selector_css_attr(html, craw_rule):
        selector = parsel.Selector(html)
        str_arr = selector.css(f"{craw_rule}").getall()
        # str1 = selector.css(f"{craw_rule}::attr({craw_attr})").get()
        deal_arr = [tmp_str for tmp_str in str_arr if tmp_str.strip()]

        flag = "/n"
        str1 = flag.join(deal_arr)

        return str1

    @staticmethod
    def selector_xpath_attr(html, craw_rule):
        selector = parsel.Selector(html)
        str_arr = selector.xpath(f"{craw_rule}").getall()
        # str1 = selector.xpath(f"{craw_rule}/@{craw_attr}").get()

        deal_arr = [tmp_str for tmp_str in str_arr if tmp_str.strip()]

        flag = "/n"
        str1 = flag.join(deal_arr)

        return str1

    @staticmethod
    def complement_url(a_url, detail_url, crawl_rule, column_regex):
        a_url = str(a_url)
        if re.findall('http', a_url) or re.findall('https', a_url):
            target_url = a_url
        else:
            if re.findall('\.\./', a_url):
                a_url = CheckAnalysis.dealPath(a_url)
            if re.findall('\./', a_url):
                a_url = CheckAnalysis.dealPath1(a_url)

            if len(crawl_rule) > 0:
                target_url = crawl_rule + a_url
            else:
                result = urlparse(detail_url)
                r_path = result.path
                r_path_arr = r_path.split("/")
                r_path_len = len(r_path_arr)
                r_path_arr1 = r_path_arr[0: r_path_len - int(column_regex)]
                r_path1 = "/".join(r_path_arr1)
                r_path2 = r_path1 + "/"

                target_url = urlunparse([str(result.scheme), str(result.netloc), r_path2, '', '', '']) + a_url

        return target_url

    @staticmethod
    def getMd5(url):
        m = hashlib.md5()
        m.update(url.encode('utf-8'))
        return m.hexdigest()

    @staticmethod
    @deprecated(message="请使用getFiles替代")
    def getFiles(driver, detail_url, rule, is_check=2):
        ym = time.strftime('%Y%m', time.localtime(time.time())) + "/"
        types = ['.docx', '.xlsx', '.xls', '.wps', '.zip', '.doc', '.pdf', '.txt', '.ofd', '.rar', '.ppt',
                 '.pptx', '.tif']
        links = driver.find_elements('xpath', f"{rule['crawlRule']}//a")
        files = {}
        files_html = {}
        files_url = []
        files_url_html = []
        for href in links:
            a_url = href.get_attribute('href')
            a_url = str(a_url)
            target_url = CheckAnalysis.complement_url(a_url, detail_url, rule['path'], rule['pathLevel'])
            ip_search = re.search("([0-9]{1,3}\.){3}[0-9]{1,3}", target_url)
            if ip_search:
                continue

            if '.html' in target_url:
                # if re.findall('\.html', target_url):
                if is_check == 1:
                    files_url_html.append(target_url)
                else:
                    file_name = href.text
                    files_html[file_name] = target_url
                continue

            for t in types:
                if t not in str(target_url).lower() and t not in href.text.lower():
                    # if not re.findall(t, str(target_url).lower()) and not re.findall(t, href.text.lower()):
                    continue

                if is_check == 1:
                    files_url.append(target_url)
                else:
                    try:
                        driver.get(target_url)
                        file_name = href.text
                        target_arr = target_url.split("/")
                        file_len = len(target_arr)
                        file_url = target_arr[file_len - 1]
                        files[file_name] = save_path + ym + file_url
                        time.sleep(2)
                    except Exception as ex:
                        log_service.error(f"driver.get ex:{ex} target_url:{target_url}")
                        continue

        if is_check == 1:
            if files_url:
                return files_url
            else:
                return files_url_html
        else:
            if files:
                return json.dumps(files)
            else:
                return json.dumps(files_html)

    @staticmethod
    def getFilesV2(driver, detail_url, rule, is_check=2):
        ym = time.strftime('%Y%m', time.localtime(time.time())) + "/"
        types = ['.docx', '.xlsx', '.xls', '.wps', '.zip', '.doc', '.pdf', '.txt', '.ofd', '.rar', '.ppt',
                 '.pptx', '.tif']
        links = driver.find_elements('xpath', f"{rule['crawlRule']}//a")
        files = {}
        files2 = []
        files_html = {}
        files_url = []
        files_url_html = []
        hrefs = []
        cache_target_url = set()
        for href in links:
            a_url = href.get_attribute('href')
            a_url = str(a_url)
            target_url = CheckAnalysis.complement_url(a_url, detail_url, rule['path'], rule['pathLevel'])
            if target_url in cache_target_url:
                continue
            cache_target_url.add(target_url)
            ip_search = re.search("([0-9]{1,3}\.){3}[0-9]{1,3}", target_url)
            if ip_search:
                continue

            ext = get_extension_from_url(target_url, '.html')
            is_valid_html = is_valid_html_extension(ext)
            hrefs.append({
                'name': href.text,
                'url': target_url,
                'html': href.get_attribute('outerHTML'),
                'is_valid_html': is_valid_html,
                'extension': ext
            })

            # if '.html' in target_url:
            if is_valid_html:
                # if re.findall('\.html', target_url):
                if is_check == 1:
                    files_url_html.append(target_url)
                else:
                    file_name = href.text
                    files_html[file_name] = target_url
                continue

            if is_check == 1:
                files_url.append(target_url)
            else:
                file_name = href.text

                qs = QiniuService(name="dc")
                files_save_path = qs.get_save_path("files")
                qs_files = qs.fetch_files([target_url], files_save_path)
                qs_file = qs_files[0] if qs_files else []
                if qs_file:
                    qs_file['name'] = file_name
                    files2.append(qs_file)

        return files2, hrefs

    @staticmethod
    def getImages(driver, detail_url, rule, is_check=2):
        ym = time.strftime('%Y%m', time.localtime(time.time())) + "/"
        types = ['.png', '.jpg', '.gif', '.jpeg']
        links = driver.find_elements('xpath', f"{rule['crawlRule']}//img")
        images = {}
        images_url = []
        origin_url = []
        for href in links:
            a_url = href.get_attribute('src')
            a_url = str(a_url)
            target_url = CheckAnalysis.complement_url(a_url, detail_url, rule['path'], rule['pathLevel'])
            # 获取原始链接
            origin_url.append(target_url)

            for t in types:
                if t not in target_url:
                    # if not re.findall(t, target_url):
                    continue

                if is_check == 1:
                    images_url.append(target_url)
                else:
                    try:
                        header = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (HTML, '
                                          'like Gecko) Chrome/99.0.4844.82 Safari/537.36'}
                        pic = requests.get(a_url, verify=False, headers=header)
                        # print(pic.status_code)
                        # 获取图片保存的路径
                        md5_str = CheckAnalysis.getMd5(target_url)
                        base_url = save_path + ym + f"/{md5_str}{t[1:]}"
                        with open("/data" + base_url, 'wb') as f:
                            f.write(pic.content)
                        # base_url = f"/{md5_str}{t[1:]}"
                        # with open("D:\\" + base_url, 'wb') as f:
                        #     f.write(pic.content)
                        images[md5_str] = base_url
                    except:
                        print(f"{traceback.format_exc()}")
        if is_check == 1:
            return images_url
        else:
            return [json.dumps(images), origin_url]

    @staticmethod
    def getImagesV2(driver, detail_url, rule, is_check=2):
        ym = time.strftime('%Y%m', time.localtime(time.time())) + "/"
        # types = ['.png', '.jpg', '.gif', '.jpeg']
        types = get_image_extension()
        links: List[WebElement] = driver.find_elements('xpath', f"{rule['crawlRule']}")
        images = {}
        images_url = []
        origin_url = []
        qiniu_images = []
        site_list = CheckAnalysis.get_site_list(rule['siteListId'])
        extra = json.loads(site_list.get("extra", "{}"))
        image_attr = extra.get('imageAttr', 'src')
        qs = QiniuService()
        for href_index, href in enumerate(links):
            a_url = href.get_attribute(image_attr)
            a_url = str(a_url)
            target_url = CheckAnalysis.complement_url(a_url, detail_url, rule['path'], rule['pathLevel'])
            # 获取原始链接
            origin_url.append(target_url)
            extension = get_extension_from_url(target_url, '.jpg')
            if not is_valid_image_extension(extension):
                continue

            t = extension
            if is_check == 1:
                images_url.append(target_url)
            else:
                try:
                    header = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (HTML, '
                                      'like Gecko) Chrome/99.0.4844.82 Safari/537.36'}
                    # pic = requests.get(a_url, verify=False, headers=header)
                    # print(pic.status_code)
                    # 获取图片保存的路径
                    md5_str = CheckAnalysis.getMd5(target_url)
                    base_url = save_path + ym + f"/{md5_str}{t[0:]}"
                    # with open("/data" + base_url, 'wb') as f:
                    #     f.write(pic.content)

                    # base_url = f"/{md5_str}{t[1:]}"
                    # with open("D:\\" + base_url, 'wb') as f:
                    #     f.write(pic.content)
                    images[md5_str] = base_url
                except:
                    print(f"{traceback.format_exc()}")

                # 七牛处理
                try:
                    md5 = calculate_md5(detail_url)
                    image_save_path = url_path_join(qs.config["document_name"], md5)
                    qiniu_images_info = qs.fetch_image([target_url], image_save_path, href_index)
                    if qiniu_images_info:
                        qiniu_images.append(qiniu_images_info[0])

                    log_service.info(f"七牛处理成功")

                except Exception as ex:
                    log_service.error(f"七牛处理异常: {str(ex)}")

        if is_check == 1:
            return images_url
        else:
            return [json.dumps(images), origin_url, qiniu_images]

    @staticmethod
    def dealPath(a_url):
        if not re.findall('\.\./', str(a_url)):
            return "/" + a_url

        a_url = a_url[3:]
        return CheckAnalysis.dealPath(a_url)

    @staticmethod
    def dealPath1(a_url):
        if not re.findall('\./', str(a_url)):
            return "/" + a_url

        a_url = a_url[2:]
        return CheckAnalysis.dealPath1(a_url)

    @staticmethod
    def ordinary_analysis(items, html, task_info):
        """ 普通模式处理数据 """
        # items = {
        #     "1138": {
        #         "5172": {
        #             "id": 5172,
        #             "siteListId": 789,
        #             "siteRuleId": 1138,
        #             "columnTitle": "标题",
        #             "columnKey": "title",
        #             "crawlRuleType": 1,
        #             "crawlRule": "//h1[@class=\"main-title\"]",
        #             "startFlag": "",
        #             "endFlag": "",
        #             "columnRegex": "",
        #             "columnDefault": "",
        #             "status": 1,
        #             "createdAt": "2023-09-21 16:03:12",
        #             "updatedAt": "2023-09-21 16:03:12",
        #             "analysisType": 1,
        #             "path": "",
        #             "pathLevel": 0
        #         },
        #         "5173": {
        #             "id": 5173,
        #             "siteListId": 789,
        #             "siteRuleId": 1138,
        #             "columnTitle": "发布日期",
        #             "columnKey": "public_time",
        #             "crawlRuleType": 1,
        #             "crawlRule": "//div[@class=\"date-source\"]",
        #             "startFlag": "",
        #             "endFlag": "",
        #             "columnRegex": "\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}:\\d{2}",
        #             "columnDefault": "",
        #             "status": 1,
        #             "createdAt": "2023-09-21 16:03:12",
        #             "updatedAt": "2023-09-21 16:10:35",
        #             "analysisType": 1,
        #             "path": "",
        #             "pathLevel": 0
        #         },
        #         "5174": {
        #             "id": 5174,
        #             "siteListId": 789,
        #             "siteRuleId": 1138,
        #             "columnTitle": "正文",
        #             "columnKey": "text",
        #             "crawlRuleType": 1,
        #             "crawlRule": "//div[@id=\"artibody\"]",
        #             "startFlag": "",
        #             "endFlag": "",
        #             "columnRegex": "",
        #             "columnDefault": "",
        #             "status": 1,
        #             "createdAt": "2023-09-21 16:03:12",
        #             "updatedAt": "2023-09-21 16:03:12",
        #             "analysisType": 1,
        #             "path": "",
        #             "pathLevel": 0
        #         }
        #     }
        # }

        items = json.loads(items)
        html = CheckAnalysis.html_replace_str(html)
        info = {}
        success_max = 0.00
        rule_id = 0
        for key, value in items.items():
            info[key] = {}
            item_len = len(value)
            false_num = 0
            for _, item in value.items():
                if item['crawlRuleType'] == 1:
                    str1 = CheckAnalysis.selector_xpath(html, f"{item['crawlRule']}")

                if item['crawlRuleType'] == 2:
                    str1 = CheckAnalysis.selector_css(html, f"{item['crawlRule']}")

                if item['crawlRuleType'] == 3:
                    arr = re.search(item['crawlRule'], html)
                    if not arr:
                        str1 = ''
                    else:
                        str1 = arr.group()

                if item['crawlRuleType'] == 4:
                    begin = html.find(item['startFlag'])
                    end = html.rfind(item['endFlag'])
                    str1 = html[begin:end]
                    if item['analysisType'] == 1:
                        soup = BeautifulSoup(str1, 'html.parser')
                        str1 = soup.get_text()

                if str1:
                    str1 = str1.strip()
                    str1 = str1.strip("/n")

                if 'columnRegex' in item and item['columnRegex'] and str1:
                    arr = re.findall(f"{item['columnRegex']}", str1)
                    str1 = arr[0] if arr else str1

                if item['columnKey'] == 'public_time':
                    if not str1:
                        str1 = task_info[
                                   'publicTime'] + " 00:00:00" if "publicTime" in task_info else "2000-01-01 00:00:00"
                    tmp_arr = CheckAnalysis.deal_format_date(str1)
                    str1 = tmp_arr[0]
                    if tmp_arr[1] == 2:
                        false_num += 1

                info[key][item['columnKey']] = str1
                if len(str1) == 0:
                    false_num += 1
                    info[key][item['columnKey']] = item['columnDefault']

            curr_success = float('%.2f' % ((item_len - false_num) / item_len))
            if curr_success >= success_max:
                rule_id = key
                success_max = curr_success

        if success_max == 0.00:
            analysis_status = 2
        elif success_max == 1.00:
            analysis_status = 1
        else:
            analysis_status = 3

        return {'info': info[rule_id], 'analysis_status': analysis_status, 'rule_id': rule_id}

    @staticmethod
    def attachment_analysis(items, html, task_info):
        """ 附件模式处理数据 """
        # items = json.loads(items)
        # html = CheckAnalysis.html_replace_str(html)
        info = {}
        success_max = 0.00
        rule_id = 0
        qs = QiniuService(name="dc")
        files_save_path = qs.get_save_path('files')
        files = qs.fetch_files([task_info['url']], files_save_path)
        info[rule_id] = {
            'title': task_info['title'],
            'text': "",
            'public_time': task_info['publicTime'],
            'files_v2': files,
        }
        analysis_status = 1

        return {'info': info[rule_id], 'analysis_status': analysis_status, 'rule_id': rule_id}

    @staticmethod
    def browser_analysis(driver, task_info, site_info, html):
        """ 浏览器模式页面处理数据 """
        items = json.loads(site_info['ruleItem'])
        html = CheckAnalysis.html_replace_str(html)
        info = {}
        success_max = 0.00
        rule_id = 0
        qs = QiniuService()
        for key, value in items.items():
            info[key] = {}
            item_len = len(value)
            false_num = 0
            files_v2 = []
            files_v2_hrefs = []
            files_rules = [item for _, item in value.items() if item['crawlRuleType'] == 5]  # 附件采集规则
            images_rules = [item for _, item in value.items() if item['crawlRuleType'] == 6]  # 图片采集规则
            for _, item in value.items():

                str1 = ""
                try:
                    if item['analysisType'] == 1:
                        if item['crawlRuleType'] == 1:
                            xpath = driver.find_elements('xpath', item['crawlRule'])
                            str1 = CheckAnalysis.deal_text(xpath)
                        if item['crawlRuleType'] == 2:
                            css = driver.find_elements('css selector', item['crawlRule'])
                            str1 = CheckAnalysis.deal_text(css)
                    else:
                        if item['crawlRuleType'] == 1:
                            str1 = driver.find_element('xpath', item['crawlRule']).get_attribute('outerHTML')
                        if item['crawlRuleType'] == 2:
                            str1 = driver.find_element('css selector', item['crawlRule']).get_attribute('outerHTML')

                    if item['crawlRuleType'] == 5:
                        try:
                            str1 = CheckAnalysis.getFiles(driver, task_info['url'], item, 2)
                        except Exception as ex:
                            log_service.error(f"CheckAnalysis.getFiles 解析异常：{str(ex)}")

                        files_v2, files_v2_hrefs = CheckAnalysis.getFilesV2(driver, task_info['url'], item, 2)
                    if item['crawlRuleType'] == 6:
                        item['siteListId'] = task_info['siteListId']
                        image_arr = CheckAnalysis.getImagesV2(driver, task_info['url'], item, 2)
                        str1 = image_arr[0]
                        info[key]['origin_url'] = image_arr[1]
                        # 七牛图片处理
                        if image_arr[2]:
                            images_v2 = qs.images_filer(image_arr[2])
                        else:
                            images_v2 = []
                        info[key]['images_v2'] = images_v2
                except Exception as ex:
                    log_service.error(f"解析异常：{str(ex)}")
                    continue

                if item['crawlRuleType'] == 3:
                    try:
                        arr = re.search(item['crawlRule'], html)
                    except:
                        arr = ''

                    if not arr:
                        str1 = ''
                    else:
                        str1 = arr.group()

                if item['crawlRuleType'] == 4:
                    begin = html.find(item['startFlag'])
                    end = html.rfind(item['endFlag'])
                    str1 = html[begin:end]
                    if item['analysisType'] == 1:
                        soup = BeautifulSoup(str1, 'html.parser')
                        str1 = soup.get_text()

                if item['crawlRuleType'] not in [5, 6] and item['analysisType'] == 1:
                    if str1:
                        str1 = str1.strip()
                        str1 = str1.strip("/n")

                    if 'columnRegex' in item and item['columnRegex'] and str1:
                        arr = re.findall(f"{item['columnRegex']}", str1)
                        str1 = arr[0] if arr else ''

                if item['columnKey'] == 'public_time':
                    if not str1:
                        str1 = task_info['publicTime'] + " 00:00:00" if "publicTime" in task_info else "2000-01-01 00:00:00"
                    tmp_arr = CheckAnalysis.deal_format_date(str1)
                    str1 = tmp_arr[0]
                    if tmp_arr[1] == 2:
                        false_num += 1

                info[key][item['columnKey']] = str1

                if len(str1) == 0:
                    false_num += 1
                    info[key][item['columnKey']] = item['columnDefault']

                # 处理是否符合关键词
                if item['columnKey'] == 'title' and site_info['sourceCategory'] == "dc_chanquan":
                    info[key]['status'] = CheckAnalysis.checkWords(task_info['siteListId'], str1)

            if files_rules:     # 附件处理
                info[key]["files_v2"] = files_v2
                info[key]["files_v2_hrefs"] = files_v2_hrefs

            curr_success = float('%.2f' % ((item_len - false_num) / item_len))
            if curr_success >= success_max:
                rule_id = key
                success_max = curr_success

        if success_max == 0.00:
            analysis_status = 2
        elif success_max == 1.00:
            analysis_status = 1
        else:
            analysis_status = 3

        return {'info': info[rule_id], 'analysis_status': analysis_status, 'rule_id': rule_id}

    # 处理时间格式
    @staticmethod
    def deal_format_date(date_str):
        if date_str == "刚刚":
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), 1]
        if re.findall("秒前", date_str):
            num = re.search("\\d{1,2}", date_str).group()
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - float(int(num) * 1))), 1]
        if re.findall("分钟前", date_str):
            num = re.search("\\d{1,2}", date_str).group()
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - float(int(num) * 60))), 1]
        if re.findall("小时前", date_str):
            num = re.search("\\d{1,2}", date_str).group()
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - float(int(num) * 3600))), 1]
        if re.findall("昨天", date_str):
            day = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - 86400))
            date_time = day
            return [date_time, 1]
        if re.findall("前天", date_str):
            day = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - 2 * 86400))
            date_time = day
            return [date_time, 1]
        if re.findall("天前", date_str):
            num = re.search("\\d{1,2}", date_str).group()
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - float(int(num) * 86400))), 1]

        date_str = date_str.strip()
        # date_str = date_str.replace('- ', '-')
        date_str = date_str.replace(', ', '-')
        date_str = date_str.replace('年', '-')
        date_str = date_str.replace('月', '-')
        date_str = date_str.replace(' 日', '')
        date_str = date_str.replace('日', '')
        date_str = date_str.replace('/n', '')
        date_str = date_str.replace('/', '-')
        date_str = date_str.replace('\\', '-')
        date_str = date_str.replace('.', '-')
        date_str = date_str.replace('时', ':')
        date_str = date_str.replace('分', ':')
        date_str = date_str.replace('秒', '')
        date_str = date_str.replace('\n', '')
        date_str = date_str.replace('- ', '-')
        date_str = date_str.replace(' -', '-')

        # date_str = date_str[0:10]

        if not date_str:
            format_date = '1970-01-01 00:00:00'
            return [format_date, 2]

        try:
            date_str = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%Y-%m-%d %H:%M")
            format_date = date_str.strftime("%Y-%m-%d %H:%M") + ":00"
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%m-%d")
            now = datetime.datetime.now()
            year = now.year
            format_date = year + "-" + date_str.strftime("%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%m-%d-%Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%Y-%m-%d")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%B %d,%Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%A, %B %d, %Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%A, %b %d, %Y %I:%M%p")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%Y%m-%d%H:%M:%S")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%Y%m-%d")
            format_date = date_str.strftime("%Y-%m-%d")
            h = random.randint(10, 23)
            m = random.randint(10, 59)
            s = random.randint(10, 59)
            format_date = format_date + f" {h}:{m}:{s}"
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%A, %d %B %Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%B- %d-%Y %H:%M")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%H:%M %Y-%m-%d")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%d %b-%Y %H:%M")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%B %d,%Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%d-%b-%Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%a %d %b %Y // %H:%M UTC")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%m-%d %H:%M")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            current_year = datetime.now().year
            format_date = (str(current_year) + format_date[4:])
            return [format_date, 1]
        except:
            pass

        return ['1970-01-01 00:00:00', 2]

    # 处理产权关键词
    @staticmethod
    def checkWords(site_id, title):
        session = mysql.create_session()
        try:
            ret = session.query(DCIntellectualProperty).filter(DCIntellectualProperty.siteListId == site_id).first()
            dc_info = query2dict(ret)
            session.close()
            keywords = dc_info['keyWords']
            filter_words = dc_info['filterWords']
            filter_arr = filter_words.split(",") if filter_words else []
            keyword_arr = keywords.split(",") if keywords else []
            for filter_word in filter_arr:
                if filter_word in title:
                    # if re.findall(filter_word, title):
                    return 2

            for keyword in keyword_arr:
                if keyword in title:
                    # if re.findall(keyword, title):
                    return 1

        except:
            if session is not None:
                session.close()
            log_service1 = LoggingService('dc_analysis_html.log')
            log_service1.dcPullLog(f"获取产权关键词失败，对应siteId：【{site_id}】，失败原因：{traceback.format_exc()}")
            return 2

        return 2

    @staticmethod
    def get_site_list(siteListId) -> dict:
        """
        get site list
        :param siteListId:
        :return:
        """
        _mysql = MySQLService()
        _session = None
        try:
            # taskId = 2792737
            _session = _mysql.create_session()
            site_list: DCSiteList = _session.get(DCSiteList, siteListId)
            site_list: dict = site_list.to_dict() if site_list else {}
            return site_list
        except Exception as ex:
            print(ex)
            return None
        finally:
            _session.close()
