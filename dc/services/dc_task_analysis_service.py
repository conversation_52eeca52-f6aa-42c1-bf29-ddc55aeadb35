import re
import time
import sys
import os
import json
import traceback
import requests
from bson import ObjectId
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from sqlalchemy.orm import load_only
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.conf.settings import get_settings
from dc.services.mongo_service import MongoService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.logging_service import LoggingService
from dc.models.model import MPCompanyAntitrust, MPCompanyInfo, DCSiteListItem, DCSiteListRule
from dc.services.check_analysis import CheckAnalysis
from dc.conf.defines import *
from dc.services.kafka_service import KafkaService
from dc.services.content_filter_service import ContentFilterService

"""
[功能]：数据采集-文章分析服务
[作者]：lws
[日期]：2023-07-17
"""


class DcTaskAnalysisService:
    mysql = MySQLService()
    mgs = MongoService()
    ess = ElasticsearchService()
    redisService = RedisService('redis')
    client1 = redisService.get_client()

    redisService1 = RedisService('redis', db=6)
    client2 = redisService1.get_client()

    log_service = LoggingService('dc_analysis_html.log')
    log_service1 = LoggingService('dc_analysis_ai.log')
    klogger = LoggingService('dc_opinion_task_kafka.log', formatter='string')

    kafka = KafkaService()

    redis_prefix = 'laravel_database_'
    site_list_hash = f'{redis_prefix}dc-site-list-hash'
    redis_analyse_key = f'{redis_prefix}dc-site-task-analyse'
    redis_weight_list = 'dc_article_weight'
    save_path = "/uploads/chanquan/"

    def __init__(self) -> None:
        super().__init__()

    # 写入主任务队列
    def handel(self):
        while True:
            info = self.client1.lpop(self.redis_analyse_key)
            if not info:
                self.log_service.dcPullLog('无数据，休眠10秒')
                time.sleep(10)
                continue

            task_info = json.loads(info)
            # task_info = {'siteListId': 384, 'mainTaskId': 21472,
            #              'url': 'https://news.pku.edu.cn/xwzh/81a4214c6e3141959f3a1bcd68e3f48a.htm', 'id': 632075,
            #              'keyword': ''}
            # mongo_info = {'body': ''}
            task_id = task_info['id']

            if task_info['taskStatus'] != 1:
                self.up_site_task('', '', task_id, 2)
                self.log_service.dcPullLog(f"子任务id【{task_id}】分析状态异常")
                continue

            try:
                m_client = self.mgs.get_collection('site-task')
                mongo_info = m_client.find_one({'_id': ObjectId(task_info['mongoKey'])})
            except:
                self.up_site_task('', '', task_id, 2)
                self.log_service.dcPullLog(f"子任务id【{task_id}】mongo链接异常，读取数据失败")

            if not mongo_info or not mongo_info['body']:
                self.up_site_task('', '', task_id, 2)
                self.log_service.dcPullLog(f"子任务id【{task_id}】mongo数据异常，对应mongoKey【{task_info['mongoKey']}】")
                continue

            if mongo_info['code'] != 200:
                self.up_site_task('', '', task_id, 2)
                self.log_service.dcPullLog(f"子任务id【{task_id}】mongo数据异常,状态码非200，对应mongoKey【{task_info['mongoKey']}】")
                continue

            self.analysis(task_info, mongo_info)

    # 获取页面响应数据
    def analysis(self,  driver, site_info, task_info: dict):
        task_id = task_info['id']
        # 浏览器处理逻辑
        if site_info['detailDcMethod'] == 2 or site_info['detailDcMethod'] == 4:
            ym = time.strftime('%Y%m', time.localtime(time.time())) + "/"
            # 火狐或谷歌模式
            try:
                driver.get(task_info['url'])
            except Exception as ex:
                # self.up_site_task('', '', task_id, 2)
                self.log_service.error(f"任务【{task_info['id']}】打开网页失败, 失败原因：{str(ex)}")
                return False

            # 快照
            if site_info['isQuickShort'] == 1:
                self.quick_short(driver, site_info['analysisMethod'], task_info, ym)

            # 解析对应数据
            analysis_ret = CheckAnalysis.browser_analysis(driver, task_info, site_info, driver.page_source)

        else:
            # 解析对应数据
            from dc.common.dc_helper import DcHelper
            html_result = DcHelper.get_html_by_requests(site_info['url'])
            analysis_ret = CheckAnalysis.ordinary_analysis(site_info['ruleItem'], html_result['text'], task_info)

        if analysis_ret['analysis_status'] == 2:
            self.up_site_task('', '', task_id, 2)
            self.log_service.dcPullLog(f"子任务【{task_id}】解析数据失败  status: {analysis_ret['analysis_status']}")
            return False

        # 如果是编辑发稿的话,需要先检查文章是否有关键词内容, 如果没有 直接可以退出
        # cf = ContentFilterService()
        # if site_info['sourceCategory'] == "dc_bianjifagao" and not cf.word_filter(cf.words, analysis_ret['info']['text']):
        #     if site_info['sourceName'] != "巨潮资讯-深沪京公告":
                # 退出前更新状态
                # self.up_site_task('', site_info['sourceCategory'], task_id, 1, analysis_ret['rule_id'])
                # return False

        # print(analysis_ret)

        es_id = self.save_es(analysis_ret['info'], analysis_ret['rule_id'], task_info, site_info)
        if es_id:
            analysis_status = analysis_ret['analysis_status']
            rule_id = analysis_ret['rule_id']
            self.up_site_task(es_id, site_info['sourceCategory'], task_id, analysis_status, rule_id)
        else:
            self.up_site_task('', '', task_id, 2)
            self.log_service.dcPullLog(f"子任务【{task_id}】写入es失败")

        # 处理反垄断数据
        # if site_id in [377, 378, 379, 380]:
        #     self.saveCompanyAntitrust(analysis_ret['info'], es_id, site_info, task_info, driver)


    # 更新任务状态
    def up_site_task(self, es_id, es_index, task_id, analysis_status=0, rule_id=0):
        try:
            session = self.mysql.create_session()
            up_sql = f"update DC_SiteTask set esIndex = '{es_index}', ruleId = {rule_id}, esId = '{es_id}', " \
                     f"analysisStatus = {analysis_status} where id = {task_id}"
            session.execute(up_sql)
            session.commit()
            session.close()
        except:
            self.log_service.error(f"子任务【{task_id}】更新执行状态失败，失败原因：{traceback.format_exc()}")

    # 生成快照
    def quick_short(self, driver, isFirefox, task_info, ym):
        pic_name = self.save_path + ym + CheckAnalysis.getMd5(task_info['url'])
        # pic_name = "a.png"
        if isFirefox:
            driver.get_screenshot_as_file("/data" + pic_name + ".png")
        else:
            # driver.set_window_size(width=1005, height=500, windowHandle='current')
            # 处理保存快照,返回网页的高度的js代码
            js_height = "return document.body.clientHeight"
            try:
                k = 1
                height = driver.execute_script(js_height)
                while True:
                    if k * 500 < height:
                        js_move = "window.scrollTo(0,{})".format(k * 500)
                        driver.execute_script(js_move)
                        time.sleep(0.2)
                        height = driver.execute_script(js_height)
                        k += 1
                    else:
                        break

                scroll_width = driver.execute_script('return document.body.parentNode.scrollWidth')
                scroll_height = driver.execute_script('return document.body.parentNode.scrollHeight')
                driver.set_window_size(scroll_width, scroll_height)
                driver.get_screenshot_as_file("/data" + pic_name + ".png")
                # driver.get_screenshot_as_file("D:\\a.png")
            except Exception as ex:
                self.log_service.error(f"子任务【{task_info['id']}】生成快照失败, 失败原因：{str(ex)}")

    # 反垄断数据
    def saveCompanyAntitrust(self, info, esId, site_info, task_info, driver):
        mysql1 = MySQLService('mysql_company')
        session1 = mysql1.create_session()
        if info['siteListId'] == 377:
            text1 = info['text']
            arr = text1.split("/n")
            for tmp1 in arr:
                arr1 = tmp1.split("\n")
                if len(arr1) < 2:
                    arr1 = tmp1.split()
                    if len(arr1) < 2:
                        continue

                if arr1[1] == "案件名称":
                    continue

                deal_arr = self.dealConclusionDate(arr1[3], info['pinjieyong'])
                df = {'title': arr1[1],
                      'source': "反垄断局",
                      'pubTime': "",
                      'conclusionDate': deal_arr[0].strip(),
                      'url': info['dc_detail_url'],
                      'companyInfo': arr1[2],
                      'isUnconditional': 1,
                      'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                      'esId': esId,
                      'year': deal_arr[1],
                      'month': deal_arr[2],
                      'esIndex': site_info['sourceCategory']}
                dch = MPCompanyAntitrust(**df)
                session1.add(dch)
            session1.commit()
        elif info['siteListId'] == 378:
            # chrome_options1 = Options()
            # chrome_options1.add_argument('--headless')
            # chrome_options1.add_argument('--no-sandbox')
            # chrome_options1.add_argument('--disable-gpu')
            # driver3 = webdriver.Chrome(options=chrome_options1)

            info['dc_detail_url'] = f"{task_info['url']}"
            info['dc_site_name'] = site_info['siteName']
            info['mainTaskId'] = task_info['mainTaskId']
            info['siteListId'] = task_info['siteListId']
            info['taskId'] = task_info['id']
            info['analysis_time'] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            info['version'] = 2

            # driver.get(task_info['url'])
            xpath = driver.find_elements('xpath', "//div[@class='zt_xilan_07']//table//tr[1]/following-sibling::tr")
            # driver.implicitly_wait(10)

            for x in xpath:
                td1 = x.find_element('xpath', './td[1]')
                if td1.text == '序号':
                    continue
                td2 = x.find_element('xpath', './td[2]')
                td3 = x.find_element('xpath', './td[3]')
                td4 = x.find_element('xpath', './td[4]')
                conclusionDate2 = td4.text.strip()

                info['title'] = td2.text
                info['content'] = td3.text
                info['public_time'] = CheckAnalysis.deal_format_date(conclusionDate2)[0]

                es = self.ess.get_connect()
                res = es.index(index=site_info['sourceCategory'], body=info)
                es.close()

                df = {'title': td2.text,
                      'source': "反垄断执法二司",
                      'pubTime': "",
                      'conclusionDate': conclusionDate2,
                      'url': task_info['url'],
                      'companyInfo': td3.text,
                      'isUnconditional': 1,
                      'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                      'esId': res['_id'] if res['_id'] else '',
                      'year': conclusionDate2[0:4],
                      'month': conclusionDate2[5:].split('月')[0].strip('0'),
                      'esIndex': site_info['sourceCategory']}
                dch = MPCompanyAntitrust(**df)
                session1.add(dch)
            session1.commit()
            # driver3.quit()
        else:
            arr_time = info['public_time'].split("-")
            df = {'title': info['title'],
                  'source': "反垄断局" if info['siteListId'] == 379 else "反垄断执法二司",
                  'pubTime': info['public_time'],
                  'conclusionDate': "",
                  'url': info['dc_detail_url'],
                  'companyInfo': self.getCompanyName(info['content'], self.session1),
                  'isUnconditional': 2,
                  'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                  'esId': esId,
                  'year': arr_time[0],
                  'month': arr_time[1].strip("0"),
                  'esIndex': site_info['sourceCategory']}
            dch = MPCompanyAntitrust(**df)
            session1.add(dch)
            session1.commit()
        session1.close()

    # 获取企业名称
    def getCompanyName(self, body, session1):
        # mysql1 = MySQLService('mysql_company')
        # session1 = mysql1.create_session()

        companyNames = self.client1.get("company_name_list")
        if companyNames:
            companyNames = json.loads(companyNames)
        else:
            companyInfos = session1.query(MPCompanyInfo).options(load_only(MPCompanyInfo.id, MPCompanyInfo.name)).all()
            for companyInfo in companyInfos:
                tmp = companyInfo.__dict__
                companyNames.append(tmp['name'])
            self.client1.set("company_name_list", json.dumps(companyNames), 86400)

        arr1 = []
        for companyName in companyNames:
            arr = re.search(companyName, body)
            if arr:
                arr1.append(arr[0])

        return '、'.join(arr1) if arr1 else ''

    # 处理反垄断日期
    def dealConclusionDate(self, date_str, year):
        date_str = date_str.strip()
        date_str = date_str.replace('年', '-')
        date_str = date_str.replace('月', '-')
        date_str = date_str.replace('日', '')

        try:
            if datetime.strptime(date_str, "%Y-%m-%d"):
                date_str1 = datetime.strptime(date_str, "%Y-%m-%d")
                format_date = datetime.strftime(date_str1, "%Y年%#m月%#d日")
                year1 = date_str1.strftime("%Y")
                month1 = date_str1.strftime("%#m")
        except:
            if datetime.strptime(date_str, "%m-%d"):
                date_str1 = datetime.strptime(date_str, "%m-%d")
                format_date1 = datetime.strftime(date_str1, "%#m月%#d日")
                if re.search("\\d{4}", year):
                    arr = re.search("\\d{4}", year)
                    format_date = str(arr[0]) + "年" + format_date1
                    year1 = arr[0]
                    month1 = date_str1.strftime("%#m")
            else:
                format_date = '1970年1月1日'
                year1 = 1970
                month1 = 1

        return [format_date, year1, month1]

    def delivery_report(self, err, msg):
        try:
            if err is not None:
                # print("Message delivery failed: {}".format(err))
                self.klogger.error("Message delivery failed: {}".format(err))
            else:
                data = {
                    'topic': msg.topic(),
                    'partition': msg.partition(),
                    'offset': msg.offset(),
                    'key': msg.key(),
                    'value': msg.value().decode('utf-8'),
                    'timestamp': msg.timestamp(),
                }
                topic = msg.topic()
                item: dict = json.loads(data['value'])
                esid = item.get(KAFKA_TOPIC_ESID_KDY)
                sourceCategory = item.get('_sourceCategory', '')
                self.klogger.info(f"kafka topic: {topic} esid: {esid} sourceCategory:{sourceCategory}")
        except Exception as ex:
            print(f"delivery_report: {traceback.format_exc()}")

    # 保存数据到es
    def save_es(self, info, rule_id, task_info, site_info):
        if site_info.get('tags') is not None:
            tags = site_info.get('tags').split(',')
        else:
            tags = ''
        info['tags'] = tags
        info['dc_detail_url'] = f"{task_info['url']}"
        info['dc_site_name'] = f"{site_info['siteName']}"
        info['mainTaskId'] = task_info['mainTaskId']
        info['siteListId'] = task_info['siteListId']
        info['taskId'] = task_info['id']
        info['analysis_time'] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        info['version'] = 2
        info['ruleId'] = rule_id
        info['publicTime'] = info['publicTime'] if 'publicTime' in info else ''
        info['coverImg'] = info['coverImg'] if 'coverImg' in info else ''
        # info['search_keyword'] = [task_info['keyword']] if task_info['keyword'] else []
        if site_info['sourceCategory'] == "dc_policy":
            info['area'] = self.getPolicyInfo(info['title'] + site_info['siteName'], 7, 'ner')
            info['industry'] = self.getPolicyInfo(info['title'] + info['text'], 6, 'ner')
            info['informationtype'] = self.getInformationType(info['title']) if self.getInformationType(info['title']) != '其他' else ''

        if site_info['sourceCategory'] == 'dc_yq':
            info['isRepeat'] = 0

        if site_info['sourceCategory'] == "dc_bianjifagao":
            info['isPublished'] = 0  # 发布状态
            info['isPass'] = 0  # pass
            info['isWaiting'] = 0  # 待定
            info['isDeal'] = 0  # 处理状态
            info['publishID'] = 0  # 发布出去的id
            info['createAt'] = info['analysis_time']
            info['content'] = info['text']
            info['_dc_type'] = 1
            # 删除 info字典 中的 text
            info.pop('text') if 'text' in info else None

        try:
            es = self.ess.get_connect()
            is_exists = es.indices.exists(index=site_info['sourceCategory'])
            if not is_exists:
                res = es.index(index=site_info['sourceCategory'], body=info)
                es.close()

                # kafka 数据变更队列
                es_id = res['_id']
                p = self.kafka.get_producer()
                p.produce(KAFKA_TOPIC_DC_ALL,
                          json.dumps({KAFKA_TOPIC_ESID_KDY: es_id, "_sourceCategory": site_info['sourceCategory'], "_loginSite": 1, **info}),
                          key=es_id, callback=self.delivery_report)
                p.flush()

                self.client2.lpush(self.redis_weight_list, json.dumps({'taskId': task_info['id'], 'type': 1}))
                self.client2.close()

                return res['_id']

            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "match": {
                                    "siteListId": task_info['siteListId']
                                }
                            },
                            {
                                "match": {
                                    "dc_detail_url.keyword": task_info['url']
                                }
                            }
                        ]
                    }
                }
            }
            search_res = es.search(index=site_info['sourceCategory'], body=query)
            search_info = search_res['hits']['hits']
            if not search_info:
                res = es.index(index=site_info['sourceCategory'], body=info)
                es.close()

                # kafka 数据变更队列
                es_id = res['_id']
                p = self.kafka.get_producer()
                p.produce(KAFKA_TOPIC_DC_ALL,
                          json.dumps({KAFKA_TOPIC_ESID_KDY: es_id, "_sourceCategory": site_info['sourceCategory'], "_loginSite": 1, **info}),
                          key=es_id, callback=self.delivery_report)
                p.flush()

                self.client2.lpush(self.redis_weight_list, json.dumps({'taskId': task_info['id'], 'type': 1}))
                self.client2.close()

                return res['_id']

            es_id = search_info[0]['_id']
            es_index = search_info[0]['_index']
            update = {
                "doc": info
            }
            es.update(index=es_index, id=es_id, body=update)
            es.close()

            # kafka 数据变更队列
            p = self.kafka.get_producer()
            p.produce(KAFKA_TOPIC_DC_ALL,
                      json.dumps({KAFKA_TOPIC_ESID_KDY: es_id, "_sourceCategory": site_info['sourceCategory'], "_loginSite": 1, **info}),
                      key=es_id, callback=self.delivery_report)
            p.flush()

            self.client2.lpush(self.redis_weight_list, json.dumps({'taskId': task_info['id'], 'type': 1}))
            self.client2.close()

            return es_id

        except:
            self.log_service.error(f"子任务【{task_info['id']}】写入es失败原因：{traceback.format_exc()}")
            return ''

    # 获取区域、行业
    def getPolicyInfo(self, content, model_id, type1):
        info = []
        params = {"model_id": model_id, "data": content, "type": type1}
        if model_id == 7:
            url = get_settings('getArea')
        elif model_id == 6:
            url = get_settings('getIndustry')

        try:
            res = requests.post(url, json=params)
            res.encoding = "UTF-8"
            if res.status_code != 200:
                self.log_service1.dcPullLog("调用ai接口失败")
                return info

            arr = json.loads(res.text)['data']
            for item in arr:
                if item['type'] == 'ORG':
                    info.append('全国')
                else:
                    info.append(item['span'])

            return list(set(info))[0:3] if info else []
        except:
            self.log_service1.dcPullLog("调用ai接口失败")
            return info

    # 获取信息类别
    def getInformationType(self, content):
        info = ''
        params = {"model_id": 4, "data": content, "type": "classification"}
        url = get_settings('getInformationType')
        try:
            res = requests.post(url, json=params)
            res.encoding = "UTF-8"
            if res.status_code != 200:
                self.log_service1.dcPullLog("调用ai接口失败")
                return info

            info = json.loads(res.text)['data']

            return info
        except:
            self.log_service1.dcPullLog("调用ai接口失败")
            return info




    # 获取数据源对应规则
    def get_rule(self, site_id):
        session = self.mysql.create_session()
        rule_res = session.query(DCSiteListRule).filter(DCSiteListRule.siteListId == site_id).all()
        if not rule_res:
            self.log_service.dcPullLog(f'数据源id【{site_id}】无对应规则，初始化任务失败')
            return ''

        rule_ids = []
        for rule_info in rule_res:
            rule = rule_info.to_dict()
            rule_ids.append(rule['id'])

        item_res = session.query(DCSiteListItem).filter(DCSiteListItem.siteRuleId.in_(rule_ids),
                                                        DCSiteListItem.status == 1).all()
        if not item_res:
            self.log_service.dcPullLog(f'数据源id【{site_id}】无对应数据项，初始化任务失败')
            return ''

        item_data = {}
        for rule_id in rule_ids:
            item_data[rule_id] = {}

        for item_info in item_res:
            item = item_info.to_dict()
            item_data[item['siteRuleId']][item['id']] = item

        session.close()

        return json.dumps(item_data)


# handel()

