import json

import pymongo
from pymongo.collection import Collection
from dc.conf.settings import get_settings
from elasticsearch import Elasticsearch, helpers


class ElasticsearchService:
    name = 'default'
    config = {}

    def __init__(self, name='elasticsearch', run_model=None) -> None:
        super().__init__()
        self.name = name
        self.config = get_settings(name, run_model=run_model)

    def get_connect(self) -> Elasticsearch:
        hosts = self.config.get('hosts')
        es = Elasticsearch(hosts=hosts)
        return es

    def upsert(self, index, filter, body):
        """
        update or insert
        调用示例：
            filter = {
                "query": {
                    "terms": {
                        "id": ["6"]
                    }
                },
                'size': 1
            }

            body = {
                "id": "6",
                "title": "王琴6",
                "age": 26
            }
            es = ElasticsearchService()
            result = es.upsert(index=test_index, filter=filter, body=body)

        :param index:
        :param filter:
        :param body:
        :return:
        """
        # 搜索
        # filter = {
        #     "query": {
        #         "terms": {
        #             "id": ["8", "9"]
        #         }
        #     },
        #     'from': 0,  # 从0开始
        #     'size': 20  # 取2个数据。类似mysql中的limit 0, 20。 注：size可以在es.search中指定，也可以在此指定，默认是10
        # }

        es = self.get_connect()
        if not es.indices.exists(index=index):  # 如果不存在创建
            es.indices.create(index=index)

        list = es.search(index=index, body=filter)

        count = list['hits']['total']['value']
        # print(f"count: {count}")

        update_results = []
        if count > 0:
            for row in list.get('hits').get('hits'):
                # print(row['_source'])
                # print(row['_id'])
                # body_update = {
                #     'doc': {
                #         "id": "1",
                #         "title": "李丽丽2",
                #         "age": 28
                #     },
                #     'doc_as_upsert': True
                # }
                id = row['_id']
                body2 = {
                    'doc': body,
                    'doc_as_upsert': True
                }
                result = es.update(index=index, id=id, body=body2)
                update_results.append(result)
        else:
            result = es.index(index=index, doc_type='_doc', body=body)
            update_results.append(result)

        return update_results

    def update_multiple_docs_score(self, docs, key='hotWeight'):
        actions = []
        es = self.get_connect()

        # 构建要更新的文档操作列表
        for doc in docs:
            print(doc, key)
            action = {
                '_op_type': 'update',
                '_index': 'dc_hotarticle',
                '_id': doc['_id'],
                'doc': {
                    key: doc['new_score']
                }
            }
            actions.append(action)

        # 发送请求
        response = helpers.bulk(es, actions=actions)

        # 返回执行结果
        return response

    # # 示例用法
    # documents = [
    #     {'_id': '1', 'new_score': 9.5},
    #     {'_id': '2', 'new_score': 8.7},
    #     {'_id': '3', 'new_score': 7.9}
    # ]
    # result = update_multiple_docs(documents)
    # print(result)

    def update_test(self):
        es = self.get_connect()

        body = {
            "hotWeight": 26
        }
        body2 = {
            'doc': body,
            'doc_as_upsert': True
        }
        result = es.update(index="dc_hotarticle", id="OcyEE4sB2C1yyCgt9HYC", body=body2)
