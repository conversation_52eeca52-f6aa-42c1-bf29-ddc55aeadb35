"""
公众号信息进行公众号关注操作
:author wjh
:date 2022-11-26
"""

import datetime
import json
import random
import time
import traceback

from sqlalchemy import text

from dc.services.gzh_service import GzhService
from dc.models.model import DCGzhArticle, DCGzhInfo, DCGzhTag, DCGzhWechat, DCGzhWechatRelation, DCGzhImport, \
    DCWechatMachine
from dc.services.mysql_service import MySQLService
from dc.services.logging_service import LoggingService
from dc.common.utils import Utils
from dc.common import utils
from dc.services.weixin_service import WeixinService
from dc.conf.settings import settings as test_settings, get_settings

gzh_settings = get_settings('gzh')
base_url = gzh_settings.get('host')

logger = LoggingService(logfile='gzh_guanzhu2.log')
wx = WeixinService()
mysql = MySQLService(name='mysql_ali')
session = mysql.create_session()


def get_gz_list(params: dict):
    """
    获取已关注公众号
    :param params:
    :return:
    """
    try:
        wxid = params.get('wxid')
        wxpid = params.get('wxpid')
        base_url = params.get('base_url')
        params = {
            'wxpid': wxpid,
            'base_url': base_url
        }
        result = wx.req_gzh(params)
        result = Utils.dict_get(result, ['result', wxid], None)
        if result is None:
            result = []
        else:
            result = [x[0] for x in result]
        return result
    except Exception as ex:
        # print(ex.with_traceback())
        return []


def get_gzh_list():
    gzlist = []

    wechats: list[DCGzhWechat] = session.query(DCGzhWechat).filter(DCGzhWechat.status == 1,
                                                                   DCGzhWechat.isUsed == 1).all()
    for wechat in wechats:
        machine = session.query(DCWechatMachine).filter(DCWechatMachine.machineIp == wechat.machineIp,
                                                        DCWechatMachine.status == 1).first()
        if machine is None:
            continue

        print(wechat.machineIp)
        gzl = get_gz_list({
            'wxid': wechat.wxId,
            'wxpid': wechat.machinePid,
            'base_url': wechat.machineIp,
        })

        gzlist.append(
            {'mid': machine.id, 'count': len(gzl), 'wechat': wechat, 'machineIp': wechat.machineIp, 'gzh': gzl})
        print(gzlist)

    # zz = len(gzlist)
    return gzlist


def callback(**kwargs):
    gzh: DCGzhInfo = kwargs.get('data')
    logger.info(f"{gzh.gzhId} {gzh.nickName}")
    print(gzh.gzhId)

    kv = 0
    max_kv = 3
    is_gz = False
    gzlist = get_gzh_list()
    for gz in gzlist:  # 已经关注部分，添加关注关系
        wechat: DCGzhWechat = gz.get('wechat')
        if gzh.gzhId in gz['gzh']:
            set_relation(gzh.gzhId, wechat.wxId)
            logger.info(f"{wechat.wxId} {wechat.nickName} -> 已关注 {gzh.gzhId} {gzh.nickName}")
            kv += 1

    if kv >= max_kv:  # 如果关注已超过要求数量，不再进行关注
        logger.info(f"{gzh.gzhId} {gzh.nickName} kv > {max_kv}")

    else:  # 需要继续关注

        gzlist2 = sorted(gzlist, key=lambda x: x.get('count', 0))
        last_kv = max_kv - kv
        logger.info(f"last_kv {last_kv} max_kv {max_kv} kv {kv}")

        gzlist3 = gzlist2[:last_kv]
        logger.info(f"gzlist3 len {len(gzlist3)} ")

        for item in gzlist3:
            wechat: DCGzhWechat = item.get('wechat')
            count = item['count']
            logger.info(f"{wechat.wxId} count:{count} ")
            logger.info(f"{wechat.wxId} {wechat.nickName} -> 关注 {gzh.gzhId} {gzh.nickName}")
            params_gz = {
                'gzhid': gzh.gzhId,
                'wxpid': wechat.machinePid,
                'base_url': wechat.machineIp
            }
            result = wx.req_gzh_gz(params_gz)
            set_relation(gzh.gzhId, wechat.wxId)

            item['count'] += 1
            time.sleep(2)

            is_gz = True

    # 更新数据
    gzh: DCGzhInfo = session.query(DCGzhInfo).filter(DCGzhInfo.gzhId == gzh.gzhId).first()
    gzh.isSubscribe = 1
    gzh.syncStatus = 1
    gzh.syncAt = Utils.showDateTime()
    session.add(gzh)
    session.commit()
    logger.info(f"{gzh.gzhId} update")

    if is_gz:
        logger.info(f"{gzh.gzhId} is_gz sleep 20")
        time.sleep(20)


def set_relation(gzhId, wxId):
    relation: DCGzhWechatRelation = session.query(DCGzhWechatRelation).filter(DCGzhWechatRelation.wxId == wxId,
                                                                              DCGzhWechatRelation.gzhId == gzhId).first()
    if relation is None:
        logger.info(f"{gzhId} {wxId} set relation")
        relation = DCGzhWechatRelation()
        relation.gzhId = gzhId
        relation.wxId = wxId
        session.add(relation)
        session.commit()
    else:
        logger.info(f"{gzhId} {wxId} exist ignore")

    return True


def items_map(**kwargs):
    callback = kwargs.get('callback')

    session = mysql.create_session()
    where = f"status=1 and syncStatus=0"
    page = 0
    pagesize = 20
    no = 0
    while True:
        try:
            offset = pagesize * page
            data: list[DCGzhInfo] = session.query(DCGzhInfo).where(text(where)).order_by(DCGzhInfo.id).offset(
                offset).limit(pagesize).all()
            if len(data) == 0:
                logger.info(f'page: {page} offset: {offset} --- break')
                break

            for gzh in data:
                logger.info(f'page: {page} offset: {offset} no: {no}')
                callback(data=gzh, params={'page': page, 'offset': offset, 'no': no})
                session.commit()
                no += 1

        except Exception as ex:
            print(ex.args)
            return False

        finally:
            page += 1

    session.close()


# items_map(callback=callback)



