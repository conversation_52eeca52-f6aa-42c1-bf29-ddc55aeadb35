import datetime
import json
import os
import re
import sys
import time
import traceback

import requests

from dc.common.utils import Utils
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.models.model import DCGzhInfo
from dc.models.model import DCGzhTag
from dc.models.model import DCGzhWechat
from dc.models.model import DCGzhArticle
from dc.models.model import DCGzhWechatRelation
from dc.models.model import DCWechatMachine
from dc.services.weixin_service_v2 import WeixinServiceV2
from dc.conf.settings import get_settings
from sqlalchemy import func
import dc.common.func as F
from dc.services.redis_service import RedisService
from dc.common.alchemy import query2dict


class GzhServiceV2:

    @staticmethod
    def follow_gzh_with_wechat(wx_id='', gzh_id='', tag='', gzh_data='', run_mode=None):
        """
        用某个微信号关注公众号
         :param gzh_data:
         :param tag:
         :param gzh_id:
         :param wx_id:
         :param run_mode:
         :return:
        """
        isSubscribe = 0
        mysql_service = MySQLService(run_model=run_mode)
        session = mysql_service.create_session()
        g: DCGzhInfo = session.query(DCGzhInfo).where(DCGzhInfo.gzhId == gzh_id).first()

        logger = LoggingService(logfile='dc_gzh_push.log')
        # 如果没有传gzh_data 并且 查询不到公众号的信息 直接返回
        if gzh_data == '' and g is None:
            return False

        wxids = str.split(wx_id, ',')
        tags = str.split(tag, ',')
        res = []
        # 尝试关注公众号
        for w in wxids:
            wechat_service = WeixinServiceV2(wx_id=w)
            if w == '':
                w = wechat_service.wechat.wxId
            if not GzhServiceV2.check_wechat_followed(wechat_service, gzh_id):
                r = wechat_service.req_gzh_gz({"gzhid": gzh_id})
                r['wxid'] = w
                # 关注 如果不成功 不写关系
                if r['status'] != 200:
                    logger.info('result err :' + f'{r}')
                    continue
                isSubscribe = 1
                res.append(r)
            else:
                isSubscribe = 1
            # 关注成功添加结果 并看是否已经有关系

            h = session.query(DCGzhWechatRelation).where(DCGzhWechatRelation.wxId == w,
                                                         DCGzhWechatRelation.gzhId == gzh_id).first()
            if h is None:
                session.add(DCGzhWechatRelation(wxId=w, gzhId=gzh_id))

        # 关注公众号是否有结果
        if len(res) > 0:
            isSubscribe = 1

        if isSubscribe == 0:
            return False

        # 如果存在公众号的信息 并且传了新的data 更新公众号的信息
        if g is None:
            if gzh_data != '':
                g_dict = json.loads(gzh_data)
                g_extra = json.loads(g_dict['externalInfo'])
                # 查看是否有关注成功 确认已经订阅的状态
                session.add(DCGzhInfo(gzhId=gzh_id, gzhNo=g_dict['aliasName'], nickName=g_dict['nickName'],
                                      principalPart=g_extra['VerifySource']['Description'], status=1,
                                      isSubscribe=isSubscribe))

        # 如果没有公众号的信息 并且传了gzh_data 写入中号的数据
        else:
            # 查看是否有关注成功 确认已经订阅的状态
            if gzh_data != '':
                g_dict = json.loads(gzh_data)
                g_extra = json.loads(g_dict['externalInfo'])
                g.gzhNo = g_dict['aliasName']
                g.nickName = g_dict['nickName']
                g.principalPart = g_extra['RegisterSource']['RegisterBody']
                g.isSubscribe = isSubscribe
                session.add(g)
            else:
                g.isSubscribe = isSubscribe
                session.add(g)

        # 更新tag信息
        if tag != '':
            for t in tags:
                ht = session.query(DCGzhTag).where(DCGzhTag.gzhId == gzh_id, DCGzhTag.tagName == t).first()
                if ht is None:
                    r = DCGzhTag(gzhId=gzh_id, tagName=t)
                    session.add(r)
        session.commit()
        session.close()
        if isSubscribe == 1 and res == []:
            return True
        return res

    @staticmethod
    def gzh_article_count(gzhid) -> int:
        """
         更新公众号文章数量
         :param gzhid: 公众号id
         :return:
        """
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        count = -1
        gzh: DCGzhInfo = session.query(DCGzhInfo).where(DCGzhInfo.gzhId == gzhid).first()
        if gzh is None:
            return count

        count = session.query(DCGzhArticle).filter(DCGzhArticle.gzhId == gzhid, DCGzhArticle.status == 1).count()
        gzh.crawlNum = count
        session.add(gzh)
        session.commit()
        session.close()
        return count

    @staticmethod
    def init_gzh_pull(id) -> bool:
        """
         重新拉取公众号文章
         :param id: id
         :return:
        """
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        gzh: DCGzhInfo = session.query(DCGzhInfo).where(DCGzhInfo.id == id).first()
        print(gzh)
        if gzh is None:
            return False

        gzh.pullStatus = 0
        gzh.pullResult = None
        gzh.pullAt = None
        session.add(gzh)
        session.commit()
        session.close()
        return True

    @staticmethod
    def unfollow_gzh(wx_id='', gzh_id=''):
        """
       取消关注
        :param gzh_id:
        :param wx_id:
        :return:
       """
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        wechat_service = WeixinServiceV2(wx_id=wx_id)
        r = wechat_service.req_gzh_qxgz({"gzhid": gzh_id})
        if r['status'] != 200:
            return r
        session.query(DCGzhWechatRelation).where(DCGzhWechatRelation.wxId == wx_id,
                                                 DCGzhWechatRelation.gzhId == gzh_id).delete()
        h: DCGzhInfo = session.query(DCGzhInfo).where(DCGzhInfo.gzhId == gzh_id).first()
        if h.isSubscribe == 1:
            h.isSubscribe = 0
            session.add(h)
        session.commit()
        session.close()
        return r

    @staticmethod
    def req_gzh_search(wx_id='', keyword=''):
        """
        搜索公众号
        :param wx_id:
        :return:
        """
        wechat_service = WeixinServiceV2(wx_id=wx_id)
        search = {"keyword": keyword}
        r = wechat_service.req_gzh_search(search)
        return r

    @staticmethod
    def req_gzh_info(wx_id='', gzh_id=''):
        """
        公众号详情
        :param gzh_id:
        :param wx_id:
        :return:
        """
        wechat_service = WeixinServiceV2(wx_id=wx_id)
        r = wechat_service.req_gzh_info({"gzhid": gzh_id})
        return r

    @staticmethod
    def get_req_login(wx_id=''):
        """
        初始化获取微信详情
        :return:
        """
        wechat_service = WeixinServiceV2(wx_id=wx_id)
        r = wechat_service.req_login({})
        if r['status'] != 200:
            print("获取登录信息错误" + wx_id)
            return []
        login_info: dict = r.get('result')
        for i in login_info:
            if i['wxid'] == wx_id:
                wechat_service.update_wechat_info(i)

        return r

    @staticmethod
    def check_wechat_by_config():
        """
        根据配置检查相应的服务器上登录的微信 ,更新或插入微信的当前数据
        :return:
        """
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        try:
            res = []
            c: [DCWechatMachine] = session.query(DCWechatMachine).where(DCWechatMachine.status == 1).all()
            hosts = [v.machineIp for v in c]

            ws = WeixinServiceV2()
            # 获取列表里的所有微信信息
            allwx: list[DCGzhWechat] = session.query(DCGzhWechat).all()
            wx_dict = F.list_to_dict_by_key(allwx, 'wxId')
            updated = {}
            for v in hosts:
                ws.base_url = v
                r = ws.req_login({})
                for wx in r['result']:
                    # 如果是未登录的pid  直接过滤掉
                    if wx.get('wxid') is None:
                        continue
                    # h: DCGzhWechat = session.query(DCGzhWechat).where(DCGzhWechat.wxId == wx['wxid']).first()
                    h: DCGzhWechat = wx_dict.get(wx['wxid'])
                    # 如果数据库里有这个数据
                    if h is not None:
                        # update
                        print("|" * 20, h, wx)
                        h.wxId = wx['wxid']
                        if wx['login'] != 0:
                            h.nickName = wx.get('nickname', '')

                        h.wxNo = wx['wxh']
                        h.isUsed = 1 if int(wx['login']) > 0 else 0
                        h.machinePid = wx['wxpid']
                        h.lastOnlineTime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        h.machineIp = v
                        session.add(h)
                        res.append(h.to_dict())

                        updated[wx['wxid']] = 1

                    # 如果数据里没有这个数据 则新增
                    else:
                        print("-" * 20, h, wx)
                        # create
                        wechat = DCGzhWechat(nickName=wx.get('nickname', ''), wxId=wx['wxid'], machineIp=v,
                                             machinePid=wx['wxpid'], wxNo=wx['wxh'],
                                             lastOnlineTime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                             isUsed=1 if int(wx['login']) > 0 else 0,
                                             status=1)
                        session.add(wechat)
                        res.append(wechat.to_dict())

            # 最后将没有使用到的微信 设置成没有启用的状态 isUsed=0
            for v in allwx:
                if updated.get(v.to_dict()['wxId']) is None:
                    v.isUsed = 0
                    session.add(v)
            session.commit()
            session.close()
        except Exception as ex:
            session.close()
            logger = LoggingService(logfile='dc_gzh_push.log')
            logger.error(f"执行失败，失败原因：{str(ex)}")
        return res

    @staticmethod
    def get_machine_list() -> list[dict]:
        """ 获取所有的机器 """
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        res = session.query(DCWechatMachine).all()
        r = []
        for i in res:
            r.append(i.to_dict())
        return r

    @staticmethod
    def start_wechat(machine_ip):
        """
        开启一个微信
        返回一个pid
        :return:
        """
        ws = WeixinServiceV2()

        return ws.req_start_wechat({"machine_ip": machine_ip})

    @staticmethod
    def get_qr_code(machine_ip):
        """
        获取登录的二维码
        :return:
        """
        ws = WeixinServiceV2()
        return ws.req_qr_code({"machine_ip": machine_ip})

    @staticmethod
    def alarm():
        """
        报个警
        :return:
        """

    @staticmethod
    def wechat_push(parmas: list[dict], logger=None):
        redis = RedisService()
        rc = redis.get_client()
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        logger = logger if logger is not None else LoggingService(logfile='dc_gzh_push.log')
        wechatService = WeixinServiceV2()
        if len(parmas) == 0:
            logger.info("wechat_push get params empty")
            return {'data_from': '', 'count': 0}

        w = parmas[0].get('wxid', None)
        ghid = parmas[0]['ghid']
        logger.info(f"wechat_push wxid:{w} | ghid:{ghid}")
        wechat: DCGzhWechat = session.query(DCGzhWechat).where(DCGzhWechat.wxId == w, DCGzhWechat.isUsed == 1).first()
        if wechat is None:
            logger.info(f"wechat_push wxid:{w} is not used")
            # return {'data_from': '', 'count': 0}

        gh: DCGzhInfo = session.query(DCGzhInfo).where(DCGzhInfo.gzhId == ghid, DCGzhInfo.status == 1).first()
        if gh is None:
            logger.info(f"wechat_push ghid:{w} is not used")
            return {'data_from': '', 'count': 0}

        for v in parmas:
            print('-' * 50, v)
            # 增加redis并发验证
            if rc.set(name=f"article:{wechatService.calc_url_md5(v['url'])}", value=1, nx=True, ex=5) is None:
                continue
            h = session.query(DCGzhArticle).where(DCGzhArticle.url_md5 == wechatService.calc_url_md5(v['url'])).first()
            if h is not None:
                logger.info("wechat_push had a repeat message " + f'{h}')
                continue
            session.add(DCGzhArticle(
                wxId=v.get('wxid', None),
                gzhId=v['ghid'],
                title=v['title'],
                url=v['url'],
                cover=v['cover'],
                wx_create_time=v['insert_time'],
                wx_pub_time=v['pub_time'],
                raw=f'{v}',
                status=1,
                dataSource=2,
                createAt=Utils.showDateTime(),
                url_md5=wechatService.calc_url_md5(v['url'])
            ))
        session.commit()
        session.close()
        if ghid != '':
            GzhServiceV2.gzh_article_count(ghid)
        return {'data_from': ghid, 'count': len(parmas)}

    @staticmethod
    def check_wechat_followed(wx: WeixinServiceV2, gh_id):
        if gh_id == '':
            return False
        p = {
            # 'wxpid': wx.pid,
            'base_url': wx.base_url
        }
        list = wx.req_gzh(params=p)
        # result = list['result'][wx.wechat.wxId]
        result = list['result']
        result = [x[0] for x in result]
        if gh_id in result:
            return True
        else:
            return False

    def search_and_follow(self, name, tag, wxid='', run_mode=None):
        """
        搜索并关注
        name :  公众号名字
        tag : 公众号标签
        """
        r = self.req_gzh_search(wxid, name)
        if r.get('status') != 200:
            print('status is wrong ' + name)
            return
        all_data = r['result']['result']['data']
        if all_data is None:
            print("get empty infos " + name)
            time.sleep(10)
            return self.search_and_follow(name, tag, wxid=wxid, run_mode=run_mode)
        print(all_data[0])
        item = all_data[0]['subBoxes'][0]['items'][0] if all_data[0]['subBoxes'] is not None else all_data[0]['items'][0]
        data = item['jumpInfo']
        extra = json.loads(data['externalInfo'])
        print(extra)
        time.sleep(5)
        wx_id = ''
        mysql_service = MySQLService(run_model=run_mode)
        session = mysql_service.create_session()
        gzh_id = data['userName']
        gzh: DCGzhInfo = session.query(DCGzhInfo).filter(DCGzhInfo.gzhId == gzh_id).first()
        if gzh:
            session.close()
            return

        sub = self.follow_gzh_with_wechat(wx_id, gzh_id=gzh_id, tag=tag, gzh_data=json.dumps(data), run_mode=run_mode)
        print(sub)
        session.close()
