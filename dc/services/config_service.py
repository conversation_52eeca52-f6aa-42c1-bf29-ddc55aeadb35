import os
import os

import yaml

from dc.conf.defines import ROOT_PATH
from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService


class ConfigService:
    logger: LoggingService = None
    config = {}

    def __init__(self, logger=None, session=None) -> None:
        super().__init__()
        self.logger = logger
        self.session = session
        self.config = get_settings('dc-service')

    @classmethod
    def get_proxy(cls, no: str = 'p01') -> str:
        proxy: dict = cls.get_config_content('proxy.yaml')
        return proxy.get(no, {})

    @classmethod
    def get_config_file(cls, config_file):
        config_file = os.path.join(ROOT_PATH, 'dc', 'conf', config_file)
        return config_file

    @classmethod
    def get_config_content(cls, config_file):
        config_file = os.path.join(ROOT_PATH, 'dc', 'conf', config_file)
        with open(config_file, 'r', encoding='utf8') as yamlfile:
            config = yaml.safe_load(yamlfile)
            return config
