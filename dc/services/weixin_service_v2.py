import datetime
import json
import random
import time
from urllib.parse import urljoin

import requests
from requests import HTTPError
from retry import retry
from sqlalchemy import create_engine, <PERSON>umn, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete
from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.models.model import DCGzhWechat
from urllib import parse as urlparse
from dc.common.func import md5


class WeixinServiceV2:
    """
    微信服务升级
    :author wjh
    :date 2022-11-10
    """
    name = 'default'
    config = {}
    debug = False

    base_url: str = None
    # wxpid = '1640'
    # gzhid = 'gh_4027f05f3f74'   # 保定晚报
    # gzhid = 'gh_e3222cc0c7b9'  # 首都之窗

    log = None  # LoggingService

    def __init__(self, name: String = '', wx_id='', logger=None) -> None:
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        super().__init__()
        self.log = logger if logger is not None else LoggingService(logfile='gzh.log')
        if wx_id != '':
            wechat: DCGzhWechat = session.query(DCGzhWechat).filter(DCGzhWechat.wxId == wx_id,
                                                                    DCGzhWechat.isUsed == 1).limit(1).first()
            if wechat is None:
                pass
            self.wechat = wechat
            self.base_url = wechat.machineIp
            self.pid = wechat.machinePid
        else:
            wechat = self.get_random_wechat()
            if wechat is not None:
                print("当前使用的微信id", wechat.wxId, wechat.nickName)
                self.wechat = wechat
            print(wechat)
            self.base_url = ''
            self.pid = ''
            if wechat is not None:
                self.base_url = wechat.machineIp
                self.pid = wechat.machinePid

        session.close()

    def req_call_bak(self, data, params: dict):
        """
        通用请求方法
        :author wjh
        :date 2022-11-10
        :param data:
        :param params:
        :return:
        """
        base_url = params.get('base_url', self.base_url)
        wxpid = params.get('wxpid', self.pid)
        url = f"{base_url}?wxpid={wxpid}"
        prefix = params
        self.log.info(f"{prefix} req_call: {url} params: {data}")
        # self.log.info(f"{prefix} req_call params: {data}")
        payload = json.dumps(data)

        result = 'null'
        for i in range(5):
            response = requests.request("POST", url, data=payload, timeout=10)
            result = response.text
            if result == 'null':
                self.log.info(f"{prefix} response is null, retry {i}")
                time.sleep(10)
            else:
                break

        if result == 'null':
            raise Exception('response is null')

        self.log.info(f"{prefix} req_call response: {json.dumps(result)}")
        jd = json.loads(result)
        result = json.dumps(jd, sort_keys=True, indent=4, separators=(',', ': '), ensure_ascii=False)
        # self.log.info(f"{prefix} req_call response2: {result}")

        return result

    def req_call(self, data, params: dict, method='GET'):
        """
        通用请求方法
        :author wjh
        :date 2022-11-10
        :param data:
        :param params:
        :param method:
        :return:
        """
        base_url = params.get('base_url', self.base_url)
        wxpid = params.get('wxpid', self.pid)
        # url = f"{base_url}?wxpid={wxpid}"
        api = data.get('api')
        assert api is not None, 'api is empty'
        url = urljoin(base_url, api)
        data = data.get('data', {})

        prefix = params
        self.log.info(f"{prefix} req_call: {url} params: {data}")
        # self.log.info(f"{prefix} req_call params: {data}")
        payload = json.dumps(data)

        @retry(tries=3, delay=2, backoff=2, exceptions=(requests.exceptions.RequestException,))
        def send_request(url, token=None, data=None, method='GET'):
            headers = {
                "Authorization": f"Bearer {token}",  # 使用传入的 Bearer Token
                "Content-Type": "application/json"
            }
            print(f"url: {url}")
            # 根据方法类型发送请求
            if method.upper() == 'POST':
                response = requests.post(url, headers=headers, data=json.dumps(data))
            else:
                response = requests.get(url, headers=headers, params=data)

            response.raise_for_status()
            if response.text is None or len(response.text.strip()) == 0 or response.text == 'null':
                raise HTTPError('response is null')

            return response

        try:
            response = send_request(url, None, data, method)
            result = response.text
        except Exception as e:
            print(e)
            result = None

        if result == 'null':
            raise Exception('response is null')

        self.log.info(f"{prefix} req_call response: {json.dumps(result)}")
        jd = json.loads(result)
        result = json.dumps(jd, sort_keys=True, indent=4, separators=(',', ': '), ensure_ascii=False)
        # self.log.info(f"{prefix} req_call response2: {result}")

        return result

    def get_result(self, params: dict) -> dict:
        """
        获取返回结果
        :author wjh
        :date 2022-11-10
        :return:
        """
        if params.get('status') == 200:
            return params.get('result')
        else:
            return None

    def req_login(self, params: dict) -> dict:
        """
        获取已登录的微信信息
        :author wjh
        :date 2022-11-10
        :return:
        """
        result = self.req_call({
            # "mode": 3,
            "api": '/getusers'
        }, params)

        if self.debug:
            print(result)
        return json.loads(result)

    def req_gzh(self, params: dict) -> dict:
        """
        获取已关注公众号列表
        :author wjh
        :date 2022-11-10
        :return:
        """
        result = self.req_call({
            # "mode": 12,
            "api": '/getbizs',
        }, params)

        if self.debug:
            print(result)
        return json.loads(result)

    def req_gzh_search(self, params: dict) -> dict:
        """
        搜索公众号
        :author wjh
        :date 2022-11-10
        :return:
        """
        wxpid = params.get('wxpid', self.pid)
        keyword = params.get('keyword')
        business_type = params.get('business_type', "1")
        result = self.req_call({
            # "mode": 1000,
            "api": '/websearch',
            "data": {
                "keyword": keyword,
                "BusinessType": business_type,
                # "wxpid": wxpid
            }
        }, params, method='POST')

        if self.debug:
            print(result)
        return json.loads(result)

    def req_gzh_info(self, params: dict) -> dict:
        """
        获取公众号信息
        :author wjh
        :date 2022-11-10
        :return:
        """
        gzhid = params.get('gzhid')
        wxpid = params.get('wxpid', self.pid)

        result = self.req_call({
            # "mode": 13,
            "api": '/getbizinfo',
            "data": {
                "ghid": f"{gzhid}",
                # "wxpid": wxpid
            }
        }, params, method='POST')

        if self.debug:
            print(result)
        return json.loads(result)

    def req_gzh_gz(self, params: dict) -> dict:
        """
        关注公众号
        :author wjh
        :date 2022-11-10
        :return:
        """
        gzhid = params.get('gzhid')
        wxpid = params.get('wxpid', self.pid)

        result = self.req_call({
            # "mode": 10,
            "api": '/followbiz',
            "data": {
                "ghid": f"{gzhid}",
                # "wxpid": wxpid
            }
        }, params, method='POST')
        # print(result)
        return json.loads(result)

    def req_gzh_qxgz(self, params: dict) -> dict:
        """
        取消关注公众号
        :author wjh
        :date 2022-11-10
        :return:
        """
        gzhid = params.get('gzhid')
        wxpid = params.get('wxpid', self.pid)

        result = self.req_call({
            # "mode": 11,
            "api": '/unfollowbiz',
            "data": {
                "ghid": f"{gzhid}",
                # "wxpid": wxpid
            }
        }, params, method='POST')
        print(result)
        return json.loads(result)

    def req_gzh_yds(self, params: dict) -> dict:
        """
        获取阅读数
        :author wjh
        :date 2022-11-10
        :return:
        """
        wxpid = params.get('wxpid', self.pid)
        url = params.get('url')

        result = self.req_call({
            # "mode": 19,
            "api": '/getreadnum',
            "data": {
                "url": url,
                "comment_id": "true",
                # "wxpid": wxpid
            }
        }, params, method='POST')

        if self.debug:
            print(result)
        return json.loads(result)


    def req_gzh_history(self, params: dict, callback) -> dict:
        """
        获取历史文章
        :author wjh
        :date 2022-11-10
        :return:
        """
        gzhid = params.get('gzhid')
        wxpid = params.get('wxpid', self.pid)
        prefix = params.get('prefix', '')

        time_stop = int(params.get('time_stop'))  # 数据截止时间，时间戳
        list_sleep = int(params.get('list_sleep'))  # 公众号历史文章列表，拉取间隔
        if list_sleep < 3:
            list_sleep = 10

        page = 1
        next_offset = None
        while True:

            self.log.info(f"\r\n {prefix}*********************************************************************************\r\n")
            self.log.info(f"{prefix}         page: {page}    next_offset: {next_offset}")
            if next_offset:
                dk = {
                    "api": '/getbizhistory',
                    "data": {
                        "ghid": f"{gzhid}",
                        "next_offset": next_offset
                    }
                }
            else:
                dk = {
                    "api": '/getbizhistory',
                    "data": {
                        "ghid": f"{gzhid}",
                    }
                }

            result = self.req_call(dk, params, method='POST')
            # self.log.info(result)
            # if result is None:
            #     self.log.info('返回内容为空，重新执行----->')
            #     raise Exception('返回内容为空--- 异常了')
            #     continue

            jj = json.loads(result)

            self.log.debug(f'\r\n {prefix} +++++++++++++++++++++++++++++++++++++++')
            account_info = jj.get('result', {}).get('AccountInfo', {})
            if not account_info:
                self.log.info(f"account_info is empty")
                continue

            for rr in jj.get('result', {}).get('MsgList', {}).get('Msg', []):
                self.log.debug(f'\r\n {prefix}====================================================')
                i = 1
                zz = rr['AppMsg']['DetailInfo']
                base_info = rr['AppMsg']['BaseInfo']

                CreateTime = int(base_info["CreateTime"])
                if CreateTime < time_stop:
                    self.log.info(f"{prefix} time stop {time_stop} CreateTime:{CreateTime} --->  return true")
                    return True

                for dd in zz:
                    self.log.info(f"{i}. {dd['Title']} -- {dd['Digest']}    ContentUrl:{dd['ContentUrl']}")
                    callback(article=dd, account=account_info, base=base_info, params=params)

                    i = i + 1

            # next page
            page_info = jj.get('result', {}).get('MsgList', {}).get('PagingInfo', {})
            if page_info['IsEnd'] == 1:
                break
            else:
                next_offset = page_info['Offset']

            page = page + 1
            time.sleep(list_sleep)

    def update_wechat_info(self, info={}):
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        self.wechat.wxId = info['wxid']
        self.wechat.nickName = info['nickname']
        self.wechat.wxNo = info['wxh']
        self.wechat.machinePid = info['wxpid']
        self.wechat.lastOnlineTime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        session.add(self.wechat)
        session.commit()
        session.close()

    def add_wechat_info(self, info={}):
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        wechat = DCGzhWechat(nickName=info['nickname'], wxId=info['wxid'], machineIp=self.wechat.machineIp,
                             machinePid=info['wxpid'], wxNo=info['wxh'],
                             lastOnlineTime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), isUsed=info['login'],
                             status=1)
        session.add(wechat)
        session.commit()
        session.close()

    @staticmethod
    def get_default_wechat():
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        wechat: DCGzhWechat = session.query(DCGzhWechat).where(DCGzhWechat.isUsed == 1).order_by(
            DCGzhWechat.lastOnlineTime.desc()).first()
        session.close()
        return wechat

    @staticmethod
    def get_random_wechat():
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        wechat: list[DCGzhWechat] = session.query(DCGzhWechat).where(DCGzhWechat.isUsed == 1, DCGzhWechat.wechatType==1).all()
        c = len(wechat)
        session.close()
        return wechat[random.randint(0, c - 1)] if c > 0 else None

    @staticmethod
    def get_wechat_by_gzh(gzhId: str = None):
        """
        通过公众号获取已关注微信号
        :author wjh
        :date 2022-12-15
        :param gzhId: 公众号id，空为返回随机微信号
        :return:
        """
        wechat: DCGzhWechat = None
        # if gzhId:
        #     mysql = MySQLService()
        #     session = mysql.create_session()
        #     wechat = session.execute(f"select * from DC_GzhWechat where status=1 and isUsed=1 and wxId IN(select distinct wxId from DC_GzhWechatRelation where gzhId='{gzhId}')").first()

        if wechat is None:
            wechat = WeixinServiceV2.get_random_wechat()

        return wechat

    def req_start_wechat(self, params: dict = {}):
        """
        启动微信 与二维码一起返回
        :return:
        """
        h = self.req_qr_code(params)
        if h != {}:
            return h

        result = self.req_call({
            "api": '/startwx',
        }, params)

        h = self.req_qr_code(params)
        if h != {}:
            return h

        return {}

    def req_qr_code(self, params: dict = {}):
        """ 获取登录二维码 """
        result = self.req_call({
            "api": '/getqrcode',
        }, params)
        r = json.loads(result)
        r['result']['imgQrcode2'] = 'data:image/jpeg;base64,' + r['result']['imgQrcode']
        return r

    def calc_url_md5(self, url):
        """
        计算公众号文章链接md5 ,去掉了变化的部分
        :author wjh
        :date 2022-11-10
        :param url:
        :return:
        """
        pp = urlparse.parse_qs(urlparse.urlsplit(url).query)
        fields = ["sessionid", "scene"]
        fields2 = ["__biz", "mid", "idx", "sn"]
        # for field in fields:
        #     if pp.get(field) is not None:
        #         del pp[field]

        pp2 = {}
        for field in fields2:
            if pp.get(field) is not None:
                pp2[field] = pp[field]

        pp2 = sorted(pp2.items())
        ps = [f"{x}={''.join(y)}" for x, y in pp2]
        full = "&".join(ps)
        return md5(full)

    def calc_url_md5_old(self, url):
        """
        计算公众号文章链接md5 ,去掉了变化的部分, 不再使用
        :author wjh
        :date 2022-11-10
        :param url:
        :return:
        """
        pp = urlparse.parse_qs(urlparse.urlsplit(url).query)
        fields = ["sessionid", "scene"]
        for field in fields:
            if pp.get(field) is not None:
                del pp[field]

        pp2 = sorted(pp.items())
        ps = [f"{x}={''.join(y)}" for x, y in pp2]
        full = "&".join(ps)
        return md5(full)
