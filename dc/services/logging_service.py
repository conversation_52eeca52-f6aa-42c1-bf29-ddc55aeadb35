import json
import logging
import os
from logging.handlers import TimedRotatingFileHandler
from pythonjsonlogger import jsonlogger

from dc.common.utils import Utils
from dc.conf.settings import get_settings


# 自定义的 JSON 格式化器
class CustomJsonFormatter(jsonlogger.JsonFormatter):
    def format(self, record):
        log_record = {
            'time': self.formatTime(record, self.datefmt),
            'levelname': record.levelname,
            'message': record.getMessage(),
            'levelno': record.lineno,
            'process': record.process,
            'func': record.funcName,
            "lineno": record.lineno,
            'pathname': record.pathname,
        }
        return json.dumps(log_record, ensure_ascii=False)

    # def add_fields(self, log_record, record, message_dict):
    #     super(CustomJsonFormatter, self).add_fields(log_record, record, message_dict)
    #     # 删除默认的 asctime 字段
    #     if 'asctime' in log_record:
    #         del log_record['asctime']
    #     # 格式化时间字段
    #     log_record['time'] = self.formatTime(record, self.datefmt)
    #     log_record['levelname'] = record.levelname
    #     log_record['message'] = record.getMessage()
    #     log_record['levelno'] = record.lineno
    #     log_record['process'] = record.process
    #     log_record['func'] = record.funcName
    #     log_record['pathname'] = record.pathname


# 标准日志格式化器
new_formatter = '[%(levelname)s]%(asctime)s:%(msecs)s.%(process)d,%(thread)d#>[%(funcName)s]:%(lineno)s  %(message)s'
fmt = logging.Formatter(new_formatter)

# JSON 格式化器
jsonFmt = CustomJsonFormatter(datefmt='%Y-%m-%d %H:%M:%S')

logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    filemode='a',
)


class LoggingService:
    logfile = 'dc.log'
    config = {}

    def __init__(self, logfile: str = 'dc.log', formatter: str = None) -> None:
        super().__init__()

        self.logfile = logfile
        self.logger = logging.getLogger(logfile)
        self.logger.setLevel(logging.DEBUG)  # 设置最低日志级别为DEBUG

        # FileHandler
        logs_path = Utils.get_project_path('logs')
        full_path = os.path.join(logs_path, logfile)
        base_path = os.path.dirname(full_path)
        Utils.mkdir(base_path)
        handler = TimedRotatingFileHandler(full_path, when='midnight')
        formatter = formatter if formatter else 'json'
        if formatter == 'json':
            handler.setFormatter(jsonFmt)
        else:
            # handler.setFormatter(fmt)
            handler.setFormatter(jsonFmt)

        handler.setLevel(logging.INFO)
        self.logger.addHandler(handler)

        # 创建控制台处理器并设置级别为DEBUG
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(fmt)
        self.logger.addHandler(console_handler)

    def debug(self, msg, *args, stacklevel=2, **kwargs):
        self.logger.debug(msg, *args, stacklevel=stacklevel, **kwargs)

    def info(self, msg, *args, stacklevel=2, **kwargs):
        self.logger.info(msg, *args, stacklevel=stacklevel, **kwargs)

    def warning(self, msg, *args, stacklevel=2, **kwargs):
        self.logger.warning(msg, *args, stacklevel=stacklevel, **kwargs)

    def error(self, msg, *args, stacklevel=2, **kwargs):
        self.logger.error(msg, *args, stacklevel=stacklevel, **kwargs)

    def exception(self, msg, *args, exc_info=True, stacklevel=2, **kwargs):
        self.logger.exception(msg, *args, exc_info=exc_info, stacklevel=stacklevel, **kwargs)

    def critical(self, msg, *args, exc_info=True, stacklevel=2, **kwargs):
        self.logger.critical(msg, *args, exc_info=exc_info, stacklevel=stacklevel, **kwargs)

    def dcPullLog(self, msg, *args, stacklevel=2, **kwargs):
        self.logger.info(msg, *args, stacklevel=stacklevel, **kwargs)
