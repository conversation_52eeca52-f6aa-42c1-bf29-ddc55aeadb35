import re
import jieba


class ContentFilterService:
    words = [
        "半导体", "集成电路", "芯片", "微电子", "晶圆", "晶圆制造", "硅片", "纳米", "第三代半导体", "碳化硅", "氮化镓",
        "人工智能", "IC设计", "芯片设计", "存储",
        "光刻机", "刻蚀设备", "镀膜设备", "量测设备", "检测设备", "清洗设备", "离子注入设备", "薄膜沉积设备",
        "胶显影设备", "CMP设备", "热处理设备", "去胶设备", "探针机",
        "鳍式场效应晶体管", "晶体管", "量子计算", "传感器", "显示屏", "封装", "面板", "闪存", "封装载板", "前道设备",
        "后道设备", "半导体设备", "汽车电子",
        "光电", "电子器件", "光掩模版", "新兴产业", "宽禁带半导体", "鸿蒙", "半导体材料", "EDA工具", "三星电子",
        "SK海力士", "台积电", "意法半导体", "英伟达", "英飞凌",
        "欧姆龙", "尼康", "东芝", "西部数据", "三星电子", "SK海力士", "ARM", "新思", "英特尔", "高通", "美光", "博通",
        "德州仪器", "苹果公司", "联发科", "恩智浦",
        "瑞萨", "铠侠", "索尼", "安森美", "美满电子", "瑞昱", "联咏", "华为", "小米", "海思", "应用材料", "阿斯麦",
        "泛林", "东京电子", "科磊", "迪恩士",
        "爱德万测试", "ASM国际", "日立高新", "泰瑞达", "新思科技", "楷登电子", "明导国际", "三星", "格芯", "联电",
        "中芯国际", "华虹集团", "高塔半导体", "力积电",
        "世界先进", "晶合集成", "京东方", "维信诺", "惠科", "华星", "奕斯伟", "特斯拉", "比亚迪", "友达", "长鑫",
        "长江存储", "长存", "粤芯", "华润微", "飞腾",
        "北方华创", "芯华章", "通富微电", "华天科技", "中芯国际", "Semiconductor", "IC", "wafer", "chip", "Fab",
        "Fabless", "CPU", "GPU",
        "FPGA", "EDA", "SoC", "Moore's Law", "SiC", "GaN", "Powerchip", "memory", "FinFET", "FD-SOI", "SEMI", "PC",
        "iPhone", "RAM", "SRAM", "DRAM", "SDRAM", "PSRAM", "SDR", "DDR", "LPDDR", "DRAM", "ROM", "EEPROM", "PROM",
        "EPROM", "eMMC", "SSD", "eMCP", "FLASH", "NAND", "3D-Xpoint", "RRAM", "FRAM", "MRAM", "PCM", "HBM", "equipment",
        "PVD", "CVD", "ALD", "EUV", "DUV", "CMP", "chipmaking machine", "SIP", "PGA", "TSV", "Through Silicon Via",
        "Fan-In", "Fan-Out", "CoWoS", "Wafer Level Package", "WLP", "Fan-out Wafer Level Package", "FOWLP", "CSP",
        "Chip Scale Package", "WLCSP", "Wafer Level Chip Scale Packaging", "InFO", "eWLB", "FOPLP", "EMIB", "HBM",
        "HMC", "Wide-IO", "Foveros", "MOSFET", "MEMS", "LED", "Mini LED", "OLED", "Micro LED", "QLED", "Micro LED",
        "LPDDR5X", "HBM", "HDD", "LTPS", "ASIC", "RISC-V", "IDM", "foundry", "TSMC", "AMD", "Socionext", "Renesas",
        "Microchip", "Skyworks", "Nvidia", "Qualcomm", "Infineon", "Apple", "STMicroelectronics", "Omron", "Nikon",
        "ASMI", "Toshiba", "ONsemi", "LAM", "Marvell", "SK Hynix", "ARM", "Arm", "Intel", "Microchip",
        "Western Digital", "Nexchip", "Samsung", "Micron", "Texas Instruments", "Broadcom", "NXP", "MediaTek",
        "Analog Devices", "ADI", "Kioxia", "Skyworks", "ASML", "Socionext", "SONY", "Realtek", "Apple", "Novatek",
        "AMAT", "Tokyo", "Hisilicon", "Huawei", "Xiaomi", "Electron", "KLA", "Screen", "Advantest", "Synopsys",
        "Hitachi High-Tech", "Teradyne", "Mentor", "Cadence", "Samsung", "GlobalFoundries", "UMC", "SMIC",
        "Shanghai Huahong", "Tower Semiconductor", "Manufacturing", "VIS", "Wolfspeed", "OpenAI", "UMC", "半導體",
        "晶片",
        "晶圓", "奈米", "晶圓代工", "成熟製程", "先進製程", "微影製程", "光阻劑", "第三代半導體", "氮化鎵", "光刻機",
        "人工智慧", "感測器", "IC設計", "IC製造", "軟體",
        "設備", "手機", "資料中心", "機器學習", "記憶體", "量子電腦", "微影設備", "曝光机", "碳化矽", "6吋", "8吋",
        "12吋", "量子技術", "台積電", "三星", "超微",
        "英特爾", "聯電", "聯合微電子", "輝達", "環球晶", "友達", "汽車", "封裝", "面板", "群創", "華邦", "鴻海",
        "寬禁帶半導體", "日月光", "智慧手機", "雲端運算",
        "富士康", "量子計算", "チップ", "半導体", "メモリ", "リソグラフィー", "半導体材料", "半導体装置", "EDAツール",
        "キオクシア", "東芝", "東京エレクトロニクス", "ソニー",
        "반도체", "칩", "반도체", "저장", "리소그래피", "반도체 소재", "반도체 장비", "EDA 도구", "삼성전자", "SK하이닉스", "智慧手機", "汽车", "AI",
        "并购", "制裁", "融资", "收购", "Sanctions", "Acquisitions"
    ]

    def word_filter(self, words: [], content: str) -> bool:
        """
        词过滤 如果有词 返回true 没有返回false
        """
        for word in words:
            if word in content:
                return True
        return False

    def word_filter_re(self, words: [], content: str) -> bool:
        """
        param: words 词过滤列表
        param: content 词过滤内容
        词过滤 如果有词 返回true 没有返回false
        """
        pattern = r'\b(?:{})\b'.format('|'.join(map(re.escape, words)))
        matches = re.search(pattern, content)
        return bool(matches)

    def word_filter_jieba(self, words: [], content: str) -> bool:
        """
        param: words 词过滤列表
        param: content 词过滤内容
        词过滤 使用jieba先拆词, 再和 words 取交集 如果有词 返回true 没有返回false
        """
        content_words = jieba.cut(content)  # 拆词
        content_words = set(content_words)  # 去重
        print(content_words, set(words))
        return len(content_words & set(words)) > 0

    # def get_words():
    #     return words
