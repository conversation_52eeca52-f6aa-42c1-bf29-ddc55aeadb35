# -*- coding:utf-8 -*-
# @Function  : 七牛云服务
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0

import os.path
import time
import traceback
from datetime import datetime
from typing import List

from qiniu import Auth, BucketManager, put_file
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement

from dc.common.qiniu_utils import *
from dc.conf.settings import get_settings2
from dc.services.logging_service import LoggingService
from dc.services.qiniu_base_service import QiniuBaseService
from dc.tests.browser import Browser


class QiniuService(QiniuBaseService):
    name: str = None
    config = {}

    logger = None  # LoggingService
    q: Auth = None
    bucket: BucketManager = None

    def __init__(self, name='bianjifagao', **kwargs) -> None:
        super().__init__(name=name, **kwargs)


    def put_file(self, localfile: str, key: str):
        """
        上传文件
        :param localfile: 本地文件目录
        :param key: 上传后文件目录
        :return:
        """
        # 生成上传 Token，可以指定过期时间等
        token = self.q.upload_token(self.config['bucket'], key, 3600)

        # 要上传文件的本地路径
        # localfile = './sync/bbb.jpg'
        ret, info = put_file(token, key, localfile, version='v1')
        self.logger.info(f"上传文件结果：{info}")

        return ret, info

    def fetch_image(self, images: list, path: str = None, index_start=0):
        """
        通过七牛上传图片
        :param images: 图片列表
        :param path: 上传目录
        :param index_start: 索引起始数值
        :return:
        """
        result = []
        for index, image in enumerate(images):
            # key = 'tmp/' + image.split('/')[-1]
            ext = get_extension_from_url(image, '.jpg')
            if not is_valid_image_extension(ext):
                self.logger.error(f"不支持的图片格式：{ext} image: {image}")
                continue

            key = get_key(calculate_md5(image) + ext, path)

            ret, info = self.bucket.fetch(image, self.config['bucket'], key)  # type: dict, ResponseInfo
            if info.status_code != 200:
                self.logger.error(f"获取图片元数据 {image}  bucket:{self.config['bucket']}  key: {key} status_code: {info.status_code} {info.error}")

            assert info.status_code == 200, 'fetch is fail'

            info2: dict = get_image_info(info)
            pic_info: dict = fetch_and_get_image_info(self.config['image_url'], key, self.logger)
            if not pic_info:
                self.logger.error("通过七牛获取图片元数据失败")
                pic_info: dict = process_image(image)

            if not pic_info:
                self.logger.error("通过本地获取图片元数据失败")

            self.logger.info(f"获取图片元数据 :{pic_info}")

            if pic_info:
                assert info2['fsize'] == pic_info['size']
                del pic_info['size']
                info2.update(pic_info)
            else:
                self.logger.error("获取图片元数据异常 exception")

            result.append({
                'index': index + index_start,
                'key': calculate_md5(image),
                'url': image,
                'image_domain': self.config['image_url'],
                'image_url': url_path_join(self.config['image_url'], info2['key']),
                'meta': info2
            })

        return result

    def fetch_files(self, images: list, path: str = None, index_start=0):
        """
        通过七牛上传文件
        :param images: 文件列表
        :param path: 上传目录
        :param index_start: 索引起始数值
        :return:
        """
        result = []
        for index, image in enumerate(images):
            # key = 'tmp/' + image.split('/')[-1]
            ext = get_extension_from_url(image, '.jpg')
            if not is_valid_attachment_extension(ext):
                self.logger.error(f"不支持的附件格式：{ext} file: {image}")
                continue

            key = get_key(calculate_md5(image) + ext, path)
            ret, info = None, None
            for attempt in range(3):
                ret, info = self.bucket.fetch(image, self.config['bucket'], key)  # type: dict, ResponseInfo
                if info.status_code == 200:
                    break
                else:
                    self.logger.error(f"获取图片元数据 {image}  bucket:{self.config['bucket']}  key: {key} status_code: {info.status_code} {info.error}")
                    time.sleep(1*attempt)

            assert info.status_code == 200, 'fetch is fail'

            result.append({
                'index': index + index_start,
                'key': calculate_md5(image),
                'url': image,
                'image_domain': self.config['image_url'],
                'image_url': url_path_join(self.config['image_url'], ret['key']),
            })

        return result

    def get_gzh_images(self, webdriver, url, xpath: str):
        """
        获取公众号图片
        :param webdriver: webdriver
        :param url: url 路径
        :param xpath: xpath for image
        :return:
        """
        if webdriver is None:
            from dc.common.webdriver_util import get_webdriver
            driver = get_webdriver(browser=Browser.Chrome)
            driver.implicitly_wait(10)
        else:
            driver = webdriver

        driver.get(url)
        result = []
        images = driver.find_elements(By.XPATH, xpath)
        for image in images:
            html = image.get_attribute('outerHTML')
            src = image.get_attribute('src')
            data_src = image.get_attribute('data-src')
            if not src:
                continue

            result.append(data_src)

        if webdriver is None:
            driver.close()

        return result

    def get_gzh_images_by_webelement(self, images: List[WebElement]):
        """
        通过 webelement 获取公众号图片
        :param images:
        :return:
        """
        result = []
        for image in images:
            html = image.get_attribute('outerHTML')
            src = image.get_attribute('src')
            data_src = image.get_attribute('data-src')
            if not src:
                continue

            result.append(data_src)

        return result

    def get_gzh_qiniu_images(self, webdriver, url, content_xpath):
        """
        获取公众号七牛图片
        :param webdriver:
        :param url:
        :param content_xpath:
        :return:
        """
        try:
            # url = 'https://mp.weixin.qq.com/s?__biz=MzIxODI2NTY4OQ==&mid=2247523746&idx=1&sn=a6e36d0b9c6aaa14c8e342d84a418ace&chksm=97effc94a0987582e8838205f8edecaab32160b784ba06612d25719346f93cffa629ee2774a1&scene=126&sessionid=1725684824#rd'
            # content_xpath = '//div[@id="js_content" or @id="js_share_notice" or @class="wx_video_play_opr" or @class="rich_media_content"]'

            # 公众号处理
            gzh_images = self.get_gzh_images(webdriver, url, content_xpath)
            self.logger.info(f"公众号处理：{gzh_images}")
            assert isinstance(gzh_images, list)

            # 七牛处理
            md5 = calculate_md5(url)
            save_path = url_path_join(self.config["document_name"], md5)
            self.logger.info(f'save path : {save_path}')
            qiniu_images = self.fetch_image(gzh_images, save_path)
            self.logger.info(f"七牛处理：{qiniu_images}")
            assert isinstance(qiniu_images, list)

            # 图片过滤
            images = self.images_filer(qiniu_images)
            self.logger.info(f"过滤后图片：{images}")
            assert isinstance(images, list)

            return images
        except Exception as ex:
            self.logger.error(f"error: {str(ex)}")
            return []

    def get_gzh_qiniu_images_by_gzh_images(self, gzh_images, url):
        """
        通过 images 获取七牛图片
        :param gzh_images:
        :param url:
        :return:
        """
        try:
            # 七牛处理
            md5 = calculate_md5(url)
            save_path = url_path_join(self.config["document_name"], md5)
            self.logger.info(f'save path : {save_path}')
            qiniu_images = self.fetch_image(gzh_images, save_path)
            self.logger.info(f"七牛处理：{qiniu_images}")
            assert isinstance(qiniu_images, list)

            # 图片过滤
            images = self.images_filer(qiniu_images)
            self.logger.info(f"过滤后图片：{images}")
            assert isinstance(images, list)

            return images
        except Exception as ex:
            self.logger.error(f"error: {str(ex)}")
            self.logger.error(f"error traceback: " + traceback.format_exc())
            return []

    def images_filer(self, images: list) -> list:
        """
        图片过滤，通过图片过滤器设置进行过滤
        :param list images:
        :return:
        """

        filter_config = self.config.get('image_filter', {})
        min_width = int(filter_config.get('min_width', 0))
        min_height = int(filter_config.get('min_height', 0))
        min_size = int(filter_config.get('min_size', 0))

        def _filter(image: dict) -> bool:
            width = image.get('meta', {}).get('width', 0)
            height = image.get('meta', {}).get('height', 0)
            size = image.get('meta', {}).get('fsize', 0)

            if min_width > 0 and 0 < width < min_width:
                print(f"min_width: {min_width} < {width}")
                return False

            if min_height > 0 and 0 < height < min_height:
                print(f"min_height: {min_height} < {height}")
                return False

            if min_size > 0 and 0 < size < min_size:
                print(f"min_size: {min_size} < {size}")
                return False

            return True

        images = [image for image in images if _filter(image)]
        return images

    def image_view(self, params: dict):
        """
        图片查看
        :param params:
        :return:
        """
        from dc.services.elasticsearch_service import ElasticsearchService
        ess = ElasticsearchService()
        es = None

        try:
            id = params.get('id')
            assert id is not None

            es = ess.get_connect()
            item = es.get(index='dc_bianjifagao', id=id)
            data = item['_source']

            # 生成HTML页面的函数
            def generate_html(data, output_file):
                html_content = """
                        <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>Image Comparison</title>
                            <style>
                                body {
                                    font-family: Arial, sans-serif;
                                    margin: 0;
                                    padding: 0;
                                    background-color: #f4f4f4;
                                }
                                .container {
                                    width: 90%;
                                    margin: 0 auto;
                                    padding: 20px;
                                }
                                .comparison-row {
                                    display: flex;
                                    justify-content: space-between;
                                    margin-bottom: 20px;
                                }
                                .comparison-row img {
                                    width: 45%;
                                    border: 1px solid #ddd;
                                    border-radius: 4px;
                                    padding: 5px;
                                    background-color: #fff;
                                }
                                .index-label {
                                    text-align: center;
                                    font-size: 16px;
                                    font-weight: bold;
                                    margin-top: 10px;
                                }
                                h2 {
                                    text-align: center;
                                    font-size: 24px;
                                    margin-bottom: 40px;
                                }
                                a {
                                    text-align: center;
                                    display: block;
                                    margin-bottom: 30px;
                                    font-size: 18px;
                                    color: #3498db;
                                    text-decoration: none;
                                }
                            </style>
                        </head>
                        <body>
                        """

                title = data.get("title", "No Title")
                page_url = data.get("url", data.get("dc_detail_url", '#'))
                images_v2 = data.get("images_v2", [])

                # 插入标题和链接
                html_content += f"""
                        <div class="container">
                            <h2>{title}</h2>
                            <a href="{page_url}" target="_blank">查看原文</a>
                        """

                # 遍历 images_v2，生成图片对比行
                for image in sorted(images_v2, key=lambda x: x['index']):
                    image_url = image.get("image_url", "#")
                    url = image.get("url", "#")
                    index = image.get("index", -1)

                    html_content += f"""
                            <div class="comparison-row">
                                <div>
                                    <img src="{image_url}" alt="image_url {index}">
                                    <div class="index-label">Index: {index}</div>
                                </div>
                                <div>
                                    <img src="{url}" alt="url {index}">
                                </div>
                            </div>
                            """

                # 关闭 container 和 body
                html_content += """
                        </div>
                        </body>
                        </html>
                        """

                # 写入 HTML 文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                return html_content

            # 获取当前时间并格式化为 YYYYMMDD_HHMMSS
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 指定生成的HTML文件名，并加上日期和时分秒
            output_file = f'tmp/image_comparison_{current_time}.html'

            # 调用生成HTML的函数
            content = generate_html(data, output_file)

            print(f"HTML file '{output_file}' has been generated.")

            return content
        except Exception as e:
            return f"{str(e)}"
        finally:
            es.close()

    def get_save_path(self, path: str):
        """
        get save path
        :param path:
        :return:
        """
        return url_path_join(self.config["document_name"], path)
