# -*- coding:utf-8 -*-
# @Function  : ELK 错误信息提示（仅供测试）
# <AUTHOR> wjh
# @Time      : 2024/7/17
# Version    : 1.0

import traceback

from sqlalchemy.orm import Session

from dc.services.logging_service import LoggingService


class ErrorService:
    """
    邮件发送服务
    :author wjh
    :date 2022-11-14
    """
    logger: LoggingService = None
    config = {}
    session: Session = None

    def __init__(self, **kwargs) -> None:
        super().__init__()
        self.logger = LoggingService(logfile='error_test.log')

    def send(self, email: dict) -> bool:
        """
        邮件发送
        :author wjh
        :date 2022-11-14
        :param email:
        :return:
        """
        try:
            # self.logger.info(f'mail send: {email}')
            raise Exception('sssss')
            return True

        except Exception as ex:
            # print(ex.args)
            self.logger.error(f'发送错误0：{traceback.format_exc()}', stacklevel=0)
            self.logger.error(f'发送错误1：{traceback.format_exc()}', stacklevel=1)
            self.logger.error(f'发送错误2：{traceback.format_exc()}', stacklevel=2)
            self.logger.error(f'发送错误3：{traceback.format_exc()}', stacklevel=3)
            return False

