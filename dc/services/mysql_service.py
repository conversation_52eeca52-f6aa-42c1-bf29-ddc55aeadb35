from sqlalchemy import create_engine, <PERSON><PERSON><PERSON>, Integer, <PERSON>, DateT<PERSON>, Enum, SmallInteger, Boolean, Foreign<PERSON>ey, \
    select, func, update, delete
from sqlalchemy.engine import Engine
from sqlalchemy.engine.mock import MockConnection
from sqlalchemy.orm import declarative_base, relationship, backref, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.types import CHAR
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import scoped_session
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum
from urllib.parse import quote_plus as urlquote
from dc.conf.settings import get_settings
from dc.models.model import Base


class MySQLService:
    name = 'default'
    config = {}
    echo_set = False    # 输出配置
    engine: Engine = None

    def __init__(self, name: str = 'mysql', run_model=None) -> None:
        super().__init__()
        self.name = name,
        self.config = get_settings(name, run_model=run_model)

    def create_engine(self, stream=False) -> Engine:
        """
        create engine
        :param stream: stream_results
        :return:
        """
        if self.engine is None:
            userName = self.config.get('userName')
            password = self.config.get('password')
            host = self.config.get('host')
            port = self.config.get('port')
            database = self.config.get('database')
            engine = create_engine(f'mysql+pymysql://{userName}:{urlquote(password)}@{host}:{port}/{database}?charset=utf8mb4',
                                   pool_size=10,
                                   max_overflow=10,
                                   pool_timeout=30,
                                   connect_args={'connect_timeout': 15},
                                   echo=self.echo_set,
                                   echo_pool=self.echo_set,
                                   pool_recycle=True,
                                   future=True,
                                   pool_pre_ping=True   # 每次连接使用前检查连接的有效性，如果连接已经断开，则会自动重连
                                   )

            # 使用游标执行查询
            if stream:
                engine.connect().execution_options(stream_results=True)

            self.engine = engine

        return self.engine


    def pool(self) -> QueuePool:
        """
        get pool
        :return: QueuePool
        """
        engine = self.create_engine()
        return engine.pool

    def create_connect(self, stream=False) -> MockConnection:
        """
        create connect
        :return: MockConnection
        """
        engine = self.create_engine(stream=stream)
        connection = engine.connect()
        return connection

    def create_session(self, stream=False) -> Session:
        """
        create session
        :return: Session
        """
        engine = self.create_engine(stream=stream)
        session = Session(bind=engine, future=True)
        session.is_active
        return session

    def query2dict(self, model_list):
        """
        查询结果转换为 dict list
        :param model_list: model list or model
        :return: list[dict]
        """
        if isinstance(model_list, list):  # 如果传入的参数是一个list类型的，说明是使用的all()的方式查询的
            if isinstance(model_list[0], Base):  # 这种方式是获得的整个对象  相当于 select * from table
                lst = []
                for model in model_list:
                    dic = {}
                    for col in model.__table__.columns:
                        dic[col.name] = getattr(model, col.name)
                    lst.append(dic)
                return lst
            else:  # 这种方式获得了数据库中的个别字段  相当于select id,name from table
                lst = []
                for result in model_list:  # 当以这种方式返回的时候，result中会有一个keys()的属性
                    lst.append([dict(zip(result.keys, r)) for r in result])
                return lst
        else:  # 不是list,说明是用的get() 或者 first()查询的，得到的结果是一个对象
            if isinstance(model_list, Base):  # 这种方式是获得的整个对象  相当于 select * from table limit=1
                dic = {}
                for col in model_list.__table__.columns:
                    dic[col.name] = getattr(model_list, col.name)
                return dic
            else:  # 这种方式获得了数据库中的个别字段  相当于select id,name from table limit = 1
                return dict(zip(model_list.keys(), model_list))
