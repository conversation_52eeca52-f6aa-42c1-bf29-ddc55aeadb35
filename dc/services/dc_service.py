import json
import logging
import os
import traceback
from logging.handlers import TimedRotatingFileHandler
import redis
from selenium.webdriver.remote.webelement import WebElement

from dc.common.GetSeleniumDriver import GetSeleniumDriver
from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.models.model import DCSiteTask, DCSiteTaskHistory
from sqlalchemy.orm import declarative_base, relationship, backref, Session
from dc.services.logging_service import LoggingService
from selenium.webdriver.common.by import By

class DcService:
    logger: LoggingService = None
    config = {}
    session: Session = None

    driver = GetSeleniumDriver().driver
    driver.implicitly_wait(15)


    def __init__(self, logger=None, session=None) -> None:
        super().__init__()
        self.logger = logger
        self.session = session
        self.config = get_settings('dc-service')

    def log_sitetask(self, task: DCSiteTask, **kwargs) -> int:
        df: dict = task.to_dict()
        df['taskId'] = df['id']
        del df['id']
        # print(df)
        df.update(kwargs)
        # self.logger.info(f"log sitetask: {task.to_json()}")
        self.logger.info(f"log sitetask: " + json.dumps(df))

        dch = DCSiteTaskHistory(**df)
        self.session.add(dch)
        self.session.commit()
        return dch.id


    def get_weixin_content(self,url: str) -> str:
        """
        获取微信链接文章内容
        :author wjh
        :date 2023-5-11
        :param url: 链接
        :return:
        """
        try:
            settings = get_settings('weixin_gzh')
            dc_config = settings.get('dc_config', [])
            content_xpath = dc_config.get('content_xpath')

            # url = "http://mp.weixin.qq.com/s?__biz=MzA5NDY5MzUzMQ==&mid=2655445229&idx=1&sn=bfec4bab524cf41b6c94fac96d717fab&chksm=8bf8f119bc8f780f2e3e5822b77322bc7301ad2b313c05ed1a9dbb068eec8c980c739a8e49c4&scene=126&&sessionid=1665391913#rd"
            self.driver.get(url)
            content = ''

            # ce: WebElement = self.driver.find_element(By.XPATH, content_xpath)
            content = self.driver.find_element(By.XPATH, content_xpath).text
            # page_source = ce.get_attribute('outerHTML')
            return content
        except Exception as ex:
            return ''
