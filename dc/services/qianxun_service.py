import datetime
import json
import random
import time
import traceback

import requests
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete

from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.models.model import DCGzhWechat
from urllib import parse as urlparse
from dc.common.func import md5
from dc.services.redis_service import RedisService


class QianxunService:
    name = 'default'
    config = {}
    extra = {}

    logger = None  # LoggingService

    def __init__(self, name: String = 'qianxun', **kwargs) -> None:
        super().__init__()
        self.name = name,
        self.config = get_settings(name)
        self.extra = kwargs
        self.logger = kwargs.get('logger') if kwargs.get('logger') is not None else LoggingService(logfile='qianxun.log')

    def get_result(self, params: dict) -> dict:
        """
        获取返回结果
        :return:
        """
        if params.get('status') == 200:
            return params.get('result')
        else:
            return None

    def req_requests(self, data, params: dict):
        """
        通用请求方法
        :return:
        """
        wxid = params.get('wxid')
        base_url = self.config.get('host')
        if wxid:
            url = f"{base_url}?wxid={wxid}"
        else:
            url = f"{base_url}"

        payload = json.dumps(data)
        headers = {
            'User-Agent': 'apifox/1.0.0 (https://www.apifox.cn)',
            'Content-Type': 'application/json'
        }
        self.logger.info(f"request url: {url}")
        self.logger.info(f"request data: {payload}")
        response = requests.request("POST", url, headers=headers, data=payload)
        self.logger.info(f"response: {response.text}")
        jd: dict = json.loads(response.text)
        result = jd.get('result') if jd.get('code') == 200 else []
        return result

    def get_wechat_list(self, params: dict = {}):
        """
        获取群聊列表
        :param params:
        :param wxid:
        :return:
        """
        data = {
            "type": "X0000",
            "data": {}
        }
        result = self.req_requests(data, params)
        return result

    def get_wechat_status(self, params: dict = {}):
        """
        微信状态检测
        :param params:
        :param wxid:
        :return:
        """
        data = {
            "type": "Q0000",
            "data": {}
        }
        result = self.req_requests(data, params)
        return result

    def get_wxid_first(self, params: dict = {}):
        """
        获取第一个 wechat
        :param params:
        :param wxid:
        :return:
        """
        wechat = self.get_wechat_first(params)
        wxid = wechat.get('wxid') if wechat else None
        if wxid is None:
            raise Exception('wxid is empty')

        return wxid

    def get_wechat_first(self, params: dict = {}) -> dict:
        """
        获取第一个 wechat
        :param params:
        :param wxid:
        :return:
        """
        list = self.get_wechat_list(params)
        wechat = list.pop() if len(list) > 0 else None
        return wechat

    def get_friend_list(self, params: dict = {}):
        """
        获取好友列表（Q0005）
        :param params:
        :return:
        """
        wxid = params.get('wxid')
        if wxid is None:
            params['wxid'] = self.get_wxid_first(params)

        data = {
            "type": "Q0005",
            "data": {
                "type": "1"
            }
        }
        result = self.req_requests(data, params)
        return result

    def get_qunliao_list(self, params: dict = {}):
        """
        获取群聊列表
        :param params:
        :return:
        """
        wxid = params.get('wxid')
        if wxid is None:
            params['wxid'] = self.get_wxid_first(params)

        data = {
            "type": "Q0006",
            "data": {
                "type": "2"
            }
        }
        result = self.req_requests(data, params)
        return result

    def send_text(self, data, params: dict = {}):
        """
        发送文本消息
        :param data:
        :param params:
        :return:
        """
        wxid = params.get('wxid')
        if wxid is None:
            wxid = self.get_wxid_first(params)
            params['wxid'] = wxid

        data = {
            "type": "Q0001",
            "data": data
        }
        result = self.req_requests(data, params)
        return result

    def push_send_text(self, data, params: dict = {}):
        """
        发送文本消息
        :param data:
        :param params:
        :return:
        """
        wechat_alert_key = f'wechat_alert'
        redisService = RedisService('redis', db=1)
        redis = redisService.get_client()
        sleep_time = 2

        rand = random.randint(100000, 999999)
        prefix = f"[{rand}] "

        try:
            taskId = redis.lpush(f'{wechat_alert_key}', json.dumps(data, ensure_ascii=False))
            self.logger.info(f"{prefix} message: {data}")

        except Exception as ex:
            self.logger.info(f"{prefix} pull exception:" + traceback.format_exc())

        finally:
            time.sleep(sleep_time)
            self.logger.info(f"{prefix} finally ++++")

        return True

    def simple_send(self, data):
        """
        通过默认配置发送文本消息
        必包含参数 type, msg，mail_title，mail_body
        :author wjh
        :date 2022-11-14
        :param data:
        :param params:
        :return:
        """
        try:
            type = data.get('type', 1000)
            mail_title = data.get('mail_title')
            mail_body = data.get('mail_body')
            mail_to = data.get('mail_to', self.config.get('mail_to'))
            mail_cc = data.get('mail_cc')
            wxid = data.get('wxid', self.config.get('aims'))

            dt = Utils.showDateTime()
            msg = f"[@,wxid=all,nick=,isAuto=true] 信息提示：\n {data.get('msg')} {dt}[emoji=D83D][emoji=DE01]"
            # msg = f"{data.get('msg')} {dt} [emoji=D83D][emoji=DE01]"
            body = {
                "channel": 'all',
                "type": type,
                "message": '推送操作',
                "data": {
                    "wxid": wxid,
                    "msg": msg
                },
                'mail_title': mail_title,  # 标题
                'mail_body': mail_body,  # 内容
                'mail_to': mail_to,  # 接收
                'mail_cc': mail_cc,  # 抄送
            }
            self.push_send_text(body)
        except Exception as ex:
            self.logger.info(f"exception:" + traceback.format_exc())
            return False

        finally:
            self.logger.info(f"finally ++++")

        return True

    def get_group_user_list(self, wxid, params: dict = {}):
        """
        获取群聊列表
        :param params:
        :param wxid:
        :return:
        """
        data = {
            "type": "Q0008",
            "data": {
                "wxid": wxid
            }
        }
        result = self.req_requests(data, params)
        return result

    def get_user_info(self, wxid,params: dict = {}):
        """
        获取群聊列表
        :param params:
        :param wxid:
        :return:
        """
        data = {
            "type": "Q0004",
            "data": {
                "wxid": wxid
            }
        }
        result = self.req_requests(data, params)
        return result
