# -*-coding:utf-8-*-
import json
import re
import time
import sys
import os
import traceback

from sqlalchemy import or_
from sqlalchemy.orm import load_only

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService
from dc.models.model import DCSiteMainTask, DCSiteTask, DCSiteListRule, DCSiteListItem, DCSiteKeywordRelation, DCSiteKeyWords
from dc.common.alchemy import query2dict
from dc.models.model import DCSiteMainTask, DCSiteTask, DCSiteList
from dc.common.alchemy import query2dict
from dc.services.mongo_service import MongoService
from dc.services.elasticsearch_service import ElasticsearchService

mysql = MySQLService()
redisService = RedisService('redis')
client1 = redisService.get_client()
log_service = LoggingService('dc_task_again.log')

mgs = MongoService()
ess = ElasticsearchService()

redis_prefix = 'laravel_database_'
redis_task_key = f'{redis_prefix}dc-main-task'
redis_task_key1 = f'{redis_prefix}dc-site-task'


class TaskAgainService:

    @staticmethod
    def main_site_list_check(params, **kwargs):
        """ 数据源重试检测（数据统计）"""
        if not params['site_list_id']:
            return {'code': 801, 'msg': '主任务id为空，请传正确参数', 'data': []}

        logger: LoggingService = kwargs.get('logger')
        task_id = int(params['site_list_id'])
        site_list_id = int(params['site_list_id'])
        siteListId = site_list_id
        session = mysql.create_session()
        try:
            ret: DCSiteList = session.query(DCSiteList).filter(DCSiteList.id == siteListId).first()
            if not ret:
                return {'code': 801, 'msg': '数据源不存在', 'data': []}

            dc_main_info = query2dict(ret)
            if not dc_main_info:
                return {'code': 801, 'msg': '数据源不存在', 'data': []}

            dc_site_list_info = dc_main_info
            #logger.info(dc_main_info)

            # mysql
            task_count = session.query(DCSiteTask).filter(DCSiteTask.siteListId == siteListId).count()
            logger.info(f"task count: {task_count}")
            main_task_count = session.query(DCSiteMainTask).filter(DCSiteMainTask.siteListId == siteListId).count()
            logger.info(f"main task count: {main_task_count}")
            session.commit()

            # mongo
            m_client = mgs.get_collection('site-task')
            myquery = {'siteListId': siteListId}
            mongo_count = m_client.count_documents(myquery)
            logger.info(f"mongo count: {mongo_count}")

            # es
            es_index = dc_site_list_info['sourceCategory']
            es = ess.get_connect()
            query = {
                 "query": {
                        "term": {
                          "siteListId": {
                            "value": siteListId
                          }
                        }
                 }
            }
            result = es.search(index=es_index, body=query)
            es_count = result['hits']['total']['value'] if result['hits']['total']['value'] else -1
            logger.info(f"es result {es_count}")

            session.close()
            return {'code': 0, 'msg': '执行成功', 'data': []}

        except:
            if session is not None:
                session.close()

            logger.info(f"重新执行主任务失败，对应main_task_id：{task_id}，失败原因：{traceback.format_exc()}")
            return {'code': 0, 'msg': '执行出现异常', 'data': []}

    @staticmethod
    def main_site_list(params, **kwargs):
        """ 数据源重试 """
        if not params['site_list_id']:
            return {'code': 801, 'msg': '主任务id为空，请传正确参数', 'data': []}

        logger: LoggingService = kwargs.get('logger', log_service)

        task_id = int(params['site_list_id'])
        site_list_id = int(params['site_list_id'])
        siteListId = site_list_id
        session = mysql.create_session()
        try:
            ret: DCSiteList = session.query(DCSiteList).filter(DCSiteList.id == siteListId).first()
            if not ret:
                return {'code': 801, 'msg': '数据源不存在', 'data': []}

            dc_main_info = query2dict(ret)
            if not dc_main_info:
                return {'code': 801, 'msg': '数据源不存在', 'data': []}

            dc_site_list_info = dc_main_info

            # mysql
            result = session.query(DCSiteTask).filter(DCSiteTask.siteListId == siteListId).delete()
            logger.info(f"delete task result {result}")
            result = session.query(DCSiteMainTask).filter(DCSiteMainTask.siteListId == siteListId).delete()
            logger.info(f"delete main task result2 {result}")
            session.commit()

            # mongo
            m_client = mgs.get_collection('site-task')
            myquery = {'siteListId': siteListId}
            result = m_client.delete_many(myquery)
            logger.info(f"mongo delete result: {result}")

            # es
            es_index = dc_site_list_info['sourceCategory']
            es = ess.get_connect()
            query = {
                 "query": {
                        "term": {
                          "siteListId": {
                            "value": siteListId
                          }
                        }
                 }
            }
            result = es.delete_by_query(index=es_index, body=query)
            logger.info(f"es delete result {result}")

            #up_sql1 = f"update DC_SiteList set lastExecTime = null where id = {dc_main_info['siteListId']}"
            #session.execute(up_sql1)
            ret.nextExecTime = None
            ret.lastExecTime = None
            session.add(ret)
            session.commit()

            session.close()
            return {'code': 0, 'msg': '执行成功', 'data': []}

        except Exception as ex:
            session.rollback()
            if session is not None:
                session.close()

            logger.info(f"重新执行主任务失败，对应main_task_id：{task_id}，失败原因：{str(ex)}")
            return {'code': 0, 'msg': f'执行出现异常: {str(ex)}', 'data': []}

    @staticmethod
    def process_site_list(params, **kwargs):
        """ 执行数据源任务 """
        if not params['site_list_id']:
            return {'code': 801, 'msg': '主任务id为空，请传正确参数', 'data': []}

        logger: LoggingService = kwargs.get('logger', log_service)
        site_list_id = int(params['site_list_id'])
        try:
            TaskAgainService.process_site_list_task(site_list_id)
            return {'code': 0, 'msg': '执行成功', 'data': []}

        except Exception as ex:
            logger.info(f"重新执行主任务失败，对应 site_list_id：{site_list_id}，失败原因：{str(ex)}")
            return {'code': 0, 'msg': f'执行出现异常: {str(ex)}', 'data': []}

    @staticmethod
    def main_task(params):
        """ 主任务重试 """
        if not params['main_task_id']:
            return {'code': 801, 'msg': '主任务id为空，请传正确参数', 'data': []}

        task_id = int(params['main_task_id'])
        session = mysql.create_session()
        try:
            ret = session.query(DCSiteMainTask).filter(DCSiteMainTask.id == task_id).first()
            if not ret:
                return {'code': 801, 'msg': '主任务不存在', 'data': []}

            dc_main_info = query2dict(ret)
            if not dc_main_info:
                return {'code': 801, 'msg': '主任务不存在', 'data': []}

            if params['is_all'] == '1':

                format_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                log_service.dcPullLog(f"开始：{format_date}")
                client1.lpush(redis_task_key, task_id)

                up_sql = f"update DC_SiteMainTask set httpStatusCode = 0, listUrlCount = 0 where id = {task_id}"
                session.execute(up_sql)
                session.commit()

                session.query(DCSiteTask).filter(DCSiteTask.mainTaskId == task_id).delete()
                session.commit()

                ret1 = session.query(DCSiteList).filter(DCSiteList.id == dc_main_info['siteListId']).first()
                if not ret1:
                    return {'code': 801, 'msg': '数据源不存在', 'data': []}

                dc_site_list_info = query2dict(ret1)
                if not dc_site_list_info:
                    return {'code': 801, 'msg': '数据源不存在', 'data': []}

                format_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                log_service.dcPullLog(f"mongo开始：{format_date}")
                m_client = mgs.get_collection('site-task')
                myquery = {"mainTaskId": task_id}
                m_client.delete_many(myquery)
                format_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                log_service.dcPullLog(f"mongo结束：{format_date}")
                format_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                log_service.dcPullLog(f"es开始：{format_date}")
                es_index = dc_site_list_info['sourceCategory']

                es = ess.get_connect()
                query = {
                    "query": {
                        "match": {
                            "main_task_id": task_id
                        }
                    }
                }
                es.delete_by_query(index=es_index, body=query)
                format_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                log_service.dcPullLog(f"es结束：{format_date}")
                last_exec_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))
                up_sql1 = f"update DC_SiteList set lastExecTime = '{last_exec_date}' where id = {dc_main_info['siteListId']}"
                session.execute(up_sql1)
                session.commit()
            else:
                ret = session.query(DCSiteTask).filter(
                    DCSiteTask.mainTaskId == task_id, or_(DCSiteTask.taskStatus == 2, DCSiteTask.analysisStatus == 2,
                                                          DCSiteTask.analysisStatus == 3)).all()

                # for info in ret:
                #     tmp = info.__dict__
                #     client1.rpush(redis_task_key1, tmp['id'])

                list1 = []
                for info in ret:
                    tmp = info.__dict__
                    client1.rpush(redis_task_key1, tmp['id'])
                    list1.append(tmp['id'])

                if len(list1) == 0:
                    session.close()
                    return {'code': 0, 'msg': '执行成功', 'data': []}

                if len(list1) == 1:
                    up_ids = list1[0]
                    up_sql1 = f"update DC_SiteTask set retryTime = 0, taskStatus = 0 where id = {up_ids}"
                else:
                    up_ids = tuple(list1)
                    up_sql1 = f"update DC_SiteTask set retryTime = 0, taskStatus = 0 where id in {up_ids}"

                session.execute(up_sql1)
                session.commit()

            session.close()
            return {'code': 0, 'msg': '执行成功', 'data': []}

        except Exception as ex:
            session.rollback()
            if session is not None:
                session.close()

            log_service.dcPullLog(f"重新执行主任务失败，对应main_task_id：{task_id}，失败原因：{str(ex)}")
            return {'code': 0, 'msg': f'执行出现异常: {str(ex)}', 'data': []}

    @staticmethod
    def process_main_task(params):
        """ 执行主任务 """
        if not params['main_task_id']:
            return {'code': 801, 'msg': '主任务id为空，请传正确参数', 'data': []}

        task_id = int(params['main_task_id'])
        session = mysql.create_session()
        try:
            ret = session.query(DCSiteMainTask).filter(DCSiteMainTask.id == task_id).first()
            if not ret:
                return {'code': 801, 'msg': '主任务不存在', 'data': []}

            # 插入成功，写入redis
            # client1.lpush(redis_task_key, task.id)
            client1.rpush(redis_task_key, task_id)
            log_service.info(f"数据写入成功，主任务id【{task_id}】")
            log_service.info(f"redis lpush: {redis_task_key} content: {task_id}")

            session.close()
            return {'code': 0, 'msg': '执行成功', 'data': []}

        except Exception as ex:
            session.rollback()
            if session is not None:
                session.close()

            log_service.dcPullLog(f"执行主任务失败，对应main_task_id：{task_id}，失败原因：{str(ex)}")
            return {'code': 0, 'msg': f'执行出现异常: {str(ex)}', 'data': []}

    @staticmethod
    def task(params):
        """ 子任务重试 """
        if not params['task_id']:
            return {'code': 801, 'msg': '任务id为空，请传正确参数', 'data': []}

        task_id = int(params['task_id'])
        session = mysql.create_session()
        try:
            ret = session.query(DCSiteTask).filter(DCSiteTask.id == task_id).first()
            if not ret:
                return {'code': 801, 'msg': '任务不存在', 'data': []}

            dc_task_info = query2dict(ret)
            if not dc_task_info:
                return {'code': 801, 'msg': '任务不存在', 'data': []}

            up_sql1 = f"update DC_SiteTask set retryTime = 0, taskStatus = 0 where id = {dc_task_info['id']}"
            session.execute(up_sql1)
            session.commit()

            client1.rpush(redis_task_key1, task_id)
            session.close()

            return {'code': 0, 'msg': '执行成功', 'data': []}
        except Exception as ex:
            session.rollback()
            if session is not None:
                session.close()

            log_service.dcPullLog(f"重新执行任务失败，对应task_id：{task_id}，失败原因：{str(ex)}")
            return {'code': 0, 'msg': f'执行出现异常:{str(ex)}', 'data': []}

    @staticmethod
    def process_site_list_task(site_list_id):
        """ 执行数据源任务 """

        # mysql = MySQLService()
        session = mysql.create_session()

        redisService = RedisService('redis')
        client1 = redisService.get_client()

        logger = log_service

        redis_prefix = 'laravel_database_'
        redis_task_key = f'{redis_prefix}dc-main-task'
        site_list_hash = f'{redis_prefix}dc-site-list-hash'

        # 手动加入主队列
        # site_list_id = 2241

        # 创建主任务
        def push_task():
            # 获取当前时间
            curr_time = time.time()
            curr_time = int(curr_time)

            # 手动加入主队列
            # site_list_id = 2241

            fields = ['id', 'detailUrl', 'sourceFrequencyType', 'sourceFrequency', 'siteUrl', 'lastExecTime', 'firstExecTime',
                      'sourceMark', 'nextExecTime', 'sourceCategory', 'sourceName', 'extra', 'siteName', 'isQuickShort',
                      'isKeyWord', 'detailDcMethod', 'sourceName', 'isHotModule', 'tags']

            ret = session.query(DCSiteList).options(load_only(*fields)).filter(DCSiteList.status == 1, DCSiteList.isUsed == 1, DCSiteList.id == site_list_id).all()
            if not ret:
                logger.info('当前执行未获取到数据源')
                exit()

            for info in ret:
                site_info = info.to_dict()

                if not site_info['id']:
                    logger.info('存在id为空的异常数据')
                    continue

                if not site_info['firstExecTime']:
                    logger.info(f"数据源【{site_info['id']}】不存在首次执行时间")
                    continue

                deal_main_task(site_info)

        def deal_time(fre_type, frequency) -> int:
            """
            处理间隔时间（秒）
            :param fre_type:
            :param frequency:
            :return:
            """
            diff_time = 0
            if fre_type == 1:
                diff_time = frequency * 60
            elif fre_type == 2:
                diff_time = frequency * 3600
            elif fre_type == 3:
                diff_time = frequency * 86400

            return diff_time

        # 处理主任务
        def deal_main_task(info):
            if 'id' not in info:
                return False

            # 获取当前提取规则
            rule_item = get_rule(info['id'])
            if rule_item == '':
                return False

            analysisMethod = 0
            if info['extra']:
                try:
                    dc_extra = json.loads(info['extra'])
                    if 'analysisMethod' in dc_extra:
                        analysisMethod = dc_extra['analysisMethod']
                except:
                    pass

            redis_arr = {'sourceCategory': info['sourceCategory'], 'siteName': info['siteName'],
                         'analysisMethod': analysisMethod, 'isQuickShort': info['isQuickShort'], 'isKeyWord': info['isKeyWord'],
                         'detailDcMethod': info['detailDcMethod'], 'sourceName': info['sourceName'], 'ruleItem': rule_item,
                         'isHotModule': info['isHotModule'], 'tags': info['tags']}
            client1.hset(site_list_hash, info['id'], json.dumps(redis_arr))
            logger.info(f"redis hset: {site_list_hash} {info['id']} content: {json.dumps(redis_arr)}")

            # res = client1.hmget(site_list_hash, site_info['id'])
            # print(json.loads(res[0]))
            if info['isKeyWord'] == 2:  # 关键词搜索
                add_main_task(info, '')
                up_site(info['id'])
            else:
                # 获取关键词，循环插入数据
                keywordRelations = session.query(DCSiteKeywordRelation).filter(DCSiteKeywordRelation.siteId == info['id']).all()
                if not keywordRelations:
                    keyWords = session.query(DCSiteKeyWords).filter(DCSiteKeyWords.status == 1).all()
                else:
                    keywordIds = []
                    for relation in keywordRelations:
                        relationArr = relation.to_dict()
                        keywordIds.append(relationArr['keywordId'])
                    keyWords = session.query(DCSiteKeyWords).filter(DCSiteKeyWords.status == 1, DCSiteKeyWords.id.in_(keywordIds)).all()

                for wordsObj in keyWords:
                    words = wordsObj.to_dict()
                    add_main_task(info, words['keyword'])

                up_site(info['id'])

        # 写主任务
        def add_main_task(info, keyWord):
            try:
                task = DCSiteMainTask(siteListId=info['id'], listUrl=info['detailUrl'], url=info['siteUrl'], keyWord=keyWord, httpStatusCode=0)
                session.add(task)
                session.commit()

                # 插入成功，写入redis
                # client1.lpush(redis_task_key, task.id)
                client1.rpush(redis_task_key, task.id)
                logger.info(f"数据源id为【{info['id']}】-关键词为【{keyWord}】的数据写入成功，主任务id【{task.id}】")
                logger.info(f"redis lpush: {redis_task_key} content: {task.id}")

            except Exception as ex:
                logger.error(f"数据源id为【{info['id']}】-关键词为【{keyWord}】的数据写入失败，失败原因：{str(ex)}")

        # 更新最后一次执行时间
        def up_site(site_id):
            return True

            last_exec_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            up_sql = f"update DC_SiteList set lastExecTime = '%s' where id = {site_id}" % last_exec_date
            try:
                session.execute(up_sql)
                session.commit()
                logger.info(f"数据源id为【{site_id}】的数据更新最后一次执行时间成功")
            except Exception as ex:
                logger.error(f"数据源id为【{site_id}】的数据更新最后一次执行时间失败，失败原因：{str(ex)}")

        # 获取数据源对应规则
        def get_rule(site_id):
            rule_res = session.query(DCSiteListRule).filter(DCSiteListRule.siteListId == site_id).all()
            if not rule_res:
                logger.info(f'数据源id【{site_id}】无对应规则，初始化任务失败')
                return ''

            rule_ids = []
            for rule_info in rule_res:
                rule = rule_info.to_dict()
                rule_ids.append(rule['id'])

            item_res = session.query(DCSiteListItem).filter(DCSiteListItem.siteRuleId.in_(rule_ids),
                                                            DCSiteListItem.status == 1).all()
            if not item_res:
                logger.info(f'数据源id【{site_id}】无对应数据项，初始化任务失败')
                return ''

            item_data = {}
            for rule_id in rule_ids:
                item_data[rule_id] = {}

            for item_info in item_res:
                item = item_info.to_dict()
                item_data[item['siteRuleId']][item['id']] = item

            return json.dumps(item_data)

            # item_data = {}
            #
            # for item_info in item_res:
            #     item = item_info.to_dict()
            #     item_data[item['id']] = item
            # print(json.dumps(item_data))
            # exit()
            # return json.dumps(item_data)

        push_task()

