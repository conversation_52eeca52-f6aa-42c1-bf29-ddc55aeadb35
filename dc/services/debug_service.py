# -*- coding:utf-8 -*-
# @Function  : 调试服务，用于程序调试日志输出
# <AUTHOR> wjh
# @Time      : 2025-01-22
# Version    : 1.0

import time
from datetime import datetime

from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService


class DebugService:

    def __init__(self, **kwargs) -> None:
        super().__init__()
        self.logger = kwargs.get('logger', LoggingService(logfile='debug_service.log'))
        self.node_write = kwargs.get('node_write', False)   # 是否每个节点实时写日志
        self.config = get_settings('debug_service')

        self.start_time = time.time()  # 程序开始时间
        self.last_time = self.start_time  # 上个节点时间
        self.node_times = []  # 存储每个节点的执行时间

    def log_node(self, node_name):
        current_time = time.time()
        elapsed_from_start = current_time - self.start_time  # 从程序开始到当前节点的时间
        elapsed_from_last = current_time - self.last_time  # 从上个节点到当前节点的时间

        # 打印调试信息
        self.logger.info(f"Node: {node_name}")
        self.logger.info(f"Elapsed time from start: {elapsed_from_start:.2f} seconds")
        self.logger.info(f"Elapsed time from last node: {elapsed_from_last:.2f} seconds")

        # 更新最后一个节点时间
        self.last_time = current_time

        # 存储当前节点的执行信息
        node_log = {
            'node': node_name,
            'elapsed_from_start': round(elapsed_from_start, 2),
            'elapsed_from_last': round(elapsed_from_last, 2)
        }
        self.node_times.append(node_log)

        if self.node_write:
            self.logger.info(f"{node_log}")

    def get_start_time(self):
        return datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S')

    def get_last_time(self):
        return datetime.fromtimestamp(self.last_time).strftime('%Y-%m-%d %H:%M:%S')

    def get_node_times(self):
        return self.node_times

