from typing import List
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import DCSiteList, DCSiteMainTask, DCSiteKeyWords, DCSiteTask


class AnalyzerService:
    """
    网页分析服务层
    """
    mysql_client = MySQLService()
    redis_client = RedisService().get_client_pool()


    @staticmethod
    def get_DCSiteList_by_id(id: int) -> DCSiteList:
        """
        根据id获取sitelist
        :param id: sitelist的id
        :return:
        """
        ret = AnalyzerService.mysql_client.create_session().query(DCSiteList).filter(DCSiteList.status == 1,
                                                                     DCSiteList.id == id).first()
        return ret

    @staticmethod
    def get_DCSiteKeyWords_lately() -> DCSiteKeyWords:
        """
        获取最新的关键词(用于测试接口)
        :return:
        """
        ret = AnalyzerService.mysql_client.create_session().query(DCSiteKeyWords).filter(DCSiteKeyWords.status == 1).order_by(
            DCSiteKeyWords.id.desc()).first()
        return ret

    @staticmethod
    def get_DCSiteMainTask_by_id(id: int) -> DCSiteMainTask:
        """
        根据id获取主任务
        :param id:
        :return:
        """
        ret = AnalyzerService.mysql_client.create_session().query(DCSiteMainTask).filter(
            DCSiteMainTask.id == id).first()
        return ret

    @staticmethod
    def get_DCSiteTasks_by_siteListId(siteListId: int) -> List[DCSiteTask]:
        """
        根据siteListId获取历史的所有相关子任务(用于去重)
        :param siteListId:
        :return:
        """
        ret = AnalyzerService.mysql_client.create_session().query(DCSiteTask).filter(DCSiteTask.siteListId == siteListId).all()
        return ret

    @staticmethod
    def add_DCSiteTask(task: DCSiteTask) -> int:
        """
        添加一个新的子任务，并推送到redis中
        :param task:
        :return:
        """
        session = AnalyzerService.mysql_client.create_session()
        session.add(task)
        session.commit()
        return task.id
