import json
import logging
import os
import traceback
from logging.handlers import TimedRotatingFileHandler
import redis
import time

from dc.common.utils import Utils
from dc.conf.settings import get_settings

new_formatter = '[%(levelname)s]%(asctime)s:%(msecs)s.%(process)d,%(thread)d#>[%(funcName)s]:%(lineno)s  %(message)s'

json_formatter = json.dumps({
    "time": "%(asctime)s",
    "levelname": "%(levelname)s",
    "message": "%(message)s",
    "levelno": "%(lineno)d",
    "process": "%(process)d",
    "func": "%(funcName)s",
    "lineno": "%(lineno)s",
    "pathname": "%(pathname)s",
})

"""
%(asctime)s 字符串形式的当前时间。默认格式是“2021-09-08 16:49:45,896”。逗号后面的是毫秒
%(created)f 时间戳, 等同于time.time()
%(relativeCreated)d 日志发生的时间相对于logging模块加载时间的相对毫秒数
%(msecs)d 日志时间发生的毫秒部分
%(levelname)s 日志级别str格式
%(levelno)s 日志级别数字形式(10, 20, 30, 40, 50)
%(name)s 日志器名称, 默认root
%(message)s 日志内容
%(pathname)s 日志全路径
%(filename)s 文件名含后缀
%(module)s 文件名不含后缀
%(lineno)d 调用日志记录函数源代码的行号
%(funcName)s 调用日志记录函数的函数名
%(process)d 进程id
%(processName)s 进程名称
%(thread)d 线程ID
%(threadName)s 线程名称
"""
fmt = logging.Formatter(new_formatter)
jsonFmt = logging.Formatter(json_formatter)

logging.basicConfig(  # 针对 basicConfig 进行配置(basicConfig 其实就是对 logging 模块进行动态的调整，之后可以直接使用)
    level=logging.INFO,  # INFO 等级以下的日志不会被记录
    # format='%(asctime)s %(filename)s[line:%(lineno)d] %(levelname)s %(message)s',  # 日志输出格式
    format=json_formatter,  # 日志输出格式
    # filename='logs/' + logfile,  # 日志存放路径(存放在当前相对路径)
    filemode='a',  # 输入模式；如果当前我们文件已经存在，可以使用 'a' 模式替代 'w' 模式
    # 与文件写入的模式相似，'w' 模式为没有文件时创建文件；'a' 模式为追加内容写入日志文件
)


class LoggingService:
    logfile = 'dc.log'
    config = {}

    def __init__(self, logfile: str = 'dc.log', formatter: str = None) -> None:
        super().__init__()

        self.logfile = logfile,
        self.logger = logging.getLogger(logfile)
        logs_path = Utils.get_project_path('logs')
        full_path = logs_path + '/' + logfile
        base_path = os.path.dirname(full_path)
        Utils.mkdir(base_path)
        # handler = logging.FileHandler(logs_path + '/' + logfile, 'a')
        handler = TimedRotatingFileHandler(full_path, when='midnight')
        formatter = formatter if formatter else 'json'
        if formatter == 'json':
            handler.setFormatter(jsonFmt)
        else:
            handler.setFormatter(fmt)

        self.logger.addHandler(handler)

    def debug(self, msg, *args, **kwargs):
        """
        Log 'msg % args' with severity 'DEBUG'.
        """
        msg = self.json_encode(msg)
        self.logger.debug(msg, *args, **kwargs)

    def info(self, msg, *args, **kwargs):
        """
        Log 'msg % args' with severity 'INFO'.
        """
        msg = self.json_encode(msg)
        self.logger.info(msg, *args, **kwargs)

    def warning(self, msg, *args, **kwargs):
        """
        Log 'msg % args' with severity 'WARNING'.
        """
        msg = self.json_encode(msg)
        self.logger.warning(msg, *args, **kwargs)

    def error(self, msg, *args, **kwargs):
        """
        Log 'msg % args' with severity 'ERROR'.
        """
        msg = self.json_encode(msg)
        self.logger.error(msg, *args, **kwargs)

    def exception(self, msg, *args, exc_info=True, **kwargs):
        """
        Convenience method for logging an ERROR with exception information.
        """
        msg = self.json_encode(msg)
        self.logger.exception(msg, *args, exc_info=True, **kwargs)

    def critical(self, msg, *args, exc_info=True, **kwargs):
        """
        Log 'msg % args' with severity 'CRITICAL'.
        """
        msg = self.json_encode(msg)
        self.logger.critical(msg, *args, exc_info, **kwargs)

    def dcPullLog(self, msg, *args, **kwargs):
        """
        log message
        :deprecated
        :param msg:
        :param args:
        :param kwargs:
        :return:
        """
        # date_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        msg = self.json_encode(msg)
        self.logger.info(msg, *args, **kwargs)

    def json_encode(self, msg):
        """json encode for logging"""
        if isinstance(msg, str):
            msg = json.dumps(msg, ensure_ascii=False)
            return msg[1:-1] if len(msg) > 1 else msg
        else:
            return json.dumps(msg, ensure_ascii=False)
