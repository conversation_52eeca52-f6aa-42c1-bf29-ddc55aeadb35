# -*- coding:utf-8 -*-
# @Function  : ELK 服务
# <AUTHOR>
# @Time      : 2024/8/7
# Version    : 1.0

import hashlib
import json
import random
import socket
import time
from datetime import datetime, timed<PERSON><PERSON>
from typing import Callable

from confluent_kafka import <PERSON><PERSON><PERSON>Ex<PERSON>, KafkaError
from sqlalchemy import create_engine, Column, Integer, String, func
from sqlalchemy.orm import Session

from dc.models.model import ELKError, DMMonitorAlert
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dateutil.parser import parse as date_parse


class ELKService:
    logger: LoggingService = None

    def __init__(self, config={}):
        default_config = {
            'debug': False,
            'version': '1.0'
        }
        self.config = {**default_config, **config}
        self.logger = config.get('logger') if config.get('logger') else LoggingService(logfile='elk_service.log')

    def error_count(self, data={}):
        """ 错误数量分析 """
        try:
            mysql = MySQLService()
            session = mysql.create_session()

            start_time_str = data.get('start_time', None)
            end_time_str = data.get('end_time', None)

            # 创建查询
            query = session.query(func.count(ELKError.id))

            # 转换字符串为 datetime 对象并添加过滤条件
            if start_time_str:
                start_time = date_parse(start_time_str)
                query = query.filter(ELKError.message_time >= start_time)
            if end_time_str:
                end_time = date_parse(end_time_str)
                # 如果结束时间只有日期部分，补充到下一天的00:00:00之前
                if end_time.time() == datetime.min.time():
                    end_time += timedelta(days=1)
                query = query.filter(ELKError.message_time < end_time)

            # 查询并统计数据总量
            count = query.scalar()
            self.logger.info(f'error_count response: {count}')
            return self.req_success([], count)
        except Exception as ex:
            self.logger.error(f"error_count exception: {str(ex)}")
            return self.req_failed(None, str(ex))
        finally:
            if session:
                session.close()

    def error_type_count(self, data={}):
        """ 错误类型分析 """
        try:
            mysql = MySQLService()
            session = mysql.create_session()

            start_time_str = data.get('start_time', None)
            end_time_str = data.get('end_time', None)
            page = int(data.get('page', 1))
            perPage = int(data.get('perPage', 20))

            query = session.query(
                ELKError.error_type,
                func.count(ELKError.id).label('count')
            )

            # 转换字符串为 datetime 对象
            if start_time_str:
                start_time = date_parse(start_time_str)
                query = query.filter(ELKError.message_time >= start_time)
            if end_time_str:
                end_time = date_parse(end_time_str)
                # 如果结束时间只有日期部分，补充到下一天的00:00:00之前
                if end_time.time() == datetime.min.time():
                    end_time += timedelta(days=1)
                query = query.filter(ELKError.message_time < end_time)

            # 按照 service 进行分组
            query = query.group_by(ELKError.error_type)

            # 获取总行数
            # total_rows_query = query.with_entities(func.count('*')).group_by(ELKError.error_type)
            # total_rows = total_rows_query.count()

            # 获取分组后的组数
            group_count = query.count()

            # 计算偏移量和限制
            offset = (page - 1) * perPage
            query = query.offset(offset).limit(perPage)

            results = query.all()
            results = [{"error_type": error_type, "count": count} for error_type, count in results]
            self.logger.info(f'error_type_count response: {results} {group_count}')
            return self.req_success(results, group_count)
        except Exception as ex:
            self.logger.error(f"error_type_count exception: {str(ex)}")
            return self.req_failed(None, str(ex))

        finally:
            if session:
                session.close()

    def error_service_count(self, data={}):
        """ 服务错误分析 """
        try:
            mysql = MySQLService()
            session = mysql.create_session()

            start_time_str = data.get('start_time', None)
            end_time_str = data.get('end_time', None)
            page = int(data.get('page', 1))
            perPage = int(data.get('perPage', 20))

            query = session.query(
                ELKError.service,
                func.count(ELKError.id).label('count')
            )

            # 转换字符串为 datetime 对象
            if start_time_str:
                start_time = date_parse(start_time_str)
                query = query.filter(ELKError.message_time >= start_time)
            if end_time_str:
                end_time = date_parse(end_time_str)
                # 如果结束时间只有日期部分，补充到下一天的00:00:00之前
                if end_time.time() == datetime.min.time():
                    end_time += timedelta(days=1)
                query = query.filter(ELKError.message_time < end_time)

            # 按照 service 进行分组
            query = query.group_by(ELKError.service)

            # 获取总行数
            # total_rows_query = query.with_entities(func.count('*')).group_by(ELKError.service)
            # total_rows = total_rows_query.count()

            # 获取分组后的组数
            group_count = query.count()

            # 计算偏移量和限制
            offset = (page - 1) * perPage
            query = query.offset(offset).limit(perPage)

            results = query.all()
            results = [{"service": service_type, "count": count} for service_type, count in results]
            self.logger.info(f'error_service_count response: {results} {group_count}')
            return self.req_success(results, group_count)

        except Exception as ex:
            self.logger.error(f"error_service_count exception: {str(ex)}")
            return self.req_failed(None, str(ex))

        finally:
            if session:
                session.close()

    def kafka_status(self, data={}):
        """ kafka 运行状态监控 """
        from dc.conf.settings import get_settings

        try:
            conf = get_settings('kafka')
            check_result = self.check_kafka_broker(conf)
            if check_result:
                self.logger.debug("Kafka broker is up and running.")
            else:
                self.logger.debug("Kafka broker is down.")

            results = {'status': check_result}
            self.logger.info(f'kafka_status response: {results}')
            return self.req_success(results)

        except Exception as ex:
            self.logger.error(f"kafka_status exception: {str(ex)}")
            return self.req_failed(None, str(ex))

    def kafka_topics(self, data={}):
        """ kafka 主题状态监控 """
        from dc.conf.settings import get_settings

        try:
            conf = get_settings('kafka')
            conf['group.id'] = 'consumer_group_elk_kafka_es'
            conf['auto.offset.reset'] = 'earliest'
            results = self.get_topics_offsets(conf, lambda topic: str(topic).startswith('elk_'))
            self.logger.info(f'kafka_topics response: {results}')
            return self.req_success(results, len(results))

        except Exception as ex:
            self.logger.error(f"kafka_topics exception: {str(ex)}")
            return self.req_failed(None, str(ex))

    def elasticsearch_status(self, data={}):
        """ 服务错误分析 """
        try:
            from dc.conf.settings import get_settings
            conf = get_settings('elasticsearch_elk')
            check_result = self.check_elasticsearch_health(conf['hosts'])
            if check_result:
                self.logger.debug("elasticsearch is up and running.")
            else:
                self.logger.debug("elasticsearch is down.")

            results = {'status': check_result}
            self.logger.info(f'elasticsearch_status response: {results}')
            return self.req_success(results)

        except Exception as ex:
            self.logger.error(f"elasticsearch_status exception: {str(ex)}")
            return self.req_failed(None, str(ex))

    def elasticsearch_count(self, data={}):
        """ elasticsearch 数量统计 """
        try:
            from dc.conf.settings import get_settings2
            from dc.services.elasticsearch_service import ElasticsearchService
            ess = ElasticsearchService(name='elasticsearch_elk')
            es = ess.get_connect()

            es_index = get_settings2('elk', 'es_index', None)
            if not es_index:
                raise Exception('elk es_index 未配置')

            start_time_str = data.get('start_time', None)
            end_time_str = data.get('end_time', None)
            index = data.get('index', es_index)

            # 创建查询
            time_filter = {}
            # 转换字符串为 datetime 对象并添加过滤条件
            if start_time_str:
                start_time = date_parse(start_time_str)
                time_filter['gte'] = start_time.strftime('%Y-%m-%d %H:%M:%S')

            if end_time_str:
                end_time = date_parse(end_time_str)
                # 如果结束时间只有日期部分，补充到下一天的00:00:00之前
                if end_time.time() == datetime.min.time():
                    end_time += timedelta(days=1)
                time_filter['lte'] = end_time.strftime('%Y-%m-%d %H:%M:%S')

            # 查询并统计数据总量
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "message2.time": time_filter
                                }
                            }
                        ]
                    }
                }
            }

            response = es.count(index=index, body=query)
            count = response['count']
            self.logger.info(f'elasticsearch_count response: {count}')
            return self.req_success([], count)
        except Exception as ex:
            self.logger.error(f"elasticsearch_count exception: {str(ex)}")
            return self.req_failed(None, str(ex))
        finally:
            if es:
                es.close()

    def req_success(self, data, count=None):
        result = {"status": 0, "data": data}
        if count:
            result['count'] = count
        return result

    def req_failed(self, data, msg='failed'):
        return {"status": 1, "data": data, 'msg': msg}

    def check_kafka_broker(self, conf: dict) -> bool:
        """
        kafka 健康检查
        :param conf: config info
        :return: bool
        """
        from confluent_kafka.admin import AdminClient, NewTopic
        from confluent_kafka import KafkaException

        try:
            admin_client = AdminClient(conf)
            cluster_metadata = admin_client.list_topics(timeout=10)
            if cluster_metadata:
                self.logger.debug("Kafka is alive")
                return True
        except KafkaException as e:
            self.logger.debug(f"KafkaException: {e}")
        except Exception as e:
            self.logger.debug(f"Exception: {e}")
        return False

    def check_elasticsearch_health(self, hosts: list) -> bool:
        """
        elasticsearch 健康检查
        :param hosts: hosts
        :return: bool
        """

        from elasticsearch import Elasticsearch, exceptions

        try:
            # 创建 Elasticsearch 客户端
            es = Elasticsearch(hosts)

            # 获取集群的健康状态
            health = es.cluster.health()

            # 检查状态字段
            status = health['status']

            if status in ['green', 'yellow']:
                self.logger.debug("Elasticsearch is up and running. Status:", status)
                return True
            else:
                self.logger.debug("Elasticsearch is in a problematic state. Status:", status)
                return False
        except exceptions.ConnectionError as e:
            self.logger.debug("Failed to connect to Elasticsearch:", str(e))
            return False

        except Exception as e:
            self.logger.debug("Failed to connect to Elasticsearch:", str(e))
            return False

    def get_topics_offsets(self, config, func_filter: Callable) -> list:
        """
        get kafka topic offsets
        :param config: config info
        :param func_filter: topic filter function
        :return: list
        """

        from confluent_kafka import Consumer, KafkaError, KafkaException, TopicPartition
        from confluent_kafka.admin import AdminClient

        # 创建 AdminClient
        admin_client = AdminClient(config)

        # 获取所有主题
        topics = admin_client.list_topics().topics

        # 用于存储结果的列表
        offsets_info = []

        # 获取每个主题及分区的偏移量信息
        for topic in topics:
            if not func_filter(topic):
                continue

            consumer = Consumer(config)
            partitions = admin_client.list_topics(topic).topics[topic].partitions

            for partition in partitions:
                tp = TopicPartition(topic, partition)
                # 获取 LOG-START-OFFSET 和 LOG-END-OFFSET
                low, high = consumer.get_watermark_offsets(tp)
                log_start_offset = low
                log_end_offset = high

                # 获取 CURRENT-OFFSET
                committed_offsets = consumer.committed([tp])
                current_offset = committed_offsets[0].offset if committed_offsets[0].offset != -1001 else log_start_offset

                # 计算 LAG
                lag = log_end_offset - current_offset

                # 将结果存入列表
                offsets_info.append({
                    'topic': topic,
                    'partition': partition,
                    'log-start-offset': log_start_offset,
                    'current-offset': current_offset,
                    'log-end-offset': log_end_offset,
                    'lag': lag
                })

            consumer.close()

        # 按 topic 和 partition 进行排序
        sorted_offsets_info = sorted(offsets_info, key=lambda x: (x['topic'], x['partition']))
        # sorted_offsets_info = [item for item in sorted_offsets_info if str(item.get('topic')).startswith('elk_')]

        # 打印结果
        # for info in sorted_offsets_info:
        #     print(info)

        return sorted_offsets_info


    def alert_detail(self, data={}):
        """ 监控报警详情 """

        mysql = MySQLService()
        session: Session = None
        try:
            session = mysql.create_session()

            alert_id = data.get('id')
            assert alert_id is not None, 'alert id is empty'
            # 创建查询
            alert: DMMonitorAlert = session.query(DMMonitorAlert).get(alert_id)
            assert alert is not None, 'alert is empty'

            data = alert.to_dict()
            data['result'] = json.loads(data.get('result', '{}'))

            return self.req_success(data)
        except Exception as ex:
            self.logger.error(f"alert_detail exception: {str(ex)}")
            return self.req_failed(None, str(ex))
        finally:
            if session:
                session.close()

    def alert_list(self, data={}):
        """ 监控报警列表 """
        mysql = MySQLService()
        session: Session = None
        try:

            session = mysql.create_session()
            start_time_str = data.get('start_time', None)
            end_time_str = data.get('end_time', None)
            page = int(data.get('page', 1))
            perPage = int(data.get('perPage', 20))

            query = session.query(DMMonitorAlert)

            # 转换字符串为 datetime 对象
            if start_time_str:
                start_time = date_parse(start_time_str)
                query = query.filter(DMMonitorAlert.createdAt >= start_time)
            if end_time_str:
                end_time = date_parse(end_time_str)
                # 如果结束时间只有日期部分，补充到下一天的00:00:00之前
                if end_time.time() == datetime.min.time():
                    end_time += timedelta(days=1)
                query = query.filter(DMMonitorAlert.createdAt < end_time)

            # 按照 service 进行分组
            # query = query.group_by(ELKError.error_type)

            # 获取总行数
            # total_rows_query = query.with_entities(func.count('*')).group_by(ELKError.error_type)
            # total_rows = total_rows_query.count()

            # 获取分组后的组数
            group_count = query.count()

            # 计算偏移量和限制
            offset = (page - 1) * perPage
            query = query.offset(offset).limit(perPage)
            results = query.all()

            results = [item.to_dict() for item in results]
            self.logger.info(f'error_type_count response: {results} {group_count}')
            return self.req_success(results, group_count)
        except Exception as ex:
            self.logger.error(f"error_type_count exception: {str(ex)}")
            return self.req_failed(None, str(ex))

        finally:
            if session:
                session.close()