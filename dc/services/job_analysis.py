# -*-coding:utf-8-*-
import os
import sys
import random
import time
import re
import urllib

import chardet
import parsel
import requests
from urllib.parse import quote

from selenium import webdriver
from selenium.webdriver import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from dc.services.mysql_service import MySQLService

log_service = LoggingService('dc_check_analysis.log')
redis_service = RedisService('redis')
client1 = redis_service.get_client()

mysql = MySQLService()
session = mysql.create_session()


class JobAnalysis:

    # 验证规则
    @staticmethod
    def job_analysis(params):
        # headers = {
        #     'authority': 'static.zhipin.com',
        #     'method': 'GET',
        #     'path': '/v2/web/geek/js/socket.js?t=1645165512072',
        #     'scheme': 'https',
        #     'accept': '*/*',
        #     'accept-encoding': 'gzip, deflate, br',
        #     'accept-language': 'zh-CN,zh;q=0.9',
        #     'host': 'www.zhipin.com',
        #     'origin': 'https://www.zhipin.com',
        #     'referer': 'https://www.zhipin.com/web/geek/job?query=%E7%99%BE%E5%BA%A6&city=100010000&page=5',
        #     'cookie': 'wd_guid=5e5ae98a-2a45-41bd-9e67-4b24c43b9a6b; historyState=state; _bl_uid=3bl7C7d67Uyba3cRgyep3R29ynC9; _9755xjdesxxd_=32; YD00951578218230%3AWM_TID=N9nd5LRPSyhFUVRQEVKBTPnnmjJ2Z4bF; lastCity=100010000; sid=sem_pz_bdpc_dasou_title; Hm_lvt_194df3105ad7148dcf2b98a91b5e727a=1661916035,1662432218,1662516196,1663041279; __g=sem_pz_bdpc_dasou_title; YD00951578218230%3AWM_NI=jikDrTZQbOptoOElO9uLmq4QFcsI5j5IsQ0UZVq8zuMcMU2GF2G8Zu5fZpKZqyxsyNGnfugAWtTonIoVtOK2B2HFDupJhkKk3%2FMofHMZxEYOZZEp8mL06mJDLs0hKKJZWGo%3D; YD00951578218230%3AWM_NIKE=9ca17ae2e6ffcda170e2e6ee94d868ac9bfa91c180f1bc8fb7c85a969a9f86d449acacfdb0e76a969af796b62af0fea7c3b92aaab7a5a5f1609a8faeb0f380ad918b8dce33b794a392b75b86bdbdaab862f797bdb2b35f9a8abfd9f97f8fbd83b6b14d86ee89d9d97ff7ba9691ca52b3a7b894fc4685b2aa96e64b95ac9c90ed43ad8abcb0ca3a82ee88a7c77fb496a88cca61f1f18390e5448f86b8a6dc459a868b91bb6790f0fa8ec95090ad0089ca418a8699a8ee37e2a3; gdxidpyhxdE=0QoIo3feWlsyUCWpNP32yAOmg9CsSN0BNHa%5C2XRQm53TNWgktM%2Fth%2BK5j1LAIQJLSuvDisytCLi%5CIpaWwvT%2Bc8ZEfrWVPj4lheVn4VMCr%2B%2FIXfAnR4I5zLgUWB6By84e0i3ykYvxJBfAkN2%5C%2BgAXvAbvk4sqQ8ppzLrjGlWy3TgiUn91%3A1663054297850; Hm_lpvt_194df3105ad7148dcf2b98a91b5e727a=1663054876; __zp_stoken__=62b5eaXhVSmo7A2p%2BRSZNd20%2FLSYkOkleNGR6XTF4TG8KO2cSNUBNWD4oGTg%2FbE5UJm4JB2xEKDApch0YQHBDQyMZO1loYD0xc2ZbV3M9AnpSCXUZFiVENhEAKTtdWDAnZFc7Dj9OXUdgPGE%3D; __c=1663041279; __l=l=%2Fwww.zhipin.com%2Fweb%2Fgeek%2Fjob%3Fquery%3D%25E7%2599%25BE%25E5%25BA%25A6%26city%3D100010000%26page%3D5&r=https%3A%2F%2Fwww.baidu.com%2Fother.php%3Fsc.Kf0000aekflRNvwhJE3UA6atJZfmEzE71kyg5EQuN2EyzFbvZG2c3apRpePS0ed5tELjEydo3NmxRPdxZo8j9YyCH2m856mBCPI0NiK0DbTfclYc4H4q8DSCHJF8frcOpzKHipTh6LnwzPgkQO_V_DOstNlCEPSKJB042U6NYeoJHe46wScy24tDJcqPW8gHkvrdquuYNoiGunR0bEbuIjaW75RI.7D_NR2Ar5Od663rj6t8AGSPticrtXFBPrM-kt5QxIW94UhmLmry6S9wiGyAp7BEIu80.TLFWgv-b5HDkrfK1ThPGujYknHb0THY0IAYqmhq1TsKdTvNzgLw4TARqn0K9u7qYXgK-5Hn0IvqzujdBULP10ZFWIWYs0ZNzU7qGujYkPHnvrj64P1fs0Addgv-b5HDYPWcsnWD10AdxpyfqnHDvnWn1PHn0UgwsU7qGujYknW6zP6KsI-qGujYs0A-bm1dcfbD0TA-b5HD0mv-b5Hn3PfKWThnqPH0kP1T%26dt%3D1663048437%26wd%3Dboss%26tpl%3Dtpl_12826_27888_0%26l%3D1536889740%26us%3DlinkVersion%253D1%2526compPath%253D10036.0-10032.0%2526label%253D%2525E4%2525B8%2525BB%2525E6%2525A0%252587%2525E9%2525A2%252598%2526linkType%253D%2526linkText%253D&g=%2Fwww.zhipin.com%2F%3Fsid%3Dsem_pz_bdpc_dasou_title&s=3&friend_source=0&s=3&friend_source=0; __a=64149207.1661327262.1662516196.1663041279.***********',
        #     'sec-ch-ua-mobile': '?0',
        #     'sec-ch-ua-platform': "Windows",
        #     'sec-fetch-dest': 'script',
        #     'sec-fetch-mode': 'no-cors',
        #     'sec-fetch-site': 'same-site',
        #     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36'
        # }

        # url = "https://www.zhipin.com/web/geek/job?query=%E7%99%BE%E5%BA%A6&city=100010000&page=5"
        # response = requests.get(url, headers=headers)
        headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN, zh;q=0.9",
            "Connection": "keep-alive",
            "Content-Length": 414,
            "Content-Type": "application/json;charset=UTF-8;",
            "Cookie": "__gc_id=f6167f86ade74adfb6e03ba617c28324; __uuid=1665986943078.58; __tlog=1665986943084.78%7C00000000%7C00000000%7C00000000%7C00000000; Hm_lvt_a2647413544f5a04f00da7eee0d5e200=1665986943; Hm_lpvt_a2647413544f5a04f00da7eee0d5e200=1665986943; acw_tc=276077d616659869423782844e158c8cfbecc41921277f5373e444cc606e82; __session_seq=3; __uv_seq=3",
            "Host": "apic.liepin.com",
            "Origin": "https://www.liepin.com",
            "Referer": "https://www.liepin.com/",
            "sec-ch-ua": '"Chromium";v="106", "Google Chrome";v="106", "Not;A=Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': "Windows",
            'sec-fetch-dest': 'script',
            'sec-fetch-mode': 'no-cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36',
        }
        url = ' https://apic.liepin.com/api/com.liepin.searchfront4c.pc-search-job'
        params = {
            "data": {
                "mainSearchPcConditionForm": {
                    "city": "010",
                    "dq": "010",
                    "pubTime": "",
                    "currentPage": 2,
                    "pageSize": 40,
                    "key": "滴滴",
                    "suggestTag": "",
                    "workYearCode": "0",
                    "compId": "",
                    "compName": "",
                    "compTag": "",
                    "industry": "",
                    "salary": "",
                    "jobKind": "",
                    "compScale": "",
                    "compKind": "",
                    "compStage": "",
                    "eduLevel": ""
                },
                "passThroughForm": {
                    "ckId": "p7vi3i97wb8z7d6p9f1oiihqa7488249",
                    "scene": "page",
                    "skId": "",
                    "fkId": "",
                    "sfrom": "search_job_pc"
                }
            }
        }

        response = requests.post(url, headers=headers, params=params)
        print(response)
        exit(1)
        print(response.content)
        return 'ok'
        return response.content

        user_Agent = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36',
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.95 Safari/537.36 OPR/26.0.1656.60',
            'Mozilla/5.0 (Windows NT 5.1; U; en; rv:1.8.1) Gecko/20061208 Firefox/2.0.0 Opera 9.50',
            'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; en) Opera 9.50',
            'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:34.0) Gecko/20100101 Firefox/34.0',
            'Mozilla/5.0 (X11; U; Linux x86_64; zh-CN; rv:1.9.2.10) Gecko/20100922 Ubuntu/10.10 (maverick) Firefox/3.6.10',
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/534.57.2 (KHTML, like Gecko) Version/5.1.7 Safari/534.57.2',
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.71 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.64 Safari/537.11',
            'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/534.16 (KHTML, like Gecko) Chrome/10.0.648.133 Safari/534.16',
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/30.0.1599.101 Safari/537.36',
            'Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; rv:11.0) like Gecko',
            'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.1 (KHTML, like Gecko) Chrome/21.0.1180.71 Safari/537.1 LBBROWSER',
            'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; LBBROWSER)',
            'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; WOW64; Trident/5.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; Media Center PC 6.0; .NET4.0C; .NET4.0E; QQBrowser/7.0.3698.400)',
            'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; QQDownload 732; .NET4.0C; .NET4.0E)',
            'Mozilla/5.0 (Windows NT 5.1) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.84 Safari/535.11 SE 2.X MetaSr 1.0',
            'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; Trident/4.0; SV1; QQDownload 732; .NET4.0C; .NET4.0E; SE 2.X MetaSr 1.0)',
        ]

        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.8',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Cookie': '__uuid=1661759174520.98; gr_user_id=41d4b52f-de5d-4d6f-86c6-5fbfc8f57b2d; __gc_id=ba0095418a3d465caefd1ba3059a9f12; __s_bid=8ae42d2bff0cf43dcff1b68a8084fc4ce0fc; gr_session_id_97dcf586237881ba=b075b05f-b31c-4950-82b0-1af692451a45; __tlog=1662530507752.17|00000000|00000000|s_00_pz0|s_00_pz0; Hm_lvt_a2647413544f5a04f00da7eee0d5e200=1662082829,1662342209,1662427529,1662530520; fe_se=-1662537024554; __session_seq=21; __uv_seq=21; Hm_lpvt_a2647413544f5a04f00da7eee0d5e200=1662537765',
            'Host': 'www.liepin.com',
            'Referer': 'https://www.liepin.com/zhaopin/?&jobTitles=&fromSearchBtn=2&ckid=66b9b401d4cbc8f3&d_=&isAnalysis=true&&init=-1&\
        searchType=1&dqs=010&industryType=industry_01&jobKind=2&&°radeFlag=1&industries=040&salary=100$999&&&key=\
        %E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90&headckid=1df6064fb78a4c7c&d_pageSize=40&siTag=ZFDYQyfloRvvhTxLnVV_Qg%7EJC8W0LLXNSaYEa5s\
        -pFFNQ&d_headId=7afc6ea79fd6b57ff64af3acaa62a467&d_ckId=2a62b38677d7ed1e7c0a0cd919475b0b&d_sfrom=search_unknown&d_&curPage=1',
            'Upgrade-Insecure-Requests': '1'
        }

        name = 'liepin'
        url0 = 'https://www.liepin.com'
        url1 = 'https://www.liepin.com/job/pn'
        url2 = '/?d_pageSize=40&d_headId=af1e7099f9b8206fd0cec1840bab13b6&d_ckId=af1e7099f9b8206fd0cec1840bab13b6&d_sfrom=search_rencai&d_curPage=0'
        page = 100
        link = "https://www.liepin.com/zhaopin/?inputFrom=www_index&workYearCode=0&key=百度&scene=input&ckId=2b2lcwjokd8oo03atlpzqyk9fpxkssb8&dq="
        ua = random.choice(user_Agent)
        headers['User_Agent'] = ua
        response = requests.get(link, headers=headers)
        return response.content

        headers = {
            'Referer': 'https://www.liepin.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36'
        }
        i = 1
        url = 'https://www.liepin.com/zhaopin/?industryType=&jobKind=&sortFlag=15°radeFlag=0&industries=&salary=&compscale=&key=Python&clean_condition=&headckid=4a4adb68b22970bd&d_pageSize=40&siTag=p_XzVCa5J0EfySMbVjghcw~fA9rXquZc5IkJpXC-Ycixw&d_headId=62ac45351cdd7a103ac7d50e1142b2a0&d_ckId=62ac45351cdd7a103ac7d50e1142b2a0&d_sfrom=search_fp&d_curPage=0&curPage={}'.format(i)
        reponse = requests.get(url, headers=headers)
        return reponse.content


        chrome_options = Options()
        # chrome_options.add_argument('--headless')
        # chrome_options.add_argument('--no-sandbox')
        # chrome_options.add_argument('--disable-gpu')
        # chrome_options.add_argument('disable-blink-features=AutomationControlled')
        driver = webdriver.Chrome(options=chrome_options)


        # 51job
        driver.get(f"https://www.liepin.com/zhaopin/?key=%E7%99%BE%E5%BA%A6&currentPage=1")
        time.sleep(3)
        return driver.page_source

        keyboard = '百度'
        i = 1
        link = "https://www.liepin.com/zhaopin/?key=" + quote(keyboard) + "&currentPage=" + str(i)
        # request headers information.
        headers = {"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.10; rv:47.0) Gecko/20100101 Firefox/47.0"}
        # send request.
        req = urllib.request.Request(link, headers=headers)
        # get response information.
        response = urllib.request.urlopen(req)
        # set utf-8 format to prevent garbled code.
        html = response.read().decode("utf-8")
        return html
        link = "https://www.liepin.com/zhaopin/?key=百度&currentPage=1"
        # headers = {
        #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        # }
        headers = {"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.10; rv:47.0) Gecko/20100101 Firefox/47.0"}
        r = requests.get(f"{link}", headers=headers)
        r.encoding = "UTF-8"
        if r.status_code != 200:
            return {'code': 801, 'msg': '当前页面无法打开，请稍后重试', 'data': []}

        info_encode = chardet.detect(r.content).get('encoding', 'UTF-8')
        html = r.content.decode(info_encode, 'ignore')
        return 'ok'
        return html

        chrome_options = Options()
        # chrome_options.add_argument('--headless')
        # chrome_options.add_argument('--no-sandbox')
        # chrome_options.add_argument('--disable-gpu')
        # chrome_options.add_argument('disable-blink-features=AutomationControlled')
        driver = webdriver.Chrome(options=chrome_options)

        # 51job
        driver.get(f"{params['url']}")
        input_obj = driver.find_element('xpath', '//input[@id="kwdselectid"]')
        input_obj.send_keys(f"{params['company']}")
        time.sleep(2)
        input_obj.send_keys(Keys.ENTER)
        time.sleep(5)

        arr = []
        for i in range(1, 2):
            try:
                if i != 1:
                    input_jump = driver.find_element('xpath', '//input[@id="jump_page"]')
                    input_jump.clear()
                    input_jump.send_keys(f"{i}")
                    time.sleep(2)
                    jump_but = driver.find_element('xpath', '//span[@class="og_but"]')
                    jump_but.click()
                    time.sleep(5)

                job_list = driver.find_elements('xpath', '//div[@class="j_joblist"]/div[@class="e"]')
                for job_obj in job_list:
                    tmp = {}
                    job_name = job_obj.find_element('xpath',
                                                    './a[@class="el"]/p[@class="t"]/span[@class="jname at"]').text
                    tmp['job_name'] = job_name
                    job_time = job_obj.find_element('xpath', './a[@class="el"]/p[@class="t"]/span[@class="time"]').text
                    tmp['public_time'] = job_time
                    job_sal = job_obj.find_element('xpath', './a[@class="el"]/p[@class="info"]/span[@class="sal"]').text
                    tmp['job_sal'] = job_sal
                    job_education = job_obj.find_element('xpath',
                                                         './a[@class="el"]/p[@class="info"]/span[@class="d at"]').text
                    education = job_education.split('|')
                    tmp['education'] = education[2]
                    tmp['work_experience'] = education[1]
                    tmp['area'] = education[0]
                    tmp['job_url'] = job_obj.find_element('xpath', './a[@class="el"]').get_attribute('href')
                    arr.append(tmp)
            except Exception as e:
                print(e)
                driver.close()
                return '页码错误，已执行完成'

                # if div_obj:
                #     action_chains = ActionChains(driver)
                #     action_chains.click_and_hold(div_obj)
                #     action_chains.pause(0.2)
                #     action_chains.move_by_offset(258 - 100, 0)
                #     action_chains.pause(0.4)
                #     action_chains.move_by_offset(100, 0)
                #     action_chains.pause(0.3)
                #     action_chains.release()
                #     action_chains.perform()
                # .drag_and_drop_by_offset(div_obj, 258, 0).perform()

        return arr

        # 猎聘
        # driver.get(f"{params['url']}")
        # input_obj = driver.find_element('xpath', '//input[@class="jsx-3599059289"]')
        # input_obj.send_keys(f"{params['company']}")
        # time.sleep(1)
        # input_obj.send_keys(Keys.ENTER)
        # time.sleep(5)
        #
        # arr = []
        # original_window = driver.current_window_handle
        # driver.switch_to.window(driver.window_handles[-1])
        #
        # max_num = driver.find_element('xpath', '//*[@id="lp-search-job-box"]/div[3]/section[1]/div[2]/ul/li[last()-1]/a').text
        # max_num = int(max_num)
        # for i in range(1, max_num + 1):
        #     if 1 < i < max_num:
        #         driver.find_element('xpath', '//button[@class="ant-pagination-item-link"]').click()
        #         time.sleep(3)
        #
        #     job_list = driver.find_elements('xpath', '//div[@class="job-list-box"]/div')
        #     driver.implicitly_wait(10)
        #     for job_obj in job_list:
        #         tmp = {}
        #         job_name = job_obj.find_element('xpath', './div/div[1]/div/a/div[1]/div/div').text
        #         tmp['job_name'] = job_name
        #         job_time = job_obj.find_element('xpath', './div/div[1]/div/a/div[1]/div/div[2]/span[2]').text
        #         tmp['job_time'] = job_time
        #         job_sal = job_obj.find_element('xpath', './div/div[1]/div/a/div[1]/span').text
        #         tmp['job_sal'] = job_sal
        #         job_description = job_obj.find_element('xpath', './div/div[1]/div/a/div[2]/span[1]').text
        #         tmp['job_description'] = job_description
        #         arr.append(tmp)
        #
        # driver.switch_to.window(original_window)
        # driver.close()
        #
        # return arr

        # # boss
        # driver.get(f"{params['url']}")
        # input_obj = driver.find_element('xpath', '//input[@class="ipt-search"]')
        # input_obj.send_keys(params['company'])
        # rand1 = random.uniform(0, 2)
        # time.sleep(rand1)
        # input_obj.send_keys(Keys.ENTER)
        # rand2 = random.uniform(2, 4)
        # time.sleep(rand2)
        #
        # # 关闭登录页
        # try:
        #     driver.find_element('xpath', '//div[@class="sign-form"]')
        #     rand3 = random.uniform(1, 2)
        #     time.sleep(rand3)
        #     driver.find_element('xpath', '//i[@class="icon-close"]').close()
        # finally:
        #     max_num = driver.find_element('xpath', '//div[@class="options-pages"]/a[last()-1]').text
        #     max_num = int(max_num)
        #     boss_arr = []
        #     for i in range(1, max_num + 1):
        #         if 1 < i < max_num:
        #             driver.find_element('xpath', '//i[@class="ui-icon-arrow-right"]').click()
        #             rand4 = random.uniform(2, 4)
        #             time.sleep(rand4)
        #
        #         job_list = driver.find_elements('xpath', '//ul[@class="job-list-box"]/li')
        #         driver.implicitly_wait(10)
        #         for job_obj in job_list:
        #             tmp = {}
        #             job_name = job_obj.find_element('xpath', './div[@class="job-card-body clearfix"]//span[@class="job-name"]').text
        #             tmp['job_name'] = job_name
        #             job_time = job_obj.find_element('xpath', './div[@class="job-card-body clearfix"]//span[@class="job-area"]').text
        #             tmp['job_time'] = job_time
        #             job_sal = job_obj.find_element('xpath', './div[@class="job-card-body clearfix"]//span[@class="salary"]').text
        #             tmp['job_sal'] = job_sal
        #             job_description = job_obj.find_element('xpath', './div[@class="job-card-body clearfix"]//ul[@class="tag-list"]/li[1]').text
        #             tmp['job_description'] = job_description
        #
        #             boss_arr.append(tmp)
        #
        #         time.sleep(3)
        #
        #     driver.close()
        #
        #     return boss_arr

    @staticmethod
    def get_char_set(driver):
        html_encoding = driver.find_element('xpath', '/html/head/meta[1]')
        html_encoding1 = html_encoding.get_attribute('content')
        if html_encoding1:
            re_encoding = "(?<=charset=)(.+)"
            arr = re.findall(f"{re_encoding}", html_encoding1)
            re_encoding1 = arr[0] if arr else 'utf-8'
        else:
            re_encoding1 = html_encoding.get_attribute('charset')

        return re_encoding1

    @staticmethod
    def deal_text(obj):
        split = "/n"
        tmp_list = [x.text for x in obj if x.text.strip(' ')]
        str1 = split.join(tmp_list)

        return str1

    @staticmethod
    def del_tmp_html():
        file_list = os.listdir('./')
        for file_name in file_list:
            if file_name == 'tmp_html.html':
                os.remove(os.path.abspath(f"tmp_html.html"))

    @staticmethod
    def selector_xpath(html, craw_rule):
        selector = parsel.Selector(text=html)
        xpath_obj = selector.xpath(f"{craw_rule}")
        craw_arr = re.findall(f"text\(\)", f"{craw_rule}")
        if craw_arr:
            str_arr = xpath_obj.getall()
        else:
            str_arr = xpath_obj.xpath(".//text()").getall()

        deal_arr = [tmp_str for tmp_str in str_arr if tmp_str.strip(' ')]

        flag = "/n"
        str1 = flag.join(deal_arr)

        return str1

    @staticmethod
    def selector_css(html, craw_rule):
        selector = parsel.Selector(html)
        css_obj = selector.css(f"{craw_rule}")
        craw_arr = re.findall(f"text\(\)", f"{craw_rule}")
        if craw_arr:
            str_arr = css_obj.getall()
        else:
            str_arr = css_obj.xpath(".//text()").getall()

        deal_arr = [tmp_str for tmp_str in str_arr if tmp_str.strip(' ')]

        flag = "/n"
        str1 = flag.join(deal_arr)

        return str1

    @staticmethod
    def html_replace_str(html):
        html = html.replace("\n", '')
        html = html.replace("\r", '')
        html = html.replace("\t", '')
        html = html.replace("<br>", '')

        return html
