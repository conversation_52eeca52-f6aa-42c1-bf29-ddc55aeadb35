# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/1/24
# Version    : 1.0

import hashlib
import random
import time

from dc.common.sign_helper import SignHelper


class SignService:
    def __init__(self, config):
        assert 'appid' in config and config['appid'], 'appid is empty'
        assert 'app_secret' in config and config['app_secret'], 'app_secret is empty'

        default_config = {
            'debug': False,
            'version': '1.0'
        }
        self.config = {**default_config, **config}

    def valid_sign(self, data):
        assert 'appid' in data and data['appid'], 'appid is empty'
        assert 'timestamp' in data and data['timestamp'], 'timestamp is empty'
        assert 'version' in data and data['version'], 'version is empty'
        assert 'sign' in data and data['sign'], 'sign is empty'

        app = self.config
        assert app, 'app is empty'

        sign = SignHelper.get_sign(data, app['app_secret'])
        if sign == data['sign']:
            if self.config['debug']:
                print('Signature is correct')
            return True
        else:
            raise Exception(f'Signature verification failed, input [{data["sign"]}] correct value [{sign}]')

    def sign(self, data):
        app = self.config
        sign = SignHelper.get_sign(data, app['app_secret'])

        if self.config['debug']:
            print(f'Signature: {sign}')

        return sign


