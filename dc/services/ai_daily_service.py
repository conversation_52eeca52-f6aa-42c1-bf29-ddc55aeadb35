import datetime
import math
import os
import subprocess
import traceback

from pdf2image import convert_from_path
from pptx import Presentation
from jwtools.io_util import *
from pptx.action import ActionSetting
from pptx.enum.action import PP_ACTION_TYPE
import pptx

from pptx.dml.color import RGBColor

from dc.models.model import DCSiteTask

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService


class AIDailyService:

    def text2ppt(self, text, filename, url):
        # 定义需要替换的文案的一个数组
        replace_words = [
            "{{title}}",  # 标题
            "{{company range}}",  # 申报公司范围
            "{{over time}}",  # 申报截止时间
            "{{interface unit}}",  # 归口单位
            "{{support ways}}",  # 支持方式
            "{{Policy Basis}}",  # 政策依据
        ]
        # 定义一个需要替换其他的文案的数组
        replace_other = "{{other}}"

        # 定义一个需要替换的目标文案的数组  包含  支持领域 申报条件  组织方式 申报程序 注意事项 其他内容
        replace_target = {
            "支持领域": "{{support_area}}",  # 支持领域
            "申报条件": "{{request_condition}}",  # 申报条件
            "组织方式": "{{organization_type}}",  # 组织方式
            "申报程序": "{{request_program}}",  # 申报程序
            "注意事项": "{{note}}",  # 注意事项
            "其他内容": "{{other_content}}",  # 其他内容
        }
        test_dict = {
            "标题": "{{title}}",
            "可申报企业范围": "{{company range}}",
            "申报公司范围": "{{company range}}",
            "申报截止时间": "{{over time}}",
            "归口单位": "{{interface unit}}",
            "支持方式": "{{support ways}}",
            "政策依据": "{{Policy Basis}}",
        }
        text = text.replace("\r", "")
        # text = read_text_from_file("result.txt")
        context_slice = text.split("###")
        if len(context_slice) <= 1:
            context_slice = text.split("##")

        print(context_slice)
        print(len(context_slice))
        d = {}
        others = []
        d["{{title}}"] = "没有找到标题"
        for s in context_slice:
            # 去掉s结尾的\n
            s = s.rstrip("\n")
            tmp = s.split("\n")
            tmp = [x for x in tmp if x != '']
            print(tmp)
            if len(tmp) == 1:
                title_arr = tmp[0].split("：")
                # if (len(titleArr) == 2) and (titleArr[0] == "标题"):
                if (len(title_arr) == 2) and ("标题" in title_arr[0]):
                    print("找到标题:为" + title_arr[1])
                    d["{{title}}"] = title_arr[1]
                # 如果没有找到标题, 并且 当前行里 有且只有一个 # 证明是一级标题 然后根据#切分 拿到最后一个值当标题
                if d["{{title}}"] == "没有找到标题" and tmp[0].count("#") == 1:
                    title_arr = tmp[0].split("#")
                    d["{{title}}"] = title_arr[-1]
                    print("找到一级标题:为" + d["{{title}}"])

            else:
                if not tmp:
                    continue
                for k, v in test_dict.items():
                    if k == tmp[0].strip():
                        # 拼接 以tmp 为数组的内容作为一个字符串
                        d[v] = "".join(tmp[1:])
                        continue
                for k, v in replace_target.items():
                    if k == tmp[0].strip():
                        others.append(k)
                        others.append("".join(tmp[1:]))
        print(d, "." * 50)
        d["others"] = others
        # 正文内容页总页数
        total_page_num = 6
        # 内容占位页
        total_other_page_nums = math.ceil(len(d['others']) / 4)
        # 其他内容页起始位置
        other_page_start = 3
        # 其他内容删除位置
        del_other_page_start = other_page_start + total_other_page_nums
        print(del_other_page_start, other_page_start, total_page_num, total_other_page_nums, len(d['others']))
        os_path = os.path.dirname(os.path.realpath(__file__))
        prs = Presentation(os_path + '/../../data/ai_daily_template/template2023.pptx')
        # copy倒数第二页的ppt 并增加一页相同的页放在最后一页前面
        # 循环
        if total_page_num > del_other_page_start:
            for i in range(del_other_page_start, total_page_num):
                rId = prs.slides._sldIdLst[i].rId
                prs.part.drop_rel(rId)
                del prs.slides._sldIdLst[i]

        replace_other_place = 0

        # 从幻灯片索引列表中删除
        for slide in prs.slides:
            shapes = slide.shapes
            for shape in shapes:
                if shape.has_text_frame:
                    tf = shape.text_frame
                    for p in tf.paragraphs:
                        r, g, b = 0, 0, 0
                        #
                        # print(p.font.color.rgb)

                        # 获取文字阴影设置
                        # shadow = run.font.shadow
                        # break

                        if p.text in replace_words:
                            if p.text != "{{title}}":
                                for run in p.runs:
                                    try:
                                        r, g, b = run.font.color.rgb
                                    except AttributeError:
                                        pass

                                    size = run.font.size
                                    name = run.font.name
                                    bold = run.font.bold
                                p.text = d[p.text]
                                # 设置字体颜色跟原来的字体颜色相同
                                p.font.color.rgb = RGBColor(r, g, b)
                                p.font.size = size
                                p.font.name = "宋体"
                                p.font.bold = bold
                            else:
                                for run in p.runs:
                                    try:
                                        r, g, b = run.font.color.rgb
                                    except AttributeError:
                                        pass

                                    size = run.font.size
                                    name = run.font.name
                                    bold = run.font.bold
                                print(r, g, b, p.text, name, size, bold)
                                p.text = d[p.text]
                                # 设置字体颜色跟原来的字体颜色相同
                                p.font.color.rgb = RGBColor(r, g, b)
                                p.font.size = size
                                p.font.name = name
                                p.font.bold = bold

                        if p.text == replace_other:
                            print("get other to replace", len(d['others']), replace_other_place)
                            if len(d['others']) > replace_other_place:
                                print(p.text)
                                for run in p.runs:
                                    try:
                                        r, g, b = run.font.color.rgb
                                    except AttributeError:
                                        pass

                                    size = run.font.size
                                    name = run.font.name
                                    bold = run.font.bold
                                p.text = d["others"][replace_other_place]
                                replace_other_place += 1
                                # 设置字体颜色跟原来的字体颜色相同
                                p.font.color.rgb = RGBColor(r, g, b)
                                p.font.size = size
                                p.font.name = "宋体"
                                p.font.bold = bold
                                # p.font.shadow = shadow
                            else:
                                p.text = ''
                        if p.text == "原文链接 : {{url}}":
                            print("source content", other_page_start, del_other_page_start)
                            # 如果url = 空 证明没有原文 去掉所有原文链接的字
                            if url == '':
                                p.text = ""
                                other_page_start += 1
                                continue
                            if math.ceil(other_page_start / 2) == math.ceil(del_other_page_start / 2):
                                # 设置一个ppt 文字的链接
                                p.text = "原文链接 : "
                                r = p.add_run()
                                r.hyperlink.address = url
                                r.text = d["{{title}}"]
                                for run in p.runs:
                                    try:
                                        r, g, b = run.font.color.rgb
                                    except AttributeError:
                                        pass
                                    size = run.font.size
                                    name = run.font.name
                                    bold = run.font.bold
                                # p.text = "原文链接 : " + d["{{title}}"]
                                # # 设置字体颜色跟原来的字体颜色相同
                                # # p.font.color.rgb = RGBColor(r, g, b)
                                r.font.size = size
                                r.font.name = name
                                r.font.bold = bold
                            else:
                                p.text = ""
                                other_page_start += 1
        save_url = os_path + '/../../data/ai_daily/' + filename + '.pptx'
        prs.save(save_url)
        return save_url

    def ppt2pdf(self, filename):
        pdf_file = str(os.path.splitext(filename)[0]) + '.pdf'
        pdf_path = os.path.dirname(os.path.splitext(pdf_file)[0])
        print(filename, pdf_file, pdf_path, "pdf----------------------------")
        command = ['libreoffice7.5', '--headless', '--convert-to', 'pdf', filename, '--outdir', pdf_path]
        #command = ['e:/chat/WeChat Files/andrace/FileStorage/File/2023-06/libreoffice_proxy.bat', '--headless',
        #           '--convert-to', 'pdf', filename, '--outdir', pdf_path]

        subprocess.run(command, check=True)

        # Convert pdf to images using pdf2image
        images = convert_from_path(pdf_file)
        # Save images
        for i, image in enumerate(images):
            image.save('output_img_{}.png'.format(i), 'PNG')
        return pdf_file

    def generate_ppt_and_pdf(self, content_id, text, url):
        # 数据库查询content_id的信息
        mysql = MySQLService()
        session = mysql.create_session()
        # 根据提交的text 生成ppt
        try:
            ppt_url = self.text2ppt(text, str(content_id), url)
        except Exception as e:
            ppt_url = ''
            print(f"get_content ppt exception:" + traceback.format_exc())
        print(ppt_url)
        try:
            # 保存ppt和pdf
            pdf_url = self.ppt2pdf(ppt_url)
        except Exception as e:
            print(f"get_content pdf exception:" + traceback.format_exc())
            pdf_url = ""
        print(pdf_url)
        ppt_url = ppt_url.split("/")[-1]
        pdf_url = pdf_url.split("/")[-1]
        return ppt_url, pdf_url

    def get_file_name(self, file, name):
        file_id, ext = os.path.splitext(file)
        print(file_id)
        return name + "-爱集微-" + datetime.datetime.now().strftime('%Y-%m-%d') + ext


