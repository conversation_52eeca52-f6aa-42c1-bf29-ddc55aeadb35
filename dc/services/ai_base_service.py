import os
import sys

import bs4
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlunparse
from jwtools.func import *

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.conf.defines import *
from dc.common.func import dict_filter_sort
from dc.conf.settings import *


class AIBaseService(object):
    config: dict = None

    def __init__(self):
        self.config = get_settings('jiwei_ai')

    def run(self):
        pass

    def split_words(self, data: list, top=0, sort: bool = False) -> dict:
        """
        文章批量拆词
        :param data: 文章内容  网站权重
            [
                {'id':1, 'content': '内容1', 'weight': 1},
                {'id':2, 'content': '内容2', 'weight': 1.2},
                {'id':3, 'content': '内容3', 'weight': 1.1}
            ]
        :param top: 一篇文章返回多少关键词
        :param sort: 是否按照权重排序，默认 False
        :return: 拆词结果
            {
                '关键词1': {'weight': 2, 'article_id': [1, 2, 3]},
                '关键词2': {'weight': 2.5, 'article_id': [1, 2, 3]}
            }
        :rtype: dict
        """
        words_weight = {}
        for item in data:
            content2 = self.escape_html_tag(item.get('content')).replace('\n', '')
            article_weight = item.get('weight', 1)
            words = self.get_words(content=content2, top=top)
            # print_line('')
            # print_vf(
            #     'words len:', len(words),
            #     'words_weight len:', len(words_weight),
            #     'words:', words
            # )
            for word, weight in words.items():
                words_weight.setdefault(word, {'weight': 0})
                current_weight = words_weight.get(word).get('weight', 0)
                words_weight[word]['weight'] = current_weight + article_weight

        if sort:
            words_weight = dict_filter_sort(
                data=words_weight,
                filter_func=None,
                sort_func=lambda x: x[1].get('weight', 0),
                top=top,
                reverse=True
            )

        return words_weight

    def escape_html_tag(self, content) -> str:
        """
        对内容进行html标签过滤
        :param content:
        :return:
        """
        html_doc = f"""
        <div id='div-main-tbody'>
        {content}
        </div>
        """
        # print_line('text')
        soup = BeautifulSoup(html_doc, 'lxml')
        tag: bs4.element.Tag = soup.find(id="div-main-tbody")
        return tag.text

    def get_words(self, content, top=50) -> dict:
        """
        进行文章拆词
        :param top: top 条数据
        :param content:  文章内容, 需要进行标签过滤
        :return: 拆词结果
                {
                    "2023": 0.12490043591988181,
                    "教院": 0.41634162602727953,
                    "笃志": 0.4001147140017096,
                    "开学典礼": 0.309310697514016,
                    "流行语": 0.2792669874751637,
                }
        :rtype: dict
        """
        data = {
            "model_id": SEPARATE_WORD_MODEL_ID,
            "data": content
        }

        response = self.send_request(JIWEI_AI_KEYWORDS, data)
        # print(response)
        data: dict = response.get('data')
        result = dict_filter_sort(
            data=data,
            filter_func=self.filter_words,
            sort_func=lambda item: item[1],
            top=top
        )

        return result

    def send_request(self, url: str, data):
        """
        send request
        :param url:
        :param data:
        :return:
        """
        try:
            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }

            if url.startswith('http:') or url.startswith('https:'):
                full_url = url
            else:
                config = get_settings('jiwei_ai')
                host = config.get('host')
                full_url = f"{host}{url}"

            response = requests.post(full_url, headers=headers, json=data)
            response.encoding = 'utf-8'
            # print(response.status_code)
            return response.json()

        except Exception as e:
            # print(e)
            return False

    def filter_words(self, word) -> bool:
        if isinstance(word, tuple):
            word = word[0]

        result = True
        if len(word) < 2:
            result = False

        if word.isdigit():
            result = False

        return result

    def filter_words2(self, word2) -> bool:
        word = word2.get('k')
        result = True
        if len(word) < 2:
            result = False

        if word.isdigit():
            result = False

        return result

    def article_duplication(self, data, threshold=0.8) -> dict | int:
        """
        文章相似度计算
        :param data:  文章内容列表
        :param model: 相似度模型
        :return: 相似度对比结果
                {
                "ret": 0,
                "msg": "执行成功",
                "data": [
                            [
                                {
                                    "es_id": "48zD2ooB2C1yyCgtAnU9",
                                    "value": "散热更强劲 爱德克斯双片式制动盘转子【太平洋汽车网 技术频道】/n爱德克斯(ADVICS)在“人与车科技展2013”(2013年5月22～24日，太平洋横滨国际会展中心)上，展出了双片式制动盘转子新产品。该产品由铸铁制作的环形制动盘和铝合金制作的转子盖(中心部分)组成，通过采用新的紧固方式减少了紧固部件并提高了生产效率和精度。制动盘和转子盖上有与车轴平行的铆钉孔，通过铆接的方式紧固在一起，生产工时和部件数量均比使用螺帽螺栓固定的方式要少。据该公司介绍，该产品已被/n丰田/n的/n雷克萨斯GS/n F SPORT车型采用。/n　　双片式制动盘转子具有独特的工字梁横截面，“工”字梁部分与制动盘呈平行状态。研究人员表示，工型梁设计促进刹车盘周围的气流通过性，减少吸热效应。正是这个原因，相比传统单片式制动盘，双片式制动盘转子在制动盘工作时能够将热量更均匀地分布在制动盘表面。所以它能够在更高的温度下正常工作，不会发生热变形。这样一来，转子能够尽量保持与制动衬块的摩擦角度保持垂直，从而使制动盘的摩擦受热更均匀。/n　　双片式制动盘转子一方面要增加制动盘的尺寸来提高制动性能，另一方面要将转子盖换成铝合金来防止重量增加。新方式通过将转子盖设计为有高低差的两层结构并嵌入制动盘的中心孔，提高了组装精度(同轴性)。双片式制动盘转子为了吸收制动器工作时产生的热量,从而抑制对制动盘造成的热膨张作用，需要在螺孔或铆钉孔中留出空隙，此时就很难保证组装精度。新产品通过改进转子盖的形状保持了组装精度，而且转子一侧的孔只在内径方向留出了空隙。(来源日经技术在线)"
                                }
                            ]
                        ]
                }
        :rtype: dict
        """
        params = {
            "model_id": SIMILAR_MODEL_ID,
            "data": data,
            "threshold": threshold,
            "result_type": "group"
        }

        response = self.send_request(JIWEI_AI_SIMILAR, params)
        data: dict = response.get('data')
        return data

    """
           SEPARATE_WORD = 11  # 拆词
           TEXT_ERROR = 12  # 文章质量
           ARTICLE_QUALITY = 13  # 文章质量
       """

    def separate_word(self, content, top=0) -> dict | int:
        """
        进行文章拆词
        :param content:  文章内容, 需要进行标签过滤
        :param model: 拆词模型
        :param top: top 条数据
        :return: 拆词结果
                {
                    "ret": 0,
                    "msg": "执行成功",
                    "data": {
                        "2023": 0.12490043591988181,
                        "教院": 0.41634162602727953,
                        "笃志": 0.4001147140017096,
                        "开学典礼": 0.309310697514016,
                        "流行语": 0.2792669874751637,
                    }
                }
        :rtype: dict
        """

        data = {
            "model_id": SEPARATE_WORD_MODEL_ID,
            "data": content
        }
        response = self.send_request(JIWEI_AI_KEYWORDS, data)
        data: dict = response.get('data')
        zz = [{'k': kk, 'v': vv} for kk, vv in data.items()]
        zz2 = sorted(zz, key=lambda x: x['v'], reverse=True)
        zz2 = list(filter(self.filter_words2, zz2))
        top = top if top > 0 else self.config.get('keywords', {}).get('top', 50)
        return zz2[:top]

    def error_correction(self, content) -> dict | int:
        """
        文本纠错
        :param content:  文章内容, 需要进行标签过滤
        :return: 拆词结果
                {
                "ret": 0,
                "msg": "执行成功",
                "data": 0.93
                }
        :rtype: float
        """
        data = {
            "model_id": ERROR_CORRECTION_MODEL_ID,
            "data": content,
            "error_correction_type": "score"
        }

        response = self.send_request(JIWEI_AI_ERROR_CORRECTION, data)
        # print(response)

        return response.get('data') * 100

    def text_quality(self, content) -> dict | int:
        """
        文本质量 TEXT_QUALITY_MODEL_ID
        :param content:  文章内容, 需要进行标签过滤
        :return: 拆词结果
               {
                "ret": 0,
                "msg": "执行成功",
                "data": 0.9997
            }
        :rtype: float
        """
        data = {
            "model_id": TEXT_QUALITY_MODEL_ID,
            "data": content,
            "result_type": "prod"
        }

        response = self.send_request(JIWEI_AI_TEXT_QUALITY, data)
        # print(response)

        return response.get('data') * 100
