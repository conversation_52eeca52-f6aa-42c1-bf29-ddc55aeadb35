import random
import os
import sys
import time

import requests
from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService
from jwtools.dt import *

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))


class ChatGLMClient:
    name = 'default'

    url = "http://192.168.1.233:5000"

    config = {}
    extra = {}

    logger = None  # LoggingService

    def __init__(self, name: str = 'chatglm_client', **kwargs) -> None:
        super().__init__()
        self.name = name,
        self.config = get_settings(name)
        self.extra = kwargs
        self.logger = kwargs.get('logger') if kwargs.get('logger') is not None else LoggingService(
            logfile='chatglm_client.log')

    def send_request(self, url: str, data):
        """
        send request
        :param url:
        :param data:
        :return:
        """
        try:
            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }
            response = requests.post(url, headers=headers, json=data)
            response.encoding = 'utf-8'
            # print(response.status_code)
            return response.json()

        except Exception as e:
            print(e)
            return False

    def request(self, data: dict):
        data.setdefault("max_length", 40960)
        prompt_length = len(data.get('prompt', ''))
        start_time = time.time()
        result = self.send_request(url=self.url, data=data)
        response_length = len(result.get('response', ''))
        work_time = time_work(start=start_time)
        info = {
            'prompt_length': prompt_length,
            'response_length': response_length,
            'work_time': work_time['worktime'],
            'response': result.get('response'),
        }

        return result, info
