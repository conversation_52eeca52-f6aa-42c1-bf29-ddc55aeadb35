import os
import sys
import traceback
import tiktoken

from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService
import tika
from tika import parser

from dc.services.chatglm_service import ChatGLMService
from dc.services.chatgpt_service import ChatGPTService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))


class AIGCService:
    name = 'default'
    config = {}
    extra = {}

    logger = None  # LoggingService

    chatgpt = ChatGPTService()
    chatglm = ChatGLMService()

    def __init__(self, name: str = 'aigc', **kwargs) -> None:
        super().__init__()
        self.name = name,
        self.config = get_settings(name)
        self.extra = kwargs
        self.logger = kwargs.get('logger') if kwargs.get('logger') is not None else LoggingService(
            logfile='aigc.log')

    def extract_text(self, filename, params: dict = {}):
        """
        提取文本内容
        支持常见格式，参见 https://www.srcmini.com/38037.html
        :param filename: 文件名称
        :param params: 参数设置
        :return: string , False if error
        """
        try:
            text_raw = parser.from_file(filename)
            content = text_raw['content'].strip()
            # print(content)
            # 打开文件，如果文件不存在则创建一个新文件
            # with open('2222.txt', 'w',encoding='utf-8') as f:
            #     # 写入内容
            #     f.write(content)
            return content
        except:
            self.logger.info(f"extract_text error: {traceback.format_exc()}")
            return ''

    def speech_to_text(self, filename, params: dict = {}):
        """
        转换语音到文本
        :author wangfeng
        :param filename: 文件名称:
        :param params: 参数设置
        :return: string
        """
        return self.chatgpt.speech_to_text(filename)

    def chat(self, question):
        """
        chatgpt 调用 version 2
        :param question:
        :return:
        """
        return self.chatgpt.chat(question)

    # 处理参考资料，生成150字摘要
    def split_reference(self, content):
        """
        分割参考资料
        """
        if len(content) <= 200:
            return content

        num = 0
        tmp_list = []
        chatSer = ChatGPTService()
        while True:
            tmp = content[750 * num:750 * (num + 1)]
            if len(tmp) == 0:
                break

            string = tmp + ", 总结上述内容, 生成200字以内摘要"

            chatStr = chatSer.chat35(question=string)
            tmp_list.append(chatStr)
            num += 1

        return self.split_reference("".join(tmp_list))

    # 生成文字或段落
    def create_section(self, content, params):
        """
        生成文字或段落
        {
            "sectionType": 1  # 1 摘要段落  2 普通文章
            "numMin": 100
            "numMax": 1000
        }
        """
        chatSet = ChatGPTService()
        if params['sectionType'] == 1:
            string = "我想让你充当一个半导体行业的资深编辑,需要参考以下内容写一段字数在" + str(params['numMin']) + "-" + str(params['numMax']) + \
                     "字内的段落,参考内容如下：" + content
        else:
            string = "我想让你充当一个半导体行业的资深编辑,需要参考以下内容写一篇字数在" + str(params['numMin']) + "-" + str(params['numMax']) + \
                     "字内的文章,参考内容如下：" + content

        chatStr = chatSet.chat35(question=string)
        chatStr = chatStr.lstrip(".").lstrip(":")
        return chatStr

    def token_len(self, model='text-davinci-003', content=''):
        tokenLen = tiktoken.encoding_for_model(model).encode(content)
        return len(tokenLen)

    def cut_content_by_token_len(self, content, length, model='text-davinci-003'):
        tokenList = tiktoken.encoding_for_model(model).encode(content)
        tokenLen = len(tokenList)
        if tokenLen <= length:
            return content
        else:
            content = tiktoken.encoding_for_model(model).decode(tokenList[0:length])
        return content
        # return self.cut_content_by_token_len(content, length)
