# -*- coding:utf-8 -*-
# @Function  : DashboardService 看板服务
# <AUTHOR> wjh
# @Time      : 2024-10-12
# Version    : 1.0

import datetime
import greenstalk
from dc.conf.settings import get_settings
from dc.services.logging_service import LoggingService


class DashboardService:
    logger: LoggingService = None
    name: str = 'dashboard'
    config = {}

    def __init__(self, name='dashboard', run_model=None, config={}):
        settings = get_settings(name, default={}, run_model=run_model)
        self.config = {**settings, **config}
        self.logger = config.get('logger') if config.get('logger') else LoggingService(logfile='dashboard_service.log')

    def previous_period(self, interval_minutes, current_time=None):
        """
        计算前一个时段的开始时间和结束时间
        :param interval_minutes: 间隔分钟数（单位: 分钟）
        :param current_time:
        :return:
        """
        # 检查 interval_minutes 是否是 60 的整数倍，或可以被 60 整除
        if not (interval_minutes % 60 == 0 or 60 % interval_minutes == 0):
            raise ValueError(f"interval_minutes 必须为 60 的整数倍，或可以被 60 整除，但当前值为 {interval_minutes}。")

        # 获取当前时间
        now = datetime.datetime.now() if current_time is None else current_time

        # 如果间隔是 60 的整数倍，按小时处理
        if interval_minutes % 60 == 0:
            hours_interval = interval_minutes // 60  # 将间隔转换为小时
            # 检查小时数是否超过 24
            if hours_interval > 24:
                raise ValueError(f"interval_minutes 对应的小时数最大不能超过 24，但当前小时数为 {hours_interval}。")

            # 检查小时数是否能整除 24（即确保可以划分成完整的24小时时段）
            if 24 % hours_interval != 0:
                raise ValueError(f"interval_minutes 对应的小时数必须能被 24 整除，但当前小时数为 {hours_interval}。")

            # 计算当前时间所在的小时时段
            current_hour = now.hour
            current_interval_start_hour = (current_hour // hours_interval) * hours_interval

            # 计算前一个时间段的结束时间，并确保分钟、秒和微秒为0
            previous_end_time = now.replace(hour=current_interval_start_hour, minute=0, second=0, microsecond=0)

            # 计算前一个时间段的开始时间
            previous_start_time = previous_end_time - datetime.timedelta(hours=hours_interval)

        else:
            # 处理间隔分钟数不是 60 的倍数的情况，按分钟计算
            current_minutes = now.minute
            # 计算当前时间所在的时间段的起始分钟
            current_interval_start_minute = (current_minutes // interval_minutes) * interval_minutes

            # 计算上一个时间段的结束时间，秒和毫秒设置为0
            previous_end_time = now.replace(minute=current_interval_start_minute, second=0, microsecond=0)

            # 计算上一个时间段的开始时间
            previous_start_time = previous_end_time - datetime.timedelta(minutes=interval_minutes)

        result = {'current_time': now.strftime('%Y-%m-%d %H:%M:%S'), 'previous_start_time': previous_start_time.strftime('%Y-%m-%d %H:%M:%S'), 'previous_end_time': previous_end_time.strftime('%Y-%m-%d %H:%M:%S')}
        return result

    def simple_previous_period(self, interval_minutes, current_time=None):
        """
        计算前一个时段的开始时间和结束时间
        :param interval_minutes: 间隔分钟数（单位: 分钟）
        :param current_time:
        :return:
        """
        now = datetime.datetime.now() if current_time is None else current_time
        previous_start_time = now - datetime.timedelta(minutes=interval_minutes)
        previous_end_time = now

        result = {'current_time': now.strftime('%Y-%m-%d %H:%M:%S'), 'previous_start_time': previous_start_time.strftime('%Y-%m-%d %H:%M:%S'), 'previous_end_time': previous_end_time.strftime('%Y-%m-%d %H:%M:%S')}
        return result

    def generate_sql_for_previous_period(self, table_name, time_field, where, interval_minutes, current_time=None):
        previous_period = self.previous_period(interval_minutes, current_time)
        self.logger.info(f"previous_period: {previous_period}")

        # 格式化SQL语句
        sql = f"""
            SELECT COUNT(*) as count
            FROM {table_name}
            WHERE {where}
            AND {time_field} >= '{previous_period['previous_start_time']}'
            AND {time_field} < '{previous_period['previous_end_time']}'
        """

        self.logger.info(f"sql: {sql}")
        return sql

    def generate_query_for_previous_period(self, query_template, interval_minutes, current_time=None):
        """
        :param query_template:
        :param interval_minutes:
        :param current_time:
        :return:
        """
        previous_period = self.previous_period(interval_minutes, current_time)
        self.logger.info(f"previous_period: {previous_period}")

        # 格式化SQL语句
        query = (query_template.replace("{{ startTime }}", previous_period['previous_start_time'])
                 .replace("{{ stopTime }}", previous_period['previous_end_time']))

        self.logger.info(f"query: {query}")
        return query

    def generate_simple_query_for_previous_period(self, query_template, interval_minutes, current_time=None):
        """
        :param query_template:
        :param interval_minutes:
        :param current_time:
        :return:
        """
        previous_period = self.simple_previous_period(interval_minutes, current_time)
        self.logger.info(f"previous_period: {previous_period}")

        # 格式化SQL语句
        query = (query_template.replace("{{ startTime }}", previous_period['previous_start_time'])
                 .replace("{{ stopTime }}", previous_period['previous_end_time']))

        self.logger.info(f"query: {query}")
        return query

    def get_beanstalk_stats(self, host, port=11300):
        """获取指定 Beanstalk 服务器的统计数据"""
        with greenstalk.Client((host, port)) as client:
            tubes = client.tubes()
            tube_stats = {}
            for tube in tubes:
                tube_stats[tube] = client.stats_tube(tube)
            return tube_stats
