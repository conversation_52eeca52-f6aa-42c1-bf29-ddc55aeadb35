# -*- coding:utf-8 -*-
"""
# File       : chat_glm.py
# Time       ：2023/9/13 16:30
# Author     ：andy
# version    ：python 3.9
"""
import os
import sys
import time
import jwt
import requests
import zhipuai
from dc.conf.settings import get_settings

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.logging_service import LoggingService
from dc.conf.defines import *

conf = get_settings('zhipu')
zhipuai.api_key = conf.get('api_key')
# zhipuai.model_api_url = conf.get('model_api_url')
logger = LoggingService(logfile='zhipu.log')


class ZhipuService:
    def __init__(self, model='chatglm_130b_34121693819088333', top_p=0.7, temperature=0.9):
        self.model = model
        self.top_p = top_p
        self.temperature = temperature

    def invoke(self, prompt: list):
        """
        同步请求
        :param prompt:
        example: [
                    {"role": "user", "content": "你好"},
                    {"role": "assistant", "content": "我是人工智能助手"},
                    {"role": "user", "content": "你叫什么名字"},
                    {"role": "assistant", "content": "我叫chatGLM"},
                    {"role":"user", "content": "你都可以做些什么事"},
                ]
        :return:
        example:{
                  "code": 200,
                  "msg": "",
                  "success": true,
                  "data": {
                      "task_id": "75931252186628016897601864755556524089",
                      "request_id": "123445676789",
                      "task_status": "SUCCESS",
                      "choices": [
                          {"role": "assistant", "content":"作为一个大型语言模型,我可以完成许多不同的任务,包括但不限于: \n1. 回答问题 \n2.提供建议……"}
                      ],
                      "usage": {
                          "prompt_tokens": 215,
                          "completion_tokens": 302,
                          "total_tokens": 517
                      }
                  }
                }
        """
        response = zhipuai.model_api.invoke(
            model=self.model,
            prompt=prompt,
            top_p=self.top_p,
            temperature=self.temperature,
        )
        try:
            if response is None:
                logger.error(" invoke completed with prompt: %s and received data: %s", prompt, response)
            else:
                logger.info(" invoke completed with prompt: %s and received data: %s", prompt, response)
        except:
            pass
        return response

    def async_invoke(self, prompt: list):
        """
        异步请求
        :param prompt:
        example: [
                    {"role": "user", "content": "你好"},
                    {"role": "assistant", "content": "我是人工智能助手"},
                    {"role": "user", "content": "你叫什么名字"},
                    {"role": "assistant", "content": "我叫chatGLM"},
                    {"role":"user", "content": "你都可以做些什么事"},
                ]
        :return:
        example:{
                    "code": 200,
                    "msg": "",
                    "success": true,
                    "data": {
                        "task_id": "75931252186628016897601864755556524089",
                        "request_id": "123445676789",
                        "task_status": "PROCESSING"
                    }
                }
        """
        response = zhipuai.model_api.async_invoke(
            model=self.model,
            prompt=prompt,
            top_p=self.top_p,
            temperature=self.temperature,
        )
        return response

    @staticmethod
    def query_async_invoke_result(task_id: str):
        """
        异步调用结果请求
        :param task_id:
        :return:
        example:{
                  "code": 200,
                  "msg": "",
                  "success": true,
                  "data": {
                      "task_id": "75931252186628016897601864755556524089",
                      "request_id": "123445676789",
                      "task_status": "SUCCESS",
                      "choices": [
                          {"role": "assistant", "content":"作为一个大型语言模型,我可以完成许多不同的任务,包括但不限于: \n1. 回答问题 \n2.提供建议……"}
                      ],
                      "usage": {
                          "prompt_tokens": 215,
                          "completion_tokens": 302,
                          "total_tokens": 517,
                      }
                  }
                }
        """
        response = zhipuai.model_api.query_async_invoke_result(task_id)
        return response

    def sse_invoke(self, prompt: list):
        """
        流式请求
        :param prompt:
        example: [
                    {"role": "user", "content": "你好"},
                    {"role": "assistant", "content": "我是人工智能助手"},
                    {"role": "user", "content": "你叫什么名字"},
                    {"role": "assistant", "content": "我叫chatGLM"},
                    {"role":"user", "content": "你都可以做些什么事"},
                ]
        :return:
        example:
                id: "fb981fde-0080-4933-b87b-4a29eaba8d17"
                event: "add"
                data: "作为一个"

                id: "fb981fde-0080-4933-b87b-4a29eaba8d17"
                event: "add"
                data: "大型语言模型"

                id: "fb981fde-0080-4933-b87b-4a29eaba8d17"
                event: "add"
                data: "我可以"

                ... ...

                Id: "fb981fde-0080-4933-b87b-4a29eaba8d17"
                event: "finish"
                meta: {"request_id":"123445676789","task_id":"75931252186628","task_status":"SUCCESS","usage":{"prompt_tokens":215,"completion_tokens":302,"total_tokens":517}}
        """
        response = zhipuai.model_api.sse_invoke(
            model=self.model,
            prompt=prompt,
            top_p=self.top_p,
            temperature=self.temperature,
        )
        # for event in response.events():
        #     if event.event == "add":
        #         yield 'data: %s\n\n' % event.data
        #     elif event.event == "error" or event.event == "interrupted":
        #         yield 'data: %s\n\n' % event.data
        #     elif event.event == "finish":
        #         yield 'data: %s, %s\n\n' % event.data, event.meta
        #         # yield event.data, event.meta
        #     else:
        #         yield 'data: %s\n\n' % event.data
        #         # yield event.data

        # received_data = []
        received_data = ""
        try:
            for event in response.events():
                if event.event in ["add", "error", "interrupted", "finish"]:
                    received_data += event.data
                if "[1302][您当前使用该API的并发数过高，请降低并发，或联系客服增加限额。]" in received_data:
                    yield 'data: %s\n\n' % "“JiweiGPT使用太火爆，您的请求正在排队中，请稍等...”"
                    break
                yield 'data: %s\n\n' % event.data if event.event != "finish" else 'data: %s\n\n' % (
                    event.data)
        except Exception as e:
            logger.error("Error while processing SSE events: %s", str(e))
            raise
        finally:
            logger.info("SSE invoke completed with prompt: %s and received data: %s", prompt, received_data)

    @staticmethod
    def generate_token(exp_seconds: int):
        """
        非SDK鉴权
        :param exp_seconds: 过期时间，单位秒
        :return:
        """
        try:
            id, secret = zhipuai.api_key.split(".")
        except Exception as e:
            raise Exception("invalid apikey", e)

        payload = {
            "api_key": id,
            "exp": int(round(time.time() * 1000)) + exp_seconds * 1000 * 1000,
            "timestamp": int(round(time.time() * 1000)),
        }

        return jwt.encode(
            payload,
            secret,
            algorithm="HS256",
            headers={"alg": "HS256", "sign_type": "SIGN"},
        )

    @staticmethod
    def token_filter(res, stop_len=3000):
        """
        过滤tokens 长度
        :param res: 对话内容
        :param stop_len: 最大长度
        :return:
        """
        # 如果最后一个问题超长
        if res and len(res[-1].get('content')) + 25 > stop_len:
            return [{
                'type': res[-1].get('type'),
                'content': res[-1].get('content')[:stop_len]}
            ]

        total_length = 0
        end_index = len(res)

        # 从底部开始计算累计长度，并确定需要提取的元素数量
        for i in range(len(res) - 1, -1, -1):
            total_length = total_length + len(res[i]['content']) + 25
            if total_length > stop_len:
                break
            end_index = i

        # 获取满足长度条件的元素
        result: list = res[end_index:]

        # 如果最后一个元素的 type 为 2，则将其删除
        if result and result[0]['type'] == 2:
            result.pop(0)

        return result

    @staticmethod
    def token_count(res):
        total2 = 0

        def do_map(x):
            nonlocal total2
            ll = len(x.get('content'))
            total2 = total2 + ll
            pp = {**x, 'len': ll, 'total': total2}
            return pp

        res2 = list(reversed(list(map(do_map, reversed(res)))))
        # print_vf(*res2)
        return res2

    # 获取字符串内容占用token长度
    @staticmethod
    def tokenizer_api(content):
        headers = {
            "Authorization": ZhipuService.generate_token(10),
            "accept": "application/json"
        }

        res = requests.post(url=ZHIPU_TOKEN_COUNT, headers=headers, json={"text": content})

        return res

