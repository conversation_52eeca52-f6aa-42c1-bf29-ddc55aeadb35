import time
import sys
import os
import json
import traceback
import psutil
from sqlalchemy.orm import load_only

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService
from dc.models.model import DCSiteMainTask, DCSiteList, DCSiteKeyWords, DCSiteListRule, DCSiteListItem, \
    DCSiteKeywordRelation

"""
[功能]：数据采集-初始化任务
[作者]：lws
[日期]：2023-07-16
"""

mysql = MySQLService()
session = mysql.create_session()

redisService = RedisService('redis')
client1 = redisService.get_client()

log_service = LoggingService('dc_create_main_task.log')

redis_prefix = 'laravel_database_'
redis_task_key = f'{redis_prefix}dc-main-task'
site_list_hash = f'{redis_prefix}dc-site-list-hash'


# 创建主任务
def push_task(siteListId):
    process_num = 0
    for process in psutil.process_iter(['pid', 'cmdline']):
        try:
            process_info = process.info
            cmdline = process_info['cmdline']

            # 检查进程的命令行是否包含指定的脚本名称
            if cmdline and cmdline[0] == 'python3' and "dc_task_init.py" in cmdline:
                process_num += 1
                # return process_info

        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass

    if process_num > 1:
        exit()

    # 获取当前时间
    curr_time = time.time()
    curr_time = int(curr_time)

    fields = ['id', 'detailUrl', 'sourceFrequencyType', 'sourceFrequency', 'siteUrl', 'lastExecTime', 'firstExecTime',
              'sourceMark', 'nextExecTime', 'sourceCategory', 'sourceName', 'extra', 'siteName', 'isQuickShort',
              'isKeyWord', 'detailDcMethod', 'sourceName', 'isHotModule', 'tags']
    ret = session.query(DCSiteList).options(load_only(*fields)).filter(DCSiteList.id == siteListId) \
        .all()
    if not ret:
        log_service.dcPullLog('当前执行未获取到数据源')
        exit()

    for info in ret:
        site_info = info.to_dict()

        if not site_info['id']:
            log_service.dcPullLog('存在id为空的异常数据')
            continue

        if not site_info['firstExecTime']:
            log_service.dcPullLog(f"数据源【{site_info['id']}】不存在首次执行时间")
            continue

        if site_info['lastExecTime']:
            deal_main_task(site_info)
            continue

        if not site_info['lastExecTime']:
            deal_main_task(site_info)
            continue


# 处理间隔时间
def deal_time(fre_type, frequency):
    diff_time = 0
    if fre_type == 1:
        diff_time = frequency * 60
    elif fre_type == 2:
        diff_time = frequency * 3600
    elif fre_type == 3:
        diff_time = frequency * 86400

    return diff_time


# 处理主任务
def deal_main_task(info):
    if 'id' not in info:
        return False

    # 获取当前提取规则
    rule_item = get_rule(info['id'])
    if rule_item == '':
        return False

    analysisMethod = 0
    if info['extra']:
        try:
            dc_extra = json.loads(info['extra'])
            if 'analysisMethod' in dc_extra:
                analysisMethod = dc_extra['analysisMethod']
        except:
            pass

    redis_arr = {'sourceCategory': info['sourceCategory'], 'siteName': info['siteName'],
                 'analysisMethod': analysisMethod, 'isQuickShort': info['isQuickShort'], 'isKeyWord': info['isKeyWord'],
                 'detailDcMethod': info['detailDcMethod'], 'sourceName': info['sourceName'], 'ruleItem': rule_item,
                 'isHotModule': info['isHotModule'], 'tags': info['tags']}
    client1.hset(site_list_hash, info['id'], json.dumps(redis_arr))
    # res = client1.hmget(site_list_hash, site_info['id'])
    # print(json.loads(res[0]))
    if info['isKeyWord'] == 2:
        add_main_task(info, '')
        up_site(info['id'])
    else:
        # 获取关键词，循环插入数据
        keywordRelations = session.query(DCSiteKeywordRelation).filter(DCSiteKeywordRelation.siteId == info['id']).all()
        if not keywordRelations:
            keyWords = session.query(DCSiteKeyWords).filter(DCSiteKeyWords.status == 1).all()
        else:
            keywordIds = []
            for relation in keywordRelations:
                relationArr = relation.to_dict()
                keywordIds.append(relationArr['keywordId'])
            keyWords = session.query(DCSiteKeyWords).filter(DCSiteKeyWords.status == 1,
                                                            DCSiteKeyWords.id.in_(keywordIds)).all()

        for wordsObj in keyWords:
            words = wordsObj.to_dict()
            add_main_task(info, words['keyword'])

        up_site(info['id'])


# 写主任务
def add_main_task(info, keyWord):
    try:
        task = DCSiteMainTask(siteListId=info['id'], listUrl=info['detailUrl'], url=info['siteUrl'], keyWord=keyWord,
                              httpStatusCode=0)
        session.add(task)
        session.commit()

        # 插入成功，写入redis
        client1.lpush(redis_task_key, task.id)

        log_service.dcPullLog(f"数据源id为【{info['id']}】-关键词为【{keyWord}】的数据写入成功，主任务id【{task.id}】")
    except:
        log_service.error(
            f"数据源id为【{info['id']}】-关键词为【{keyWord}】的数据写入失败，失败原因：{traceback.format_exc()}")


# 更新最后一次执行时间
def up_site(site_id):
    last_exec_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    up_sql = f"update DC_SiteList set lastExecTime = '%s' where id = {site_id}" % last_exec_date
    try:
        session.execute(up_sql)
        session.commit()
        log_service.dcPullLog(f"数据源id为【{site_id}】的数据更新最后一次执行时间成功")
    except:
        log_service.error(f"数据源id为【{site_id}】的数据更新最后一次执行时间失败，失败原因：{traceback.format_exc()}")


# 获取数据源对应规则
def get_rule(site_id):
    rule_res = session.query(DCSiteListRule).filter(DCSiteListRule.siteListId == site_id).all()
    if not rule_res:
        log_service.dcPullLog(f'数据源id【{site_id}】无对应规则，初始化任务失败')
        return ''

    rule_ids = []
    for rule_info in rule_res:
        rule = rule_info.to_dict()
        rule_ids.append(rule['id'])

    item_res = session.query(DCSiteListItem).filter(DCSiteListItem.siteRuleId.in_(rule_ids),
                                                    DCSiteListItem.status == 1).all()
    if not item_res:
        log_service.dcPullLog(f'数据源id【{site_id}】无对应数据项，初始化任务失败')
        return ''

    item_data = {}
    for rule_id in rule_ids:
        item_data[rule_id] = {}

    for item_info in item_res:
        item = item_info.to_dict()
        item_data[item['siteRuleId']][item['id']] = item

    return json.dumps(item_data)

    # item_data = {}
    #
    # for item_info in item_res:
    #     item = item_info.to_dict()
    #     item_data[item['id']] = item
    # print(json.dumps(item_data))
    # exit()
    # return json.dumps(item_data)


# siteListId = 1889
siteListId = 1894
push_task(siteListId)
