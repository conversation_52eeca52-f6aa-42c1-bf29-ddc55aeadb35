# -*- coding:utf-8 -*-
# @Function  : js
# <AUTHOR> wjh
# @Time      : 2024-11-11
# Version    : 1.0
import json

jj = {
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "range": {
            "analysis_time": {
              "gte": "{{ startTime }}",
              "lte": "{{ stopTime }}"
            }
          }
        },
        {
          "term": {
            "is_repeat": {
              "value": "1"
            }
          }
        }
      ]
    }
  },
  "aggs": {
    "type_count": {
      "terms": {
        "field": "type",
        "size": 10
      }
    }
  }
}

print(json.dumps(json.dumps(jj)))
print(json.dumps(jj))
