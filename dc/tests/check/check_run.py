# -*- coding:utf-8 -*-
# @Function  : 监控报警服务
# <AUTHOR>
# @Time      : 2024/8/23
# Version    : 1.0
import time

from dc.services.logging_service import LoggingService

logger = LoggingService(logfile="check_run.log")

try:
    logger.info('scheduler start...')

    while True:
        logger.info("current time:" + time.strftime('%Y-%m-%d %H:%M:%S',time.localtime()))
        time.sleep(60)

    logger.info(f'scheduler started')

except (KeyboardInterrupt, SystemExit):
    logger.info("system exit")

except Exception as ex:
    logger.error(f"scheduler exception {str(ex)}")
