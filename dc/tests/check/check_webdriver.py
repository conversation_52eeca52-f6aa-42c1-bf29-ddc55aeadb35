import os
import sys
from dc.common.webdriver_util import *

try:

    driver = get_webdriver(Browser.Chrome)
    driver.implicitly_wait(10)
    driver.get("https://www.baidu.com")
    print(driver.title)
    page_source = driver.page_source
    # print(page_source)
    driver.quit()

except Exception as e:
    import traceback
    print("Error initializing WebDriver:")
    print(traceback.format_exc())
    raise
