import sys
import os
from time import time
import requests
from dc.common.utils import Utils
from jwtools.func import *
from jwtools.io_util import *

from dc.services.chatglm_client import ChatGLMClient

client = ChatGLMClient()


def get_prompt(filename):
    project_path = get_parent_directory(__file__, 4)
    data_path = os.path.join(project_path, "data")
    file = os.path.join(data_path, filename)
    return read_text_from_file(file)


# 调用示例
article = get_prompt("article.txt")
# article2 = get_prompt("article2.txt")

prompts = [
    {
        "prompt": "一只狗有几条腿",
        "history": []
    },
    {
        "prompt": "一只鸡有几条腿",
        "history": []
    },
    {
        "prompt": article,
        "history": []
    },
]

for prompt in prompts:
    result, info = client.request(prompt)
    print_vf(
        result,
        info,
        info['response']
    )
