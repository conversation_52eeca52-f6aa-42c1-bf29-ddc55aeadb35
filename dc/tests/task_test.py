import sys
import os

from sqlalchemy.orm import Query

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from dc.models.model import DCSiteList
from dc.services.task_again_service import TaskAgainService
from dc.common.utils import Utils
from elasticsearch import Elasticsearch
from dc.conf.settings import get_settings
from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.mysql_service import MySQLService

# conf = get_settings('elasticsearch')
# es = Elasticsearch("http://192.168.1.123:9200")


redis_prefix = 'laravel_database_'
redis_analyse_key = f'{redis_prefix}dc-site-task-analyse'
redis_task_key = f'{redis_prefix}dc-site-task'
redis_task_blacklist = f'{redis_prefix}dc-site-task-blacklist'

logger = LoggingService('task-test.log')

try:

    ess = ElasticsearchService()
    es = ess.get_connect()

    mysql = MySQLService()
    session = mysql.create_session()

    redisService = RedisService()
    redis = redisService.get_client()


    task_length = redis.llen(redis_task_key)
    #print(f"task_length: {task_length}")
    logger.info(f"task_length: {task_length}")

    analyse_length = redis.llen(redis_analyse_key)
    #print(f"analyse_length: {analyse_length}")
    logger.info(f"analyse_length: {analyse_length}")

    if task_length + analyse_length > 0:     # 存在处理的任务
        logger.info(f"task_length {task_length} > 0 or analyse_length: {analyse_length} > 0, 不进行处理")

    else:
        logger.info(f"task site exec.")

        query: Query = session.query(DCSiteList).filter_by(status=1, version=1).order_by(DCSiteList.id)
        #data = query.all()
        count = query.count()
        logger.info(f"count: {count}")

        row: DCSiteList = query.first()
        if row is None:
            logger.info(f"site list is empty")
        else:
            logger.info(f"site list id: {row.id}")
            logger.info(f"site list2: {row.to_json()}")
            #print(row.id, row.status, row.version)
            site_list_id = row.id

            # task exec
            ts = TaskAgainService()
            ts.main_site_list_check({"site_list_id": site_list_id}, logger=logger)
            ts.main_site_list({"site_list_id": site_list_id}, logger=logger)
            ts.main_site_list_check({"site_list_id": site_list_id}, logger=logger)

            # site lite for mysql
            row.version = 2
            row.versionTime = Utils.showDateTime()
            session.add(row)
            session.commit()

            logger.info(f"site list version update: {row.id}")

except Exception as ex:
    print(ex)

finally:
    session.close()
    es.close()
    redis.close()

