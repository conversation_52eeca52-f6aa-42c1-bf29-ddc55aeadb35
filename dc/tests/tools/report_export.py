"""
舆情报告导出
2024-1-4
"""

import json
import random
import time
import traceback

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhArticle
from dc.services.beanstalk_service import BeanstalkService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.services.weixin_service import WeixinService
import shutil

logger = LoggingService('report_export.log')
mysql = MySQLService()


def handle():
    session = None
    rand = random.randint(100000, 999999)
    prefix = f"[{rand}] "

    monitorId = 1082
    try:

        session = mysql.create_session()
        reports: list = session.execute(f'select * from OP_Report where monitorId={monitorId}')

        for report in reports:
            name = report['name']
            # /data/nfs/uploads/html_uploads/yuqing_pdf/202305/08/6520_140361836764583f254d9d0.pdf
            source_file_path = report['reportFileUrl']
            new_file_path = f"/data/1082/{name}.pdf"

            shutil.copy(source_file_path, new_file_path)
            print(f"文件已成功从 '{source_file_path}' 复制并重命名为 '{new_file_path}'")

    except Exception as ex:
        logger.error(f"{prefix} exception:" + traceback.format_exc())

    finally:
        if session is not None:
            session.close()


handle()
