# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/2/4
# Version    : 1.0
import json
import time

from selenium import webdriver
from selenium.webdriver.common.by import By

from dc.tests.browser import Browser
from dc.tests.logins.proxy_config import *
import pickle
from dc.models.model import DCSiteList, DCSiteListItem, DCSiteListRule, DCSiteTask
from dc.services.mysql_service import MySQLService
from selenium.webdriver.chrome.options import Options


def get_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '')


def get_public_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '') + ' 00:00:00'


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=firefox_executable_path, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=chrome_executable_path, options=opt)


def get_dc_webdriver(site_info) -> webdriver:
    if site_info['detailDcMethod'] == 2:
        # 火狐或谷歌模式
        try:
            if site_info['analysisMethod'] == 1:
                opt = webdriver.FirefoxOptions()
                # opt.add_argument("--headless")
                opt.add_argument('--disable-gpu')
                driver = webdriver.Firefox(options=opt)
            else:
                chrome_options = Options()
                # chrome_options.add_argument('--headless')
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-gpu')
                # prefs = {'profile.default_content_settings.popups': 0,
                #          'download.default_directory': "/data" + self.save_path + ym}
                # prefs = {'profile.default_content_settings.popups': 0,
                #          'download.default_directory': "D:\\"}
                # chrome_options.add_experimental_option('prefs', prefs)
                driver = webdriver.Chrome(options=chrome_options)

            driver.implicitly_wait(10)
            return driver

        except:
            return False

    else:
        return False


def login(driver, params):
    driver.get(params['login_page'])

    login = driver.find_element(By.XPATH, '//span[@class="svg-container phone"]')
    print(login.click())

    time.sleep(2)

    login = driver.find_element(By.XPATH, '//input[@name="mobile"]')
    login.send_keys(params['username'])
    time.sleep(2)

    login = driver.find_element(By.XPATH, '//input[@name="password"]')
    login.send_keys(params['password'])
    time.sleep(2)

    login = driver.find_element(By.CLASS_NAME, 'cx-agree-check')
    login.click()
    time.sleep(2)

    login = driver.find_element(By.CLASS_NAME, 'login-btn')
    login.click()

    time.sleep(3)
