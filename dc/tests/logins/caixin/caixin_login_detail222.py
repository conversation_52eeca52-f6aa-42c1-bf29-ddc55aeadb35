import json

from selenium import webdriver
from dc.tests.browser import <PERSON><PERSON><PERSON>
from dc.tests.logins.proxy_config import *
from dc.models.model import DCSiteList, DCSiteListItem, DCSiteListRule, DCSiteTask
from dc.services.mysql_service import MySQLService


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=firefox_executable_path, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=chrome_executable_path, options=opt)


params = {
    'site': 'https://www.caixin.com/',
    'login_page': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F',
    'tasks': [
        {
            'name': 'tech',
            'list_url': 'https://www.caixin.com/tech/',
            'item_url': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F'
        }
    ],
    'username': '13022183350',
    'password': 'ijiwei@54322'
}


def get_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '')


def get_public_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '') + ' 00:00:00'


mysql = MySQLService()
session = mysql.create_session()
site_list_id = 1901
siteList: DCSiteList = session.query(DCSiteList).get(site_list_id)
print(siteList.to_dict())

siteRule: DCSiteListRule = session.query(DCSiteListRule).filter(DCSiteListRule.siteListId == siteList.id).first()
print(siteRule.to_dict())

siteItem: [DCSiteListItem] = session.query(DCSiteListItem).filter(DCSiteListItem.siteRuleId == siteRule.id).all()
print([item.to_dict() for item in siteItem])

from dc.services.dc_task_analysis_service import DcTaskAnalysisService
ta = DcTaskAnalysisService()

dc_extra = json.loads(siteList.extra if siteList.extra else '{}')
analysisMethod = dc_extra.get('analysisMethod', 0)

task_info = {
    "id": 0,
    "siteListId": siteList.id,
    # "mainTaskId":  2389211,
    "baseUrl": "https://www.jiemian.com",
    "url": "https://www.jiemian.com/article/10765596.html",
    # "title":  "\u8bda\u610f\u836f\u4e1a\uff1a\u76ee\u524d\u516c\u53f8\u751f\u4ea7\u7ecf\u8425\u4e00\u5207\u6b63\u5e38\uff0c\u5927\u5065\u5eb7\u4ea7\u54c1\u5df2\u9010\u6b65\u4e0a\u5e02",
    # "taskStatus":  1,
    # "taskAt":  "2024-02-02 09:10:48",
    # "analysisStatus":  0,
    # "httpStatusCode":  200,
    # "mongoCollection":  "",
    # "mongoKey":  "65bc4117f0617b7129354959",
    # "retryTime":  1,
    # "createdAt":  "2024-02-02 09:08:05",
    # "updatedAt":  "2024-02-02 09:10:48",
    # "esIndex":  "",
    # "esId":  "",
    # "ruleId":  0,
    # "publicTime":  "",
    # "coverImg":  "",
    "detailDcMethod": siteList.detailDcMethod,
    "analysisMethod": analysisMethod,
    "extra": dc_extra,
    "detail_info": {
        'url': 'https://www.caixin.com/2024-02-02/102163007.html'
    }
}

task = DCSiteTask()
task.title = ""
task.url = ""
task.baseUrl = siteList.siteUrl

task_info2: dict = siteList.to_dict()
task_info2.update(task_info)
mongo_info = {}
ta.analysis(task_info2, mongo_info)



session.close()


exit(0)

driver = get_webdriver(Browser.Chrome)
driver.implicitly_wait(10)

content_xpath = '//div[@id="Main_Content_Val"]'
durl = 'https://www.caixin.com/2024-01-31/102162155.html'
driver.get(durl)

pub = driver.find_element(By.XPATH, '//div[@id="artInfo"]')
pub_time = pub.text.split('\n')[0].replace('年', '-').replace('月', '-').replace('日', '')
print(pub_time)



# article = driver.find_element(By.XPATH, article_xpath)
from dc.common.dc_helper import DcHelper

content = DcHelper.get_content(driver.page_source, content_xpath, By.XPATH)
print(content)

time.sleep(3)
driver.close()
