import time

from selenium.webdriver.common.by import By
from datetime import datetime, timedelta
from selenium import webdriver
from dc.tests.browser import Browser
from dc.tests.logins.proxy_config import *


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=firefox_executable_path, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=chrome_executable_path, options=opt)


params = {
    'site': 'https://www.caixin.com/',
    'login_page': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F',
    'tasks': [
        {
            'name': 'tech',
            'list_url': 'https://www.caixin.com/tech/',
            'item_url': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F'
        }
    ],
    'username': '13022183350',
    'password': 'ijiwei@54322'
}


def get_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '')


def get_public_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '') + ' 00:00:00'


driver = get_webdriver(Browser.Chrome)
driver.implicitly_wait(10)
driver.get(params['login_page'])

login = driver.find_element(By.XPATH, '//span[@class="svg-container phone"]')
print(login.click())

time.sleep(2)

login = driver.find_element(By.XPATH, '//input[@name="mobile"]')
login.send_keys(params['username'])
time.sleep(2)

login = driver.find_element(By.XPATH, '//input[@name="password"]')
login.send_keys(params['password'])
time.sleep(2)

login = driver.find_element(By.CLASS_NAME, 'cx-agree-check')
login.click()
time.sleep(2)

login = driver.find_element(By.CLASS_NAME, 'login-btn')
login.click()

time.sleep(3)

# article_xpath = '//div[@id="Main_Content_Val"]'
# durl ='https://www.caixin.com/2024-01-31/102162155.html'

for task in params['tasks']:  # type: dict
    print(task)
    driver.get(task['list_url'])

    current_date = int((datetime.now() - timedelta(days=20)).strftime("%Y%m%d"))

    # 最早发布时间
    last_public_time = int(get_date(driver.find_element(By.XPATH, '//div[@class="stitXtuwen_list"]/*[last()]/dd/span').text))
    while last_public_time > current_date:
        pageNavBox = driver.find_element(By.XPATH, '//div[@class="pageNavBox"]//a')
        pageNavBox.click()
        print('pageNavBox')
        time.sleep(2)
        last_public_time = int(get_date(driver.find_element(By.XPATH, '//div[@class="stitXtuwen_list"]/*[last()]/dd/span').text))
        if last_public_time <= current_date:
            break
            print('pageNavBox break')

    infos = []
    elements = driver.find_elements(By.XPATH, '//div[@class="stitXtuwen_list"]/*')

    for element in elements:
        link = element.find_element(By.XPATH, './/h4/a')
        public_time = element.find_element(By.XPATH, './dd/span')
        info = {
            'title': link.text,
            'url': link.get_attribute('href'),
            'public_time': get_public_date(public_time.text),
        }
        print(info)
        infos.append(info)

        pass

    # 详情处理
    for info in infos:
        print(info)



time.sleep(20)
driver.close()
