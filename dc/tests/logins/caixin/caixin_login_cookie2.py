import time

from selenium import webdriver
from selenium.webdriver.common.by import By

from dc.tests.browser import Browser
from dc.tests.logins.proxy_config import *
import pickle


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=firefox_executable_path, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=chrome_executable_path, options=opt)


params = {
    'site': 'https://www.caixin.com/tech/',
    'login_page': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F',
    'tasks': [
        {
            'name': 'tech',
            'list_url': 'https://www.caixin.com/tech/',
            'item_url': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F'
        }
    ],
    'username': '13022183350',
    'password': 'ijiwei@54322'
}


def get_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '')


def get_public_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '') + ' 00:00:00'


driver = get_webdriver(Browser.Chrome)
driver.implicitly_wait(10)


def login():
    driver.get(params['login_page'])

    login = driver.find_element(By.XPATH, '//span[@class="svg-container phone"]')
    print(login.click())

    time.sleep(2)

    login = driver.find_element(By.XPATH, '//input[@name="mobile"]')
    login.send_keys(params['username'])
    time.sleep(2)

    login = driver.find_element(By.XPATH, '//input[@name="password"]')
    login.send_keys(params['password'])
    time.sleep(2)

    login = driver.find_element(By.CLASS_NAME, 'cx-agree-check')
    login.click()
    time.sleep(2)

    login = driver.find_element(By.CLASS_NAME, 'login-btn')
    login.click()

    time.sleep(3)



# 检查是否存在保存的 cookies 文件
try:
    cookies = pickle.load(open("cookies.pkl", "rb"))
    # 加载网站
    driver.get(params['site'])
    for cookie in cookies:
        driver.add_cookie(cookie)
    # 重新加载网站
    driver.refresh()

except FileNotFoundError:
    # 如果没有找到 cookies 文件，则进行正常登录流程
    login()

    # 登录成功后保存 cookies
    pickle.dump(driver.get_cookies(), open("cookies.pkl", "wb"))

    driver.get(params['site'])

# time.sleep(10)

# 处理一个单独页面

detail_url = 'https://www.caixin.com/2024-02-04/102163571.html'
driver.get(detail_url)

print(driver.page_source)
element = driver.find_element(By.XPATH, '//div[@id="Main_Content_Val"]')
print(element.text)


# 结束
driver.quit()
