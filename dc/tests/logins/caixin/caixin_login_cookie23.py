import json
import time

from selenium import webdriver
from selenium.webdriver.common.by import By

from dc.tests.browser import Browser
from dc.tests.logins.proxy_config import *
import pickle
from dc.models.model import DCSiteList, DCSiteListItem, DCSiteListRule, DCSiteTask
from dc.services.mysql_service import MySQLService
from selenium.webdriver.chrome.options import Options
from dc.tests.logins.caixin.caixin_helper import *

params = {
    'site': 'https://www.caixin.com/tech/',
    'login_page': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F',
    'tasks': [
        {
            'name': 'tech',
            'list_url': 'https://www.caixin.com/tech/',
            'item_url': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F'
        }
    ],
    'username': '13022183350',
    'password': 'iji<PERSON>@54322'
}

if False:

    # 检查是否存在保存的 cookies 文件
    try:
        cookies = pickle.load(open("cookies.pkl", "rb"))
        # 加载网站
        driver.get(params['site'])
        for cookie in cookies:
            driver.add_cookie(cookie)
        # 重新加载网站
        driver.refresh()

    except FileNotFoundError:
        # 如果没有找到 cookies 文件，则进行正常登录流程
        login()

        # 登录成功后保存 cookies
        pickle.dump(driver.get_cookies(), open("cookies.pkl", "wb"))

        driver.get(params['site'])

    # time.sleep(10)

# 处理一个单独页面

mysql = MySQLService()
session = mysql.create_session()
site_list_id = 1901
siteList: DCSiteList = session.query(DCSiteList).get(site_list_id)
print(siteList.to_dict())

siteRule: DCSiteListRule = session.query(DCSiteListRule).filter(DCSiteListRule.siteListId == siteList.id).first()
print(siteRule.to_dict())

siteItem: [DCSiteListItem] = session.query(DCSiteListItem).filter(DCSiteListItem.siteRuleId == siteRule.id).all()
print([item.to_dict() for item in siteItem])

from dc.services.dc_task_analysis_service import DcTaskAnalysisService

ta = DcTaskAnalysisService()

dc_extra = json.loads(siteList.extra if siteList.extra else '{}')
analysisMethod = dc_extra.get('analysisMethod', 0)

task_info = {
    "id": 0,
    "siteListId": siteList.id,
    # "mainTaskId":  2389211,
    "baseUrl": "https://www.jiemian.com",
    "url": "https://www.caixin.com/2024-02-04/102163571.html",
    # "title":  "\u8bda\u610f\u836f\u4e1a\uff1a\u76ee\u524d\u516c\u53f8\u751f\u4ea7\u7ecf\u8425\u4e00\u5207\u6b63\u5e38\uff0c\u5927\u5065\u5eb7\u4ea7\u54c1\u5df2\u9010\u6b65\u4e0a\u5e02",
    # "taskStatus":  1,
    # "taskAt":  "2024-02-02 09:10:48",
    # "analysisStatus":  0,
    # "httpStatusCode":  200,
    # "mongoCollection":  "",
    # "mongoKey":  "65bc4117f0617b7129354959",
    # "retryTime":  1,
    # "createdAt":  "2024-02-02 09:08:05",
    # "updatedAt":  "2024-02-02 09:10:48",
    # "esIndex":  "",
    # "esId":  "",
    "ruleId": siteRule.id,
    "rule": siteRule.to_dict(),
    # "publicTime":  "",
    # "coverImg":  "",
    "detailDcMethod": siteList.detailDcMethod,
    "analysisMethod": analysisMethod,
    "extra": dc_extra,
    "detail_info": {
        'url': 'https://www.caixin.com/2024-02-04/102163571.html'
    }
}

driver = get_dc_webdriver(task_info)

task_info2: dict = siteList.to_dict()
task_info2.update(task_info)
mongo_info = {}
ta.analysis(driver, task_info2, mongo_info)

session.close()

# 结束
driver.quit()
