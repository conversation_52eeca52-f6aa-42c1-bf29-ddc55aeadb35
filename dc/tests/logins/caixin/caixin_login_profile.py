import time

from selenium import webdriver
from dc.tests.browser import <PERSON>rowser
from dc.tests.logins.proxy_config import *


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=firefox_executable_path, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=chrome_executable_path, options=opt)


params = {
    'site': 'https://www.caixin.com/',
    'login_page': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F',
    'tasks': [
        {
            'name': 'tech',
            'list_url': 'https://www.caixin.com/tech/',
            'item_url': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F'
        }
    ],
    'username': '13022183350',
    'password': 'ijiwei@54322'
}

# 创建或指定一个用户数据目录
user_data_dir = os.path.join(os.path.dirname(__file__), "user_data")

# 确保目录存在
if not os.path.exists(user_data_dir):
    os.makedirs(user_data_dir)

opt = webdriver.ChromeOptions()
opt.add_argument("no-sandbox")
opt.add_argument("--disable-extensions")
# opt.add_argument("--headless")
opt.add_argument(f"user-data-dir={user_data_dir}")  # 设置 Chrome 选项以使用用户数据目录
driver = webdriver.Chrome(executable_path=chrome_executable_path, options=opt)

# 访问网站
driver.get(params['login_page'])

# ... 进行爬取或其他操作 ...
time.sleep(10)

# driver.close()
driver.quit()
