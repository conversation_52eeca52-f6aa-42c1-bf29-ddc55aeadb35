from selenium import webdriver
from dc.tests.browser import Browser
from dc.tests.logins.proxy_config import *
from dc.models.model import DCSiteList, DCSiteListItem, DCSiteListRule
from dc.services.mysql_service import MySQLService


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=firefox_executable_path, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=chrome_executable_path, options=opt)


params = {
    'site': 'https://www.caixin.com/',
    'login_page': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F',
    'tasks': [
        {
            'name': 'tech',
            'list_url': 'https://www.caixin.com/tech/',
            'item_url': 'https://u.caixin.com/web/login?url=https%3A%2F%2Fwww.caixin.com%2F'
        }
    ],
    'username': '13022183350',
    'password': 'ijiwei@54322'
}


def get_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '')


def get_public_date(date_str: str):
    return date_str.strip().replace("年", '').replace("月", '').replace("日", '') + ' 00:00:00'


mysql = MySQLService()
session = mysql.create_session()
site_list_id = 1901
siteList: DCSiteList = session.query(DCSiteList).get(site_list_id)
print(siteList.to_dict())

siteRule: DCSiteListRule = session.query(DCSiteListRule).filter(DCSiteListRule.siteListId == siteList.id).first()
print(siteRule.to_dict())

siteItem: [DCSiteListItem] = session.query(DCSiteListItem).filter(DCSiteListItem.siteRuleId == siteRule.id).all()
print([item.to_dict() for item in siteItem])

session.close()
exit(0)

driver = get_webdriver(Browser.Chrome)
driver.implicitly_wait(10)

content_xpath = '//div[@id="Main_Content_Val"]'
durl = 'https://www.caixin.com/2024-01-31/102162155.html'
driver.get(durl)

pub = driver.find_element(By.XPATH, '//div[@id="artInfo"]')
pub_time = pub.text.split('\n')[0].replace('年', '-').replace('月', '-').replace('日', '')
print(pub_time)



# article = driver.find_element(By.XPATH, article_xpath)
from dc.common.dc_helper import DcHelper

content = DcHelper.get_content(driver.page_source, content_xpath, By.XPATH)
print(content)

time.sleep(3)
driver.close()
