import json
import pickle
import sys
import os
import time

from selenium.webdriver.common.by import By

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from selenium import webdriver
from dc.tests.browser import Browser
from dc.tests.logins.proxy_config import *
from dc.models.model import DCSiteList, DCSiteTask, DCSiteMainTask, DCSiteListRule

write_mode = 'test'
mysql = MySQLService(run_model=write_mode)
session = mysql.create_session()
url = 'https://www.digitimes.com/'
cookie_file_name = 'cookie.pkl'
siteInfo: [DCSiteList] = session.query(DCSiteList).filter(DCSiteList.siteUrl == url).all()

print(siteInfo)
if siteInfo is None:
    # 退出程序
    print('siteInfo is None')  # 退出程序
    exit(0)
extra = json.loads(siteInfo[0].extra) if siteInfo[0].extra != '' else {}

params = {
    'site': url,
    'login_page': "https://www.digitimes.com/newregister/join.asp",  # TODO  有些网站的登录页面不一样 需要从siteInfo中获取
    'login_check': "https://www.digitimes.com/newregister/join.asp",  # TODO  登录检测页面，检查是否登录状态有效
    'detail_url': 'https://www.digitimes.com/index.asp',  # TODO  有些网站的登录页面不一样 需要从siteInfo中获取
    'username': extra.get('username', ''),
    'password': extra.get('password', '')
}


def get_list(driver, site, history_links):
    """
    获取列表
    :param driver:  webdriver
    :param site: 网站配置
    :param history_links: 历史记录
    """
    blocklist = driver.find_elements(by='xpath', value=site.ListSelector)
    if len(blocklist) == 0:
        # 如果没有链接 则继续
        return []
    datas = []
    repeatLinks = 0
    for l in blocklist:
        link = l.find_element(by='xpath', value=site.crawlRule)
        title = l.find_element(by='xpath', value=site.crawlRuleTtile).text if site.crawlRuleTtile != '' else link.text
        public_time = l.find_element(by='xpath', value=site.TimeSelector).text if site.TimeSelector != '' else ''
        image = l.find_element(by='xpath', value=site.CoverImageSelector).get_attribute(
            'src') if site.CoverImageSelector != '' else ''
        if link not in history_links:
            datas.append({
                'url': link.get_attribute('href'),
                'title': title,
                'time': public_time,
                'image': image
            })

        else:
            repeatLinks += 1
    return datas, repeatLinks


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=firefox_executable_path, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=chrome_executable_path, options=opt)


# params = {
#     'site': 'https://www.digitimes.com',
#     'login_page': 'https://www.digitimes.com/newregister/join.asp',
#     'username': '<EMAIL>',
#     'password': 'ijiwei@com9527'
# }
def get_next_page(driver, site):
    return


def run(p):
    all_links = []
    if p['username'] == '' or p['password'] == '':
        exit(0)  # 如果没有配置账号密码,则直接退出

    driver = get_webdriver(Browser.Chrome)
    driver.implicitly_wait(10)
    # driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
    #     'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
    # })  # 为了解决无头浏览器无法访问的问题
    driver.get(p['login_page'])
    # 如何cookie文件存在 则加载cookie
    if os.path.exists(cookie_file_name):
        with open(cookie_file_name, 'rb') as f:
            cookies = pickle.load(f)
            for cookie in cookies:
                driver.add_cookie(cookie)
            driver.refresh()
    else:
        login = driver.find_element(By.XPATH, '//input[@name="EMail"]')
        login.send_keys(p['username'])

        login = driver.find_element(By.XPATH, '//input[@name="Password"]')
        login.send_keys(p['password'])

        # login = driver.find_element(By.XPATH, '//span[@class="checkmark"]')
        # login.click()
        # time.sleep(5)

        login = driver.find_element(By.XPATH, '//button[@name="Submit"]')
        login.click()
        cookies = driver.get_cookies()

        # 存储Cookie到文件
        with open("cookies.pkl", "wb") as f:
            pickle.dump(cookies, f)

    for site in siteInfo:  # type: DCSiteList
        session.flush()
        siteTask = session.query(DCSiteTask).filter(DCSiteTask.siteListId == site.id).order_by(
            DCSiteTask.id.desc()).limit(
            50)
        history_links = [task.url for task in siteTask] if siteTask is not None else []

        siteRule: DCSiteListRule = session.query(DCSiteListRule).filter(DCSiteListRule.siteListId == site.id).first()
        # print(siteRule.to_dict())

        # 打开列表页
        driver.get(site.detailUrl)

        # 获取数据库中siteTask的历史记录
        while True:
            # 获取列表
            links_list, repeat = get_list(driver, site, history_links)
            # 将links_list 逐条添加到 all_links中
            all_links.extend(links_list)
            if repeat >= 2:
                print('重复链接已不支持翻页')
                # 如果达到预期 则对比历史记录并根据历史记录去重 并取消翻页的操作
                break
            else:
                if site.listUrl != "":
                    get_next_page(driver, siteInfo)
                # 翻页操作
                # 判断是否是滚动加载,  如果是滚动加载 则判断条数 或者时间 是否达到预期
                pass
                break

        # 翻转all_links的元素位置
        all_links.reverse()  # 倒序

        # 单独处理每一条的all_links 打开新的网页并抓取详情
        for link in all_links:
            pass

        # 写入主任务
        session.add(
            DCSiteMainTask(siteListId=site.id, url=site.siteUrl, listUrl=site.detailUrl, listUrlCount=len(all_links),
                           httpStatusCode=200))

        # 批量写入数据库
        for link in all_links:
            task = DCSiteTask(siteListId=site.id, url=link['url'], title=link['title'], publicTime=link['time'],
                              coverImg=link['image'])
            session.add(task)
            session.commit()

            # 子任务处理

            from dc.services.dc_task_analysis_service import DcTaskAnalysisService

            ta = DcTaskAnalysisService()
            dc_extra = json.loads(site.extra if site.extra else '{}')
            analysisMethod = dc_extra.get('analysisMethod', 0)

            task_info_old = {
                "id": 0,
                "siteListId": site.id,
                # "mainTaskId":  2389211,
                "baseUrl": "https://www.jiemian.com",
                "url": task.url,
                "title": task.title,
                "ruleId": siteRule.id,
                "rule": siteRule.to_dict(),
                "detailDcMethod": site.detailDcMethod,
                "analysisMethod": analysisMethod,
                "extra": dc_extra,
            }
            # driver = get_dc_webdriver(task_info)

            site_info = site.to_dict()
            site_info['ruleItem'] = ta.get_rule(site.id)
            site_info["analysisMethod"] = analysisMethod
            task_info: dict = task.to_dict()
            ta.analysis(driver, site_info, task_info)

        session.commit()
        # 根据剩余列表 打开详情并获取详情的信息
        time.sleep(300)

    driver.close()
    return all_links


run(params)
