import sys
import os
import time

from selenium.webdriver.common.by import By

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from selenium import webdriver
from common.browser import <PERSON><PERSON>er
from dc.tests.logins.proxy_config import *


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=firefox_executable_path, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        # opt.add_argument("--headless")
        return webdriver.Chrome(executable_path=chrome_executable_path, options=opt)


params = {
    'site': 'https://www.digitimes.com',
    'login_page': 'https://www.digitimes.com/newregister/join.asp',
    'username': '<EMAIL>',
    'password': 'ijiwei@com9527'
}


driver = get_webdriver(Browser.Chrome)
driver.implicitly_wait(10)
driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
        'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
    })  # 为了解决无头浏览器无法访问的问题
driver.get(params['login_page'])

login = driver.find_element(By.XPATH, '//input[@name="EMail"]')
login.send_keys(params['username'])

login = driver.find_element(By.XPATH, '//input[@name="Password"]')
login.send_keys(params['password'])

# login = driver.find_element(By.XPATH, '//span[@class="checkmark"]')
# login.click()
# time.sleep(5)

login = driver.find_element(By.XPATH, '//button[@name="Submit"]')
login.click()


time.sleep(10)
driver.close()
