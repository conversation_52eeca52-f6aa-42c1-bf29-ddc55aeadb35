# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/1/19
# Version    : 1.0

import platform
import os

if os.name == "nt":
    firefox_binary_location = r"C:\Program Files\Mozilla Firefox\firefox.exe"
    firefox_executable_path = r"C:\Windows\System32\geckodriver.exe"

    chrome_binary_location = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    chrome_executable_path = r"D:\phpstudy_pro\Extensions\php\php7.4.3nts\chromedriver.exe"

else:
    firefox_binary_location = r"/usr/bin/firefox"
    firefox_executable_path = r"/usr/local/bin/geckodriver"

    chrome_binary_location = '/usr/bin/google-chrome'
    chrome_executable_path = '/usr/local/bin/chromedriver'


