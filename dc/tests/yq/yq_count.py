# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 文章处理数量统计 [仅测试]
# <AUTHOR> wjh
# @Time      : 2023/10/07
# Version    : 1.0

import datetime
import json
import os
import sys
import time
import traceback
import requests
from sqlalchemy import desc
from dc.common.utils import Utils
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import DCSiteTask, DCGzhArticle, DCArticle, DCHotArticleWordsWeight
from jwtools.io_util import *
from jwtools.func import *
from jwtools.dt import *

mysql = MySQLService('mysql', run_model='test')
redisService = RedisService('redis', db=6,run_model='prod')
client2 = redisService.get_client()
session = mysql.create_session()

last_count = {
    'redis_count': 0,
    'article_count': 0,
    'hot_article_count': 0,
}

while True:
    print_line('')
    redis_count = client2.llen('dc_article_weight')
    article_count = session.query(DCArticle).count()
    hot_article_count = session.query(DCHotArticleWordsWeight).count()

    current_count = {
        'redis_count': redis_count,
        'article_count': article_count,
        'hot_article_count': hot_article_count,
    }

    change_count = dict(map(lambda x, y: (x[0], y[1] - x[1]), last_count.items(), current_count.items()))
    print(f"current_count: {current_count}")
    print(f"change_count: {change_count}")

    last_count.update(current_count)
    session.commit()

    time.sleep(30)
