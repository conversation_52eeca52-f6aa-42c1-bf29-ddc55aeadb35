# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 文章初始化加入队列 [仅测试]
# <AUTHOR> wjh
# @Time      : 2023/10/07
# Version    : 1.0

import datetime
import json
import os
import sys
import traceback
import requests
from sqlalchemy import desc
from dc.common.utils import Utils
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import DCSiteTask, DCGzhArticle
from jwtools.io_util import *
from jwtools.func import *

mysql = MySQLService('mysql', run_model='prod')
session = mysql.create_session()

redisService = RedisService('redis', db=6)
client2 = redisService.get_client()

wechat_file = Utils.get_project_path('tmp/wechat_last_id')
yq_file = Utils.get_project_path('tmp/yq_last_id')

page_count = 50
page_size = 1000


def init_yq():
    print_line('init_yq')
    # task_id = 3930376
    task_id = read_text_from_file(yq_file)
    for page in range(0, page_count):
        ret = session.query(DCSiteTask) \
            .filter(DCSiteTask.id < task_id, DCSiteTask.esIndex == 'dc_yq') \
            .order_by(DCSiteTask.id.desc()) \
            .offset(page * page_size) \
            .limit(page_size) \
            .all()
        if not ret:
            continue
        for info in ret:
            tmp = info.to_dict()
            client2.lpush('dc_article_weight', json.dumps({'taskId': tmp['id'], 'type': 1}))

        last_id = ret[-1].id
        print(f"page: {page} last_id: {last_id}")

        write_text_to_file(yq_file, str(last_id))


def init_wechat():
    print_line('init_wechat')
    # wechat_id = 1081960
    wechat_id = read_text_from_file(wechat_file)
    for page in range(0, page_count):
        ret1 = session.query(DCGzhArticle) \
            .filter(DCGzhArticle.id <= wechat_id) \
            .order_by(DCGzhArticle.id.desc()) \
            .offset(page * page_size) \
            .limit(page_size) \
            .all()
        if not ret1:
            continue
        for info1 in ret1:
            tmp1 = info1.to_dict()
            client2.lpush('dc_article_weight', json.dumps({'taskId': tmp1['id'], 'type': 2}))

        last_id = ret1[-1].id
        print(f"page: {page} last_id: {last_id}")

        write_text_to_file(wechat_file, str(last_id))

    session.close()


def get_set_last_id(key: str, val=None):
    if val:
        result = client2.set(key, val)
    else:
        result = client2.get(key)

    return result


init_wechat()

init_yq()
