# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 舆情数据清理 [仅测试]
# <AUTHOR> wjh
# @Time      : 2023/10/07
# Version    : 1.0

import datetime
import json
import os
import sys
import traceback
import requests
from sqlalchemy import desc
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import DCSiteTask, DCGzhArticle, DCHotArticleWordsWeight,DCArticle
from dc.services.elasticsearch_service import ElasticsearchService

mysql = MySQLService('mysql', run_model='test')
session = mysql.create_session()

redisService = RedisService('redis', db=6)
client2 = redisService.get_client()

ess = ElasticsearchService(run_model='test')
es = ess.get_connect()
test_index = 'dc_hotarticle'
redis_key = 'dc_article_weight'

input("请输入任意键继续")


# DCHotArticleWordsWeight
result = session.query(DCHotArticleWordsWeight).update({
    'day1Weight': 0,
    'day2Weight': 0,
    'day3Weight': 0,
    'day4Weight': 0,
    'day5Weight': 0,
    'day6Weight': 0,
    'day7Weight': 0,
    'weight': 0,
})
print(result)

# DCArticle
result = session.query(DCArticle).delete()
print(result)

# redis dc_article_weight
result = client2.delete(redis_key)
print(result)

# es delete
result = es.indices.delete(index=test_index)
print(result)


session.commit()

# close
es.close()
client2.close()
session.close()





