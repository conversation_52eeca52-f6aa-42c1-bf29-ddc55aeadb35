from dc.common.webdriver_util import *

webdriver_url = "http://192.168.1.181:4444"
# url = "https://www.baidu.com"
url = "http://mp.weixin.qq.com/s?__biz=MzIyMjAxOTE2Mg==&mid=2247497234&idx=1&sn=036f412248c805f0a767403ad490c158&chksm=e90796e8134c80febc67191e188c2c5f8d12d1845aba9da274b46cd180d6667004db10ce2f7b&scene=126&sessionid=1709005155#rd"

driver = get_remote_webdriver(webdriver_url, Browser.Chrome)
# driver = get_webdriver(Browser.Chrome)
# driver.implicitly_wait(10)


driver.get(url)
# print(driver.title)
title = driver.title
print(driver.page_source)
print(title)
driver.quit()