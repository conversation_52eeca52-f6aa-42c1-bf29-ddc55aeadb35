import unittest
from flask import current_app
from dc.app import create_app, db, app


class BasicsTestCase(unittest.TestCase):
    def setUp(self):
        self.app = app
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.db = db
        #db.create_all

    def tearDown(self):
        #db.session.remove()
        #db.drop_all()
        #db.session.commit()
        self.app_context.pop()

    def test_hello(self):
        print('hello')
