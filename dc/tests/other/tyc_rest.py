# -*-coding:utf-8-*-
import datetime
import random
import time
import sys
import os

from selenium import webdriver
from selenium.webdriver import Keys, DesiredCapabilities
from selenium.webdriver.chrome.options import Options

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.qianxun_service import QianxunService

from dc.models.model import MPCompanyInfo, MPCompanyRecruitOriginal

"""
[功能]：数据采集-招聘数据采集
[作者]：lws
[日期]：2022-10-19
"""

mysql = MySQLService('mysql_company')
un = '18810072465'
pwd = 'ijiweibj08f'

less_money = 50
max_daily_dec = 1
daily_money = {}
qx = QianxunService()
msg_type = 9000


def fix_money(str=''):
    str = str.replace('元', '')
    return str.replace(',', '')


# 写入主任务队列
def handel():
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--headless")
    chrome_options.add_experimental_option("excludeSwitches", ["ignore-certificate-errors"])

    ca = DesiredCapabilities.CHROME
    ca["goog:loggingPrefs"] = {"performance": "ALL"}
    driver = webdriver.Chrome(options=chrome_options, desired_capabilities=ca)
    driver.implicitly_wait(10)
    driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
        'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
    })

    driver.get('http://open.tianyancha.com/')
    print(driver.page_source)
    # 点击登录/注册
    driver.find_element(by='xpath', value='/html/body/div[1]/div/div/div[1]/div[1]/div/div[2]/div/div').click()
    time.sleep(random.randint(1, 3))
    # 点击密码登录

    driver.find_element(by='xpath', value='/html/body/div[1]/div/div[2]/div/div/div[1]/div/div[1]/div[2]').click()
    time.sleep(random.randint(1, 3))
    # 输入账号
    print('点击密码登录')
    driver.find_element(by='xpath',
                        value='/html/body/div[1]/div/div[2]/div/div/div[1]/div/div[2]/div[1]/input').send_keys(un)
    time.sleep(random.randint(1, 3))
    print('输入账号')  # 输入密码

    driver.find_element(by='xpath',
                        value='/html/body/div[1]/div/div[2]/div/div/div[1]/div/div[2]/div[2]/input').send_keys(pwd)
    time.sleep(random.randint(1, 3))
    print('输入密码')
    # 点击我已同意该协议

    driver.find_element(by='xpath',
                        value='/html/body/div[1]/div/div[2]/div/div/div[1]/div/div[2]/div[5]/label/span/input').click()
    time.sleep(random.randint(1, 3))
    print('点击我已同意该协议')
    # 点击登录

    driver.find_element(by='xpath',
                        value='/html/body/div[1]/div/div[2]/div/div/div[1]/div/div[2]/div[4]/div[1]').click()
    time.sleep(5)
    print('点击登录')
    while True:

        # 跳转到个人中心
        driver.get('https://open.tianyancha.com/console/usercenter')
        time.sleep(random.randint(1, 5))

        # 获取金额
        money = driver.find_element(by='xpath',
                                    value='/html/body/div[1]/div/div/div[1]/div[3]/div/div/div[2]/div[1]/span[2]').text
        print(f"获取到余额为 {money}")
        money = fix_money(money)
        money = float(money)
        print(money, type(money))
        d = datetime.date.today()
        today = f"{d}"
        if today not in daily_money:
            daily_money[today] = money
        # 判断金额或者当天金额超标
        if money < 3000:
            msg = f"天眼查余额过低 : {money}, 请联系人员及时充值"
            qx.simple_send({
                'type': msg_type,
                'msg': msg,
                'mail_title': msg,
                'mail_body': msg,
            })
        elif (daily_money[today] - money) > max_daily_dec:
            msg = f"天眼查余额今日消耗已达 : {daily_money[today] - money} 元"
            qx.simple_send({
                'type': msg_type,
                'msg': msg,
                'mail_title': msg,
                'mail_body': msg,
            })

        time.sleep(600)

    driver.close()


handel()

# d = datetime.date.today()
# date_money = {}
# date_money[f"{d}"] = 222.22
# print(d, date_money)

# m = '2086.21'
# print(float(m))
