import sys
import os

import requests


sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.conf.settings import get_settings2
from dc.common.utils import Utils
from dc.tests.browser import Browser
from dc.common import utils
from urllib.parse import urlparse, parse_qs, parse_qsl
from urllib import request

zz = get_settings2('webdriver', 'host')
print(zz)
exit(0)

# r = requests.get("https://blog.csdn.net/sinat_32857543/article/details/124754502")
#url = 'http://scjgj.zhangzhou.gov.cn/cms/siteresource/article.shtml?id=60458196718550004&siteId=60456039315100001'
#url = 'http://gxj.xa.gov.cn/gzdt/tzgg/5e831711f99d6502031840ff.html'
#url = "https://www.liepin.com/zhaopin/?key=百度&currentPage=1"
url = "http://zjjcmspublic.oss-cn-hangzhou-zwynet-d01-a.internet.cloud.zj.gov.cn/jcms_files/jcms1/web3387/site/attach/0/a562192cb1e64981afc8ea997f4e9ac2.pdf"

def req_requests():
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
    }
    r = requests.get(url, headers=headers)
    r.encoding = 'utf-8'
    print(r.text)
    exit(0)

    path = urlparse(url).path
    npath = path.removeprefix('/').replace('/', '_') + '.html'
    project_path = Utils.get_project_path()
    file = project_path + '/tmp/' + npath
    print(file)
    with open(file, "wb") as f:
        f.write(r.content)


def req_requests_time():

    all_time = 0
    num = 10
    for i in range(0, num):

        print(f"---{i}----")
        time1 = Utils.millisecond_time()
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        }
        r = requests.get(url, headers=headers)
        r.encoding = 'utf-8'
        time2 = Utils.millisecond_time()
        exec_time = time2 - time1
        all_time += exec_time
        print(f"status_code {r.status_code}")
        print(f'{time1} - {time2} => {exec_time}')

    avg_time = all_time/num
    print(f"avg_time: {avg_time}")


def req_requests_one():
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
    }


    print(Utils.is_file('http://zjjcmspublic.oss-cn-hangzhou-zwynet-d01-a.internet.cloud.zj.gov.cn/jcms_files/jcms1/web3387/site/attach/0/a562192cb1e64981afc8ea997f4e9ac2.pdf'))
    print(Utils.is_file('http://zjjcmspublic.oss-cn-hangzhou-zwynet-d01-a.internet.cloud.zj.gov.cn/jcms_files/jcms1/web3387/site/attach/0/a562192cb1e64981afc8ea997f4e9ac2.html'))
    print(Utils.is_file('http://zjjcmspublic.oss-cn-hangzhou-zwynet-d01-a.internet.cloud.zj.gov.cn/jcms_files/jcms1/web3387/site/attach/0/a562192cb1e64981afc8ea997f4e9ac2'))
    print(Utils.is_file('http://zjjcmspublic.oss-cn-hangzhou-zwynet-d01-a.internet.cloud.zj.gov.cn/jcms_files/jcms1/web3387/site/attach/0/a562192cb1e64981afc8ea997f4e9ac2.doc'))
    print(Utils.is_file('http://zjjcmspublic.oss-cn-hangzhou-zwynet-d01-a.internet.cloud.zj.gov.cn/jcms_files/jcms1/web3387/site/attach/0/a562192cb1e64981afc8ea997f4e9ac2.doc?xx=dsdsfksfs'))

    exit(0)

    r = requests.get(url, headers=headers)
    r.encoding = 'utf-8'
    time2 = Utils.millisecond_time()



def req_urllib():
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
    }
    # 将请求信息包装好
    # req = urllib.request.Request(url=url,data=data,headers=headers,method="GET")
    req = request.Request(url=url, headers=headers, method="GET")
    # 发送请求信息得到响应
    response = request.urlopen(req)
    print(response.read().decode("utf-8"))


#req_urllib()

#req_requests_time()


#req_requests()
req_requests_one()
