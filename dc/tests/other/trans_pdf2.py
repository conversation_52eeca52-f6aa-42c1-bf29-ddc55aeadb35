import PyPDF2
import pdfplumber
from googletrans import Translator
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
# 打开原始 PDF 文件和新的 PDF 文件
pdf_file = open('test.pdf', 'rb')
output_file = open('translated.pdf', 'wb')
# 创建 PDF Reader 和 PDF Writer
pdf_reader = PyPDF2.PdfReader(pdf_file)
pdf_writer = PyPDF2.PdfWriter()
# 遍历每一页的内容
for page_num in range(len(pdf_reader.pages)):
    # 读取原始 PDF 页面文本内容
    page = pdf_reader.pages[page_num]
    page_text = page.extract_text()
    # 将英文文本翻译成中文文本
    translator = Translator()
    translated_text = translator.translate(page_text, dest='zh-CN').text
    # 使用 pdfplumber 库读取 PDF 页面文本内容
    with pdfplumber.open('original.pdf') as pdf:
        pdf_page = pdf.pages[page_num]
        text_objects = pdf_page.extract_words()
    # 将英文文本替换成中文文本
    for word in text_objects:
        if word["text"] in page_text:
            page_text = page_text.replace(word["text"], translated_text[text_objects.index(word)])
    # 使用 reportlab 库创建新的 PDF 页面
    c = canvas.Canvas(page_num)
    c.setPageSize(letter)
    c.drawString(100, 750, page_text)
    c.save()
    # 将新的 PDF 页面添加到 PDF Writer 中
    pdf_writer.addPage(PyPDF2.PdfFileReader(f'{page_num}.pdf').getPage(0))
# 将新的 PDF 文件保存到磁盘中
pdf_writer.write(output_file)
# 关闭文件
pdf_file.close()
output_file.close()