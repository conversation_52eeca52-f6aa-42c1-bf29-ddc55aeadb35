import random
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.common import utils

user = '<EMAIL>'
password = 'JiWeiWang1234'
host = 'smtpdm.aliyun.com:80'
host2 = 'smtpdm.aliyun.com'

user2 = {
    '<EMAIL>': '小微2'
}

recipients = {
    '<EMAIL>': '小王'
}


smtp_user = {"<EMAIL>": "小微"}
smtp_user_email = "<EMAIL>"

from_user = {"<EMAIL>": "中台小助手"}

def test_yagmail():
    import yagmail

    # yagmail.SMTP(user='发件人邮箱账号', password='授权码', host='SMTP 服务器域名')
    yag = yagmail.SMTP(smtp_user, password=password, host=host2, port=80, smtp_ssl=False, smtp_starttls=False)
    contents = '邮件内容' + str(random.randint(1000,9999))  # 邮件内容
    #contents['From'] = '小李'
    subject = '第一封邮件2'  # 邮件主题
    receiver = '<EMAIL>'  # 接收方邮箱账号
    #yag.user = {user:'dsfs'}
    yag.send(receiver, subject, contents)
    yag.close()
    print('发送成功')


def test_smtplib():
    import smtplib
    from email.mime.text import MIMEText
    from email.header import Header
    # 编写HTML类型的邮件正文
    msg = MIMEText('<html><h1>你好2！</h1></html>', 'html', 'utf-8')
    msg['Subject'] = Header('邮件标题4', 'utf-8')
    msg['From'] = '小王'
    # 连接发送邮件
    smtp = smtplib.SMTP()
    smtp.connect(host)
    smtp.login(user, password)
    smtp.sendmail(from_addr=smtp_user_email, to_addrs='<EMAIL>', msg=msg.as_string())
    smtp.quit()


#test_smtplib()

test_yagmail()

#        $transport = Transport::fromDsn('smtp://<EMAIL>:<EMAIL>:80');





