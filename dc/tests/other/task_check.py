import sys
import os

from sqlalchemy.orm import Query

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from dc.models.model import DCSiteList
from dc.services.task_again_service import TaskAgainService
from dc.common.utils import Utils
from elasticsearch import Elasticsearch
from dc.conf.settings import get_settings
from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.mysql_service import MySQLService

# conf = get_settings('elasticsearch')
# es = Elasticsearch("http://192.168.1.123:9200")


redis_prefix = 'laravel_database_'
redis_analyse_key = f'{redis_prefix}dc-site-task-analyse'
redis_task_key = f'{redis_prefix}dc-site-task'
redis_task_blacklist = f'{redis_prefix}dc-site-task-blacklist'

logger = LoggingService('task-check.log')


def check_one(site_list_id):
    try:

        ess = ElasticsearchService()
        es = ess.get_connect()

        mysql = MySQLService()
        session = mysql.create_session()

        redisService = RedisService()
        redis = redisService.get_client()

        query: Query = session.query(DCSiteList).filter_by(id=site_list_id)
        row: DCSiteList = query.first()
        if row is None:
            logger.info(f"site list is empty")
        else:
            logger.info(f"site list id: {row.id}")
            # logger.info(f"site list2: {row.to_json()}")
            # print(row.id, row.status, row.version)
            site_list_id = row.id

            # task exec
            ts = TaskAgainService()
            ts.main_site_list_check({"site_list_id": site_list_id}, logger=logger)

    except Exception as ex:
        print(ex)

    finally:
        session.close()
        es.close()
        redis.close()


def check_all():
    try:

        ess = ElasticsearchService()
        es = ess.get_connect()

        mysql = MySQLService()
        session = mysql.create_session()

        redisService = RedisService()
        redis = redisService.get_client()

        query: Query = session.query(DCSiteList).filter_by(status=1, version=2).order_by(DCSiteList.id)
        data = query.all()
        row: DCSiteList
        for row in data:
            logger.info('------------------------')
            if row is None:
                logger.info(f"site list is empty")
                continue
            else:
                logger.info(f"site list id: {row.id}")
                # logger.info(f"site list2: {row.to_json()}")
                # print(row.id, row.status, row.version)
                site_list_id = row.id

                # task exec
                ts = TaskAgainService()
                ts.main_site_list_check({"site_list_id": site_list_id}, logger=logger)

    except Exception as ex:
        print(ex)

    finally:
        session.close()
        es.close()
        redis.close()


site_list_id = 212
check_one(site_list_id)

#check_all()

