#! /usr/bin/python
# coding=utf-8
"""
功能简要说明

功能详细说明
:author: wjh
:date: 2022-11-13
:version: 1.0.0
"""

import json
import random

import requests
import sys
import os
import time

from bs4 import BeautifulSoup


def dosth(type: int, desc: str, **kwargs) -> dict:
    """
    这里是注释，没有别的
    <AUTHOR>
    @date 2022-11-13 19:28:35
    :param type: 类型
    :param desc: 描述
    :param kwargs: 其它参数
    :return:处理后结果
    """
    print(type, desc, kwargs)
    pass

def dosth2(type: int, desc: str, **kwargs) -> dict:
    """
    这里是注释，没有别的
    :author wjh
    :date 2022-11-13 19:28:35
    :param type: 类型
    :param desc: 描述
    :param kwargs: 其它参数
    :return:处理后结果
    """
    print(type, desc, kwargs)
    pass


map()

result = dosth(1, 'sss', g=123, b=456)
print(result)

result = dosth2(1, 'sss', g=123, b=456)
print(result)
