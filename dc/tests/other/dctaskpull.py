import random
import traceback
from time import sleep

import urllib3

from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.models.model import DC<PERSON>iteTask, DCSiteList
from dc.services.logging_service import LoggingService
from dc.services.mongo_service import MongoService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

mysql = MySQLService()
# session = mysql.create_session()

redisService = RedisService('redis')
ms = MongoService()

redis_prefix = 'laravel_database_'

redis_analyse_key = f'{redis_prefix}dc-site-task-analyse'
redis_task_key = f'{redis_prefix}dc-site-task'
redis_task_blacklist = f'{redis_prefix}dc-site-task-blacklist'

logService = LoggingService('dc-test.log')


def handle():
    logService.dcPullLog('启动数据采集拉取服务...')
    max_retry_time = get_settings('dc.task-pull.max_retry_time', 3)
    timeout = get_settings('dc.task-pull.timeout', 20)
    sleep_time = get_settings('dc.task-pull.sleep', 10)
    redis = redisService.get_client()
    session = None
    prefix = None

    len = redis.llen(redis_task_key)
    logService.dcPullLog(f"{prefix} task len {len}")

    for i in range(0, len):
        try:
            taskId = redis.lindex(redis_task_key, i)
            logService.dcPullLog(f"{prefix} taskId index {i} taskId {taskId} ...")

            rand = random.randint(100000, 999999)
            prefix = f"[{rand}] "

            dt = Utils.showTime2()
            logService.dcPullLog(f'{prefix} ============================================' + dt)

            session = mysql.create_session()
            #taskId = redis.rpop(f'{redis_task_key}')
            if taskId is None:
                sleep(sleep_time)
                logService.dcPullLog(f"{prefix} taskId is empty, sleep {sleep_time} ...")
                continue

            taskId = int(taskId)
            logService.dcPullLog(f"{prefix} taskId:{taskId} type: {type(taskId)}")

            task: DCSiteTask = session.get(DCSiteTask, taskId)
            if task is None:
                logService.dcPullLog(f"{prefix} taskId:{taskId} task empty")
                sleep(10)
                continue

            # 重复执行
            if task.taskStatus > 0:
                logService.dcPullLog(f"{prefix} taskId:{taskId} task 重复执行")

            # retryTime
            if task.retryTime > max_retry_time:
                logService.dcPullLog(f"{prefix} taskId: {taskId} retryTime {task.retryTime} > {max_retry_time}")
                continue

            site_list: DCSiteList = session.query(DCSiteList).filter_by(id=task.siteListId).first()
            logService.dcPullLog(f'{prefix} taskId: {taskId} detailDcMethod: {site_list.detailDcMethod}')

            session.close()

        except Exception as ex:
            if session is not None:
                session.close()
            logService.info(f"{prefix} pull exception:" + traceback.format_exc())


handle()
