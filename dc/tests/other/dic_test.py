import json
import sys
import os


sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.common.utils import Utils
from dc.models.model import DCSiteTask, DCSiteMainTask, InformationSchemaColumns
from dc.services.mysql_service import MySQLService

mysql = MySQLService()
session = mysql.create_session()

# 只是个测试

#extra = '{"browserName":"firefox","analysisMethod":1,"nextPage":"multi"}'
extra = '{"analysisMethod2":"1","nextPage":"redirect"}'


if extra:
    jj: dict = json.loads(extra)
    # {"browserName":"firefox","analysisMethod":1,"nextPage":"multi"}
    jj['pp'] = jj.get('pp', '')
    print(jj)
    # if pp and pp == 'firefox':
    #     print(2222)
    # else:
    #     print(1111)
    # exit(0)

#zzz = session.query(InformationSchemaColumns).filter(InformationSchemaColumns.TABLE_NAME == 'DC_SiteList').all()
#zzz = session.query(InformationSchemaColumns).all()
#print(zzz)

# conn = mysql.create_connect()
# # 执行查询操作
# results = conn.execute('select * from `information_schema`.`columns` where TABLE_SCHEMA=\'information_schema\' and table_name=\'columns\';')
# # 从查找的结果中遍历
# for result in results:
#     print(result)





