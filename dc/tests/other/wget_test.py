import sys
import os

import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.common.utils import Utils
from dc.tests.browser import Browser
from dc.common import utils
from urllib.parse import urlparse, parse_qs, parse_qsl
from urllib import request

# r = requests.get("https://blog.csdn.net/sinat_32857543/article/details/124754502")
url = 'http://xakj.xa.gov.cn/kjdt/tzgg/5db11459fd85085f475dc1af.html'
#url = 'http://gxj.xa.gov.cn/gzdt/tzgg/5e831711f99d6502031840ff.html'

import wget

url = 'http://cache.yisu.com/upload/information/20200622/113/21696.png'
wget.download(url, './logo.png')
