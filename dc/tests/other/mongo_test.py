import sys
import os
import time

from bson import ObjectId
from faker import Faker
from pymongo import ReturnDocument
from pymongo.results import InsertOneResult, InsertManyResult, DeleteResult, UpdateResult

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.common.utils import Utils

# 参照：
# https://blog.csdn.net/wanger5354/article/details/122118712
from dc.services.mongo_service import MongoService

# conn = pymongo.MongoClient('****************************************************')
# database = conn.get_database('yuqing')
# collection = database.get_collection('test-2')

mongo = MongoService()
collection = mongo.get_collection('site-task')

zz= collection.find_one({})

exit(0)


# one = collection.find_one()
# print(one)

# collection.insert_one({"id": 1, "name": "xxx1"})

# collection.insert_many([
#    {"id": 2, "name": "xxx2"},
#    {"id": 3, "name": "xxx3"}
# ])


def test_old():
    one = collection.find_one({"id": 2})
    print(one)
    # 更新一条数据
    # collection.update_one({"id": 2}, {'$set': {"name": "xxx22"}})
    one = collection.find_one({"id": 2})
    # one2 = collection.find({"_id": ObjectId("2")})
    print(one)
    exit(0)
    rows = collection.find()
    for row in rows:
        print(row)


# test_old()


def test_find():
    rows = collection.find({"taskId": 304075})
    for row in rows:
        print(row)


# test_find()


faker: Faker = Faker(locale='zh_CN')  # 配置为中文


def get_row(id):
    result = dict()
    result['id'] = id
    result['name'] = faker.name()
    result['age'] = faker.random.randint(10, 30)
    result['taskId'] = faker.random.randint(100000, 999999)
    result['mainTaskId'] = faker.random.randint(10000, 99999)
    result['listSiteId'] = faker.random.randint(1000, 9999)
    # 1、地址
    result['addr'] = faker.address()
    # 2、邮箱
    result['email'] = faker.email()
    # 3、公司名称
    result['company'] = faker.company()
    # 4、公司邮箱
    result['company_email'] = faker.company_email()
    # 5、工作岗位
    result['job'] = faker.job()

    # 6、更多个人信息
    result['memo'] = faker.text()
    result['created'] = Utils.showDateTime()

    return result


def insert_init_data():
    start_time = Utils.millisecond_time()
    for i in range(0, 1000000):
        vv = get_row()
        # print(vv)
        result: InsertOneResult = collection.insert_one(vv)
        print(result.inserted_id)
    end_time = Utils.millisecond_time()
    exec_time = end_time - start_time
    print(f"exec_time: {exec_time}")


# insert_init_data()

inserted = []
batch_size = 1000


def insert_batch_test():
    start_time = Utils.millisecond_time()
    for i in range(0, 100):
        row = get_row(i)
        # print(f"--{i}--")
        # print(f"--{row}--")
        batch_insert(row)

    end_time = Utils.millisecond_time()
    exec_time = end_time - start_time
    print(f"exec_time: {exec_time}")

    batch_insert(None, True)


def batch_insert(row: dict = [], force=False):
    if force and len(inserted) > 0:
        result: InsertManyResult = collection.insert_many(inserted)
        print(result.inserted_ids)
        inserted.clear()

    inserted.append(row)
    if len(inserted) >= batch_size:
        result: InsertManyResult = collection.insert_many(inserted)
        print(result.inserted_ids)
        inserted.clear()


def find_test2():
    start_time = Utils.millisecond_time()
    zz = collection.find({'listSiteId': 2888})
    cc = 0
    for r in zz:
        print(r['name'])
        cc = cc + 1
    print(cc)
    end_time = Utils.millisecond_time()
    exec_time = end_time - start_time
    print(f"exec_time: {exec_time}")


def test_xcds():
    global filter
    mid = ObjectId('632d367972dc33fa7b0957bc')
    filter = {"_id": mid}
    doc = collection.find_one(filter)
    print(doc)
    result = collection.update_one(filter, {'$set': {"age": 8888}}, upsert=True)
    print(result)
    doc = collection.find_one(filter)
    print(doc)


#test_xcds()
def test_delete_manay():
    filter = {"age": 23}
    result = collection.count_documents(filter)
    print(result)

    # delete manay
    result: DeleteResult = collection.delete_many(filter)
    print(f"mongo result : {result.deleted_count}")

    result = collection.count_documents(filter)
    print(result)


#test_delete_manay()


insert_batch_test()

# find_test2()

#jj = "{'taskId': 437526, 'mainTaskId': 10269, 'siteListId': 212, 'url': 'http://stcsm.sh.gov.cn/zwgk/tzgs/gsgg/zhgsgg/20010702/0016-150585.html', 'charset': 'utf-8', 'body': '<html>\r\n<head><title>404 Not Found</title></head>\r\n<body>\r\n<center><h1>404 Not Found</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n', 'code': 404, 'createdAt': '2022-10-13 14:58:28', 'version': 2}"
md = {
    "addr": '34343434'
}


# <pymongo.results.UpdateResult object at 0x107a946d0>
#collection.find_one_and_update()
# result = collection.find_one_and_update(filter={"age": 13388899}, update={'$set': md}, upsert=True, return_document=ReturnDocument.AFTER)
# print(result)
