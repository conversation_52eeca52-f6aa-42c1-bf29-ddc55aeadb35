import sys
import os
import time

import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.qianxun_service import QianxunService
from dc.services.logging_service import LoggingService
from dc.conf.settings import get_settings2
from dc.common.utils import Utils
from dc.tests.browser import Browser
from dc.common import utils
from urllib.parse import urlparse, parse_qs, parse_qsl
from urllib import request


# url = "http://124.126.18.3:9099/soya/apps/doc/server/?action=user.createVerifyImg"
# url = "http://192.168.1.127:9099/soya/apps/doc/server/?action=user.createVerifyImg"
url = "http://124.126.18.3:9099/soya/apps/doc/server/?action=enum.getDictDebug&enumType=searchFileType"
# http://124.126.18.3:9099/mp/mptest/cookie
# http://192.168.1.127:9099/mp/mptest/cookie
# http://192.168.1.127:9099/soya/apps/doc/server/?action=test.query

logger = LoggingService('mp_debug.log')

assert 1 == 2, '1 不等于 2'

exit(0)
qx = QianxunService()

num = 10
i = 0
while True:
    i = i + 1
    dt = Utils.showDateTime()
    logger.info(f"---{i}--{dt}--")
    time1 = Utils.millisecond_time()
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
    }
    r = requests.get(url, headers=headers)
    r.encoding = 'utf-8'
    logger.info(r.text)
    time2 = Utils.millisecond_time()
    exec_time = time2 - time1
    logger.info(f"status_code {r.status_code}")
    logger.info(f'exec_time time: => {exec_time}')

    if exec_time > 4000:
        logger.info('出现问题---!!!!!!!!!!!!!')
        qx.simple_send({
            'type': 2001,
            'msg': f"接口访问超时",
            'mail_title': f"接口访问超时",
            'mail_body': f"接口访问超时",

        })

    time.sleep(60)
