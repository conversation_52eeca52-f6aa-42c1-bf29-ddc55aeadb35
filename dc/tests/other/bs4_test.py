#!/usr/bin/python3

import requests
from bs4 import BeautifulSoup, Tag, ResultSet


def test_first():
    url = 'http://www.baidu.com'
    # response = urllib.request.urlopen('http://www.lvlvcm.com/User/Article/info?id=305')
    # html = response.read()
    # print(html)
    jar = requests.cookies.RequestsCookieJar()
    jar.set('PHPSESSID', '0scpothjne64uslvn41aa4rci3')
    jar.set('curr_money', '2.4700')
    jar.set('user_id', 'hHqlqIWmpbCweqapfX2xq4CbxJ8')
    r = requests.get(url, cookies=jar)
    r.encoding = 'utf-8'
    html2 = r.text
    print(html2)
    print(r.url)
    # respones = requests.get('http://t.cn/EAmvkJ4')
    # print(respones.url)
    url = 'http://t.cn/EAWKWsS'


# test_first()


html_doc = """
<table id='main-tbody'>
<tr>
    <td>A</td>   
    <td>B</td>  
    <td>C</td>
    <td>D</td>
    <td>E</td>
    <td>F</td>
    <td>G</td>
    <td>H</td>
    <td>I</td>
    <td>J</td>
    <td>K</td>
    <td>L</td>
    <td>M</td>
    <td>N</td>
    <td><b>b1</b>OOOO <p>p2</p</td>
</tr>
</table>
"""

soup = BeautifulSoup(html_doc, 'lxml')
# y: ResultSet = soup.find(id='main-tbody').find_all('td')
y: ResultSet = soup.find_all(name="td")
print(y)
tag: Tag
data = [tag.text for tag in y]
print(data)

ts = soup.find(id="main-tbody").find_all('td')
pp = [t.text for t in ts]
print(pp)
