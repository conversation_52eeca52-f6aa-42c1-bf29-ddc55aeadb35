import sys
import os
import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
import random
import string
from dc.common.utils import Utils
from dc.tests.browser import Browser
from dc.common import utils

print(random.randint(100, 999))

# 从a-zA-Z0-9生成指定数量的随机字符：
ran_str = ''.join(random.sample(string.ascii_letters + string.digits, 16))
print(ran_str)


print(random.choice(['剪刀', '石头', '布']))


# 打乱排序
items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0]
print(random.shuffle(items))