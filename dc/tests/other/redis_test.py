import random
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.redis_service import RedisService

redis = RedisService('redis')
client = redis.get_client()
key = 'redis-test'
list_key = 'redis-test-list'


def test_black():
    redis_task_blacklist = f'00-dc-site-task-blacklist2'
    zz = client.hexists(redis_task_blacklist, 1000)
    print(zz)


#test_black()

len = client.llen(key)

print(len)
for i in range(0, len):
    pv = client.lindex(key, i)
    print(pv)
