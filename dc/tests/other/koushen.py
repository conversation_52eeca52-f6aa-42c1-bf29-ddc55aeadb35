import json
import sys
import os
import time
import threading

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.common.utils import Utils
from dc.tests.browser import <PERSON><PERSON>er
from selenium import webdriver
from selenium.webdriver import DesiredCapabilities
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.remote_connection import RemoteConnection
from dc.common import utils
from dc.services.mysql_service import MySQLService
from dc.models.model import DCSiteCheck
from dc.services.elasticsearch_service import ElasticsearchService

webdriver_url = "http://192.168.1.120:4444"

mysql = MySQLService()
ess = ElasticsearchService()
es = ess.get_connect()


def get_remote_webdriver(browser: Browser):
    print('webdriver_url:' + webdriver_url)
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.FIREFOX,
                                options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.EDGE,
                                options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.CHROME,
                                options=opt)


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/geckodriver", options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/chromedriver", options=opt)


driver = get_remote_webdriver(Browser.Chrome)
# driver = get_webdriver(Browser.Chrome)
driver.implicitly_wait(10)

total_time = 0
test_index = 'wechat'
session = mysql.create_session()
query = session.query(DCSiteCheck)


def get(driver, v):
    print(driver.title)
    print(v.siteUrl)
    driver.get(v.siteUrl)
    if len(driver.page_source) > 0:
        if driver.page_source == '<html><head></head><body></body></html>':
            print("打不开 继续重试")
        else:
            print(driver.page_source)
    else:
        print("打不开 继续重试")


res: DCSiteCheck = query.all()
print(res)
while True:

    if len(res) > 0:
        for v in res:
            try:
                get(driver, v)
            except Exception as ex:
                continue
    else:
        break

# def test_bs4(xhtml):
#     from bs4 import BeautifulSoup
#     from lxml import etree
#
#     # xpath
#     xpath_selector = etree.HTML(xhtml)
#     ret = xpath_selector.xpath('//meta[@name="Maketime"]/@content')
#     print(ret)
#
#     # css
#     bsObj = BeautifulSoup(xhtml, 'html.parser')
#     # bsElems = bsObj.find_all('script', type='text/javascript')
#     elem = bsObj.find(name="div", attrs={"class": 'title'})
#     print(elem)
#     print(elem.text)


driver.quit()
session.close()
