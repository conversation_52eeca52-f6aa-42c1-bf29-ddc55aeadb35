import random
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.models.model import DCSiteTask
from dc.services.mysql_service import MySQLService

from dc.services.redis_service import RedisService

redis = RedisService('redis-json')
client = redis.get_client()
key = 'redis-test'
list_key = 'redis-test-list'


def set_test():
    result = client.set(key, random.randint(100, 999))
    print(result)


def get_test():
    data = client.get(key)
    print(data)


def push_test():
    for i in range(1, 10):
        client.lpush(list_key, i)


def pop_test():
    while True:
        data = client.brpop(list_key, 5)
        print(data)


# push_test()

# pop_test()

def json_test():
    data = {
        'foo': {
            'bar' : 'baz'
        }
    }

    r = client
    r.json().set('doc', '$', data)
    doc = r.json().get('doc', '$')
    foo = r.json().get('doc', '$.foo')
    bar = r.json().get('doc', '$..bar')

    print(doc)

def json_test2():
    data = {
        'foo': {
            'bar' : 'baz'
        }
    }

    r = client
    doc = r.json().get('doc', '$')
    foo = r.json().get('doc', '$.foo')
    bar = r.json().get('doc', '$..bar')

    json = r.json()
    print(doc)
    print(foo)
    print(bar)
    print(json.get('doc', '$.foo.bar'))


#json_test2()
#mysql = MySQLService()
#session = mysql.create_session()
#task: DCSiteTask = session.get(DCSiteTask, 139)
#print(task)


redis_task_blacklist = f'00-dc-site-task-blacklist'
zz = client.hexists(redis_task_blacklist, 1000)
print(zz)