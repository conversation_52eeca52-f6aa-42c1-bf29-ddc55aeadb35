import PyPDF2
from googletrans import Translator

# 打开 PDF 文件
pdf_file = open('test.pdf', 'rb')
pdf_reader = PyPDF2.PdfReader(pdf_file)

# 读取 PDF 内容
page_content = ''
for page_num in range(len(pdf_reader.pages)):
    page = pdf_reader.pages[page_num]
    page_content += page.extract_text()

# 翻译 PDF 内容
translator = Translator()
translated_content = translator.translate(page_content, dest='zh-CN')

# 输出翻译结果
print(translated_content.text)

# 关闭 PDF 文件
pdf_file.close()
