"""
阿里云 mysql 连通性测试，仅供测试
:author wjh
:2023-01-17
"""
import os
import sys
import time
import traceback

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.gzh_service import GzhService
from dc.models.model import DCGzhArticle, DCGzhInfo, DCGzhTag, DCGzhWechat, DCGzhWechatRelation, DCGzhImport, DCWechatMachine
from dc.services.mysql_service import MySQLService
from dc.services.logging_service import LoggingService
from dc.services.weixin_service import WeixinService
from dctest.conf.settings import settings as test_settings, get_settings

gzh_settings = get_settings('gzh')
base_url = gzh_settings.get('host')

logger = LoggingService(logfile='dc_gzh_mysql.log')
wx = WeixinService()
mysql = MySQLService()

num = 1
sleep_time = 30
while True:
    try:
        logger.info(f"num: [{num}]")
        session = mysql.create_session()
        article: DCGzhArticle = session.query(DCGzhArticle).first()
        logger.info(f"[{num}] article: {article.id}")
        session.commit()

        logger.info(f"[{num}] sleep: {sleep_time}")
        time.sleep(sleep_time)
    except Exception as ex:
        logger.info(f"[{num}] except: {traceback.format_exc()}")
    finally:
        num += 1
        logger.info(f"[{num}] finally")
        if session:
            session.close()
