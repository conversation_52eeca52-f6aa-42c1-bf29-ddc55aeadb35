import sys
import os

import requests


sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.conf.settings import get_settings2
from dc.common.utils import Utils
from dc.tests.browser import Browser
from dc.common import utils
from urllib.parse import urlparse, parse_qs, parse_qsl
from urllib import request

url = "https://apiac.liepin.com/api/com.liepin.job.capp.job.job-detail"

def req_requests():
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
    }

    d = '{"data":{"jobKind":"2","jobId":50727189,"referer":"JOB_SEARCH"}}'

    r = requests.post(url,data=d, headers=headers,verify=False)
    r.encoding = 'utf-8'
    print(r.text)
    exit(0)


req_requests()

