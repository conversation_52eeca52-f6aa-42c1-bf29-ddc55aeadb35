import random
import sys
import os

from dc.services.dc_service import DcService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.redis_service import RedisService



ds = DcService()
content = ds.get_weixin_content('https://mp.weixin.qq.com/s?__biz=MzIwMDQwNDA5Ng==&mid=2651408000&idx=3&sn=561c45278cdeeaffb51dbbb1614275fb&chksm=8d005b6fba77d2790e97bef5e22043f67dd63b0604103c66263b403895a3b16029cdba55fb6c#rd')

print(content)









exit(0)

redis = RedisService('redis')
client = redis.get_client()
key = 'laravel_database_dc-site-task'
list_key = 'redis-test-list'


def test_black():
    redis_task_blacklist = f'00-dc-site-task-blacklist2'
    zz = client.hexists(redis_task_blacklist, 1000)
    print(zz)


#test_black()

result = client.rpush(key, 99724)
print(result)
