import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.redis_service import RedisService
from dc.services.qianxun_service import QianxunService

wxid = '21041832920@chatroom'
qx = QianxunService()
w = qx.get_wxid_first()
p = {
    'wxid': w
}
r = qx.get_group_user_list(wxid=wxid, params=p)
d = {}
for i in r:
    i.get('wxid')
    u = qx.get_user_info(wxid=i.get('wxid'), params=p)
    d[u.get('wxid')] =[u.get('wxid'), u.get('wxNum'), u.get('nick')]

print(d)
