import sys
import os

from bs4 import BeautifulSoup, ResultSet
from selenium.webdriver.common.by import By

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from selenium import webdriver
from time import sleep
from dc.common.utils import Utils
from dc.tests.browser import <PERSON><PERSON><PERSON>


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/geckodriver", options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/chromedriver", options=opt)



driver = get_webdriver(Browser.Chrome)
driver.implicitly_wait(10)
#driver.get("http://jxj.hangzhou.gov.cn/col/col1229279601/index.html?number=A007A003")
#driver.get("http://jxj.hangzhou.gov.cn/art/2021/9/10/art_1229252267_3931587.html")
driver.get("http://stockpage.10jqka.com.cn/000623/company/")
sleep(5)
# div = driver.find_element(By.XPATH, '//div[@class="title"]')
# print(div.text)
page_source = driver.page_source
print(page_source)
driver.quit()
exit(0)

soup = BeautifulSoup(page_source, 'lxml')
# y: ResultSet = soup.find(id='main-tbody').find_all('td')
rs: ResultSet = soup.find_all(attrs={"class": "title"})
data = [tag.text for tag in rs]
print(data)
