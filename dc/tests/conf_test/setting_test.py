# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/9/4
# Version    : 1.0

import sys
import os
from dc.tests.test_simple_basics import SimpleBasicsTestCase
from dc.conf.settings import *


class SettingTest(SimpleBasicsTestCase):
    name = None

    def setUp(self):
        print('TestOne-setUp')
        self.name = '1223'
        pass

    def test_base_config(self):
        mysql = get_settings('mysql', run_model='dev')
        print(f"mysql: {mysql}")
        self.assertIsInstance(mysql, dict)

        user_name = get_settings2('mysql', 'userName', run_model='dev')
        print(f"user_name: {user_name}")
        self.assertEqual(user_name, "root")

        user_name = get_settings2('mysql', 'userName2', run_model='dev')
        print(f"user_name: {user_name}")
        # self.assertEqual(user_name, "root22")

        hosts = get_settings2('elasticsearch', 'hosts')
        print(f"hosts: {hosts}")
        self.assertIsInstance(hosts, list)

        hosts = get_settings3('elk', 'log_monitor_alert_api', 'host', 'kkk')
        print(f"hosts: {hosts}")
        self.assertIsInstance(hosts, str)

    def test_base_config2(self):
        mysql = get_settings('proxy', run_model='dev')
        print(f"mysql: {mysql}")
        self.assertIsInstance(mysql, dict)

        # 示例数据
        data = {
            "p01": {
                "type": "proxy-pool",
                "region": "gn",
                "params": {
                    "username": "t10565224196293",
                    "password": "nxdhhkof",
                    "http": {
                        "host": "f753.kdltps.com",
                        "port": 15818
                    },
                    "socks": {
                        "host": "f753.kdltps.com",
                        "port": 20818
                    }
                }
            }
        }

        # 示例调用
        keys = ["p01", "params", "http", "host"]
        result = get_dict_value(data, keys, default_value="default_host")
        print(result)  # 输出: "f753.kdltps.com"

        # 当某个键不存在时
        keys = ["p01", "params", "http", "non_existent_key"]
        result = get_dict_value(data, keys, default_value="default_value")
        print(result)  # 输出: "default_value"

        # 当某个键不存在时
        keys = ["p01", "params", "http2", "non_existent_key"]
        result = get_dict_value(data, keys, default_value="default_value")
        print(result)  # 输出: "default_value"
