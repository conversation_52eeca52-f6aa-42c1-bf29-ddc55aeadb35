# -*- coding:utf-8 -*-
# @Function  : attachment_analysis_test
# <AUTHOR>
# @Time      : 2024-10-29
# Version    : 1.0

from dc.common.functools_wraps import *
from dc.common.webdriver_util import get_webdriver
from dc.models.model import DCSiteTask
from dc.services.check_analysis import CheckAnalysis
from dc.tests.browser import Browser


def attachment_analysis_test():
    driver = get_webdriver(Browser.Chrome, options={
        # 'arguments': ['--proxy-server=http://f753.kdltps.com:15818']
    })

    try:
        taskId = 11504271
        # task_info = self.get_task_info(taskId)

        task_info = {}
        with get_session(run_model="prod") as session:
            task_info = session.get(DCSiteTask, taskId)
            if task_info:
                task_info = task_info.to_dict()

        print(task_info)

        result2 = CheckAnalysis.attachment_analysis({}, "", task_info)
        print(result2)

        # result2 = {
        #     "info": {
        #         "title": "PEC Meeting Agenda - June 11, 2024 [128KB]",
        #         "text": "",
        #         "public_time": "",
        #         "files_v2": [
        #             {
        #                 "index": 0,
        #                 "key": "6a758d967cb78df67010d2b6bf042a41",
        #                 "url": "https://www.trade.gov/sites/default/files/2024-05/PEC Meeting Agenda - June 11, 2024.pdf",
        #                 "image_domain": "https://s.laoyaoba.com/",
        #                 "image_url": "https://s.laoyaoba.com/tmp3/dc/files/6a758d967cb78df67010d2b6bf042a41.pdf"
        #             }
        #         ]
        #     },
        #     "analysis_status": 1,
        #     "rule_id": 0
        # }

    except Exception as ex:
        print(ex)
    finally:
        driver.close()


attachment_analysis_test()
