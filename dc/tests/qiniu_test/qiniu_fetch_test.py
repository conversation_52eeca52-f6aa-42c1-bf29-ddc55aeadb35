# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0
import mimetypes
import time

import requests

from dc.common.qiniu_utils import get_extension_from_url, url_path_join
from dc.common.webdriver_util import get_remote_webdriver, get_webdriver
from selenium.webdriver.common.by import By
from dc.services.qiniu_service import QiniuService
from dc.tests.browser import Browser
from dc.tests.test_simple_basics import SimpleBasicsTestCase

qs = QiniuService(name='dc')

image_urls = [
    # 'https://www.judiciary.senate.gov/download/2024-09-17-qfr-responses-goldfeder?download=1',
    # 'https://www.cecc.gov/sites/evo-subsites/www.cecc.gov/files/2024-07/AR19%20Executive%20Summary%20Translation%20-%20Final%20Text%20for%20Publication_2.pdf',
    # 'https://www.trade.gov/sites/default/files/2023-09/ACSCC%20Recommendations%20August%202023.pdf',
    # 'https://us.house.gov/hcfs/cecc/users/sflipse/2024%20Advocacy/Biden%20Letter%20unjustly%20detained%20Americans/Letter%20to%20POTUS%20--%20Unjustly%20Detained%20Americans.pdf',
    'https://www.cecc.gov/sites/evo-subsites/www.cecc.gov/files/2024-10/Letter%20to%20POTUS%20--%20Unjustly%20Detained%20Americans.pdf',
]

for image_url in image_urls:
    image_save_path = url_path_join(qs.config["document_name"], 'test')
    extension = qs.fetch_files([image_url], image_save_path)
    print(extension)
