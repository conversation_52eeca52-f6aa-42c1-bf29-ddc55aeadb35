# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0

import os
import time
from selenium.webdriver import DesiredCapabilities
from dc.common.webdriver_util import get_webdriver
from selenium.webdriver.common.by import By

from dc.services.qiniu_service import QiniuService
from dc.tests.browser import Browser

# 基础下载路径（根目录）
base_download_dir = r"D:\tmp\123"
#
# qs = QiniuService(name='bianjifagao')
#
# images = [
#     '//static.sse.com.cn/disclosure/listedinfo/announcement/c/new/2024-10-24/603880_20241024_M36Q.pdf',
# ]
#
# print(f"images len: {len(images)}")
# qiniu_images = qs.fetch_files(images, 'tmp3')
# print("qiniu_images:")
# print(qiniu_images)


import requests

# 设置目标 URL 和 headers，包括 Referer
url = "https://static.sse.com.cn/disclosure/listedinfo/announcement/c/new/2024-10-24/603880_20241024_M36Q.pdf"
headers = {
    "Referer": "http://www.sse.com.cn/disclosure/listedinfo/announcement/"  # 将此处替换为实际的 referer
}

# 发送 GET 请求
response = requests.get(url, headers=headers, stream=True)

# 确保响应状态码是 200
if response.status_code == 200:
    # 保存文件
    with open("downloaded_file.pdf", "wb") as f:
        for chunk in response.iter_content(chunk_size=1024):
            if chunk:
                f.write(chunk)
    print("附件下载成功")
else:
    print(f"下载失败，状态码: {response.status_code}")
