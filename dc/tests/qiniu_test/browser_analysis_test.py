# -*- coding:utf-8 -*-
# @Function  : browser_analysis_test
# <AUTHOR>
# @Time      : 2024-10-29
# Version    : 1.0

from dc.common.webdriver_util import get_webdriver
from dc.services.check_analysis import CheckAnalysis
from dc.tests.browser import <PERSON><PERSON><PERSON>


def test_browser_analysis(self):
    driver = get_webdriver(Browser.Chrome, options={
        # 'arguments': ['--proxy-server=http://f753.kdltps.com:15818']
    })

    try:
        taskId = 10913153
        # task_info = self.get_task_info(taskId)

        task_info = {
            'id': 10913153,
            'siteListId': 1889,
            'mainTaskId': 4253180,
            'baseUrl': 'https://news.sina.com.cn',
            'url': 'https://finance.sina.com.cn/stock/stockzmt/2024-09-19/doc-incprwmw3929345.shtml',
            'title': '中金：美联储“非常规”降息开局',
            'taskStatus': 1,
            'taskAt': '2024-09-19 10:17:56',
            'analysisStatus': 1,
            'httpStatusCode': 200,
            'mongoCollection': '',
            'mongoKey': '66eb89cf1d1eedc9ca487f67',
            'retryTime': 1,
            'createdAt': '2024-09-19 10:16:45',
            'updatedAt': '2024-09-19 10:30:00',
            'esIndex': 'dc_bianjifagao',
            'esId': '9YYdCJIBpcSEA4FCbY0X',
            'ruleId': 1878,
            'publicTime': None,
            'coverImg': '',
            'keyword': '',
            'detailDcMethod': 2
        }

        site_info = {
            "sourceCategory": "dc_bianjifagao",
            "siteName": "新浪",
            "analysisMethod": 0,
            "isQuickShort": 2,
            "isKeyWord": 2,
            "detailDcMethod": 2,
            "sourceName": "新浪-全部滚动新闻bjfg(列表页当天数据大于1000)",
            "ruleItem": "{\"1878\":{\"7445\":{\"id\":7445, \"siteListId\":0, \"siteRuleId\":1878, \"columnTitle\":\"正文标题\", \"columnKey\":\"title\", \"crawlRuleType\":1, \"crawlRule\":\"//h1[@class=\\\"main-title\\\"]|//h1[@id=\\\"artibodyTitle\\\"]\", \"startFlag\":\"\", \"endFlag\":\"\", \"columnRegex\":\"\", \"columnDefault\":\"\", \"status\":1, \"createdAt\":\"2024-01-10 14:40:46\", \"updatedAt\":\"2024-05-28 10:51:56\", \"analysisType\":1, \"path\":\"\", \"pathLevel\":0}, \"7446\":{\"id\":7446, \"siteListId\":0, \"siteRuleId\":1878, \"columnTitle\":\"发布日期\", \"columnKey\":\"public_time\", \"crawlRuleType\":1, \"crawlRule\":\"//span[@class=\\\"date\\\"]|//span[@id=\\\"pub_date\\\"]\", \"startFlag\":\"\", \"endFlag\":\"\", \"columnRegex\":\"\", \"columnDefault\":\"\", \"status\":1, \"createdAt\":\"2024-01-10 14:40:46\", \"updatedAt\":\"2024-05-28 10:51:56\", \"analysisType\":1, \"path\":\"\", \"pathLevel\":0}, \"7447\":{\"id\":7447, \"siteListId\":0, \"siteRuleId\":1878, \"columnTitle\":\"正文内容\", \"columnKey\":\"text\", \"crawlRuleType\":1, \"crawlRule\":\"//div[@class=\\\"clearfix appendQr_wrap\\\"]/preceding-sibling::*|//div[@id=\\\"article\\\"]\", \"startFlag\":\"\", \"endFlag\":\"\", \"columnRegex\":\"\", \"columnDefault\":\"\", \"status\":1, \"createdAt\":\"2024-01-10 14:40:46\", \"updatedAt\":\"2024-08-06 17:53:43\", \"analysisType\":1, \"path\":\"\", \"pathLevel\":0}, \"8480\":{\"id\":8480, \"siteListId\":null, \"siteRuleId\":1878, \"columnTitle\":\"图片\", \"columnKey\":\"images\", \"crawlRuleType\":6, \"crawlRule\":\"//div[@class=\\\"clearfix appendQr_wrap\\\"]/preceding-sibling::*//img|//div[@id=\\\"article\\\"]//img\", \"startFlag\":\"\", \"endFlag\":\"\", \"columnRegex\":\"\", \"columnDefault\":\"\", \"status\":1, \"createdAt\":\"2024-09-19 09:40:59\", \"updatedAt\":\"2024-09-19 09:40:59\", \"analysisType\":1, \"path\":\"\", \"pathLevel\":0}}}",
            "isHotModule": 2,
            "tags": "本土IC-媒体"
        }
        driver.get(task_info['url'])

        # site_list = self.get_site_list(task_info['siteListId'])
        # assert site_list is not None

        result2 = CheckAnalysis.browser_analysis(driver, task_info, site_info, driver.page_source)

        result = {
            "info": {
                "title": "广州开发区、广州市黄埔区专利培育公共服务平台首场培训活动圆满落幕",
                "status": 2,
                "author": "本网",
                "public_time": "2024-09-06 00:00:00",
                "text": "为进一步推动广州开发区广州市黄埔区专利培育公共服务事业发展，帮助企业进一步准确把握政策要求，清晰了解知识产权优势示范企业等的申报程序、关键点，提升申报成功率，9月3日下午，在广州开发区知识产权局的指导下，由广州精英知识产权服务有限公司主办的“广东省示范企业暨省重点商标名录申报实务专场交流会”在华南新材料创新园举行。广州开发区知识产权局副局长田帅率队出席，高金富恒集团副总经理、华新园副总经理柴琰以及现场企业代表、服务机构等近50人参加本次活动。/n区知识产权局田帅副局长作开场致辞，他表示，知识产权是科技创新中必不可少的一环。为鼓励企业完善知识产权创造培育，根据“区知识产权高质量发展30条”规定，获评省知识产权示范企业可获一次性扶持15万元，新纳入广东省重点商标保护名录的每件可获一次性扶持5万元。截至目前，已有国家、省级知识产权优势、示范企业近400家，累计纳入省重点商标保护名录约150件。今年以来，区局积极指导高价值专利培育布局中心建设运营，常态化举办“IP转化营”等品牌活动，认定专利转化、培育等6家公共服务平台，推广海外侵权责任险、海外专利申请险等多样化险种。1-7月，全区专利授权量12185件排名全市第一，其中发明专利同比增长36.2%；同期，PCT国际专利申请量418件，占领全市半壁江山。/n会上，广东商标协会项目部部长米煜妍作《广东省重点商标保护名录管理及溯源认证工作解读》，从申报流程、申报材料到行政、司法运用以及最新政策等方面做了详细讲解，帮助企业更深入了解申报程序、要点。随后，精英知识产权集团业务部总监郭健诚从省示范企业的申报程序、申报材料以及申报时遇到的问题做了全面讲解，并结合往期辅导企业申报时所遇到的情况，对申报流程及注意事项提出了实用性意见建议。/n作为广州开发区、广州市黄埔区专利培育公共服务平台的首场培训，此次活动旨在激发企业在创新中的主导作用，提升知识产权核心竞争力。接下来，区知识产权局将继续聚焦创新主体需求，开展系列知识产权培训活动，推广实施《创新管理—知识产权管理指南（ISO56005）》国际标准，督促平台联合建设单位——广州名扬、精英知识产权服务有限公司，坚持规范、便利、精准、普惠的原则，积极开展走访对接服务，免费指导企业开展专利挖掘布局等，助力更多区内企业成功申报国家知识产权优势、示范企业，实现知识产权赋能实体经济高质量发展。（来源：区知识产权局）",
                "files": "{}",
                "origin_url": [
                    "https://www.hp.gov.cn/img/1/1272/1272209/9857587.jpg",
                    "https://www.hp.gov.cn/img/1/1272/1272210/9857587.jpg"
                ],
                "images_v2": [
                    {
                        "key": "3dc3dc3fe2e69c55002563042242fc2f",
                        "url": "https://www.hp.gov.cn/img/1/1272/1272209/9857587.jpg",
                        "image_domain": "http://s.laoyaoba.com/",
                        "image_url": "http://s.laoyaoba.com/tmp3/3dc3dc3fe2e69c55002563042242fc2f/3dc3dc3fe2e69c55002563042242fc2f.html",
                        "meta": {
                            "fsize": 6905373,
                            "hash": "lo_ItaiRhJ_8WjgrGxqBkUhwMek0",
                            "key": "tmp3/3dc3dc3fe2e69c55002563042242fc2f/3dc3dc3fe2e69c55002563042242fc2f.html",
                            "mimeType": "image/jpeg",
                            "req_id": "DR4AAACdqh79fPQX",
                            "x_log": "X-Log",
                            "format": "jpeg",
                            "width": 3562,
                            "height": 2259,
                            "colorModel": "ycbcr",
                            "orientation": "Top-left"
                        }
                    },
                    {
                        "key": "db9abcf0b561d0fc8d2be57b095e4e75",
                        "url": "https://www.hp.gov.cn/img/1/1272/1272210/9857587.jpg",
                        "image_domain": "http://s.laoyaoba.com/",
                        "image_url": "http://s.laoyaoba.com/tmp3/db9abcf0b561d0fc8d2be57b095e4e75/db9abcf0b561d0fc8d2be57b095e4e75.html",
                        "meta": {
                            "fsize": 2741297,
                            "hash": "Fnuiq33RM2yas2B6FSwyHV_ybaaO",
                            "key": "tmp3/db9abcf0b561d0fc8d2be57b095e4e75/db9abcf0b561d0fc8d2be57b095e4e75.html",
                            "mimeType": "image/jpeg",
                            "req_id": "cZMAAADXqXn_fPQX",
                            "x_log": "X-Log",
                            "format": "jpeg",
                            "width": 3072,
                            "height": 2098,
                            "colorModel": "ycbcr",
                            "orientation": "Top-left"
                        }
                    }
                ],
                "images": "{\"3dc3dc3fe2e69c55002563042242fc2f\":\"/uploads/chanquan/202409//3dc3dc3fe2e69c55002563042242fc2fjpg\", \"db9abcf0b561d0fc8d2be57b095e4e75\":\"/uploads/chanquan/202409//db9abcf0b561d0fc8d2be57b095e4e75jpg\"}"
            },
            "analysis_status": 1,
            "rule_id": "469"
        }

        print(result2)
        images = result2['info']['images_v2']
        print(images)

        self.assertIsInstance(images, list)

    except Exception as ex:
        print(ex)
    finally:
        driver.close()


test_browser_analysis()
