# -*- coding:utf-8 -*-
# @Function  : dc_taskpull_test
# <AUTHOR>
# @Time      : 2024-10-29
# Version    : 1.0
from dc.common.qiniu_utils import *

urls = [
    "https://www.trade.gov/sites/default/files/2024-05/PEC%20Meeting%20Agenda%20-%20June%2011%2C%202024.pdf",
    "https://www.trade.gov/sites/default/files/2024-05/PEC%20Meeting%20Notice%20-%20June%2011%2C%202024.pdf",
    "https://www.trade.gov/sites/default/files/2023-09/ACSCC%20Recommendations%20August%202023.pdf",
    "https://www.trade.gov/sites/default/files/2022-09/ACSCC%2029%20June%202022%20Freight%20Subcommittee%20recommendation%20package.pdf"
]

for url in urls:
    ext = get_extension_from_url(url, '.html')
    is_html = is_valid_html_extension(ext)
    print(f"ext: {ext} is_html: {is_html}")
