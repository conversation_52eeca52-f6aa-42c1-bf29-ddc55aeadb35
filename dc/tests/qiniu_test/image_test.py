# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0
from selenium.webdriver.common.by import By

from dc.tests.browser import Browser

from dc.conf.settings import get_settings

from dc.services.qiniu_service import QiniuService
from dc.tests.test_simple_basics import SimpleBasicsTestCase
import os


class ImageTest(SimpleBasicsTestCase):
    name = None

    qs: QiniuService = None

    def setUp(self):
        self.name = '七牛云测试类'
        self.qs = QiniuService(name='bianjifagao')
        print(self.name)

    def test_gzh_image(self):
        content = self.qs.image_view({'id': 'EtKCA5IBG8DIxTyBw3it'})
        print(content)
        self.assertIsInstance(content, str)
