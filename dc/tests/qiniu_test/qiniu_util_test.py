# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0

from selenium.webdriver.common.by import By

from dc.common.qiniu_utils import *
from dc.services.qiniu_service import QiniuService
from dc.tests.browser import Browser
from dc.tests.test_simple_basics import SimpleBasicsTestCase

image_url = 'http://static.sse.com.cn/disclosure/listedinfo/announcement/c/new/2024-10-24/603880_20241024_M36Q.pdf'
extension = get_extension_from_url(image_url, '.jpg')
print(extension)



import requests

cookies = {
    'gdp_user_id': 'gioenc-3e135785%2C6e57%2C5503%2C88g8%2Cdc34gd01ga18',
    'ba17301551dcbaf9_gdp_session_id': '94ed7043-6f6e-43aa-8890-c9f2e6914db6',
    'ba17301551dcbaf9_gdp_session_id_sent': '94ed7043-6f6e-43aa-8890-c9f2e6914db6',
    'ba17301551dcbaf9_gdp_sequence_ids': '{%22globalKey%22:89%2C%22VISIT%22:3%2C%22PAGE%22:11%2C%22VIEW_CLICK%22:74%2C%22VIEW_CHANGE%22:2%2C%22CUSTOM%22:3}',
    'acw_tc': '246ed11b17297388657597600e77a11e84b7d6f1201903b3df92a03731',
    'cdn_sec_tc': '246ed11b17297388657597600e77a11e84b7d6f1201903b3df92a03731',
    'acw_sc__v2': '6719b87134528d81b4ba6cdd5aa6060d79b10194',
}

headers = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Cache-Control': 'no-cache',
    # 'Cookie': 'gdp_user_id=gioenc-3e135785%2C6e57%2C5503%2C88g8%2Cdc34gd01ga18; ba17301551dcbaf9_gdp_session_id=94ed7043-6f6e-43aa-8890-c9f2e6914db6; ba17301551dcbaf9_gdp_session_id_sent=94ed7043-6f6e-43aa-8890-c9f2e6914db6; ba17301551dcbaf9_gdp_sequence_ids={%22globalKey%22:89%2C%22VISIT%22:3%2C%22PAGE%22:11%2C%22VIEW_CLICK%22:74%2C%22VIEW_CHANGE%22:2%2C%22CUSTOM%22:3}; acw_tc=246ed11b17297388657597600e77a11e84b7d6f1201903b3df92a03731; cdn_sec_tc=246ed11b17297388657597600e77a11e84b7d6f1201903b3df92a03731; acw_sc__v2=6719b87134528d81b4ba6cdd5aa6060d79b10194',
    'Pragma': 'no-cache',
    'Proxy-Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
}

response = requests.get(
    'http://static.sse.com.cn/disclosure/listedinfo/announcement/c/new/2024-10-24/603880_20241024_M36Q.pdf',
    # cookies=cookies,
    headers=headers,
    verify=False,
)

print(response.status_code)
print(response.headers)
print(response.text)