# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0
import os
import time
from selenium.webdriver.common.by import By
from dc.common.webdriver_util import get_webdriver
from dc.tests.browser import Browser

# 基础下载路径（根目录）
base_download_dir = r"D:\tmp\123"

# 设置 Firefox 的初始 preferences
firefox_options = {
    "browser.download.folderList": 2,  # 2 = custom location
    "browser.helperApps.neverAsk.saveToDisk": "application/pdf,application/octet-stream,application/vnd.ms-excel",  # 自动下载指定类型的文件
    "pdfjs.disabled": True  # 禁用 PDF 预览
}

# 获取 WebDriver 实例，只实例化一次
driver = get_webdriver(browser=Browser.Firefox, options={
    'preferences': firefox_options,
    'arguments': [],
    'extensions': []
})

# 访问目标网站
driver.get('http://www.sse.com.cn/disclosure/listedinfo/announcement/')
driver.implicitly_wait(10)

# 获取所有下载按钮元素
elements = driver.find_elements(By.XPATH, '//a[@title="点击下载公告文件"]')

print(f"找到 {len(elements)} 个附件")

# 用于记录每个附件信息的列表
attachments = []


# 检查文件是否下载完成（排除临时文件并通过文件大小变化判断）
def is_download_complete(download_dir):
    previous_size = -1
    for _ in range(60):  # 最长等待60秒
        files = [f for f in os.listdir(download_dir) if not (f.endswith(".part") or f.endswith(".htm"))]
        if not files:
            time.sleep(1)
            continue

        # 检查符合条件的文件大小是否变化
        current_size = sum(os.path.getsize(os.path.join(download_dir, f)) for f in files)
        if current_size == previous_size:
            return True
        previous_size = current_size
        time.sleep(1)
    return False  # 超时后返回 False


# 循环处理每个下载元素
for idx, element in enumerate(elements):

    if idx > 5:
        break

    # 动态设置下载目录：abc_序号
    download_dir = os.path.join(base_download_dir, f"abc_{idx}")

    # 如果目录不存在，则创建该目录
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)

    # 清空下载目录
    for file in os.listdir(download_dir):
        file_path = os.path.join(download_dir, file)
        if os.path.isfile(file_path):
            os.unlink(file_path)

    # 在 WebDriver 中动态设置新的下载目录
    driver.profile.set_preference("browser.download.dir", download_dir)
    driver.profile.update_preferences()  # 确保新的设置生效

    # 获取下载链接和文件名
    href = element.get_attribute('href')
    name = element.get_attribute('download')

    # 将信息存入 attachments 列表
    attachments.append({
        "name": name,
        "href": href,
        "download_dir": download_dir
    })

    # 输出下载信息
    print(f"正在下载: {href} 到目录: {download_dir}")

    # 点击下载
    element.click()

    # 等待下载完成
    if is_download_complete(download_dir):
        print(f"附件 {name} 下载完成.")
    else:
        print(f"附件 {name} 下载超时或失败.")

# 打印附件信息
for idx, attachment in enumerate(attachments):
    print(f"{idx}. 附件名: {attachment['name']}, 下载链接: {attachment['href']}, 存储路径: {attachment['download_dir']}", end='\n')

# 关闭浏览器
driver.quit()
