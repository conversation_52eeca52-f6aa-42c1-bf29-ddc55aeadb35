# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0

from selenium.webdriver.common.by import By

from dc.common.qiniu_utils import *
from dc.services.qiniu_service import QiniuService
from dc.tests.browser import Browser
from dc.tests.test_simple_basics import SimpleBasicsTestCase


class QiniuTest(SimpleBasicsTestCase):
    name = None

    qs: QiniuService = None

    def setUp(self):
        self.name = '七牛云测试类'
        self.qs = QiniuService(name='bianjifagao')
        print(self.name)

    def test_put_file(self):
        """上传文件测试"""
        localfile = "/Downloads/123.jpg"
        key = "tmp3/screenshot/123.jpg"
        ret, info = self.qs.put_file(localfile, key)
        print(ret, info)
        # self.assertIsInstance(gzh_images, list)

    def test_gzh_image(self):
        url = 'https://mp.weixin.qq.com/s?__biz=MzIxODI2NTY4OQ==&mid=2247523746&idx=1&sn=a6e36d0b9c6aaa14c8e342d84a418ace&chksm=97effc94a0987582e8838205f8edecaab32160b784ba06612d25719346f93cffa629ee2774a1&scene=126&sessionid=1725684824#rd'
        content_xpath = '//div[@id="js_content" or @id="js_share_notice" or @class="wx_video_play_opr" or @class="rich_media_content"]'

        qs = QiniuService(name='pic')
        gzh_images = qs.get_gzh_images(None, url, content_xpath + '//img')
        print("公众号处理：")
        print(gzh_images)
        self.assertIsInstance(gzh_images, list)

    def test_qiniu_image_by_step(self):
        url = 'https://mp.weixin.qq.com/s?__biz=MzIxODI2NTY4OQ==&mid=2247523746&idx=1&sn=a6e36d0b9c6aaa14c8e342d84a418ace&chksm=97effc94a0987582e8838205f8edecaab32160b784ba06612d25719346f93cffa629ee2774a1&scene=126&sessionid=1725684824#rd'
        content_xpath = '//div[@id="js_content" or @id="js_share_notice" or @class="wx_video_play_opr" or @class="rich_media_content"]'

        qs = QiniuService(name='pic')

        # 公众号处理
        gzh_images = qs.get_gzh_images(None, url, content_xpath + '//img')
        print("公众号处理：")
        print(gzh_images)
        self.assertIsInstance(gzh_images, list)

        # 七牛处理
        qiniu_images = qs.fetch_image(gzh_images, 'tmp2/gzh3/')
        print("七牛处理：")
        print(qiniu_images)
        self.assertIsInstance(qiniu_images, list)

        # 图片过滤
        images = [image for image in qiniu_images if image.get('meta', {}).get('width', 0) > 100]
        print("过滤后图片：")
        print(images)
        self.assertIsInstance(images, list)

    def test_qiniu_image_by_service(self):
        url = 'https://mp.weixin.qq.com/s?__biz=MzIxODI2NTY4OQ==&mid=2247523746&idx=1&sn=a6e36d0b9c6aaa14c8e342d84a418ace&chksm=97effc94a0987582e8838205f8edecaab32160b784ba06612d25719346f93cffa629ee2774a1&scene=126&sessionid=1725684824#rd'
        content_xpath = '//div[@id="js_content" or @id="js_share_notice" or @class="wx_video_play_opr" or @class="rich_media_content"]'

        qs = QiniuService(name='bianjifagao')
        images = qs.get_gzh_qiniu_images(None, url, content_xpath + '//img')
        print(images)
        self.assertIsInstance(images, list)

    def test_gzh_image_by_service(self):
        # url = 'https://mp.weixin.qq.com/s?__biz=MzIxODI2NTY4OQ==&mid=2247523746&idx=1&sn=a6e36d0b9c6aaa14c8e342d84a418ace&chksm=97effc94a0987582e8838205f8edecaab32160b784ba06612d25719346f93cffa629ee2774a1&scene=126&sessionid=1725684824#rd'
        url = 'http://mp.weixin.qq.com/s?__biz=MjM5NjE3NDI2Mw==&mid=2650679924&idx=1&sn=9e019034d1893a5778d197ed2d01b512&chksm=bff5b96dca251271b189f4b6d357c0aadd252b758cabf9f36111f9978dc2893f8c97eda2fecd&scene=126&sessionid=1730463018#rd'
        content_xpath = '//div[@id="js_content" or @id="js_share_notice" or @class="wx_video_play_opr" or @class="rich_media_content"]'

        qs = QiniuService(name='bianjifagao')
        images = qs.get_gzh_images(None, url, content_xpath + '//img')
        print(images)
        self.assertIsInstance(images, list)

    def test_qiniu_image_by_service2(self):
        url = 'https://mp.weixin.qq.com/s?__biz=MzIxODI2NTY4OQ==&mid=2247523746&idx=1&sn=a6e36d0b9c6aaa14c8e342d84a418ace&chksm=97effc94a0987582e8838205f8edecaab32160b784ba06612d25719346f93cffa629ee2774a1&scene=126&sessionid=1725684824#rd'
        content_xpath = '//div[@id="js_content" or @id="js_share_notice" or @class="wx_video_play_opr" or @class="rich_media_content"]'

        settings = get_settings('webdriver')
        webdriver_url = settings.get('host')

        qs = QiniuService(name='bianjifagao')
        from dc.common.webdriver_util import get_remote_webdriver
        driver = get_remote_webdriver(webdriver_url, Browser.Chrome, options={
            # 'arguments': ['--proxy-server=http://f753.kdltps.com:15818']
        })
        driver.get(url)

        elements = driver.find_elements(By.XPATH, content_xpath + '//img')
        gzh_images = qs.get_gzh_images_by_webelement(elements)
        print("gzh_images:")
        print(gzh_images)
        driver.close()

        qiniu_images = qs.get_gzh_qiniu_images_by_gzh_images(gzh_images, url)
        print("qiniu_images:")
        print(qiniu_images)
        self.assertIsInstance(qiniu_images, list)

    def test_filter_image(self):
        images = [
            {
                "key": "f2258fd93d75a67c31fe568292c85405",
                "url": "https://mmbiz.qpic.cn/mmbiz_png/llXdVGpWpOr6aBZJtGpz3nmwz9VcD8S1pnVicIgNv7k60COGZHH7RVMibnykr4epiclB0FSdDFUNZ6yXcMKFFia5Lw/640?wx_fmt=png",
                "image_domain": "http://s.laoyaoba.com/",
                "image_url": "http://s.laoyaoba.com/bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/f2258fd93d75a67c31fe568292c85405.png",
                "meta": {
                    "fsize": 386631,
                    "hash": "Fgw_FPpM_01zMwiv2Mzvg4GRMC9L",
                    "key": "bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/f2258fd93d75a67c31fe568292c85405.png",
                    "mimeType": "image/png",
                    "req_id": "llgAAACdyI2EvPMX",
                    "x_log": "X-Log",
                    "format": "png",
                    "width": 1080,
                    "height": 434,
                    "colorModel": "nrgba"
                }
            },
            {
                "key": "e6c097ac2d8e01dabaa06a6ac3c6d15e",
                "url": "https://mmbiz.qpic.cn/mmbiz_png/llXdVGpWpOqZF8VM4kVT2Ua8I2iaMUnY8KM97XTJcq706ILTgTTbIiaCTHWOnw4JU7r487B5nk23XRY6jS5KKyibw/640?wx_fmt=png",
                "image_domain": "http://s.laoyaoba.com/",
                "image_url": "http://s.laoyaoba.com/bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/e6c097ac2d8e01dabaa06a6ac3c6d15e.png",
                "meta": {
                    "fsize": 26265,
                    "hash": "Ft3kndeqgZrFjZVMjkn2jexuD2Na",
                    "key": "bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/e6c097ac2d8e01dabaa06a6ac3c6d15e.png",
                    "mimeType": "image/png",
                    "req_id": "bsoAAADeIK6EvPMX",
                    "x_log": "X-Log",
                    "format": "png",
                    "width": 1080,
                    "height": 441,
                    "colorModel": "nrgba"
                }
            },
            {
                "key": "f8b1f624927fcb46516f8cbcccc4d6f7",
                "url": "https://mmbiz.qpic.cn/mmbiz_png/llXdVGpWpOoiclfYOYcen51sWZa9fZ73AxqzhJlWvy0FsiaEibictrCVVmw5nVwiaJmj8MdhdYicDtibX4mezB0KMX8yA/640?wx_fmt=png",
                "image_domain": "http://s.laoyaoba.com/",
                "image_url": "http://s.laoyaoba.com/bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/f8b1f624927fcb46516f8cbcccc4d6f7.png",
                "meta": {
                    "fsize": 10539,
                    "hash": "Fmg1Aq15qL_lW6idcR2-OJCf-oZ-",
                    "key": "bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/f8b1f624927fcb46516f8cbcccc4d6f7.png",
                    "mimeType": "image/png",
                    "req_id": "EKMAAADl_8iEvPMX",
                    "x_log": "X-Log",
                    "format": "png",
                    "width": 1080,
                    "height": 60,
                    "colorModel": "nrgba"
                }
            },
            {
                "key": "4b12769a7ef3b3c3c0d7774a36c0849b",
                "url": "https://mmbiz.qpic.cn/mmbiz_jpg/llXdVGpWpOqEBoDRbKicHK1fQSo3NA6mMMDDYFPGtGIjFDyBZKztJsdFAg8v5rm8GkcC6rm8vJm94KydZxfhogA/640?wx_fmt=jpeg&from=appmsg",
                "image_domain": "http://s.laoyaoba.com/",
                "image_url": "http://s.laoyaoba.com/bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/4b12769a7ef3b3c3c0d7774a36c0849b.jpg",
                "meta": {
                    "fsize": 17169,
                    "hash": "FmexYkNTRixZvMN0FQ4-oiRrZpVn",
                    "key": "bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/4b12769a7ef3b3c3c0d7774a36c0849b.jpg",
                    "mimeType": "image/jpeg",
                    "req_id": "sGAAAACqzOCEvPMX",
                    "x_log": "X-Log",
                    "format": "jpeg",
                    "width": 680,
                    "height": 158,
                    "colorModel": "ycbcr"
                }
            },
            {
                "key": "581813e219dad04f59b4a2ca53de4202",
                "url": "https://mmbiz.qpic.cn/mmbiz_jpg/llXdVGpWpOqEBoDRbKicHK1fQSo3NA6mMaDB0AGSSntM1fSlKcUXlib1cpPLbXg2xbBtnp9CuwLx7Q9OicNYYNcBg/640?wx_fmt=jpeg&from=appmsg",
                "image_domain": "http://s.laoyaoba.com/",
                "image_url": "http://s.laoyaoba.com/bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/581813e219dad04f59b4a2ca53de4202.jpg",
                "meta": {
                    "fsize": 16423,
                    "hash": "FmiTN3Xv4gFC7eu4oDIp6KomufNc",
                    "key": "bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/581813e219dad04f59b4a2ca53de4202.jpg",
                    "mimeType": "image/jpeg",
                    "req_id": "WwIAAADiOAOFvPMX",
                    "x_log": "X-Log",
                    "format": "jpeg",
                    "width": 680,
                    "height": 158,
                    "colorModel": "ycbcr"
                }
            },
            {
                "key": "6517de0a8ddd4a7e007b4bb70cf51b49",
                "url": "https://mmbiz.qpic.cn/mmbiz_jpg/llXdVGpWpOqEBoDRbKicHK1fQSo3NA6mMTJD1lKXHeiabpgibPFX0mJ6iaYDxhhzI79J6HdHCxuSohwsKib9j9rmKWQ/640?wx_fmt=jpeg&from=appmsg",
                "image_domain": "http://s.laoyaoba.com/",
                "image_url": "http://s.laoyaoba.com/bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/6517de0a8ddd4a7e007b4bb70cf51b49.jpg",
                "meta": {
                    "fsize": 28404,
                    "hash": "FqTjSN4gtMh8QOiexMX3jDdm-ARj",
                    "key": "bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/6517de0a8ddd4a7e007b4bb70cf51b49.jpg",
                    "mimeType": "image/jpeg",
                    "req_id": "9nIAAABNnieFvPMX",
                    "x_log": "X-Log",
                    "format": "jpeg",
                    "width": 680,
                    "height": 158,
                    "colorModel": "ycbcr"
                }
            },
            {
                "key": "10761cf41862424bbe6f37b3b1273ffe",
                "url": "https://mmbiz.qpic.cn/mmbiz_png/De6To5m4jtckU9mgqEY6RRF0ib68TFu2jxhaiaSviaBib2J79ZmBcJCCM9H19fSQc2RYZvtriajS2Yrm4nTQGqM9J5A/640?wx_fmt=png",
                "image_domain": "http://s.laoyaoba.com/",
                "image_url": "http://s.laoyaoba.com/bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/10761cf41862424bbe6f37b3b1273ffe.png",
                "meta": {
                    "fsize": 24073,
                    "hash": "FpaOOH2ceQ1eWbS__cs3iQfn1zUV",
                    "key": "bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/10761cf41862424bbe6f37b3b1273ffe.png",
                    "mimeType": "image/png",
                    "req_id": "sc8AAAB-cEmFvPMX",
                    "x_log": "X-Log",
                    "format": "png",
                    "width": 1080,
                    "height": 422,
                    "colorModel": "nrgba"
                }
            }
        ]

        print(f"images len: {len(images)}")
        qiniu_images = self.qs.images_filer(images)
        print("qiniu_images:")
        print(qiniu_images)
        print(f"qiniu_images len: {len(qiniu_images)}")
        self.assertIsInstance(qiniu_images, list)

    def test_fetch_image(self):
        images = [
            'https://s.laoyaoba.com/bianjifagao/82c69ae8ae652241aa7e4c62cb949ac6/6517de0a8ddd4a7e007b4bb70cf51b49.jpg',
            'https://s.laoyaoba.com/bianjifagao/ab504a7d91bb7e54a41e4f94f2076607/403724be377ac13d772143a8dc719db2.svg',
        ]

        print(f"images len: {len(images)}")
        qiniu_images = self.qs.fetch_image(images, 'tmp3')
        print("qiniu_images:")
        print(qiniu_images)

    def test_is_valid_extension(self):
        types = is_valid_image_extension('jpg')
        print(types)
        self.assertTrue(types)

        types = is_valid_image_extension('.jpg')
        print(types)
        self.assertTrue(types)

        types = is_valid_image_extension('.svg')
        print(types)
        self.assertFalse(types)

    def test_get_extension_from_url(self):
        # url = 'https://d.ifengimg.com/w538_h868_ablur_q90_webp/x0.ifengimg.com/ucms/2024_36/ACD68ED702213AF2AB18AB371E21F4B6149C3FAA_size594_w538_h868.png'
        url = 'http://image.sinajs.cn/n/cn/dk/640x360xxfhd/sh000001.png'
        types = get_extension_from_url(url, '.jpg')
        print(types)
        self.assertIsInstance(types, str)

    def test_get_extension_from_url2(self):
        # url = 'https://d.ifengimg.com/w538_h868_ablur_q90_webp/x0.ifengimg.com/ucms/2024_36/ACD68ED702213AF2AB18AB371E21F4B6149C3FAA_size594_w538_h868.png'
        from urllib.parse import urljoin

        detail_url = 'https://finance.sina.com.cn/stock/cpbd/2024-09-19/doc-incprwmw3909649.shtml'
        image_url = '//image.sinajs.cn/n/cn/dk/640x360xxfhd/sh000001.png'

        # 使用 urljoin 来拼接 image_url 和 detail_url
        full_image_url = urljoin(detail_url, image_url)
        print(full_image_url)
        self.assertIsInstance(full_image_url, str)

    def test_get_extension_from_url_attention(self):
        image_url = 'https://www.tsia.org.tw/api/DownloadPage?pageID=649'
        extension = get_extension_from_url(image_url, '.jpg')
        print(extension)
        self.assertIsInstance(extension, str)
