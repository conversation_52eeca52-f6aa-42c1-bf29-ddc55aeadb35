# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0

import mimetypes

import requests

from dc.common.qiniu_utils import get_extension_from_url


def get_extension_from_url222(url, default_extension='.jpg'):
    """
    根据图片 URL 请求头的 Content-Type 推断文件扩展名。
    :param url: 图片的 URL 地址
    :param default_extension: 无法推断时的默认扩展名，默认为 '.jpg'
    :return: 文件扩展名 (例如 '.jpg', '.png')，如果无法推断则返回默认扩展名
    """
    try:

        header = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (HTML, '
                          'like Gecko) Chrome/99.0.4844.82 Safari/537.36'}

        # 发起 HEAD 请求，获取图片的 Content-Type
        response = requests.head(url, allow_redirects=True, verify=False, headers=header)
        if response.status_code == 405:  # 服务器返回 405 Method Not Allowed，则表示不支持 HEAD 请求
            response = requests.get(url, allow_redirects=True, stream=True, verify=False, headers=header)

        content_type = response.headers.get('Content-Type').split(';')[0].strip()

        # 根据 Content-Type 推断扩展名
        extension = mimetypes.guess_extension(content_type)
        if extension is None:
            return default_extension
        return extension
    except Exception as e:
        # 请求失败时，返回默认扩展名
        print(f"Error fetching extension: {e}")
        return default_extension


# image_url = 'https://www.tsia.org.tw/api/DownloadPage?pageID=649'
image_urls = [
    # 'http://www.sse.com.cn/disclosure/listedinfo/announcement/c/new/2024-10-25/603188_20241025_W6G7.pdf',
    # 'https://www.bis.doc.gov/index.php/component/docman/?task=doc_download&gid=3417',
    # 'https://www.judiciary.senate.gov/download/2024-09-17-qfr-responses-goldfeder?download=1',
    # 'https://www.judiciary.senate.gov/download/2024-09-17-qfr-responses-goldfeder?download=1',
    # 'https://www.aei.org/wp-content/uploads/2024/10/A-Crisis-in-Leadership-Examining-the-Successes-and-Failures-of-University-Presidents-October-2024.pdf?x85095',
    # 'https://www.trade.gov/sites/default/files/2022-09/ACSCC%2029%20June%202022%20Freight%20Subcommittee%20recommendation%20package.pdf',
    # 'https://www.wuhu.gov.cn/openness/public/6596211/39624731.html',
    # 'https://kjj.sjz.gov.cn/columns/68f71d83-f258-4b60-aade-2df3d85d076e/202410/29/616b0794-8160-4de7-97cc-9b98e9e5f357.html',
    # 'https://news.qq.com/rain/a/20241029A082R400',
    # 'http://www.cninfo.com.cn/new/disclosure/detail?stockCode=835179&announcementId=1221560934&orgId=gfbj0835179&announcementTime=2024-10-29',
    'https://us.house.gov/hcfs/cecc/users/sflipse/2024%20Advocacy/Biden%20Letter%20unjustly%20detained%20Americans/Letter%20to%20POTUS%20--%20Unjustly%20Detained%20Americans.pdf',
    'https://www.cecc.gov/sites/evo-subsites/www.cecc.gov/files/2024-10/Letter%20to%20POTUS%20--%20Unjustly%20Detained%20Americans.pdf',
]
# 'application/pdf; charset=utf-8'

for image_url in image_urls:
    extension = get_extension_from_url(image_url, '.jpg')
    print(extension)
