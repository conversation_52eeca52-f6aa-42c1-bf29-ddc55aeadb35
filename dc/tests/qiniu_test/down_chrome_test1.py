# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0

import os
import time
from selenium.webdriver import DesiredCapabilities
from dc.common.webdriver_util import get_webdriver
from selenium.webdriver.common.by import By
from dc.tests.browser import Browser

# 基础下载路径（根目录）
base_download_dir = r"D:\tmp\123"

driver = get_webdriver(browser=Browser.Chrome, options={
    'arguments': [],
    'experimental_options': {"prefs": {
        "download.default_directory": base_download_dir,  # 设置默认下载路径
        "download.prompt_for_download": False,  # 禁止下载时弹出确认窗口
        "download.directory_upgrade": True,  # 允许目录升级
        "safebrowsing.enabled": True  # 启用安全浏览
    }},
    'extensions': [],
})

html = """
<a class="table_titlewrap" href="//static.sse.com.cn/disclosure/listedinfo/announcement/c/new/2024-10-24/603880_20241024_M36Q.pdf" 
target="_blank">
<span onclick="clickPdf($(this))">
南卫股份关于控股股东及高级管理人员收到中国证券监督管理委员会立案告知书的公告 
</span>
</a>
"""


# 打开目标网站
# driver.get('http://www.sse.com.cn/disclosure/listedinfo/announcement/')
driver.get(r"file://D:\woodw\code\python\ijiwei-dc2-alone\dc\tests\qiniu_test\2.html")

driver.implicitly_wait(10)

# 检查文件是否下载完成（排除临时文件并通过文件大小变化判断）
def is_download_complete(download_dir):
    previous_size = -1
    for _ in range(60):  # 最长等待60秒
        # 排除 .crdownload 和 .htm 文件，只检查正常文件
        files = [f for f in os.listdir(download_dir) if not (f.endswith(".crdownload") or f.endswith(".htm"))]
        if not files:  # 如果没有找到任何正常文件，继续等待
            time.sleep(1)
            continue

        # 检查符合条件的文件大小是否变化
        current_size = sum(os.path.getsize(os.path.join(download_dir, f)) for f in files)
        if current_size == previous_size:  # 如果文件大小不变，认为下载完成
            return True
        previous_size = current_size
        time.sleep(1)  # 每秒检查一次
    return False  # 超时后返回 False


# 动态设置下载目录：abc_序号
download_dir = os.path.join(base_download_dir, f"abc_ccc")

# 如果目录不存在，则创建该目录
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

# 清空下载目录
for file in os.listdir(download_dir):
    file_path = os.path.join(download_dir, file)
    if os.path.isfile(file_path):
        os.unlink(file_path)

# 设置下载路径并允许下载
driver.execute_cdp_cmd("Page.setDownloadBehavior", {
    "behavior": "allow",
    "downloadPath": download_dir
})

# 获取下载链接和文件名
href = "ddd"
name = "dddddddd"

# 将信息存入 attachments 列表
attachment = {
    "name": name,
    "href": href,
    "download_dir": download_dir
}

# 输出下载信息
print(f"正在下载: {href} 到目录: {download_dir}")

time.sleep(3)

# 等待下载完成
if is_download_complete(download_dir):
    print(f"附件 {name} 下载完成.")
else:
    print(f"附件 {name} 下载超时或失败.")

# 关闭浏览器
driver.quit()

