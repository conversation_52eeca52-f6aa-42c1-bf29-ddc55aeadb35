# -*- coding:utf-8 -*-
# @Function  : 七牛云测试类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0
import time

from dc.common.webdriver_util import get_remote_webdriver, get_webdriver
from selenium.webdriver.common.by import By

from dc.common.qiniu_utils import *
from dc.services.qiniu_service import QiniuService
from dc.tests.browser import Browser
from dc.tests.test_simple_basics import SimpleBasicsTestCase

image_url = 'http://static.sse.com.cn/disclosure/listedinfo/announcement/c/new/2024-10-24/603880_20241024_M36Q.pdf'
extension = get_extension_from_url(image_url, '.jpg')
print(extension)

import requests

driver = get_webdriver(browser=Browser.Chrome, options={
    'arguments': [],
    'experimental_options': {"prefs": {
        "download.default_directory": r"D:\tmp\123",  # 设置默认下载路径
        "download.prompt_for_download": False,  # 禁止下载时弹出确认窗口
        "download.directory_upgrade": True,  # 允许目录升级
        "safebrowsing.enabled": True  # 启用安全浏览
    }},
    'extensions': [],
})

driver.get('http://www.sse.com.cn/disclosure/listedinfo/announcement/')

driver.implicitly_wait(10)


time.sleep(30)


elements = driver.find_elements(By.XPATH, '//a[@title="点击下载公告文件"]')

print(len(elements))



for element in elements:
    print(element.get_attribute('href'))
    print(element.get_attribute('download'))
    element.click()
    time.sleep(10)
    # break

# print(driver.page_source)

time.sleep(20)

driver.quit()
