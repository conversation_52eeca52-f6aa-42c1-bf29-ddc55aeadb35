# -*- coding:utf-8 -*-
# @Function  : firefox5
# <AUTHOR>
# @Time      : 2024-10-24
# Version    : 1.0


import os
import time
from selenium.webdriver.common.by import By
from dc.common.webdriver_util import get_webdriver
from dc.tests.browser import Browser

# 基础下载路径（根目录）
base_download_dir = r"D:\tmp\123"


# 获取所有下载链接和名称
def get_download_links(driver):
    driver.get('http://www.sse.com.cn/disclosure/listedinfo/announcement/')
    driver.implicitly_wait(10)
    elements = driver.find_elements(By.XPATH, '//a[@title="点击下载公告文件"]')

    attachments = []
    for idx, element in enumerate(elements):
        href = element.get_attribute('href')
        name = element.get_attribute('download') or f'file_{idx}'
        attachments.append({
            "name": name,
            "href": href
        })
    return attachments


# 检查文件是否下载完成（通过文件大小变化和排除临时文件）
def is_download_complete(download_dir):
    previous_size = -1
    for _ in range(60):  # 最长等待60秒
        files = [f for f in os.listdir(download_dir) if not (f.endswith(".part") or f.endswith(".htm"))]
        if not files:
            time.sleep(1)
            continue

        current_size = sum(os.path.getsize(os.path.join(download_dir, f)) for f in files)
        if current_size == previous_size:
            return True
        previous_size = current_size
        time.sleep(1)
    return False  # 超时后返回 False


# 下载单个附件
def download_attachment(attachment, download_dir):
    # 如果目录不存在，则创建该目录
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)

    # 清空下载目录
    for file in os.listdir(download_dir):
        file_path = os.path.join(download_dir, file)
        if os.path.isfile(file_path):
            os.unlink(file_path)

    # 设置 Firefox 的 preferences
    firefox_options = {
        "browser.download.folderList": 2,  # 2 = custom location
        "browser.download.dir": download_dir,  # 设置自定义下载路径
        "browser.helperApps.neverAsk.saveToDisk": "application/pdf,application/octet-stream,application/vnd.ms-excel",  # 不弹出对话框，自动下载指定类型的文件
        "pdfjs.disabled": True  # 禁用 PDF 预览
    }

    # 获取 WebDriver 实例
    driver = get_webdriver(browser=Browser.Firefox, options={
        'preferences': firefox_options,
        'arguments': [],
        'extensions': []
    })

    # 打开附件的下载链接
    driver.get(attachment['href'])
    print(f"正在下载: {attachment['href']} 到目录: {download_dir}")

    # 等待下载完成
    if is_download_complete(download_dir):
        print(f"附件 {attachment['name']} 下载完成.")
    else:
        print(f"附件 {attachment['name']} 下载超时或失败.")

    # 关闭当前 WebDriver 实例
    driver.quit()


# 主流程
def main():
    # 创建一个主 WebDriver 实例用于获取下载链接
    main_driver = get_webdriver(browser=Browser.Firefox, options={
        'preferences': {
            "pdfjs.disabled": True  # 禁用 PDF 预览
        },
        'arguments': [],
        'extensions': []
    })

    # 获取所有附件的下载链接
    attachments = get_download_links(main_driver)
    print(f"找到 {len(attachments)} 个附件")

    # 关闭主 WebDriver 实例
    main_driver.quit()

    # 下载每个附件，分别设置不同的下载目录
    for idx, attachment in enumerate(attachments):
        if idx > 5:
            break

        download_dir = os.path.join(base_download_dir, f"abc_{idx}")
        download_attachment(attachment, download_dir)

    # 打印下载的附件信息
    for idx, attachment in enumerate(attachments):
        print(f"{idx}. 附件名: {attachment['name']}, 下载链接: {attachment['href']}, 存储路径: {os.path.join(base_download_dir, f'abc_{idx}')}", end='\n')


if __name__ == "__main__":
    main()
