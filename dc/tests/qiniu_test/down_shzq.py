# -*- coding:utf-8 -*-
# @Function  : 上海证券
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0

import os
import time
from selenium.webdriver import DesiredCapabilities
from dc.common.webdriver_util import get_webdriver
from selenium.webdriver.common.by import By
from dc.tests.browser import Browser
from dc.tests.qiniu_test.attachment_util import *

# 基础下载路径（根目录）
base_download_dir = r"D:\tmp\123"

driver = get_webdriver(browser=Browser.Chrome, options={
    'arguments': [
        '--disable-popup-blocking',
        '--allow-running-insecure-content',
        '--no-sandbox',
        '--disable-web-security'
    ],
    'experimental_options': {"prefs": {
        "download.default_directory": base_download_dir,  # 设置默认下载路径
        "download.prompt_for_download": False,  # 禁止下载时弹出确认窗口
        "download.directory_upgrade": True,  # 允许目录升级
        "safebrowsing.enabled": True  # 启用安全浏览
    }},
    'extensions': [],
})

# 打开目标网站
driver.get('http://www.sse.com.cn/disclosure/listedinfo/announcement/')

driver.implicitly_wait(10)

download_file = '603188_20241025_江苏亚邦染料股份有限公司简式权益变动报告书（亚邦投资控股集团有限公司）-99.pdf'

# 动态设置下载目录：abc_序号
download_dir = get_random_dir(base_download_dir)
create_download_dir(download_dir)
print(f"下载目录：{download_dir}")

# 设置下载路径并允许下载
driver.execute_cdp_cmd("Page.setDownloadBehavior", {
    "behavior": "allow",
    "downloadPath": download_dir
})

info = {
    'title': '点击下载公告文件',
    'href': '/disclosure/listedinfo/announcement/c/new/2024-10-25/603188_20241025_W6G7.pdf',
    'download': download_file,
}

# 插入 <a> 标签到 body
script = f'''
    var newLink = document.createElement("a");
    newLink.className = "iconfont iconxiazai iconxiazai_8888";
    newLink.href = "{info['href']}";
    newLink.title = "{info['title']}";
    newLink.download = "{info['download']}";
    newLink.innerHTML = "88888888";
    document.body.appendChild(newLink);
    newLink.click();  // 点击该链接
'''
driver.execute_script(script)

# 等待下载完成
if is_download_complete(download_dir):
    print(f"附件 {download_file} 下载完成.")
else:
    print(f"附件 {download_file} 下载超时或失败.")

time.sleep(3)

# 关闭浏览器
driver.quit()
