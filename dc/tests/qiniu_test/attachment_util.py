# -*- coding:utf-8 -*-
# @Function  : attachment_util
# <AUTHOR>
# @Time      : 2024-10-25
# Version    : 1.0

import os
import random
import time


# 检查文件是否下载完成（排除临时文件并通过文件大小变化判断）
def is_download_complete(download_dir):
    previous_size = -1
    for _ in range(60):  # 最长等待60秒
        # 排除 .crdownload 和 .htm 文件，只检查正常文件
        files = [f for f in os.listdir(download_dir) if not (f.endswith(".crdownload") or f.endswith(".htm"))]
        if not files:  # 如果没有找到任何正常文件，继续等待
            time.sleep(1)
            continue

        # 检查符合条件的文件大小是否变化
        current_size = sum(os.path.getsize(os.path.join(download_dir, f)) for f in files)
        if current_size == previous_size:  # 如果文件大小不变，认为下载完成
            return True
        previous_size = current_size
        time.sleep(1)  # 每秒检查一次

    return False  # 超时后返回 False


def create_download_dir(download_dir) -> str:
    # 如果目录不存在，则创建该目录
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)

    # 清空下载目录
    for file in os.listdir(download_dir):
        file_path = os.path.join(download_dir, file)
        if os.path.isfile(file_path):
            os.unlink(file_path)

    return download_dir


def get_random_dir(base_download_dir, download_dir: str = None) -> str:
    if download_dir:
        current_download_dir = os.path.join(base_download_dir, download_dir)
    else:
        r = random.Random()
        rint = r.randint(100000, 999999)
        current_download_dir = os.path.join(base_download_dir, f"abc_{rint}")

    return current_download_dir
