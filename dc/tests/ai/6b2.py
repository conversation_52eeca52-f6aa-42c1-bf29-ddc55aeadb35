import json

from services.chatglm_service import ChatGLMService

glm = ChatGLMService()
# token = glm.get_token()
# print(token)

data_full = {
    # "top_p": 0.7,
    # "temperature": 0.9,
    "prompt": "他之前担任什么职务？",
    # "requestTaskNo": "1542097269879345154",
    "history": [
        "清华大学校长是谁？",
        "截止 2023 年，清华大学校长是王希勤"
    ]
}

data = {
    "prompt": "兔子有几只脚？",
}

history = []
# result: dict = glm.execute_engine(data)
# print(json.dumps(result, ensure_ascii=False, indent=4))

while True:
    question = input("请输入你的问题:\n")
    if len(question.strip()) == 0:
        question = input("请再次输入你的问题:\n")
        continue
    elif question == "exit":
        print('exit')
        exit(0)
    else:
        result = glm.execute_engine({
            "prompt": question,
            "history": history
        })
        history.append(question)
        history.append(result['outputText'])
        print(json.dumps(result, ensure_ascii=False, indent=4))
