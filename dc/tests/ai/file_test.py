import random
import sys
import os


pfile = 'd:/test/susu.xlsx'
# pfile = 'd:/fapiao.pdf'
# pfile = 'd:/test/shengdu.pdf'
# 打开 PDF 文件
pdf_file = open(pfile, 'rb')

import io
import pytesseract
import sys

from PIL import Image
from tika import parser
from wand.image import Image as wi


def extra_mp3_text():
    """抽取pdf文本内容(tika)"""
    text_raw = parser.from_file(pfile)
    content = text_raw['content'].strip()
    print(content)
    # 打开文件，如果文件不存在则创建一个新文件
    # with open('2222.txt', 'w',encoding='utf-8') as f:
    #     # 写入内容
    #     f.write(content)
    return content


# extra_mp3_text()


def extra_mp3_text():
    import tika
    from tika import parser
    # Set the path to the Tika binary file
    # tika.initVM(jvmargs=['-D', 'tika.path=/path/to/tika-app.jar'])
    # Parse the MP3 audio file
    parsed = parser.from_file('d:/test/01053065691(01053065691)_20230501101854.mp3')
    # Extract the metadata and text content
    metadata = parsed['metadata']
    text_content = parsed['content']
    print(text_content)


# extra_mp3_text()


def extra_mp3_text_PyPDF2():
    """抽取pdf文本内容(PyPDF2)"""
    global pdf_file, text
    import PyPDF2
    # 打开 PDF 文件
    pdf_file = open('d:/深度学习入门：基于Python的理论与实现.pdf', 'rb')
    # 创建 PdfReader 对象
    pdf_reader = PyPDF2.PdfReader(pdf_file)
    # 获取 PDF 的页数
    num_pages = len(pdf_reader.pages)
    # 逐页读取文本内容
    for page in range(num_pages):
        pdf_page = pdf_reader.pages[page]
        text = pdf_page.extract_text()
        print(text)
    # 关闭 PDF 文件
    pdf_file.close()


# extra_mp3_text_PyPDF2()