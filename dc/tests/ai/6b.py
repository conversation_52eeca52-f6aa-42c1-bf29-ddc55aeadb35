# -*- coding:utf-8 -*-
from wudao.api_request import executeEngine, getToken

# 接口 API KEY
API_KEY = "05afd3b4ac3047e99a23dd417df73463"
# 公钥
PUBLIC_KEY = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJBg/jeSpPV/dlYHtyvsSgLC2lv2Big3KOZcakqYr0ROoNu/fvE2ydH9dj+N/PTCKJ8Tfwfn9ZDAKbtVBWimfSkCAwEAAQ=="

# 能力类型
ability_type = "chatGLM"
# 引擎类型
engine_type = "chatGLM"
# 请求参数样例
data = {
    "top_p": 0.7,
    "temperature": 0.9,
    "prompt": "他之前担任什么职务？",
    "requestTaskNo": "1542097269879345154",
    "history": [
        "清华大学校长是谁？",
        "截止 2023 年，清华大学校长是王希勤"
    ]
}
'''注意这里仅为了简化编码每一次请求都去获取 token， 线上环境 token 有过期时间， 客户端可
自行缓存，过期后重新获取。
'''
token_result = getToken(API_KEY, PUBLIC_KEY)
if token_result and token_result["code"] == 200:
    token = token_result["data"]
    resp = executeEngine(ability_type, engine_type, token, data)
    print(resp)
else:
    print("获取 token 失败，请检查 API_KEY 和 PUBLIC_KEY")
