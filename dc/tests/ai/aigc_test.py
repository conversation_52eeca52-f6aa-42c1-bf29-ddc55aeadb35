import random
import sys
import os
import time

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.chatgpt_service import ChatGPTService
from dc.services.aigc_service import AIGCService
from jwtools.dt import *
from jwtools.func import *

# 测试文本内容提取
pdffile = r'E:\ijiwei\2023\爱集微咨询（厦门）有限公司员工手册.pdf'
aigc = AIGCService()
result = aigc.extract_text(pdffile)
print(result)

from jwtools.io_util import *

write_text_to_file('shouce.txt',result)
exit(0)

# 测试mp3 内容提取
# mp3file = 'd:/test/01053065691(01053065691)_20230501101854.mp3'
# text = aigc.speech_to_text(mp3file)
# print(text)

agic = AIGCService()

# string = '爱集微的发展历程一直在不断前行,目前,爱集微已在国内多个城市设立分支机构,并实现员工总数超过200人的发展状态.未来,爱集微将继续扩大规模,向更多城市拓展销售网络,进一步提高服务质量,不断提升客户体验,为客户提供更优质的服务,全力提升客户满意度,以提升爱集微的影响力.未来,爱集微将继续稳固和扩大服务范围,并竭力满足客户的需求,以打造更加优秀的服务体系.爱集微将一如既往地为中国服务行业的发展做出贡献,不断改进服务体系,拓展市场,提高服务质量,提升客户满意度与体验感,全力推动行业的发展和变革,真正做到以客户为中心,以创新为驱动, 成为中国服务行业的引领品牌.:爱集微旨在提升客户体验、优化产品和服务的水平,以便满足越来越多顾客的需求.为此,我们采取了一系列的措施,以追求更具创新性和可持续发展的产品和服务发展前景.我们正在努力改善客户的体验,以确保更满意的用户体验.一方面,我们开发了一套完整的自助服务系统,使客户能够得到更好的帮助.另一方面,我们不断加强客户反馈系统,以了解客户的真实需求,并深入了解他们的体验情况.此外,我们也在持续优化产品和服务,以满足用户需求.我们把客户意见作为主导,调整产品和服务的设计,以便给用户带来最大快乐感.同时,我们也和合作伙伴紧密合作,增强品牌影响力,提升客户粘性.爱集微一直致力于提升客户体验、优化产品和服务.我们相信,通过不断改进、创新和提升,我们将会不断实现更高水平的产品和服务发展,从而为更多的用户提供更好的服务.未来,我们将会继续不断优化客户体验和产品服务,为顾客提供更优质的产品和服务.爱集微有着巨大的发展潜力,因此建立良好的合作伙伴关系非常重要.在用户体验和技术上与合作伙伴进行强化合作,可以进一步提升爱集微的发展前景.这样的合作可以让双方获得最大的利益,例如获得更多的创新思路和解决方案,并以此进入新的市场.爱集微在技术研发上可以和合作伙伴一起致力于改进现有服务,例如改善用户体验,提高市场关注度,加强核心技术的发展,以及发现和解决新技术中出现的问题.同时联合合作伙伴对市场有更准确的理解也是必不可少的,因为这可以使爱集微得到更多的新客户,进而让服务范围更大,产生更多的利益.同时,爱集微还可以通过建立良好的合作伙伴关系来拓展新的市场,在制定合理的发展战略时可以得到双方的支持,从价格、物流、技术、服务等多方面拓展新的市场.同时,双方之间的交流和共享资源对于企业发展也是非常有帮助的,可以从各方面获取发展的动力,并形成互利共赢的局面.综上,爱集微与合作伙伴之间的强化合作,可以通过改进用户体验、提高市场关注度,获得更多的创新思路和解决方案,以及拓展新的市场,实现双方的价值最大化,有利于发展双方的企业,促进企业合作的发展.爱集微将继续与合作伙伴建立更紧密的合作关系,共同实现企业价值最大化,并实现企业发展的目标.'
# print(string)
#
# print(agic.token_len(content=string))
# print(agic.cut_content_by_token_len(string, 1500))


chatgpt = ChatGPTService()

start = time_s(3)
data = chatgpt.chat("一只狗几条腿")
print(data)
print_vf(
    time_work(start)
)

start = time_s(3)
data = chatgpt.chat4("一只鸡几条腿")
print(data)
print_vf(
    time_work(start)
)

exit(0)

start = time_s(3)
data2 = chatgpt.chat4("你是一名资深编辑 需要写作当前的这篇新闻章节,新闻的主题是:  新能源电池的博弈时代 ,字数控制在1200-1500字,  用新闻稿的方式  不要段落标题、不要说明 , 用中文返回")
print(data2)
print_vf(
    time_work(start)
)

#
# data2 = chatgpt.chat4("你是一名资深编辑 需要写作当前的这篇新闻章节,新闻的主题是:  新能源电池的博弈时代 ,字数控制在1200-1500字,  用新闻稿的方式  不要段落标题、不要说明 , 用中文返回")
# print(data2)