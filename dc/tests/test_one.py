import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

from flask_sqlalchemy import BaseQuery
from dc.models.model_test import User
from flask import current_app
from dc.tests.test_basics import BasicsTestCase


class TestOne(BasicsTestCase):

    name = None

    def setUp(self):
        print('TestOne-setUp')
        self.name = '1223'
        pass

    def setUpClass(cls) -> None:
        print('setUpClass-setUp')
        pass

    def test_cc(self):
        # with app.app_context():
        """增加数据"""
        print('TestOne-test_cc')

    def test_cc2(self):
        print('TestOne-test_cc2')

    def test_from_other(self):
        print('TestOne-test_from_other')
        print(self.name)
