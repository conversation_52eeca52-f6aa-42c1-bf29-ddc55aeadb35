"""
功能描述
:author wjh
:date 2023-02-13
"""
import json
import os
import sys
from dc.services.chatgpt_service import ChatGPTService

gpt = ChatGPTService()

question = '''
请提取新闻标题，“请问公司截至2月20日的股东人数是多少?尊敬的投资者：感谢您的关注和支持。截止2月20日，18738。”，保留25个字以内
'''

question = '''
情感分析，使用正面 负面 中性描述，“四川募投项目土建收尾工作和设备安装调试进行中”
'''
#
# question22 = '''
# 请提取新闻标题，“请问公司截至2月20日的股东人数是多少?尊敬的投资者：感谢您的关注和支持。截止2月20日，18738。”，保留25个字以内
# '''

# question = '''
# 提取摘要，“尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢”，保留30个字以内
# '''

questions = [
    "情感分析，使用正面 负面 中性描述，“四川募投项目土建收尾工作和设备安装调试进行中”",
    "情感分析，使用正面 负面 中性描述，“小张的妈妈去世了，非常伤心”",
    "情感分析，使用正面 负面 中性描述，“积极提升品牌活力，提升品牌势能”"
]
#
# for question in questions:
#     result = gpt.chat(question)
#     print(result + '\n')

res = gpt.stock_chat({"id": 30, "answer": "报告期各期末，公司股东权益分别为13,807.40万元、17,031.24万元、21,473.60万元和25,798.58万元。报告期内，公司经营情况良好，利润规模逐年增加，公司股东权益逐年增长。"})
# result = gpt.chat(question)
# print(result)
print(res)

for i in range(1, 10):
    result = gpt.chat(question)
    print(result)
    
res = gpt.stock_chat({"id": 30, "answer": "报告期各期末，公司股东权益分别为13,807.40万元、17,031.24万元、21,473.60万元和25,798.58万元。报告期内，公司经营情况良好，利润规模逐年增加，公司股东权益逐年增长。"})
# result = gpt.chat(question)
# print(result)
print(res)
