import json

import openai


# from pydub import AudioSegment


# song = AudioSegment.from_mp3("sy.mp3")
#
# # PyDub handles time in milliseconds
# ten_minutes = 1 * 60 * 1000
#
# first_10_minutes = song[:ten_minutes]
#
# first_10_minutes.export("sy2.mp3", format="mp3")


def speech_to_text(file, api_key=""):
    if api_key != "":
        openai.api_key = api_key
    else:
        openai.api_key = "***************************************************"

    audio_file = open(file, "rb")
    transcript = openai.Audio.transcribe("whisper-1", audio_file)
    data = json.loads(str(transcript))
    print(data["text"])
    return data["text"]
