# 过滤文章内容 ,如果包含 words 中的词, 则返回true 否则返回false
import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

from dc.services.content_filter_service import ContentFilterService





content ="""
没有8T1C LTPO
就别谈顶级屏幕”
盘点2023年智能手机市场，不难发现LTPO替代LTPS已成为行业一大趋势。特别是2023年下半年开始，8T1C LTPO屏幕开始在国内高端旗舰机型上使用。业内人士普遍认为，未来8T1C LTPO将是旗舰手机的主流方案。更有人称，“没有8T1C LTPO就别谈顶级屏幕”。8T1C LTPO是未来的方向，也是旗舰项目的标准配置。
8T1C LTPO
什么是8T1C LTPO？
8T1C是一种新型的像素驱动电路，能实现更高的阳极和驱动管TFT复位频率。事实上，LTPO最开始使用传统的7T1C像素电路。8T1C就是在原来7T1C基础上增加了驱动管TFT复位驱动电路。在同尺寸的屏幕上，每一个像素点都要多做一个驱动电路，设计的难度和成本都大大增加。
为何8T1C LTPO屏幕受到高端旗舰青睐？它有哪些技术优势？
1
显示效果更优
对于用户来说，衡量一块好屏幕首先看显示效果。8T1C作为LTPO驱动像素电路的升级方案，首先在显示体验上带来大幅提升。
显示均一性方面，由于8T1C在传统7T1C基础上增加了TFT复位驱动电路，通过周期性针对像素进行3次补偿和复位动作（120Hz基频），把像素状态显示从120Hz提升到360Hz，拥有更好的像素一致性，可实现更清晰通透的显示画质。比如，无论是刷抖音还是小红书，8T1C可以改善上一帧画面对下一帧画面的影响，视觉效果更好。
2
画质更流畅
8T1C频率切换的步幅由原来7T1C最快的120Hz提升到360Hz，得益于均匀的360Hz复位和补偿，画面切换更快更流畅，同时大幅降低了画面抖动感。在使用微信、抖音、今日头条，以及玩游戏时，画质更流畅，操作体验更好。
3
功耗更低
LTPO更为人熟知的是自适应调节带来的低功耗收益。如今，长续航已经越来越成为刚需，8T1C LTPO在低功耗方面实现再进阶。
首先，8T1C LTPO支持的驱动频率更多。8T1C LTPO的复位驱动电路可以使用多pulse，因而可以使用更多的驱动频率，如1Hz、30Hz、40Hz、60Hz、72Hz、90Hz、120Hz等。不同的驱动频率适配不同应用场景，对应用场景实施精细化管理，“精打细算”，不浪费功耗。另一方面，8T1C LTPO可以带来直接切频的效果，无需插入过渡帧率，从而降低功耗。
此外，8T1C LTPO对功耗收益和闪烁效果的保证，使得全场景可低至1Hz显示，同时切换到1Hz更快且无闪烁，是当前真正实现1Hz全局AOD的关键，同时综合护眼表现更优秀。
显示效果优异更流畅，功耗更低更长续航，难怪高端旗舰机热衷8T1C LTPO 屏幕

"""
wf = ContentFilterService()
result = wf.word_filter(wf.words, content)
print(result)