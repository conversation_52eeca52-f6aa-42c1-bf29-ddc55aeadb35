import unittest
from flask import current_app
from dc.app import create_app, db, app


class SimpleBasicsTestCase(unittest.TestCase):
    def setUp(self):
        # self.app = app
        # self.app_context = self.app.app_context()
        # self.app_context.push()
        # self.db = db
        #db.create_all
        pass

    def tearDown(self):
        #db.session.remove()
        #db.drop_all()
        #db.session.commit()
        #self.app_context.pop()
        pass

    def test_hello(self):
        print('hello')
