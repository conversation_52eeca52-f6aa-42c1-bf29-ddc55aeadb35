# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/9/23
# Version    : 1.0
import os.path

from dc.models.model import DCSiteTask
from dc.common.func import *
from dc.common.functools_wraps import *

with get_session(run_model='test') as session:
    site_task: DCSiteTask = session.get(DCSiteTask, 435965)
    print(site_task.title)

with get_redis_connection(run_model='test') as redis_client:
    result = redis_client.get('dog')
    print(result)

fileName = os.path.join(get_project_path('tmp'), '123.txt')
with file_manager(fileName, 'w') as f:
    f.write('hello')
    f.write('world')
    f.writelines(['hello', 'world'])
    f.write("\n232332")
