# -*- coding:utf-8 -*-
# @Function  : wrapper_test
# <AUTHOR> wjh
# @Time      : 2024-10-12
# Version    : 1.0

from dc.common.functools_wraps import *
from dc.services.logging_service import LoggingService

logger = LoggingService(logfile='wrapper_test.log')

with try_catch(logger, raise_exception=False):
    result = 10 / 2  # 这将引发一个异常
    print(result)

try:

    with try_catch(logger, raise_exception=True):
        result = 10 / 0  # 这将引发一个异常
        print(222)

except Exception as ex:
    print(ex)

exit(0)

with get_redis_connection(run_model='test') as redis:
    redis.set('test', 22)
    article = redis.get('test')
    print(article)

exit(0)

with get_session() as session:
    article = session.query(DCGzhArticle).first()
    print(article.to_dict())

with get_elasticsearch_connection(run_model='prod') as client:
    coll = client.search(index='dc_bianjifagao', body={})
    zz = coll['hits']['hits']
    print(zz)

with get_mongo_client(run_model='prod') as client:
    coll = client.get_database('yuqing').get_collection('test')
    zz = coll.find_one({'id': 1})
    print(zz)
