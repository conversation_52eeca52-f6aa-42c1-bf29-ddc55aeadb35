import datetime
import json
import time
import sys
import os

import numpy as np

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.logging_service import LoggingService
from dc.common.utils import Utils

log_service = LoggingService('zzz.log')


mysql_data = [
    "23", "1000",
    "67", "481",
    "212", "5418",
    "215", "501",
    "216", "2630",
    "219", "500",
    "220", "321",
    "221", "443",
    "222", "1746",
    "223", "832",
    "224", "271",
    "225", "271",
    "226", "708",
    "227", "1336",
    "228", "281",
    "229", "329",
    "230", "389",
    "232", "187",
    "237", "296",
    "238", "1234",
    "239", "330",
    "240", "384",
    "242", "267",
    "243", "386",
    "244", "453",
    "245", "3557",
    "246", "379",
    "247", "317",
    "248", "12",
    "250", "21",
    "253", "10154",
    "254", "4",
    "255", "6",
    "277", "5155",
    "278", "2001",
    "279", "1493",
    "280", "1019",
    "281", "1336",
    "282", "186",
    "283", "1384",
    "284", "3000",
    "285", "451",
    "286", "2051",
    "288", "390",
    "290", "2535",
    "291", "93",
    "292", "1777",
    "293", "809",
    "294", "902",
    "295", "1000",
    "296", "401",
    "297", "936",
    "298", "1854",
    "299", "460",
    "300", "281",
    "301", "167",
    "303", "5208",
    "304", "3851",
    "305", "400",
    "306", "1000",
    "307", "650",
    "308", "1001",
    "309", "400",
    "310", "400",
    "311", "405",
    "312", "397",
    "313", "1582",
    "314", "4292",
    "315", "42",
    "316", "1000",
    "317", "500",
    "319", "492",
    "320", "1000",
    "322", "576",
    "323", "1000",
    "324", "20",
    "325", "1040",
    "326", "1238",
    "327", "388",
    "328", "500",
    "329", "278",
    "330", "664",
    "331", "2252",
    "332", "597",
    "333", "218",
    "334", "1200",
    "335", "400",
    "336", "240",
    "337", "800",
    "338", "654",
    "339", "200",
    "340", "1392",
    "341", "480",
    "342", "3359",
    "343", "500",
    "345", "418",
    "346", "90",
    "347", "2500",
    "348", "1000",
    "349", "633",
    "350", "3443",
    "351", "525",
    "353", "500",
    "354", "500",
    "355", "493",
    "356", "650",
    "357", "2823",
    "359", "1502",
    "360", "500",
    "361", "649",
    "362", "487",
    "364", "1250",
    "365", "15",
    "366", "134",
    "367", "302",
    "368", "312",
    "369", "323",
    "370", "585",
    "371", "40",
    "372", "2139"]

buckets = [
    {
        "key": 23,
        "doc_count": 1000
    },
    {
        "key": 212,
        "doc_count": 4875
    },
    {
        "key": 215,
        "doc_count": 499
    },
    {
        "key": 216,
        "doc_count": 2625
    },
    {
        "key": 219,
        "doc_count": 491
    },
    {
        "key": 220,
        "doc_count": 287
    },
    {
        "key": 221,
        "doc_count": 407
    },
    {
        "key": 222,
        "doc_count": 1740
    },
    {
        "key": 223,
        "doc_count": 605
    },
    {
        "key": 224,
        "doc_count": 270
    },
    {
        "key": 225,
        "doc_count": 260
    },
    {
        "key": 226,
        "doc_count": 708
    },
    {
        "key": 227,
        "doc_count": 1336
    },
    {
        "key": 228,
        "doc_count": 278
    },
    {
        "key": 229,
        "doc_count": 329
    },
    {
        "key": 230,
        "doc_count": 389
    },
    {
        "key": 232,
        "doc_count": 187
    },
    {
        "key": 237,
        "doc_count": 287
    },
    {
        "key": 238,
        "doc_count": 1234
    },
    {
        "key": 240,
        "doc_count": 384
    },
    {
        "key": 242,
        "doc_count": 246
    },
    {
        "key": 243,
        "doc_count": 386
    },
    {
        "key": 244,
        "doc_count": 452
    },
    {
        "key": 245,
        "doc_count": 3557
    },
    {
        "key": 246,
        "doc_count": 379
    },
    {
        "key": 247,
        "doc_count": 314
    },
    {
        "key": 248,
        "doc_count": 12
    },
    {
        "key": 250,
        "doc_count": 21
    },
    {
        "key": 253,
        "doc_count": 8094
    },
    {
        "key": 254,
        "doc_count": 4
    },
    {
        "key": 255,
        "doc_count": 3
    },
    {
        "key": 277,
        "doc_count": 5097
    },
    {
        "key": 278,
        "doc_count": 1253
    },
    {
        "key": 279,
        "doc_count": 1485
    },
    {
        "key": 280,
        "doc_count": 1018
    },
    {
        "key": 281,
        "doc_count": 1336
    },
    {
        "key": 282,
        "doc_count": 186
    },
    {
        "key": 283,
        "doc_count": 692
    },
    {
        "key": 284,
        "doc_count": 2916
    },
    {
        "key": 285,
        "doc_count": 450
    },
    {
        "key": 286,
        "doc_count": 2034
    },
    {
        "key": 288,
        "doc_count": 388
    },
    {
        "key": 290,
        "doc_count": 2534
    },
    {
        "key": 291,
        "doc_count": 93
    },
    {
        "key": 292,
        "doc_count": 1777
    },
    {
        "key": 293,
        "doc_count": 808
    },
    {
        "key": 294,
        "doc_count": 889
    },
    {
        "key": 295,
        "doc_count": 996
    },
    {
        "key": 296,
        "doc_count": 397
    },
    {
        "key": 297,
        "doc_count": 933
    },
    {
        "key": 298,
        "doc_count": 1849
    },
    {
        "key": 299,
        "doc_count": 459
    },
    {
        "key": 300,
        "doc_count": 258
    },
    {
        "key": 301,
        "doc_count": 166
    },
    {
        "key": 303,
        "doc_count": 5206
    },
    {
        "key": 304,
        "doc_count": 3778
    },
    {
        "key": 306,
        "doc_count": 996
    },
    {
        "key": 307,
        "doc_count": 648
    },
    {
        "key": 308,
        "doc_count": 1000
    },
    {
        "key": 309,
        "doc_count": 194
    },
    {
        "key": 310,
        "doc_count": 398
    },
    {
        "key": 311,
        "doc_count": 405
    },
    {
        "key": 312,
        "doc_count": 397
    },
    {
        "key": 313,
        "doc_count": 1552
    },
    {
        "key": 314,
        "doc_count": 4245
    },
    {
        "key": 315,
        "doc_count": 40
    },
    {
        "key": 316,
        "doc_count": 990
    },
    {
        "key": 317,
        "doc_count": 495
    },
    {
        "key": 319,
        "doc_count": 485
    },
    {
        "key": 320,
        "doc_count": 997
    },
    {
        "key": 322,
        "doc_count": 576
    },
    {
        "key": 323,
        "doc_count": 861
    },
    {
        "key": 324,
        "doc_count": 20
    },
    {
        "key": 325,
        "doc_count": 1009
    },
    {
        "key": 326,
        "doc_count": 1202
    },
    {
        "key": 327,
        "doc_count": 387
    },
    {
        "key": 328,
        "doc_count": 495
    },
    {
        "key": 329,
        "doc_count": 278
    },
    {
        "key": 330,
        "doc_count": 617
    },
    {
        "key": 331,
        "doc_count": 2240
    },
    {
        "key": 332,
        "doc_count": 597
    },
    {
        "key": 333,
        "doc_count": 217
    },
    {
        "key": 334,
        "doc_count": 1196
    },
    {
        "key": 335,
        "doc_count": 393
    },
    {
        "key": 336,
        "doc_count": 236
    },
    {
        "key": 337,
        "doc_count": 796
    },
    {
        "key": 338,
        "doc_count": 650
    },
    {
        "key": 339,
        "doc_count": 199
    },
    {
        "key": 340,
        "doc_count": 1377
    },
    {
        "key": 341,
        "doc_count": 464
    },
    {
        "key": 342,
        "doc_count": 3358
    },
    {
        "key": 343,
        "doc_count": 480
    },
    {
        "key": 345,
        "doc_count": 413
    },
    {
        "key": 346,
        "doc_count": 90
    },
    {
        "key": 347,
        "doc_count": 2438
    },
    {
        "key": 348,
        "doc_count": 993
    },
    {
        "key": 349,
        "doc_count": 616
    },
    {
        "key": 350,
        "doc_count": 3280
    },
    {
        "key": 351,
        "doc_count": 59
    },
    {
        "key": 353,
        "doc_count": 490
    },
    {
        "key": 354,
        "doc_count": 493
    },
    {
        "key": 355,
        "doc_count": 492
    },
    {
        "key": 356,
        "doc_count": 650
    },
    {
        "key": 357,
        "doc_count": 2566
    },
    {
        "key": 359,
        "doc_count": 1435
    },
    {
        "key": 360,
        "doc_count": 492
    },
    {
        "key": 361,
        "doc_count": 644
    },
    {
        "key": 362,
        "doc_count": 484
    },
    {
        "key": 364,
        "doc_count": 1211
    },
    {
        "key": 365,
        "doc_count": 15
    },
    {
        "key": 366,
        "doc_count": 134
    },
    {
        "key": 367,
        "doc_count": 302
    },
    {
        "key": 368,
        "doc_count": 312
    },
    {
        "key": 369,
        "doc_count": 323
    },
    {
        "key": 370,
        "doc_count": 575
    },
    {
        "key": 371,
        "doc_count": 40
    },
    {
        "key": 372,
        "doc_count": 2135
    }
]
dict_es: dict = {}
for x in buckets:
    # print(f"{x['key']}  {x['doc_count']}")
    dict_es[x['key']] = x['doc_count']

print(dict_es)

dict_mysql: dict = {}

ii = 0
while True:
    # print(f"{mysql_data[ii]}  {mysql_data[ii+1]}")
    key = int(mysql_data[ii])
    val = int(mysql_data[ii + 1])
    dict_mysql[key] = val
    ii = ii + 2
    if ii >= len(mysql_data):
        break

print('------------')
print(dict_mysql)

c_es = set(dict_es.keys())
c_mysql = set(dict_mysql.keys())
print(c_es)
print(c_mysql)

print('-----')
print(c_mysql-c_es)

print(sum(dict_mysql.values()))

filter()

exit(0)

total = 0
for v in dict_mysql.values():
    total = total + v

print(total)