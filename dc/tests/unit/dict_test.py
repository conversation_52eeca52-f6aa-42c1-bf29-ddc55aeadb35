from dc.tests.test_simple_basics import SimpleBasicsTestCase
from jwtools.func import *


class DictTest(SimpleBasicsTestCase):
    name = None

    def setUp(self):
        print('setUp')
        self.name = '1223'
        pass

    def test_default(self):
        """默认值"""
        print('test_cc')
        data = {
            "key": 157,
            "doc_count": 1566,
            "name": "wjh"
        }

        data2 = {
            "key": 259,
            "doc_count": 2435,
            "age": 2443
        }

        print_vf(
            data.setdefault("name2", "wjh"),
        )

    def test_update(self):
        """更新"""
        print('test_cc')
        data = {
            "key": 157,
            "doc_count": 1566,
            "name": "wjh"
        }

        data2 = {
            "key": 259,
            "doc_count": 2435,
            "age": 2443
        }

        print_vf(
            # data.setdefault("name2", "wjh"),
            'data', data,
            'data2', data2,
        )

        data.update(data2)
        print_vf(
            'data', data,
            'data2', data2,
        )
