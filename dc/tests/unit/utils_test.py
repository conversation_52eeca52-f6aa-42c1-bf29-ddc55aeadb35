import datetime
import time
import sys
import os


sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.logging_service import LoggingService
from dc.common.utils import Utils

log_service = LoggingService('zzz.log')


def timefstr(timeStamp_checkpoint):
    #timeStamp_checkpoint = time.time()
    timeArray = time.localtime(timeStamp_checkpoint)
    print(f"timeArray: {timeArray}")
    checkpoint = time.strftime("%Y-%m-%d %H:%M:%S %f", timeArray)
    #print("checkpoint：%s" % checkpoint)
    return checkpoint


if __name__ == "__main__":
    start_time = time.time()
    time.sleep(3.3)
    Utils.exec_time(start_time=start_time, echo=True, logger=log_service)
    exit(0)

    #print(timefstr(time.time()))
    #dt = datetime.datetime.now()
    #print(dt)
    #print(time.time().strftime('%Y %m %d %H %M %S %f'))
    #exit(0)

    zz = datetime.datetime.fromtimestamp(time.time())
    print(zz)
    exit(0)

    time = datetime.datetime.now(time.time()).strftime('%Y %m %d %H %M %S %f')
    print(time)
    exit(0)

    checkpoint = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
    print("checkpoint：%s" % checkpoint)

    exit(0)

    timeStamp_checkpoint = time.time()
    timeArray = time.localtime(timeStamp_checkpoint)
    print(f"timeArray: {timeArray}")
    checkpoint = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
    print("checkpoint：%s" % checkpoint)


    exit(0)

    start_time = time.time()
    time.sleep(3.3)
    Utils.exec_time(start_time=start_time, echo=True)
    #print(Utils.time_ms())

    print(time.time())
    #print(time.time_ns())
