import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

from flask import current_app
from dc.tests.test_basics import BasicsTestCase
from dc.tests.test_one import TestOne


class TestTwo(BasicsTestCase):

    def test_cc(self):
        #with app.app_context():
        """增加数据"""
        print('TestTwo-test_cc')


    def test_cc2(self):
        print('TestTwo-test_cc2')


    def test_cc3(self):
        one = TestOne()
        one.setUp()
        one.test_from_other()
        print('TestOne-test_cc3-----')
