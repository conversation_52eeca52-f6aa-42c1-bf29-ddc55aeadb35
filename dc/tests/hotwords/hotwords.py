import os
import sys
import time
from datetime import datetime

from dc.models.model import DCHotArticleWordsWeight
from dc.services.mysql_service import MySQLService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.zhipu_service import ZhipuService
import dc.services.word_processing
import unittest
from unittest.mock import MagicMock

text = """受美联储加息周期有望结束的乐观情绪提振，欧洲股市连续第四天上涨，创下7月以来的最长连涨纪录。
　　斯托克欧洲600指数收盘上涨1.6%，房地产和汽车股涨幅最大。斯托克600房地产指数创下九个月来最大涨幅，这一负债累累的行业受益于债券收益率的下降。诺和诺德股价给指数带来了最大提振，公司公布的第三季度销售额大幅增长，对其治疗肥胖症和糖尿病的药物需求有增无减。
　　其他个股方面，德国汉莎航空股价大涨，该公司表示有望实现今年和2024年的财务目标。壳牌加快股票回购，股价走高。荷兰国际集团下跌，该公司警告称利息收入可能面临压力。
　　周三美联储连续第二次会议维持利率不变，美联储主席暗示加息可能结束，投资者情绪获得提振。英国央行随后跟进，将基准利率维持在15年高点，行长贝利称，考虑降息太早了。
　　欧洲股市本周表现喜人，10月一度遭遇大跌，导致该指数几近抹去今年的涨幅。投资者仍然关注货币政策，同时分析最新的公司业绩，并继续跟踪以色列-哈马斯战争的发展。
　　“昨天的决定几乎在意料之中。现在的问题是随着美国经济增长放缓，欧洲经济面临衰退，未来几个季度长期债券收益率将下降多快，”Liberum策略师Susana Cruz表示。“虽然我们仍然看到收益率下降带来一些上行空间，但我认为增长担忧，以及我们迄今为止在欧洲看到的糟糕业绩，将在未来六到九个月内拖累股市。"""

# res = dc.services.word_processing.sep_and_del_stop_word(text)

def determine_date_format(date_string):
    # 尝试使用 "%Y-%m-%d" 格式解析日期字符串
    try:
        parsed_date = datetime.strptime(str(date_string), "%Y-%m-%d")
        return parsed_date
    except ValueError:
        pass

    # 尝试使用 "%Y-%m-%d %H:%M:%S" 格式解析日期字符串
    try:
        parsed_date = datetime.strptime(str(date_string), "%Y-%m-%d %H:%M:%S")
        return parsed_date

    except ValueError:
        pass

    # 如果都无法解析，则返回 None 或其他默认值，表示日期格式未知
    return None


def days_between(date_string):
    # 输入的日期字符串
    parsed_date = determine_date_format(date_string)

    # 获取当前时间
    current_datetime = datetime.now()

    # 计算时间差
    time_difference = current_datetime.date() - parsed_date.date()

    # 获取时间差的天数部分
    days_difference = time_difference.days

    return days_difference


def between_hot_time(date_string):
    # 解析日期字符串为datetime对象
    parsed_date = determine_date_format(date_string)
    if parsed_date is None:
        return False  # 日期格式无法解析，不在热门时间内

    # 提取时间部分
    time_part = parsed_date.time()

    # 定义热门时间范围
    hot_start_time = 1  # 凌晨1点
    hot_end_time = 6  # 凌晨6点

    # 判断时间是否在热门时间范围内
    if hot_start_time <= time_part.hour < hot_end_time:
        return True
    else:
        return False

# print(between_hot_time("2023-01-01 02:00:00"))

zhipu = ZhipuService('chatglm_pro')
prompt = "以下是一篇文章，{}。这段文字描述的是与半导体、芯片、集成电路产业链（设计、制造、封测、装备、材料、EDA/IP等）及其重点应用领域（手机、服务器、汽车、IoT等）相关的内容吗？只回答：是、否，不需要其他字体。".format(
    text[:1000])
response = zhipu.invoke([
    {"role": "user", "content": prompt}
])
# 这是张玉写的prompt，先用这个吧

print(response['data']['choices'][0]['content'])

if "否" in response['data']['choices'][0]['content']:
    print("否")
else:
    print("yes")

print(len("""更多数据图表（原数据供下载）、文档可付费加入199IT数据交流群扫下面二维码或点击原文阅读即可加入"""))
# mysql2 = MySQLService(run_model='test')
# session2 = mysql2.create_session()
#
# newWordRecord = DCHotArticleWordsWeight()
# newWordRecord.word = "ai"
# newWordRecord.weight = 1
# newWordRecord.status = 1
# session2.add(newWordRecord)
# session2.commit()
# session2.close()
