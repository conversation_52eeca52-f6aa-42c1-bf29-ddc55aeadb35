# -*- coding:utf-8 -*-
# @Function  : 微信公众号升级
# <AUTHOR>
# @Time      : 2024/9/20
# Version    : 1.0

from urllib.parse import urljoin

import requests
import json

token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJleHAiOjE3Mjc1MDczNzQsImlzcyI6ImthbmFkZWJsaXNzdCIsIm5hbWUiOiJ3b29kd3dhbmciLCJuYmYiOjE3MjY4MTYxNzQsInBlcm1pc3Npb24iOjE4MSwic3ViIjoic21wMW90Z3NnNC5hcGlmb3guY24iLCJkYXlfbGltaXQiOnsiZ2V0Yml6aGlzdG9yeSI6MjAwLCJnZXRiaXppbmZvIjoyMDAsImdldHJlYWRudW0iOjIwMDAsIndhdmVyaWZ5aW5mbyI6MTAwLCJ3ZWJzZWFyY2giOjEwMH19.2YUCticqhPzzMpFPkkyR2RrSTQKT1M0G5_3ecoegT6-6GKr1qQU-hcpJz8xcBzyGRhBA3ib1AzpptfuSSfStRA'

# api_url = 'http://wx.blisst.cn:23230'
api_url = 'http://wx.blisst.cn:22235'

debug = True


def send_request(url, token, data=None, method='GET'):
    """
    发送带有 Bearer 认证的 HTTP 请求，支持 GET 和 POST 方法

    :param url: API 请求的 URL
    :param token: Bearer Token，用于认证
    :param data: 请求体数据，字典格式（仅用于 POST 请求）
    :param method: 请求方法，支持 'GET' 或 'POST'
    :return: 响应的 JSON 数据或错误信息
    """
    # 定义请求头，包括 Authorization 和 Content-Type
    headers = {
        "Authorization": f"Bearer {token}",  # 使用传入的 Bearer Token
        "Content-Type": "application/json"
    }

    print(f"url: {url}")

    try:
        # 根据方法类型发送请求
        if method.upper() == 'POST':
            response = requests.post(url, headers=headers, data=json.dumps(data))
        elif method.upper() == 'GET':
            response = requests.get(url, headers=headers, params=data)
        else:
            return {"error": f"不支持的 HTTP 方法: {method}"}

        # 检查响应状态码
        if response.status_code == 200:
            return response.json()  # 返回响应的 JSON 数据
        else:
            return {
                "error": f"请求失败，状态码: {response.status_code}",
                "message": response.text
            }
    except Exception as e:
        return {"error": f"请求出错: {str(e)}"}


# 调用示例
def start_wx():
    # 启动微信
    url = urljoin(api_url, '/startwx')
    get_result = send_request(url, token)
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))
    return get_result


def clear_wx():
    # 清理微信
    url = urljoin(api_url, '/cleanwx')
    get_result = send_request(url, token)
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def get_qrcode():
    url = urljoin(api_url, '/getqrcode')
    get_result = send_request(url, token)
    if debug:
        print("GET 请求结果:")
        # print(format_json(get_result))
        print(get_result)

    return get_result


def get_user_info():
    # 获取登录信息
    get_data = {"param1": "value1", "param2": "value2"}
    url = urljoin(api_url, '/getusers')
    get_result = send_request(url, token)
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def req_gzh():
    # 获取关注列表
    get_data = {"param1": "value1", "param2": "value2"}
    url = urljoin(api_url, '/getbizs')
    get_result = send_request(url, token, None, 'GET')
    if debug:
        print("GET 请求结果:")
        print(f"count:{len(get_result.get('result', []))}")
        print(format_json(get_result))

    return get_result


def req_gzh_gz(params):
    # 关注公众号
    # get_data = {"param1": "value1", "param2": "value2"}
    url = urljoin(api_url, '/followbiz')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def req_gzh_qxgz(params):
    # 取关公众号
    # get_data = {"param1": "value1", "param2": "value2"}
    url = urljoin(api_url, '/unfollowbiz')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def req_gzh_search(params):
    # 搜索
    url = urljoin(api_url, '/websearch')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def get_search_guide(params={}):
    # 搜索发现
    url = urljoin(api_url, '/searchguide')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def get_search_suggestion(params):
    # 搜索建议
    url = urljoin(api_url, '/searchsuggestion')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def req_gzh_history(params):
    # 获取公众号历史1
    url = urljoin(api_url, '/getbizhistory')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def req_gzh_info(params):
    # 获取公众号信息
    # get_data = {"param1": "value1", "param2": "value2"}
    url = urljoin(api_url, '/getbizinfo')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def gzh_history(params):
    # 获取关注列表
    # get_data = {"param1": "value1", "param2": "value2"}
    url = urljoin(api_url, '/getbizhistory')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def req_gzh_yds(params):
    # 获取阅读数
    # get_data = {"param1": "value1", "param2": "value2"}
    url = urljoin(api_url, '/getreadnum')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))

    return get_result


def get_zhuti_info(params):
    # 获取主体信息
    url = urljoin(api_url, '/waverifyinfo')
    get_result = send_request(url, token, params, 'POST')
    if debug:
        print("GET 请求结果:")
        print(format_json(get_result))
        print(get_result.get('result', ''))

    return get_result


def format_json(json_str):
    # 将 JSON 字符串转换为 Python 对象
    if isinstance(json_str, str):
        parsed_json = json.loads(json_str)
    else:
        parsed_json = json_str

    # 使用 json.dumps 进行格式化输出，指定缩进为 4 个空格
    formatted_json = json.dumps(parsed_json, indent=4, ensure_ascii=False)
    return formatted_json
