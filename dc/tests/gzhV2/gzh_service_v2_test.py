# -*- coding:utf-8 -*-
# @Function  : 公众号升级测试
# <AUTHOR>
# @Time      : 2024/9/24
# Version    : 1.0
import json
import unittest

from dc.services.logging_service import LoggingService

from dc.services.gzh_service import GzhService
from dc.services.gzh_service_v2 import GzhServiceV2

from dc.tests.gzhV2.gzh_utils import *
from dc.tests.test_basics import BasicsTestCase


class GzhServiceV2Test(BasicsTestCase):

    gzh_service: GzhService = None
    gzh_service2: GzhServiceV2 = None
    logger = LoggingService(logfile='gzh_service_v2_test.log')
    logger2 = LoggingService(logfile='gzh_service_v2_test2.log', formatter='string')

    ghid = 'gh_b36f44f9c048'
    wx_id = ''
    base_url = 'http://wx.blisst.cn:22235'

    def setUp(self):
        super().setUp()
        self.gzh_service = GzhService()
        self.gzh_service2 = GzhServiceV2()

    def test_follow_gzh_with_wechat(self):
        # 用某个微信号关注公众号
        result = GzhServiceV2.follow_gzh_with_wechat(wx_id=self.wx_id, gzh_id=self.ghid)
        self.assertStatus(result)

    def test_gzh_article_count(self):
        # 更新公众号文章数量
        result = GzhServiceV2.gzh_article_count(self.ghid)
        self.assertIsInstance(result, int)

    def test_init_gzh_pull(self):
        # 重新拉取公众号文章
        result = GzhServiceV2.init_gzh_pull(self.ghid)
        self.assertTrue(result)

    def test_unfollow_gzh(self):
        # 取消关注
        result = GzhServiceV2.unfollow_gzh(wx_id=self.wx_id, gzh_id=self.ghid)
        self.assertStatus(result)

    def test_req_gzh_search(self):
        # 搜索公众号
        result = GzhServiceV2.req_gzh_search(wx_id=self.wx_id, keyword="中国日报")
        self.assertStatus(result)

    def test_req_gzh_info(self):
        # 公众号详情
        result = GzhServiceV2.req_gzh_info(wx_id=self.wx_id, gzh_id=self.ghid)
        self.assertStatus(result)

    def test_get_req_login(self):
        # 初始化获取微信详情
        result = GzhServiceV2.get_req_login(wx_id=self.wx_id)
        self.assertStatus(result)

    def test_check_wechat_by_config(self):
        # 根据配置检查相应的服务器上登录的微信 ,更新或插入微信的当前数据
        result = GzhServiceV2.check_wechat_by_config()
        self.assertStatus(result)

    def test_get_machine_list(self):
        # 获取所有的机器
        result = GzhServiceV2.get_machine_list()
        self.assertStatus(result)

    def test_start_wechat(self):
        # 开启一个微信
        result = GzhServiceV2.start_wechat(self.base_url)
        self.assertStatus(result)

    def test_get_qr_code(self):
        # 获取登录的二维码
        result = GzhServiceV2.get_qr_code(self.base_url)
        self.assertStatus(result)

    def test_wechat_push(self):
        # 公众号推送
        parmas = [
            {
                "biz": "MzAxNTAxMjYyNQ==",
                "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/biaibludZ7TZQdWDDYU44E6tMQA8kukfVt9hM2UA1MuCP80V9Ee8pbAK5HUTdFFOwicjOQ47IAtmTvhrWtjKZLibicg/640?wxtype=jpeg&wxfrom=0",
                "digest": "",
                "ghid": "gh_fb8555e17118",
                "insert_time": "2024-09-29 15:53:52",
                "name": "山西大学就业",
                "play_url": "",
                "pub_time": "2024-09-29 15:50:02",
                "title": "国家开发银行2025年校园招聘",
                "url": "http://mp.weixin.qq.com/s?__biz=MzAxNTAxMjYyNQ==&mid=2650815651&idx=1&sn=b2c510901ecf72b3a30839ff513cabda&chksm=81657ec15a825e62b645ba07fa3f5a467983180369a5451a73d3c4342f09bc3cdf7d7a9bcc9a&scene=0&xtrack=1#rd",
                "wxid": "wxid_b5kio3je1gja12"
            },
            {
                "biz": "MzAxNTAxMjYyNQ==",
                "cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/biaibludZ7TZQdWDDYU44E6tMQA8kukfVtbdwd7cJ7QVfoYPCQW4P5UsKwM3Z8BHCOnFticQTdzWlqarDomDZ0G8A/300?wxtype=jpeg&wxfrom=0",
                "digest": "",
                "ghid": "gh_fb8555e17118",
                "insert_time": "2024-09-29 15:53:52",
                "name": "山西大学就业",
                "play_url": "",
                "pub_time": "2024-09-29 15:50:02",
                "title": "海尔智家2025届中国区市场校园招聘正式启动",
                "url": "http://mp.weixin.qq.com/s?__biz=MzAxNTAxMjYyNQ==&mid=2650815651&idx=2&sn=2affe470751fab75700ddd28382e9b50&chksm=81334d47a9c32bcb6fd9051b3dfafdb99031d65f20a7f8ae66ace55569cbe786162eb2d11ed4&scene=0&xtrack=1#rd",
                "wxid": "wxid_b5kio3je1gja12"
            }
        ]
        result = GzhServiceV2.wechat_push(parmas)
        self.assertStatus(result)

    def test_search_and_follow(self):
        # 搜索并关注
        result = GzhServiceV2.search_and_follow(name='中国日报', tag='报刊', wxid=self.wx_id)
        self.assertStatus(result)


    def assertStatus(self, result, code=200):
        self.assertEqual(result.get('status', 0), code)
