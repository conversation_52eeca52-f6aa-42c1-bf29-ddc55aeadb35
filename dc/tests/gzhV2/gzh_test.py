"""
微信公众号升级
2024-09-23
"""
import time

from dc.tests.gzhV2.gzh_utils import *


# clear_wx()
# time.sleep(5)
#
# start_wx()
# time.sleep(10)
#
# get_qrcode()
# exit(0)

# user_info = get_user_info()
# exit(0)

user_info = {
    "result": [
        {
            "login": 1,
            "nickname": "bin",
            "wxh": "wxid_q2a6m6mwzwyd22",
            "wxid": "wxid_q2a6m6mwzwyd22",
            "wxpid": 11508
        }
    ],
    "status": 200,
    "user": "kanadeblisst15"
}

# req_gzh()
# time.sleep(3)

# req_gzh_gz({'ghid': 'gh_b36f44f9c048'})
# time.sleep(5)
# exit(0)

# req_gzh_qxgz({'ghid': 'gh_b0d4767385f4'})
# time.sleep(5)

# gzh_guanzhu_list()
# exit(0)
# time.sleep(2)

# req_gzh_info({'ghid': 'gh_3707d9541c3f'})
# exit(0)

# req_gzh_history({'ghid': 'gh_3707d9541c3f'})
# exit(0)


# get_zhuti_info({'url': "https://mp.weixin.qq.com/wxawap/waverifyinfo?action=get&appid=wx8ca72ead29d3a293"})


# get_search_guide({})

get_search_guide({'keyword': '芯片'})
exit(0)

# req_gzh_yds({'url': "http://mp.weixin.qq.com/s?__biz=MzAxNzE1OTA1MA==&mid=2651832066&idx=1&sn=cb2c11281772cf52bb1cda9551498fde&chksm=8012d9e7b76550f19bef46f474aba65df563aac9bd8db070b6fde9305f11a317a238cb681d50&scene=199&sessionid=1726828904#rd"})
# exit(0)


# get_websearch()
# exit(0)

# POST 请求示例
# post_data = {"key1": "value1", "key2": "value2"}
# post_result = send_request(url, token, data=post_data, method='POST')
# print("POST 请求结果:", post_result)
