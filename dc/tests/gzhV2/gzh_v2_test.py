# -*- coding:utf-8 -*-
# @Function  : 公众号升级测试
# <AUTHOR>
# @Time      : 2024/9/24
# Version    : 1.0

import unittest

from dc.tests.gzhV2.gzh_utils import *
from dc.tests.test_basics import BasicsTestCase


class GzhV2Test(BasicsTestCase):

    ghid = 'gh_b36f44f9c048'

    # ----------------- 微信号 --------------

    @unittest.skip("暂时跳过")
    def test_startwx(self):
        # 启动微信
        result = start_wx()
        self.assertStatus(result)

    @unittest.skip("暂时跳过")
    def test_cleanwx(self):
        # 清理微信
        result = clear_wx()
        self.assertStatus(result)

    @unittest.skip("暂时跳过")
    def test_getqrcode(self):
        # 获取登录二维码
        result = get_qrcode()
        self.assertStatus(result)

    def test_getusers(self):
        # 获取登录信息
        result = get_user_info()
        self.assertStatus(result)

    # ----------------- 公众号 --------------

    def test_followbiz(self):
        # 关注公众号
        result = req_gzh_gz({'ghid': self.ghid})
        self.assertStatus(result)

    def test_unfollowbiz(self):
        # 取关公众号
        result = req_gzh_qxgz({'ghid': self.ghid})
        self.assertStatus(result)

    def test_getbizs(self):
        # 获取关注列表
        result = req_gzh()
        self.assertStatus(result)

    def test_getbizinfo(self):
        # 获取公众号信息
        result = req_gzh_info({'ghid': self.ghid})
        self.assertStatus(result)

    # ----------------- 公众号文章 --------------

    def test_getbizhistory(self):
        # 获取公众号历史1
        result = req_gzh_history({'ghid': self.ghid})
        self.assertStatus(result)

    def test_getreadnum(self):
        # 获取阅读数
        result = req_gzh_yds({'url': "http://mp.weixin.qq.com/s?__biz=MzAxNzE1OTA1MA==&mid=2651832066&idx=1&sn=cb2c11281772cf52bb1cda9551498fde&chksm=8012d9e7b76550f19bef46f474aba65df563aac9bd8db070b6fde9305f11a317a238cb681d50&scene=199&sessionid=1726828904#rd"})
        self.assertStatus(result)

    # ----------------- 公众号主体 --------------

    def test_waverifyinfo(self):
        # 获取主体信息
        result = get_zhuti_info({'url': "https://mp.weixin.qq.com/wxawap/waverifyinfo?action=get&appid=wx8ca72ead29d3a293"})
        self.assertStatus(result)

    # ----------------- 搜一搜 --------------

    def test_websearch(self):
        # 搜索
        params = {
            "keyword": "创芯海门",
            "BusinessType": "1"
        }
        result = req_gzh_search(params)
        self.assertStatus(result)

    def test_searchguide(self):
        # 搜索发现
        result = get_search_guide()
        self.assertStatus(result)

    def test_searchsuggestion(self):
        # 搜索建议
        result = get_search_suggestion({'keyword': '芯片'})
        self.assertStatus(result)

    def assertStatus(self, result, code=200):
        self.assertEqual(result.get('status', 0), code)
