"""
微信公众号升级
2024-09-23
"""
import datetime
import time

from dc.tests.gzhV2.gzh_utils import *

next_params = None
page = 1
while True:
    params = {
        # "keyword": "创芯海门",
        "keyword": "日报",
        "BusinessType": "1"
    }
    if next_params:
        params['next_params'] = next_params

    print(f"==================== page: {page} ====================")
    print(f"params: {params}")
    result: dict = req_gzh_search(params)
    data = result.get('result', {}).get('result', {}).get('data', [])
    offset = result.get('result', {}).get('result', {}).get('offset', 0)
    pageNumber = result.get('result', {}).get('result', {}).get('pageNumber', 0)
    page_size = offset/pageNumber

    item_count = 0
    for item in data:  # type: dict
        for box in item.get('subBoxes', []):  # type: dict
            for item in box.get('items'):  # type: dict
                # print(item.get('jumpInfo'))
                item_count += 1
                print(f'------------- {item_count} -------------')
                print(item.get('jumpInfo')['userName'])
                print(item.get('jumpInfo')['nickName'])
                print(item.get('jumpInfo')['signature'])
                print(item.get('jumpInfo')['aliasName'])

    next_params = result.get('result', {}).get('next_params', {})
    print(next_params)

    time.sleep(3)

    if item_count < page_size:
        print(f'item_count: {item_count} < page_size: {page_size} break')
        break

    page += 1
    if page > 5:
        print('page > 5 break')
        break
