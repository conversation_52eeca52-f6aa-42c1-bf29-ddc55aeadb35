"""
微信公众号升级
2024-09-23
"""

import datetime
import time

from dc.tests.gzhV2.gzh_utils import *

user_info = {
    "result": [
        {
            "login": 1,
            "nickname": "bin",
            "wxh": "wxid_q2a6m6mwzwyd22",
            "wxid": "wxid_q2a6m6mwzwyd22",
            "wxpid": 11508
        }
    ],
    "status": 200,
    "user": "kanadeblisst15"
}

pagingInfo = None
page = 1
while True:
    params = {'ghid': 'gh_b36f44f9c048'}
    if pagingInfo:
        params['next_offset'] = pagingInfo.get('Offset')

    print(f"==================== page: {page} ====================")
    print(f"params: {params}")
    result: dict = req_gzh_history(params)
    msgs = result.get('result', {}).get('MsgList', {})
    for msg in msgs.get('Msg', []):  # type: dict
        print('-----------------------------------------------------------------')
        infos = msg.get('AppMsg', {}).get('DetailInfo', [])
        base_info = msg.get('AppMsg', {}).get('BaseInfo', {})
        CreateTime = int(base_info["CreateTime"])
        create_time = datetime.datetime.fromtimestamp(CreateTime).strftime("%Y-%m-%d %H:%M:%S")
        print(f'CreateTime: {CreateTime} {create_time}')
        for info in infos:
            print(info.get('Title'))

    pagingInfo = msgs.get('PagingInfo', {})
    print(pagingInfo)

    time.sleep(3)

    if pagingInfo.get('IsEnd') == 0 and pagingInfo.get('Offset'):
        page += 1
        if page > 50:
            print('page > 5 break')
            break
        continue
    else:
        print('break')
        break

exit(0)

# video
video_list = result.get('result', {}).get('VideoList', {})
for msg in video_list.get('Msg', []):  # type: dict
    print('-----------------------------------------------------------------')
    infos = msg.get('AppMsg', {}).get('DetailInfo', [])
    for info in infos:
        print(info.get('Title'))

pagingInfo = video_list.get('PagingInfo', {})
print(pagingInfo)

exit(0)

# POST 请求示例
# post_data = {"key1": "value1", "key2": "value2"}
# post_result = send_request(url, token, data=post_data, method='POST')
# print("POST 请求结果:", post_result)
