# -*- coding:utf-8 -*-
# @Function  : 公众号升级测试
# <AUTHOR>
# @Time      : 2024/9/24
# Version    : 1.0

import unittest

from dc.services.gzh_service_v2 import GzhServiceV2
from dc.services.logging_service import LoggingService

from dc.services.gzh_service import GzhService

from dc.tests.gzhV2.gzh_utils import *
from dc.tests.test_basics import BasicsTestCase


class GzhServiceTest(BasicsTestCase):

    gzh_service: GzhService = None
    gzh_service2: GzhServiceV2 = None
    logger = LoggingService(logfile='gzh_service_test.log')
    logger2 = LoggingService(logfile='gzh_service_test2.log', formatter='string')

    ghid = 'gh_b36f44f9c048'
    wx_id = ''

    def setUp(self):
        super().setUp()
        self.gzh_service = GzhService()
        self.gzh_service2 = GzhServiceV2()

    def test_follow_gzh_with_wechat(self):
        # 用某个微信号关注公众号
        result = GzhService.follow_gzh_with_wechat(wx_id=self.wx_id, gzh_id=self.ghid)
        self.assertStatus(result)

    def assertStatus(self, result, code=200):
        self.assertEqual(result.get('status', 0), code)
