# -*- coding:utf-8 -*-
# @Function  : 公众号升级测试
# <AUTHOR>
# @Time      : 2024/9/24
# Version    : 1.0

import unittest

from dc.common.utils import Utils

from dc.models.model import DCGzhWechat, DCGzhArticle

from dc.services.gzh_service import GzhService
from dc.services.logging_service import LoggingService
from dc.services.weixin_service import WeixinService
from dc.tests.gzhV2.gzh_utils import *
from dc.tests.test_basics import BasicsTestCase


class GzhV2Test(BasicsTestCase):
    ghid = 'gh_b36f44f9c048'

    weixin_service: WeixinService = None
    gzh_service: GzhService = None
    logger = LoggingService(logfile='weixin_service_test.log')
    logger2 = LoggingService(logfile='article.log', formatter='string')
    gzhid: str = 'gh_e3222cc0c7b9'

    def setUp(self):
        super().setUp()
        self.weixin_service = WeixinService(wx_id='', logger=self.logger)
        self.gzh_service = GzhService()

    # ----------------- 微信号 --------------

    def test_req_login(self):
        # 获取已登录的微信信息
        result = self.weixin_service.req_login({})
        print(result)
        self.assertStatus(result)

    def test_req_gzh(self):
        # 获取已关注公众号列表
        result = self.weixin_service.req_gzh({})
        print(result)
        self.assertStatus(result)

    def test_req_gzh_search(self):
        # 搜索公众号
        result = self.weixin_service.req_gzh_search({'keyword': '中国日报'})
        print(result)
        self.assertStatus(result)

    def test_req_gzh_info(self):
        # 获取公众号信息
        result = self.weixin_service.req_gzh_info({'gzhid': 'gh_e3222cc0c7b9'})
        print(result)
        self.assertStatus(result)

    def test_req_gzh_gz(self):
        # 关注公众号
        result = self.weixin_service.req_gzh_gz({'gzhid': 'gh_e3222cc0c7b9'})
        print(result)
        self.assertStatus(result)

    def test_req_gzh_qxgz(self):
        # 取消关注公众号
        result = self.weixin_service.req_gzh_qxgz({'gzhid': 'gh_e3222cc0c7b9'})
        print(result)
        self.assertStatus(result)

    def test_req_gzh_yds(self):
        # 获取阅读数
        result = self.weixin_service.req_gzh_yds({'url': "http://mp.weixin.qq.com/s?__biz=MzAxNzE1OTA1MA==&mid=2651832066&idx=1&sn=cb2c11281772cf52bb1cda9551498fde&chksm=8012d9e7b76550f19bef46f474aba65df563aac9bd8db070b6fde9305f11a317a238cb681d50&scene=199&sessionid=1726828904#rd"})
        print(result)
        self.assertStatus(result)

    def test_req_gzh_history(self):
        # 获取公众号历史1
        time_stop = Utils.time_days(10, only_day=True)
        time_stop = Utils.str2timestamp(time_stop)
        prefix = '102--'

        wechat: DCGzhWechat = self.weixin_service.get_wechat_by_gzh(None)
        result = self.weixin_service.req_gzh_history({
            "base_url": wechat.machineIp if wechat else self.weixin_service.wechat.machineIp,
            "wxpid": wechat.machinePid if wechat else self.weixin_service.wechat.machinePid,
            "wxid": wechat.wxId if wechat else self.weixin_service.wechat.wxId,
            "gzhid": self.gzhid,
            "time_stop": time_stop,
            "list_sleep": 2,
            "desc": "获取历史文章",
            "prefix": prefix
        }, callback=self.show_article)

        print(result)
        # self.assertStatus(result)

    def show_article(self, **kwargs):
        """
        进行文章信息处理
        :param kwargs: 参数配置
        :return: None
        """
        print('------show_article----')
        logger = self.logger
        wx = self.weixin_service

        ainfo = kwargs.get('article')
        account_info: dict = kwargs.get('account')
        base_info: dict = kwargs.get('base')
        params: dict = kwargs.get('params')
        gzhid = params.get('gzhid')
        wxid = params.get('wxid')
        wxpid = params.get('wxpid')
        base_url = params.get('base_url')
        logger.info(ainfo)

        url_md5 = wx.calc_url_md5(ainfo['ContentUrl'])
        url_md5_old = wx.calc_url_md5_old(ainfo['ContentUrl'])
        logger.info(f"url_md5: {url_md5} url_md5_old: {url_md5_old} url: {ainfo['ContentUrl']}")

        article = DCGzhArticle()
        article.url = ainfo['ContentUrl']
        article.url_md5 = url_md5
        article.cover = ainfo['CoverImgUrl']
        article.title = ainfo['Title']
        article.gzhId = account_info['UserName']
        article.wxId = wxid
        article.gzhId = gzhid
        CreateTime = base_info["CreateTime"]
        UpdateTime = base_info["UpdateTime"]
        article.wx_create_time = Utils.timestamp2str(CreateTime)
        article.wx_pub_time = Utils.timestamp2str(UpdateTime)
        article.status = 1
        article.createAt = Utils.showDateTime()

        self.logger2.info({
            'title': article.title,
            'wx_create_time': article.wx_create_time,
            'wx_pub_time': article.wx_pub_time,
            'url': article.url,
        })

        logger.info(f"插入文章：{article.to_dict()}")

    def test_getusers(self):
        # 获取登录信息
        result = get_user_info()
        self.assertStatus(result)

    # ----------------- 公众号 --------------

    def test_followbiz(self):
        # 关注公众号
        result = req_gzh_gz({'ghid': self.ghid})
        self.assertStatus(result)

    def test_unfollowbiz(self):
        # 取关公众号
        result = req_gzh_qxgz({'ghid': self.ghid})
        self.assertStatus(result)

    def test_getbizs(self):
        # 获取关注列表
        result = req_gzh()
        self.assertStatus(result)

    def test_getbizinfo(self):
        # 获取公众号信息
        result = req_gzh_info({'ghid': self.ghid})
        self.assertStatus(result)

    # ----------------- 公众号文章 --------------

    def test_getbizhistory(self):
        # 获取公众号历史1
        result = req_gzh_history({'ghid': self.ghid})
        self.assertStatus(result)

    def test_getreadnum(self):
        # 获取阅读数
        result = req_gzh_yds({
            'url': "http://mp.weixin.qq.com/s?__biz=MzAxNzE1OTA1MA==&mid=2651832066&idx=1&sn=cb2c11281772cf52bb1cda9551498fde&chksm=8012d9e7b76550f19bef46f474aba65df563aac9bd8db070b6fde9305f11a317a238cb681d50&scene=199&sessionid=1726828904#rd"})
        self.assertStatus(result)

    # ----------------- 公众号主体 --------------

    def test_waverifyinfo(self):
        # 获取主体信息
        result = get_zhuti_info({'url': "https://mp.weixin.qq.com/wxawap/waverifyinfo?action=get&appid=wx8ca72ead29d3a293"})
        self.assertStatus(result)

    # ----------------- 搜一搜 --------------

    def test_websearch(self):
        # 搜索
        params = {
            "keyword": "创芯海门",
            "BusinessType": "1"
        }
        result = req_gzh_search(params)
        self.assertStatus(result)

    def test_searchguide(self):
        # 搜索发现
        result = get_search_guide()
        self.assertStatus(result)

    def test_searchsuggestion(self):
        # 搜索建议
        result = get_search_suggestion({'keyword': '芯片'})
        self.assertStatus(result)

    def assertStatus(self, result, code=200):
        self.assertEqual(result.get('status', 0), code)
