# -*- coding:utf-8 -*-
# @Function  : 公众号升级测试
# <AUTHOR>
# @Time      : 2024/9/24
# Version    : 1.0

import unittest

from dc.common.utils import Utils

from dc.models.model import DCGzhWechat, DCGzhArticle

from dc.services.gzh_service_v2 import GzhServiceV2
from dc.services.logging_service import LoggingService
from dc.services.weixin_service_v2 import WeixinServiceV2
from dc.tests.gzhV2.gzh_utils import *
from dc.tests.test_basics import BasicsTestCase


class GzhV2Test(BasicsTestCase):
    ghid = 'gh_b36f44f9c048'

    weixin_service: WeixinServiceV2 = None
    gzh_service: GzhServiceV2 = None
    logger = LoggingService(logfile='weixin_service_test.log')
    logger2 = LoggingService(logfile='article.log', formatter='string')

    gzhid: str = 'gh_e3222cc0c7b9'

    def setUp(self):
        super().setUp()
        self.weixin_service = WeixinServiceV2(wx_id='', logger=self.logger)
        self.gzh_service = GzhServiceV2()

    # ----------------- 微信号 --------------

    def test_req_login(self):
        # 获取已登录的微信信息
        params = {
            'base_url': 'http://wx.blisst.cn:22235',
            'wxpid': '11508',
        }
        result = self.weixin_service.req_login(params)
        print(result)
        self.assertStatus(result)

    def test_req_gzh(self):
        # 获取已关注公众号列表, 少了一层
        params = {
            'base_url': 'http://wx.blisst.cn:22235',
            'wxpid': '11508',
        }
        result = self.weixin_service.req_gzh(params)
        print(result)
        self.assertStatus(result)

    def test_req_gzh_search(self):
        # 搜索公众号
        params = {
            'base_url': 'http://wx.blisst.cn:22235',
            'wxpid': '11508',
            'keyword': '中国日报'
        }
        result = self.weixin_service.req_gzh_search(params)
        print(result)
        self.assertStatus(result)

    def test_req_gzh_info(self):
        # 获取公众号信息
        params = {
            'base_url': 'http://wx.blisst.cn:22235',
            'gzhid': 'gh_e3222cc0c7b9'
        }
        result = self.weixin_service.req_gzh_info(params)
        print(result)
        self.assertStatus(result)

    def test_req_gzh_gz(self):
        # 关注公众号
        params = {
            'base_url': 'http://wx.blisst.cn:22235',
            'gzhid': 'gh_e3222cc0c7b9'
        }
        result = self.weixin_service.req_gzh_gz(params)
        print(result)
        self.assertStatus(result)

    def test_req_gzh_qxgz(self):
        # 取消关注公众号
        params = {
            'base_url': 'http://wx.blisst.cn:22235',
            'gzhid': 'gh_e3222cc0c7b9'
        }
        result = self.weixin_service.req_gzh_qxgz(params)
        print(result)
        self.assertStatus(result)

    def test_req_gzh_yds(self):
        # 获取阅读数
        params = {
            'base_url': 'http://wx.blisst.cn:22235',
            'url': "http://mp.weixin.qq.com/s?__biz=MzAxNzE1OTA1MA==&mid=2651832066&idx=1&sn=cb2c11281772cf52bb1cda9551498fde&chksm=8012d9e7b76550f19bef46f474aba65df563aac9bd8db070b6fde9305f11a317a238cb681d50&scene=199&sessionid=1726828904#rd"
        }
        result = self.weixin_service.req_gzh_yds(params)
        print(result)
        self.assertStatus(result)

    def test_req_gzh_history(self):
        # 获取公众号历史1
        time_stop = Utils.time_days(10, only_day=True)
        time_stop = Utils.str2timestamp(time_stop)
        prefix = '101--'

        wechat: DCGzhWechat = self.weixin_service.get_wechat_by_gzh(None)
        result = self.weixin_service.req_gzh_history({
            # "base_url": wechat.machineIp if wechat else self.weixin_service.wechat.machineIp,
            "base_url": 'http://wx.blisst.cn:22235',
            "wxpid": wechat.machinePid if wechat else self.weixin_service.wechat.machinePid,
            "wxid": wechat.wxId if wechat else self.weixin_service.wechat.wxId,
            "gzhid": self.gzhid,
            "time_stop": time_stop,
            "list_sleep": 2,
            "desc": "获取历史文章",
            "prefix": prefix
        }, callback=self.show_article)

        print(result)
        # self.assertStatus(result)

    def show_article(self, **kwargs):
        """
        进行文章信息处理
        :param kwargs: 参数配置
        :return: None
        """
        print('------show_article----')
        logger = self.logger
        wx = self.weixin_service


        ainfo = kwargs.get('article')
        account_info: dict = kwargs.get('account')
        base_info: dict = kwargs.get('base')
        params: dict = kwargs.get('params')
        gzhid = params.get('gzhid')
        wxid = params.get('wxid')
        wxpid = params.get('wxpid')
        base_url = params.get('base_url')
        logger.info(ainfo)

        url_md5 = wx.calc_url_md5(ainfo['ContentUrl'])
        url_md5_old = wx.calc_url_md5_old(ainfo['ContentUrl'])
        logger.info(f"url_md5: {url_md5} url_md5_old: {url_md5_old} url: {ainfo['ContentUrl']}")

        article = DCGzhArticle()
        article.url = ainfo['ContentUrl']
        article.url_md5 = url_md5
        article.cover = ainfo['CoverImgUrl']
        article.title = ainfo['Title']
        article.gzhId = account_info['UserName']
        article.wxId = wxid
        article.gzhId = gzhid
        CreateTime = base_info["CreateTime"]
        UpdateTime = base_info["UpdateTime"]
        article.wx_create_time = Utils.timestamp2str(CreateTime)
        article.wx_pub_time = Utils.timestamp2str(UpdateTime)
        article.status = 1
        article.createAt = Utils.showDateTime()

        self.logger2.info({
            'title': article.title,
            'wx_create_time': article.wx_create_time,
            'wx_pub_time': article.wx_pub_time,
            'url': article.url ,
        })

        logger.info(f"插入文章：{article.to_dict()}")

    @unittest.skip("暂时跳过")
    def test_req_start_wechat(self):
        # 启动微信 与二维码一起返回
        params = {
            'base_url': 'http://wx.blisst.cn:22235',
        }
        result = self.weixin_service.req_start_wechat(params)
        print(result)
        self.assertStatus(result)

    # ----------------- 公众号主体 --------------

    @unittest.skip("暂时跳过")
    def test_waverifyinfo(self):
        # 获取主体信息
        result = get_zhuti_info({'url': "https://mp.weixin.qq.com/wxawap/waverifyinfo?action=get&appid=wx8ca72ead29d3a293"})
        self.assertStatus(result)

    def assertStatus(self, result, code=200):
        self.assertEqual(result.get('status', 0), code)
