# -*- coding:utf-8 -*-
# @Function  : 数据采集-文章分析服务
# <AUTHOR> lws
# @Time      : 2023-07-17
# Version    : 1.0

from selenium.webdriver.common.by import By

from dc.common.webdriver_util import get_remote_webdriver, get_webdriver
from dc.tests.browser import Browser

webdriver_url="192.168.1.181:4444"
driver = get_webdriver(Browser.Chrome, options={
    # 'arguments': ['--proxy-server=http://f753.kdltps.com:15818']
})

url="http://mp.weixin.qq.com/s?__biz=MjM5OTcwNDgyMg==&mid=2663884675&idx=1&sn=5f1ac45bce55c84db058b78f43ca8bb7&chksm=bdfa6e701833c7ebedabc148700344c627a7eda39df81a23640d9bf588a509cc59a325b8f2ba&scene=0&xtrack=1#rd"

driver.get(url)
html_content = driver.page_source
content_xpath='//div[@id="js_content" or @id="js_share_notice" or @class="wx_video_play_opr" or @class="rich_media_content"]'
content = driver.find_element(By.XPATH, content_xpath).text
# content = format_content(content)
print(content)