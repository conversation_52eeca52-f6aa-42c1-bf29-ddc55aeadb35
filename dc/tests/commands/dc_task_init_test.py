# -*- coding:utf-8 -*-
# @Function  : 数据采集-初始化任务-手动加入主队列
# <AUTHOR>
# @Time      : 2023-07-16
# Version    : 1.0

import time
import sys
import os
import json
import traceback
from datetime import datetime

import psutil
from sqlalchemy.orm import load_only

from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService
from dc.models.model import DCSiteMainTask, DCSiteList, DCSiteKeyWords, DCSiteListRule, DCSiteListItem, DCSiteKeywordRelation

site_list_id = 2241


def process_site_list_task(site_list_id):

    mysql = MySQLService()
    session = mysql.create_session()

    redisService = RedisService('redis')
    client1 = redisService.get_client()

    logger = LoggingService('dc_task_init.log')

    redis_prefix = 'laravel_database_'
    redis_task_key = f'{redis_prefix}dc-main-task'
    site_list_hash = f'{redis_prefix}dc-site-list-hash'

    # 手动加入主队列
    # site_list_id = 2241

    # 创建主任务
    def push_task():
        # 获取当前时间
        curr_time = time.time()
        curr_time = int(curr_time)

        # 手动加入主队列
        # site_list_id = 2241

        fields = ['id', 'detailUrl', 'sourceFrequencyType', 'sourceFrequency', 'siteUrl', 'lastExecTime', 'firstExecTime',
                  'sourceMark', 'nextExecTime', 'sourceCategory', 'sourceName', 'extra', 'siteName', 'isQuickShort',
                  'isKeyWord', 'detailDcMethod', 'sourceName', 'isHotModule', 'tags']

        ret = session.query(DCSiteList).options(load_only(*fields)).filter(DCSiteList.status == 1, DCSiteList.isUsed == 1, DCSiteList.id == site_list_id).all()
        if not ret:
            logger.info('当前执行未获取到数据源')
            exit()

        for info in ret:
            site_info = info.to_dict()

            if not site_info['id']:
                logger.info('存在id为空的异常数据')
                continue

            if not site_info['firstExecTime']:
                logger.info(f"数据源【{site_info['id']}】不存在首次执行时间")
                continue

            deal_main_task(site_info)

    def deal_time(fre_type, frequency) -> int:
        """
        处理间隔时间（秒）
        :param fre_type:
        :param frequency:
        :return:
        """
        diff_time = 0
        if fre_type == 1:
            diff_time = frequency * 60
        elif fre_type == 2:
            diff_time = frequency * 3600
        elif fre_type == 3:
            diff_time = frequency * 86400

        return diff_time

    # 处理主任务
    def deal_main_task(info):
        if 'id' not in info:
            return False

        # 获取当前提取规则
        rule_item = get_rule(info['id'])
        if rule_item == '':
            return False

        analysisMethod = 0
        if info['extra']:
            try:
                dc_extra = json.loads(info['extra'])
                if 'analysisMethod' in dc_extra:
                    analysisMethod = dc_extra['analysisMethod']
            except:
                pass

        redis_arr = {'sourceCategory': info['sourceCategory'], 'siteName': info['siteName'],
                     'analysisMethod': analysisMethod, 'isQuickShort': info['isQuickShort'], 'isKeyWord': info['isKeyWord'],
                     'detailDcMethod': info['detailDcMethod'], 'sourceName': info['sourceName'], 'ruleItem': rule_item,
                     'isHotModule': info['isHotModule'], 'tags': info['tags']}
        client1.hset(site_list_hash, info['id'], json.dumps(redis_arr))
        logger.info(f"redis hset: {site_list_hash} {info['id']} content: {json.dumps(redis_arr)}")

        # res = client1.hmget(site_list_hash, site_info['id'])
        # print(json.loads(res[0]))
        if info['isKeyWord'] == 2:  # 关键词搜索
            add_main_task(info, '')
            up_site(info['id'])
        else:
            # 获取关键词，循环插入数据
            keywordRelations = session.query(DCSiteKeywordRelation).filter(DCSiteKeywordRelation.siteId == info['id']).all()
            if not keywordRelations:
                keyWords = session.query(DCSiteKeyWords).filter(DCSiteKeyWords.status == 1).all()
            else:
                keywordIds = []
                for relation in keywordRelations:
                    relationArr = relation.to_dict()
                    keywordIds.append(relationArr['keywordId'])
                keyWords = session.query(DCSiteKeyWords).filter(DCSiteKeyWords.status == 1, DCSiteKeyWords.id.in_(keywordIds)).all()

            for wordsObj in keyWords:
                words = wordsObj.to_dict()
                add_main_task(info, words['keyword'])

            # up_site(info['id'])

    # 写主任务
    def add_main_task(info, keyWord):
        try:
            task = DCSiteMainTask(siteListId=info['id'], listUrl=info['detailUrl'], url=info['siteUrl'], keyWord=keyWord, httpStatusCode=0)
            session.add(task)
            session.commit()

            # 插入成功，写入redis
            # client1.lpush(redis_task_key, task.id)
            client1.rpush(redis_task_key, task.id)
            logger.info(f"数据源id为【{info['id']}】-关键词为【{keyWord}】的数据写入成功，主任务id【{task.id}】")
            logger.info(f"redis lpush: {redis_task_key} content: {task.id}")

        except Exception as ex:
            logger.error(f"数据源id为【{info['id']}】-关键词为【{keyWord}】的数据写入失败，失败原因：{str(ex)}")

    # 更新最后一次执行时间
    def up_site(site_id):
        return True

        last_exec_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        up_sql = f"update DC_SiteList set lastExecTime = '%s' where id = {site_id}" % last_exec_date
        try:
            session.execute(up_sql)
            session.commit()
            logger.info(f"数据源id为【{site_id}】的数据更新最后一次执行时间成功")
        except Exception as ex:
            logger.error(f"数据源id为【{site_id}】的数据更新最后一次执行时间失败，失败原因：{str(ex)}")

    # 获取数据源对应规则
    def get_rule(site_id):
        rule_res = session.query(DCSiteListRule).filter(DCSiteListRule.siteListId == site_id).all()
        if not rule_res:
            logger.info(f'数据源id【{site_id}】无对应规则，初始化任务失败')
            return ''

        rule_ids = []
        for rule_info in rule_res:
            rule = rule_info.to_dict()
            rule_ids.append(rule['id'])

        item_res = session.query(DCSiteListItem).filter(DCSiteListItem.siteRuleId.in_(rule_ids),
                                                        DCSiteListItem.status == 1).all()
        if not item_res:
            logger.info(f'数据源id【{site_id}】无对应数据项，初始化任务失败')
            return ''

        item_data = {}
        for rule_id in rule_ids:
            item_data[rule_id] = {}

        for item_info in item_res:
            item = item_info.to_dict()
            item_data[item['siteRuleId']][item['id']] = item

        return json.dumps(item_data)

        # item_data = {}
        #
        # for item_info in item_res:
        #     item = item_info.to_dict()
        #     item_data[item['id']] = item
        # print(json.dumps(item_data))
        # exit()
        # return json.dumps(item_data)

    push_task()


process_site_list_task(site_list_id)