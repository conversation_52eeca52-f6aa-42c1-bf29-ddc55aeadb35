import datetime
import sys
import os
import time

import sqlalchemy
from sqlalchemy import distinct, func, text
from sqlalchemy.orm import Query

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.common.utils import Utils
from dc.services.mysql_service import MySQLService
from dc.models.model import DCGzhWechat, DCGzhWechatRelation, DCGzhArticle

mysql = MySQLService()
session = mysql.create_session()
data = session.query(DCGzhArticle).where(text("status=1 and wx_create_time > '2022-10-18 19:00:09'")).order_by(DCGzhArticle.createAt).limit(10).all()

print(data)

exit(0)

query: Query = session.query(distinct(DCGzhWechatRelation.wxId)).all()
print(query)
# xx: sqlalchemy.engine.row.Row
# r: sqlalchemy.engine.row.Row
# pzx = [list(r)[0] for r in query]
# print(pzx)

pp = session.execute("select * from DC_GzhWechat where status=1 and isUsed=1 and wxId IN(select distinct wxId from DC_GzhWechatRelation where gzhId='gh_4b93b18b5042')").first()
wxid = pp.wxId if pp and pp.wxId else None
print(wxid)

exit(0)

for xx in query:
    print(xx)
    print(type(xx))

session.commit()
session.close()


def test_dict_get():
    d: dict = {"ss": "dd", "ii": {'p1': 'p11', 'p2': 'p22', 'pp': {'c1': 'c11', 'c2': 'c22'}}}
    z = d.get("dsds", 10)
    print(z)
    print(d.get("ss", 11))
    print(d.get("ii.pp"))
    print(Utils.dict_get(d, ['ii', 'pp']))
    print(Utils.dict_get(d, ['ii', 'pp2'], 2323))


# test_dict_get()

exit(0)
