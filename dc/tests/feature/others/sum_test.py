import json
import sys
import os
import re

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from bson import ObjectId
from dc.services.mongo_service import MongoService
import uuid


def test_map():
    numbers_list = [2, 6, 8, 10, 11, 4, 12, 7, 13, 17, 0, 3, 21]
    mapped_list = list(map(lambda num: num % 2, numbers_list))
    print(mapped_list)
    mapped_list = list(map(lambda num: num * 2, numbers_list))
    print(mapped_list)
    dict = [{'key': 'k1', 'value': 1}, {'key': 'k2', 'value': 2}]
    mapped_list = list(map(lambda num: num['key'], dict))
    print(mapped_list)


test_map()


def test_filter():
    numbers_list = [2, 6, 8, 10, 11, 4, 12, 7, 13, 17, 0, 3, 21]
    filtered_list = list(filter(lambda num: (num > 7), numbers_list))
    print(filtered_list)


test_filter()

dd = range(1, 4)
print(sum(dd))
