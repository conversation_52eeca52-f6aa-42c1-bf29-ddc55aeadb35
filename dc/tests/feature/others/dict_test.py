import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.common.utils import Utils


a = [1, 2, 3, 4, 5, 6, 7, 8]
b = [10, 9, 8, 7, 6, 5, 4, 3]


zip_obj = zip(a, b)
#print(zip_obj)
print(list(zip_obj))

def do(*params):
    print(params)

do(*zip(a, b))
exit(0)

a1, a2 = zip(*zip(a, b))
#print(a1)
#print(a2)
print('------')
print(list(a1))
print(list(a2))
#print(set(a1))
exit(0)

result = {
    "f1":"ff",
    "f2":"dd"
}

result2 = [{
    "f1": "ff",
    "f2": "dd"
}, {
    "f1": "ff22",
    "f2": "dd22"
}]

uu = [dict(zip(r.keys(), r)) for r in result2]
print([dict(zip(r.keys(), r)) for r in result2])
print([dict(zip(r, r)) for r in result2])

# 将zip对象转换成字典看看
a = [1, 2, 3]
d = [['a', 'b', 'c'], ['aa', 'bb', 'cc'], ['aaa', 'bbb', 'ccc']]

# 如果一个是key值，一个内层list就是一行value
xxx = [dict(zip(a, value)) for value in d]
print(xxx)

print([set(value) for value in d])

alist = ['a1', 'a2', 'a3']
blist = ['1', '2', '3']
for a, b in zip(alist, blist):
  print( a, b)

exit(0)
#oo = [dict(zip(result.keys, r)) for r in result]
#print(oo)

#print(list(zip(result.keys, r) for r in result))

print(dict(zip(result.values(), result)))

from dc.services.mysql_service import MySQLService
from dc.models.model import DCGzhWechat
mysql = MySQLService()
session = mysql.create_session()
wechart: DCGzhWechat = session.query(DCGzhWechat).all()
#print(wechart.to_dict())
zz = mysql.query2dict(wechart)
print(zz)

session.commit()
session.close()


def test_dict_get():
    d: dict = {"ss": "dd", "ii": {'p1': 'p11', 'p2': 'p22', 'pp': {'c1': 'c11', 'c2': 'c22'}}}
    z = d.get("dsds", 10)
    print(z)
    print(d.get("ss", 11))
    print(d.get("ii.pp"))
    print(Utils.dict_get(d, ['ii', 'pp']))
    print(Utils.dict_get(d, ['ii', 'pp2'], 2323))


#test_dict_get()

exit(0)
