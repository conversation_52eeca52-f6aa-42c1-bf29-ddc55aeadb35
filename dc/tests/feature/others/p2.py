# coding: utf-8
import multiprocessing
import time
from multiprocessing.queues import Queue


def func(msg):
    print("msg:", msg)
    time.sleep(3)
    print("end")

queue = Queue()
if __name__ == "__main__":
    # 维持执行的进程总数为processes，当一个进程执行完毕后会添加新的进程进去
    pool = multiprocessing.Pool(processes=3)
    for i in range(5):
        msg = "hello %d" % (i)
        queue.put(msg)
        # 非阻塞式，子进程不影响主进程的执行，会直接运行到 pool.join()
        pool.apply_async(func, (msg,))

        # 阻塞式，先执行完子进程，再执行主进程
        # pool.apply(func, (msg, ))   

    print("Mark~ Mark~ Mark~~~~~~~~~~~~~~~~~~~~~~")
    # 调用join之前，先调用close函数，否则会出错。
    pool.close()
    # 执行完close后不会有新的进程加入到pool,join函数等待所有子进程结束
    pool.join()
    print("Sub-process(es) done.")