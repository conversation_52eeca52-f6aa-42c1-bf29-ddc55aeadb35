import json
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

def temp(name, *args,**kwargs):
    print(name)
    print(args)
    print(kwargs)


def print_x(*args):
    print('--------')
    for arg in args:
        print(arg)


def test_extract():
    global first
    my_tuple = ("wang", "yuan", "wai")
    xxx = {"ame": "232", "pww": "dsd"}
    # temp('wjh')
    temp('wjh', *my_tuple, **xxx)
    temp("wang", "yuan", "wai", name2="wangyuanwai", age=32)
    first = (1, 2, 3)
    second = [1, 2, 3]
    third = "123"
    fourth = range(4)
    print_x(*first)
    print_x(*second)
    print_x(*third)
    print_x(*fourth)


test_extract()

a = 200
b = 66
print("A") if a > b else print("B")

if a > b and a > 2:
    print('a >b')


if a > b or a > 2:
    print('a >b')



i = 1
while i < 6:
  print(i)
  i += 1
else:
  print("i is no longer less than 6")


x = lambda a : a + 10
print(x(5))

x = lambda a, b : a * b
print(x(5, 6))



def myfunc(n):
  return lambda a : a * n

mydoubler = myfunc(2)

print(mydoubler(11))


def params_type(arg1: str, arg2, age: int, data: list):
    """

    :param arg1:
    :param arg2:
    :param age:
    :param data:
    :return:
    """
    pass


params_type()