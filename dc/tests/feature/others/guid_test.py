import json
import sys
import os
import re

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from bson import ObjectId
from dc.services.mongo_service import MongoService
import uuid

ms = MongoService()
conn = ms.get_collection('site-task')
# cc = conn.find_one({"_id": ObjectId('62ff3e77e51bda3c858684b1')})
# print(cc)

# dd = {x,y for x,y in zip('abcd', '1234')}
dd = [{x: y} for x, y in zip('abcd', '1234')]
print(dd)

knights = {'gallahad': 'the pure', 'robin': 'the brave'}
for x, y in knights.items():
    print(x, y)

print('---------------')
#m = re.search(r'(\w+)', 'abc-123', re.A)
#print(m.group()) #完整匹配
#print(m.group(1)) #子组1
#print(m.group(2)) #子组2
#print(m.groups()) #全部子组


str = "时间：2022-08-01 17:04      浏览次数： 1127      来源： 北京      字号：[ 大 中 小 ]z"

#m = re.search(r'(?<=来源：)(.+)(?=字号)', str, re.A)
#print(m)


zzz = re.match(r'(?<=来源：)(.+)(?=字号)', str, re.A)
print(zzz)

