import functools
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.common.func import md5
from dc.common.utils import Utils

def info(func):
    @functools.wraps(func)
    def wrap(a, b):
        return func(a, b)

    return wrap

@info
def multiply(x, y):
    return x * y


if __name__ == '__main__':
    print(multiply(2, 3))
    print("multiply name:", multiply.__name__)


