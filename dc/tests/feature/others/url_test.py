import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.weixin_service import WeixinService

#from urllib.parse import urlparse,parse_qs,urlsplit
from urllib import parse as urlparse
#from urllib.parse import urlparse
from dc.common.func import md5

from dc.common.utils import Utils


url = 'http://mp.weixin.qq.com/s?__biz=MzI4MjAzNzUxMQ==&mid=2651439396&idx=2&sn=3711df84c4cdc7a2c4bc843defd5158b&chksm=f05d7d4cc72af45a834d4851a3575ae7e68e4a4dd27502b226cd95cd30a2a2a3c2d8c25a84db&scene=126&&sessionid=1666758432#rd'
url2 = 'http://mp.weixin.qq.com/s?__biz=MzI4MjAzNzUxMQ==&mid=2651439396&idx=2&sn=3711df84c4cdc7a2c4bc843defd5158b&chksm=f05d7d4cc72af45a834d4851a3575ae7e68e4a4dd27502b226cd95cd30a2a2a3c2d8c25a84db&scene=126&&sessionid=16667584323#rd'


def calc_url_md5(url):
    pp = urlparse.parse_qs(urlparse.urlsplit(url).query)
    del pp['sessionid']
    pp2 = sorted(pp.items())
    ps = [f"{x}={''.join(y)}" for x, y in pp2]
    full = "&".join(ps)
    return md5(full)


print(calc_url_md5(url))
print(calc_url_md5(url2))
wx = WeixinService()
print(wx.calc_url_md5(url))
print(wx.calc_url_md5(url2))
exit(0)


print(urlparse.urlparse(url))

pp = urlparse.parse_qs(urlparse.urlsplit(url).query)
print(pp)
del pp['sessionid']
print('---1')
pp2 = sorted(pp.items())
print(pp2)
print('---2')
ps = [f"{x}={''.join(y)}" for x,y in pp2]
print("&".join(ps))



exit(0)


print('---')

print([{'key': x, 'value': pp.get(x)} for x in pp])

exit(0)

# 排序

def test_sort():
    enty = [1, 3, 6, 2]
    enty.sort()
    print(enty)
    list = [
        {'id': 1, 'name': 'wjh', 'age': 44},
        {'id': 3, 'name': 'wjh3', 'age': 22},
        {'id': 2, 'name': 'wjh2', 'age': 33},
        {'id': 4, 'name': 'wjh4', 'age': 11},
    ]
    print(sorted(list, key=lambda x: x['age']))


#test_sort()
