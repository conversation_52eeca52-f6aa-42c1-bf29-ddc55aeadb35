import sys
import os
import time

import requests
from assertpy import assert_that

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

apis = [
    {
        'name': '接口名称',
        'url': '12',
        'params': {},
        'timeout': 3000,
        'demo': '备注',
    }
]

# assert 1 == 2, ' 1不等于2'

try:

    assert_that(None).is_none()
    #assert_that('').is_none()
    assert_that(' ').is_none()
except AssertionError as aex:
    print(aex.args)
    # print(aex.args)
