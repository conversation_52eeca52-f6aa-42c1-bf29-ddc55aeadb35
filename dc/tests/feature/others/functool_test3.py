import functools
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.common.func import md5
from dc.common.utils import Utils
import functools


def test_cmp_to_key():
    nums = [3, 30, 34, 5, 9]
    new_nums = sorted(nums, key=functools.cmp_to_key(lambda x, y: y - x))
    new_nums2 = sorted(nums, key=functools.cmp_to_key(lambda x, y: x - y))
    print(new_nums)
    print(new_nums2)
    nums = [3, 30, 34, 5, 9]
    nums2 = map(str, nums)
    print(list(nums2))
    result2 = [{
        "f1": "ff",
        "f2": "dd"
    }, {
        "f1": "ff22",
        "f2": "dd22"
    }]
    print(list(map(lambda x: x.get('f1'), result2)))


# test_cmp_to_key()


def test_partial():
    def add(a, b):
        print("当前结果值", a + b)

    add = functools.partial(add, 1)
    add(2)


# test_partial()
