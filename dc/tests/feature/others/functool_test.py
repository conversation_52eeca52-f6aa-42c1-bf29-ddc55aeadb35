import functools
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.common.func import md5
from dc.common.utils import Utils


def decorator(func):
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        return result

    return wrapper


def decorator2(func):

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        return result

    return wrapper


#@decorator
def fun_test():
    print("8888")

@decorator
def fun_test2():
    print("8888")

@decorator2
def fun_test3():
    print("8888")


if __name__ == '__main__':
    fun_test()
    print(fun_test.__name__)

    fun_test2()
    print(fun_test2.__name__)

    fun_test3()
    print(fun_test3.__name__)