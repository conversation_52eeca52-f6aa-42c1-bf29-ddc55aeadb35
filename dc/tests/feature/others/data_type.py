import hashlib
import json
import sys
import os

dirname = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
sys.path.append(dirname)

from dc.common.func import md5

def test_data_type():
    global x
    # str
    x = "Hello World"
    # int
    x = 29
    # float
    x = 29.5
    # complex
    x = 1j
    # list
    x = ["apple", "banana", "cherry"]
    # tuple
    x = ("apple", "banana", "cherry")
    # range
    x = range(6)
    # dict
    x = {"name": "<PERSON>", "age": 63}
    # set
    x = {"apple", "banana", "cherry"}
    # frozenset
    x = frozenset({"apple", "banana", "cherry"})
    # bool
    x = True
    # bytes
    x = b"Hello"
    # bytearray
    x = bytearray(5)
    # memoryview
    x = memoryview(bytes(5))


def test_list():
    thislist = ["apple", "banana", "cherry", "pear", "eggs"]
    print(thislist)
    print(thislist[0])
    print(thislist[1:3])
    thislist[3] = "mango"
    print(thislist[1:3])
    for x in thislist:
        print(x)

    # 检查项目是否存在
    if "apple" in thislist:
        print(111)

    print(len(thislist))

    # 使用 append() 方法追加项目
    thislist.append("huhu")
    print(thislist)

    # 插入项目作为第二个位置：
    thislist.insert(1, "orange2")
    print(thislist)

    # remove() 方法删除指定的项目：：
    thislist.remove("banana")
    print(thislist)

    # pop() 方法删除指定的索引（如果未指定索引，则删除最后一项）：：
    thislist.pop()
    print(thislist)

    # del 关键字删除指定的索引：
    del thislist[0]
    print(thislist)

    # del 关键字也能完整地删除列表：
    # del thislist
    # print(thislist)

    # clear() 方法清空列表：
    # thislist.clear()
    # print(thislist)

    # 使用 copy() 方法来复制列表：
    mylist = thislist.copy()
    print(mylist)

    # 制作副本的另一种方法是使用内建的方法 list()
    thislist = ["apple", "banana", "cherry"]
    mylist = list(thislist)
    print(mylist)

    # 合并两个列表
    list1 = ["a", "b", "c"]
    list2 = [1, 2, 3]

    list3 = list1 + list2
    print(list3)

    # 使用 extend() 方法将 list2 添加到 list1 的末尾：
    list1 = ["a", "b", "c"]
    list2 = [1, 2, 3]

    list1.extend(list2)
    print(list1)

    # list() 构造函数
    thislist = list(("apple", "banana", "cherry"))  # 请注意双括号
    print(thislist)


def db_query():
    pass


def test_dict():
    thisdict = {
        "brand": "Porsche",
        "model": "911",
        "year": 1963
    }
    print(thisdict)
    print(thisdict['brand'])
    print(thisdict.get('brand'))
    # update
    thisdict.update(brand="oo")
    thisdict["year"] = 2019
    print(thisdict)

    for x in thisdict:
        print(x)
        print(thisdict[x])

    print('----values----')
    for x in thisdict.values():
        print(x)

    for x, y in thisdict.items():
      print(x, y)

    if "model" in thisdict:
        print("Yes, 'model' is one of the keys in the thisdict dictionary")

    print(len(thisdict))

    # append
    thisdict["color"] = "red"
    thisdict.update(no2="d333")
    print(thisdict)


    # 删除
    thisdict.pop("model")
    del thisdict['year']
    print(thisdict)

    thisdict.popitem()
    print(thisdict)

    # del thisdict
    # thisdict.clear()

    mydict = thisdict.copy()
    print(mydict)

    mydict = dict(thisdict)

    thisdict = dict(brand="Porsche", model="911", year=1963)
    # 请注意，关键字不是字符串字面量
    # 请注意，使用了等号而不是冒号来赋值
    print(thisdict)

    zz = dict(status_code=11, text= 222)
    #print(zz.status_code)
    xx = json.loads( json.dumps(zz))
    print(xx.status_code)



# test_data_type()

# db_query()

# test_list()

#test_dict()


def hash_md5(data: str):
    return md5(data)


print(hash_md5("1"))