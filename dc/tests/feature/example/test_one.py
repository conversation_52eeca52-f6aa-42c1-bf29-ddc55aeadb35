from dc.models.model_mp import TradeControlCompany
from dc.services.mysql_service import MySQLService
from dc.tests.test_basics import BasicsTestCase


class TestOne(BasicsTestCase):

    name = None

    def test_print(self):
        print('TestOne-test_cc')
        self.assertTrue(True)


    def test_mysql(self):
        mysql = MySQLService(name="mysql_company")
        session = mysql.create_session()
        data = session.query(TradeControlCompany).order_by(TradeControlCompany.id).all()
        for item in data:
            print(item.companyName)

        session.close()

        self.assertTrue(len(data) > 0)