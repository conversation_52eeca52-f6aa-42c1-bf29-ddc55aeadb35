# -*- coding:utf-8 -*-
import unittest
from unittest.mock import patch, MagicMock, call
from dc.services.logging_service import LoggingService
from dc.commands.trade_control_report.trade_control_queue import es_data, update_result, main

class TestTradeControlQueue(unittest.TestCase):
    @patch('dc.commands.trade_control_report.trade_control_queue.es')
    def test_es_data(self, mock_es):
        """测试ES数据查询和ID提取功能"""
        # 模拟ES查询返回结果
        mock_hits = [
            {'_id': 'id1'},
            {'_id': 'id2'},
            {'_id': 'id3'}
        ]
        mock_es.search.return_value = {
            'hits': {'hits': mock_hits}
        }

        # 执行测试函数
        result = es_data(batch_size=10)

        # 验证结果
        self.assertEqual(result, ['id1', 'id2', 'id3'])
        # 验证查询参数是否正确
        mock_es.search.assert_called_once()
        call_args = mock_es.search.call_args
        self.assertEqual(call_args[1]['index'], 'dc_tradecontrol')
        self.assertEqual(call_args[1]['body']['size'], 10)

    @patch('dc.commands.trade_control_report.trade_control_queue.helpers.bulk')
    @patch('dc.commands.trade_control_report.trade_control_queue.es')
    @patch('dc.commands.trade_control_report.trade_control_queue.logger')
    def test_update_result_success(self, mock_logger, mock_es, mock_bulk):
        """测试ES批量更新成功场景"""
        mock_bulk.return_value = (3, [])  # (成功数量, 失败列表)
        actions = [{'_id': 'id1'}, {'_id': 'id2'}]

        update_result(actions)

        # 验证bulk调用
        mock_bulk.assert_called_once_with(mock_es, actions)
        # 验证日志
        mock_logger.info.assert_any_call("批量更新成功")
        # 验证刷新索引
        mock_es.indices.refresh.assert_called_once_with(index='dc_tradecontrol')

    @patch('dc.commands.trade_control_report.trade_control_queue.helpers.bulk')
    @patch('dc.commands.trade_control_report.trade_control_queue.logger')
    def test_update_result_failed(self, mock_logger, mock_bulk):
        """测试ES批量更新失败场景"""
        mock_failures = [{'error': 'reason1'}, {'error': 'reason2'}]
        mock_bulk.return_value = (1, mock_failures)
        actions = [{'_id': 'id1'}, {'_id': 'id2'}]

        update_result(actions)

        # 验证失败日志
        mock_logger.info.assert_any_call("以下内容更新失败")
        for fail in mock_failures:
            mock_logger.info.assert_any_call(f"失败详情: {fail}")

    @patch('dc.commands.trade_control_report.trade_control_queue.client')
    @patch('dc.commands.trade_control_report.trade_control_queue.es_data')
    @patch('dc.commands.trade_control_report.trade_control_queue.update_result')
    @patch('dc.commands.trade_control_report.trade_control_queue.logger')
    @patch('dc.commands.trade_control_report.trade_control_queue.es')
    def test_main_with_data(self, mock_es, mock_logger, mock_update, mock_es_data, mock_client):
        """测试主函数有数据的情况"""
        # 模拟有数据返回
        mock_es_data.side_effect = [['id1', 'id2'], []]
        # 执行主函数
        main()

        # 验证数据处理流程
        self.assertEqual(mock_es_data.call_count, 2)  # 第一次有数据，第二次无数据
        # 验证Redis调用
        expected_calls = [call.lpush('dc_trade_control_queue', 'id1'),
                          call.lpush('dc_trade_control_queue', 'id2')]
        mock_client.assert_has_calls(expected_calls, any_order=False)
        # 验证更新调用
        mock_update.assert_called_once()
        # 验证连接关闭
        mock_es.close.assert_called_once()
        mock_client.close.assert_called_once()

    @patch('dc.commands.trade_control_report.trade_control_queue.es_data')
    @patch('dc.commands.trade_control_report.trade_control_queue.logger')
    @patch('dc.commands.trade_control_report.trade_control_queue.es')
    @patch('dc.commands.trade_control_report.trade_control_queue.client')
    def test_main_no_data(self, mock_client, mock_es, mock_logger, mock_es_data):
        """测试主函数无数据的情况"""
        mock_es_data.return_value = []
        main()

        mock_logger.info.assert_any_call("没有新数据需要处理，处理结束")
        mock_es.close.assert_called_once()
        mock_client.close.assert_called_once()


    @patch('dc.commands.trade_control_report.trade_control_queue.client')
    @patch('dc.commands.trade_control_report.trade_control_queue.es_data')
    @patch('dc.commands.trade_control_report.trade_control_queue.logger')
    @patch('dc.commands.trade_control_report.trade_control_queue.es')  # 补充 es 补丁（原测试漏了）
    def test_main_exception(self, mock_es, mock_logger, mock_es_data, mock_client):
        """测试主函数处理过程中发生异常的情况"""
        # 第一次调用返回异常数据，第二次返回空列表（让循环退出）
        mock_es_data.side_effect = [['error_id'], []]
        # 模拟 Redis 操作抛出异常
        mock_client.lpush.side_effect = Exception("Redis连接失败")

        main()

        # 验证错误日志
        mock_logger.error.assert_any_call("处理ES数据error_id时发生错误：Redis连接失败")
        # 验证循环退出后连接已关闭
        mock_es.close.assert_called_once()
        mock_client.close.assert_called_once()

if __name__ == '__main__':
    unittest.main()