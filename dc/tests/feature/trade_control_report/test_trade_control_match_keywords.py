import unittest
from unittest.mock import Mock, patch, MagicMock
from dc.commands.trade_control_report import match_keywords_summary_by_ai
from itertools import cycle  # 导入cycle用于循环返回值
from dc.commands.trade_control_report.trade_control_match_keywords import (
    get_company_keywords,
    get_id_from_redis,
    get_data_from_es,
    summary_by_ai,
    is_similar_conclusion,
    match_keyword_attachements,
    match_keyword_text,
    create_monitor_report,
    save_report_to_db
)


class TestTradeControlMatchKeywords(unittest.TestCase):

    @patch('dc.commands.trade_control_report.trade_control_match_keywords.mysql')
    def test_get_company_keywords(self, mock_mysql):
        """测试获取公司关键词结构"""
        # 模拟数据库会话和查询结果
        mock_session = Mock()
        mock_mysql.create_session.return_value = mock_session

        # 模拟公司查询结果
        mock_company = Mock()
        mock_company.customer_id = 1  # 公司的customer_id为1

        # 关键修正：将关键词模拟为可解包的元组结构
        # 原错误是因为使用了Mock对象而不是可解包的结构
        keyword_tuple = ("test_keyword", 100)  # (关键词, 公司ID)

        # 调整查询结果顺序和结构
        all_results = [
            [mock_company],  # 第一次查询：公司列表
            [keyword_tuple],  # 第二次查询：关键词元组列表
            [("show_keyword", "child1"), ("show_keyword", "child2")]  # 第三次查询：子关键词
        ]
        all_results_iter = iter(all_results)
        mock_session.query.return_value.filter.return_value.all.side_effect = lambda: next(all_results_iter, [])

        result = get_company_keywords()
        self.assertEqual(len(result), 1)
        self.assertIn(1, result)
        self.assertIn("test_keyword", result[1])
        self.assertEqual(result[1]["test_keyword"]["company_id"], 100)
        self.assertEqual(result[1]["test_keyword"]["childwords"], ["child1", "child2"])
        mock_session.close.assert_called_once()

    @patch('dc.commands.trade_control_report.trade_control_match_keywords.client')
    def test_get_id_from_redis(self, mock_client):
        """测试从Redis获取ID"""
        # 测试有数据的情况
        mock_client.rpop.return_value = "es123"
        self.assertEqual(get_id_from_redis(), "es123")

        # 测试无数据的情况
        mock_client.rpop.return_value = None
        self.assertIsNone(get_id_from_redis())

    @patch('dc.commands.trade_control_report.trade_control_match_keywords.es')
    def test_get_data_from_es(self, mock_es):
        """测试从ES获取数据"""
        # 测试成功获取
        mock_es.get.return_value = {"_source": {"title": "test", "text": "content"}}
        result = get_data_from_es("es123")
        self.assertEqual(result["title"], "test")

        # 测试获取失败
        mock_es.get.side_effect = Exception("连接失败")
        result = get_data_from_es("es123")
        self.assertIsNone(result)

    @patch('dc.commands.trade_control_report.trade_control_match_keywords.invoke')
    def test_summary_by_ai(self, mock_invoke):
        """测试AI总结功能"""
        mock_invoke.return_value = "*定位摘要*：测试总结内容。"
        result = summary_by_ai("test content", "keyword")
        self.assertEqual(result, "*定位摘要*：测试总结内容。")
        self.assertTrue(mock_invoke.called)

    @patch('dc.commands.trade_control_report.trade_control_match_keywords.invoke')
    def test_is_similar_conclusion(self, mock_invoke):
        """测试结论相似度判断"""
        # 测试相似情况
        mock_invoke.return_value = "是"
        self.assertTrue(is_similar_conclusion("内容1", "内容2"))

        # 测试不相似情况
        mock_invoke.return_value = "否"
        self.assertFalse(is_similar_conclusion("内容1", "内容2"))

        # 测试混合结果
        mock_invoke.return_value = "是不是"
        self.assertFalse(is_similar_conclusion("内容1", "内容2"))

    @patch('dc.commands.trade_control_report.trade_control_match_keywords.summary_by_ai')
    @patch('dc.commands.trade_control_report.trade_control_match_keywords.is_similar_conclusion')
    def test_match_keyword_attachements(self, mock_similar, mock_summary):
        """测试附件关键词匹配"""
        # 关键修复：使用cycle让返回值可循环，避免StopIteration
        mock_summary.side_effect = cycle(["总结1", "总结2"])  # 循环返回这两个值
        mock_similar.return_value = False

        # 测试单页内容
        content = "This is a test Page with keyword"
        result = match_keyword_attachements(content, "keyword")
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["page"], 1)

        # 测试多页内容
        content = "Page 1: keyword here Page 2: another keyword"
        result = match_keyword_attachements(content, "keyword")
        self.assertEqual(len(result), 2)

        # 测试去重逻辑
        mock_similar.return_value = True
        result = match_keyword_attachements(content, "keyword")
        self.assertEqual(len(result), 1)



    @patch('dc.commands.trade_control_report.trade_control_match_keywords.summary_by_ai')
    @patch('dc.commands.trade_control_report.trade_control_match_keywords.is_similar_conclusion')
    def test_match_keyword_text(self, mock_similar, mock_summary):
        """测试文本关键词匹配"""
        mock_summary.side_effect = ["总结1", "总结2"]
        mock_similar.return_value = False

        # 测试匹配结果
        content = "keyword test keyword example"
        result = match_keyword_text(content, "keyword")
        self.assertTrue(len(result["conclusions"]) > 0)
        self.assertEqual(result["conclusions"][0]["position"], 1)

        # 测试去重逻辑
        mock_similar.return_value = True
        result = match_keyword_text(content, "keyword")
        self.assertEqual(len(result["conclusions"]), 1)

    def test_create_monitor_report(self):
        """测试生成监测报告数据"""
        es_content = {
            'createdAt': '2023-01-01',
            'title_zh': '测试标题',
            'dc_site_name': '测试来源',
            'dc_detail_url': 'http://test.com'
        }
        result = create_monitor_report(
            es_content, 100, 1, "keyword", "显示关键词", "子关键词",
            "es123", "2", "测试总结", 3)

        self.assertEqual(result["companyId"], 100)
        self.assertEqual(result["customer_id"], 1)
        self.assertEqual(result["keyword"], "keyword")
        self.assertEqual(result["page"], 3)
        self.assertEqual(result["content"], "测试总结")

    @patch('dc.commands.trade_control_report.trade_control_match_keywords.TradeControlMonitorReport')
    def test_save_report_to_db(self, mock_report_cls):
        """测试保存报告到数据库"""
        mock_session = Mock()
        report_data = {
            "companyId": 100,
            "customer_id": 1,
            "MonitorDate": "2023-01-01",
            "keyword": "test",
            "keywordShow": "显示",
            "keywordRelation": "子关键词",
            "newsId": "es123",
            "title": "标题",
            "content": "内容",
            "newsSource": "来源",
            "page": 1,
            "type": "2",
            "url": "http://test.com"
        }

        # 测试正常保存
        save_report_to_db(mock_session, report_data)
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

        # 测试保存失败
        mock_session.commit.side_effect = Exception("保存失败")
        with self.assertLogs(level='ERROR'):
            save_report_to_db(mock_session, report_data)


if __name__ == '__main__':
    unittest.main()