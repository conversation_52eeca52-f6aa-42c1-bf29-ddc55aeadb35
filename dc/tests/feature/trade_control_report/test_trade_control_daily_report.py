import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import date, datetime, timedelta
from dc.commands.trade_control_report.trade_control_daily_report import (
    getCustomerId,
    getCompanyIdOfCustomerId,
    getKeywordsOfCompany,
    getNewsReportsVideosTotalCount,
    build_daily_report_dict,
    save_daily_report_to_db
)


class TestTradeControlDailyReport(unittest.TestCase):
    """贸易管制日报生成逻辑的单元测试类"""

    def setUp(self):
        """初始化测试环境，创建模拟会话和测试日期"""
        self.mock_session = Mock()
        self.test_date = date(2025, 7, 24)  # 测试用日期
        self.yesterday = (datetime.now().date() - timedelta(days=1))

    def test_getCustomerId(self):
        """测试获取指定日期有监测记录的customer_id"""
        # 模拟查询结果
        mock_query_result = [(1,), (2,), (3,)]  # 这是最终all()应返回的结果

        # 2. 按实际查询链配置Mock（关键步骤）
        # 假设函数中实际调用链是：query().filter().distinct().all()
        # 则需要按顺序设置每个方法的返回值
        mock_query = self.mock_session.query.return_value  # 第一步：获取query对象
        mock_filter = mock_query.filter.return_value  # 第二步：调用filter()后的返回值
        mock_distinct = mock_filter.distinct.return_value  # 第三步：调用distinct()后的返回值
        mock_distinct.all.return_value = mock_query_result  # 第四步：all()返回可迭代结果

        # 3. 执行测试函数
        result = getCustomerId(self.mock_session, self.test_date)

        # 4. 验证结果和调用链
        self.assertEqual(result, [1, 2, 3])  # 验证结果正确
        mock_query.filter.assert_called()  # 验证filter被调用（不严格限制次数）
        mock_filter.distinct.assert_called_once()  # 验证distinct被调用
        mock_distinct.all.assert_called_once()  # 验证all被调用

    def test_getCustomerId_empty(self):
        """测试当没有监测记录时的处理逻辑"""
        self.mock_session.query().filter().distinct().all.return_value = []

        result = getCustomerId(self.mock_session, self.test_date)
        self.assertEqual(result, [])

    def test_getCompanyIdOfCustomerId(self):
        """测试获取customer_id对应的company_id"""
        # 模拟输入和查询结果（可迭代的列表）
        test_customer_ids = [1, 2]
        mock_result = [(1, "comp1"), (2, "comp2")]

        mock_query = self.mock_session.query.return_value  # 第一步：query()返回的对象
        mock_filter = mock_query.filter.return_value  # 第二步：filter()的返回值
        mock_filter.all.return_value = mock_result  # 最终all()返回可迭代的模拟结果

        # 执行测试函数
        result = getCompanyIdOfCustomerId(self.mock_session, test_customer_ids)

        # 验证结果和调用链
        self.assertEqual(result, {1: "comp1", 2: "comp2"})
        # 验证查询链被正确调用
        mock_query.filter.assert_called_once()
        mock_filter.all.assert_called_once()

    def test_getCompanyIdOfCustomerId_empty(self):
        """测试当输入为空时的处理逻辑"""
        result = getCompanyIdOfCustomerId(self.mock_session, [])
        self.assertEqual(result, {})
        self.mock_session.query().filter.assert_not_called()

    def test_getKeywordsOfCompany(self):
        """测试获取客户对应的关键词"""
        # 模拟输入和查询结果
        test_customer_ids = [1, 2]
        mock_result = [
            (1, "keyword1"),
            (1, "keyword2"),
            (2, "keyword3")
        ]
        self.mock_session.query().filter().filter().all.return_value = mock_result

        # 执行测试函数
        result = getKeywordsOfCompany(self.mock_session, test_customer_ids, self.test_date)

        # 验证结果（关键词应排序并去重）
        self.assertEqual(result, {
            1: "keyword1,keyword2",
            2: "keyword3"
        })

    def test_getKeywordsOfCompany_empty(self):
        """测试当没有关键词时的处理逻辑"""
        result = getKeywordsOfCompany(self.mock_session, [], self.test_date)
        self.assertEqual(result, {})

    def test_getNewsReportsVideosTotalCount(self):
        """测试统计新闻、报告和视频数量"""
        # 模拟查询结果（customer_id, news_count, report_count, video_count）
        mock_result = [(1, 2, 3, 1), (2, 0, 1, 0)]
        self.mock_session.query().filter().filter().group_by().all.return_value = mock_result

        # 执行测试函数
        result = getNewsReportsVideosTotalCount(self.mock_session, [1, 2], self.test_date)

        # 验证结果（总数应为三者之和）
        self.assertEqual(result, {
            1: [2, 3, 1, 6],  # 2+3+1=6
            2: [0, 1, 0, 1]  # 0+1+0=1
        })

    def test_build_daily_report_dict(self):
        """测试构建日报字典的完整逻辑"""
        # 模拟各个依赖函数的返回值
        with patch('dc.commands.trade_control_report.trade_control_daily_report.getCustomerId') as mock_get_cids, \
                patch('dc.commands.trade_control_report.trade_control_daily_report.getKeywordsOfCompany') as mock_get_kw, \
                patch('dc.commands.trade_control_report.trade_control_daily_report.getNewsReportsVideosTotalCount') as mock_get_counts, \
                patch('dc.commands.trade_control_report.trade_control_daily_report.getCompanyIdOfCustomerId') as mock_get_comp:
            # 设置模拟返回值
            mock_get_cids.return_value = [1, 2]
            mock_get_kw.return_value = {1: "kw1,kw2", 2: "kw3"}
            mock_get_counts.return_value = {1: [2, 3, 1, 6], 2: [0, 1, 0, 1]}
            mock_get_comp.return_value = {1: "comp1", 2: "comp2"}

            # 执行测试函数
            result = build_daily_report_dict(self.mock_session, self.test_date)

            # 验证结果结构和内容
            self.assertEqual(len(result), 2)
            self.assertEqual(result[1], [
                "comp1", 6, 2, 3, 1, "kw1,kw2", self.test_date
            ])
            self.assertEqual(result[2], [
                "comp2", 1, 0, 1, 0, "kw3", self.test_date
            ])

    def test_build_daily_report_dict_empty(self):
        """测试当没有客户ID时构建日报的逻辑"""
        with patch('dc.commands.trade_control_report.trade_control_daily_report.getCustomerId') as mock_get_cids:
            mock_get_cids.return_value = []
            result = build_daily_report_dict(self.mock_session, self.test_date)
            self.assertEqual(result, {})

    def test_save_daily_report_to_db_insert(self):
        """测试保存日报时的插入逻辑"""
        # 准备测试数据
        test_report = {
            1: ["comp1", 6, 2, 3, 1, "kw1,kw2", self.test_date]
        }

        # 模拟查询结果（不存在记录，应插入）
        self.mock_session.query().filter().first.return_value = None

        # 执行测试函数
        save_daily_report_to_db(self.mock_session, test_report)

        # 验证操作
        self.mock_session.add.assert_called_once()
        self.mock_session.commit.assert_called_once()

    def test_save_daily_report_to_db_update(self):
        """测试保存日报时的更新逻辑"""
        # 准备测试数据
        test_report = {
            1: ["comp1", 6, 2, 3, 1, "kw1,kw2", self.test_date]
        }

        # 模拟查询结果（存在记录，应更新）
        mock_obj = Mock()
        self.mock_session.query().filter().first.return_value = mock_obj

        # 执行测试函数
        save_daily_report_to_db(self.mock_session, test_report)

        # 验证更新操作
        self.assertEqual(mock_obj.companyId, "comp1")
        self.assertEqual(mock_obj.totalCount, 6)
        self.mock_session.commit.assert_called_once()

    def test_save_daily_report_to_db_exception(self):
        """测试保存日报时发生异常的回滚逻辑"""
        # 模拟异常
        self.mock_session.commit.side_effect = Exception("DB Error")

        # 执行测试函数并验证异常
        with self.assertRaises(Exception):
            save_daily_report_to_db(self.mock_session, {1: ["comp1", 6, 2, 3, 1, "kw1,kw2", self.test_date]})

        # 验证回滚被调用
        self.mock_session.rollback.assert_called_once()


if __name__ == '__main__':
    unittest.main()
