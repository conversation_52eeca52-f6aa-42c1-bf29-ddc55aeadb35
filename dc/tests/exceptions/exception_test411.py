# -*- coding:utf-8 -*-
# @Function  : ELK 信息提示
# <AUTHOR> wjh
# @Time      : 2023/8/31
# Version    : 1.0

import json
import sys
import logging
import traceback

from dc.services.logging_service import LoggingService
import dc.common.exceptions


# 示例代码，触发异常
def main():
    try:
        print(112)
        raise ValueError("This is a test exception")
    except Exception as ex:
        print(str(ex))


if __name__ == "__main__":
    main()
