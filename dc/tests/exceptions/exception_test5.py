# -*- coding:utf-8 -*-
# @Function  : ELK 信息提示
# <AUTHOR> wjh
# @Time      : 2023/8/31
# Version    : 1.0
import sys
import logging
import traceback

from dc.services.logging_service import LoggingService

# 设置日志记录
logger = LoggingService(logfile="ex3.log")


def handle_exception(exc_type, exc_value, exc_traceback):
    # 这里可以定义全局异常处理的逻辑
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # 格式化堆栈信息
    stack_info = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))

    # 输出到日志文件
    logger.error("Uncaught exception:\n%s", stack_info)

    # 输出到控制台
    print(f"Error: {exc_value}")
    print(f"Stack Trace:\n{stack_info}")


# 设置全局异常处理
# sys.excepthook = handle_exception

# 示例代码，触发异常
def main():
    try:
        raise ValueError("This is a test exception")
    except Exception as ex:
        logger.error(f"pull exception:" + traceback.format_exc())


if __name__ == "__main__":
    main()
