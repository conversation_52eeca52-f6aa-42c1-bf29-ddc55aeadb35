# -*- coding:utf-8 -*-
# @Function  : ELK 信息提示
# <AUTHOR> wjh
# @Time      : 2023/8/31
# Version    : 1.0

import json
import sys
import logging
import traceback

from dc.services.logging_service import LoggingService
import dc.common.exceptions
from dc.services.redis_service import RedisService


# 示例代码，触发异常
def main():
    redis = RedisService(name='redis', run_model='test').get_client()
    redis.select(4)

    redis.set('test','{"time": "2024-07-09 14:38:58,382", "levelname": "ERROR", "message": "Uncaught exception:\nTraceback (most recent call last):\\n  File \"D:\\woodw\\code\\python\\ijiwei-dc2\\dc\\commands\\exceptions\\exception_test41.py\", line 23, in <module>\\n    main()\\n  File \"D:\\woodw\\code\\python\\ijiwei-dc2\\dc\\commands\\exceptions\\exception_test41.py\", line 19, in main\\n    raise ValueError(\"This is a test exception\")\\nValueError: This is a test exception\\n", "levelno": "106", "process": "22508", "func": "error", "lineno": "106", "pathname": "D:\woodw\code\python\ijiwei-dc2\dc\services\logging_service.py"}')


    zz = redis.get('tset')
    print(zz)
    d: dict = json.loads(zz)
    print(d.get('pathname'))


if __name__ == "__main__":
    main()
