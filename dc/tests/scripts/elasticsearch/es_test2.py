from faker import Faker

from dc.services.elasticsearch_service import ElasticsearchService

ess = ElasticsearchService()
es = ess.get_connect()
faker = Faker(locale='zh_CN')  # 配置为中文

test_index = 'dc_wechat,dc_yq'

query = {
    "query": {
        "match_all": {}
    },
    "sort": [
        {
            "public_time": {
                "order": "desc"
            }
        }
    ],
    "size": 100
}

# 执行查询
response = es.search(index=test_index, body=query)

# 输出响应
for doc in response['hits']['hits']:
    print(doc['_index'])
