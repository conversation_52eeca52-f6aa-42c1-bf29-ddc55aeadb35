# -*- coding:utf-8 -*-
# @Function  : elasticsearch 使用示例
# <AUTHOR> wjh
# @Time      : 2023/6/6 9:59
# Version: 1.0
import random
import sys
import os
from faker import Faker
from dc.common.utils import Utils
from elasticsearch import Elasticsearch
from dc.conf.settings import get_settings
from dc.services.elasticsearch_service import ElasticsearchService


ess = ElasticsearchService()
es = ess.get_connect()
faker = Faker(locale='zh_CN')  # 配置为中文

test_index = 'es_demo'


def get_body(params={}):
    body = {
        'no': random.randint(10000, 99999),
        'name': faker.name(),
        'age': faker.random.randint(20, 30),
        'phone_number': faker.phone_number(),
        'city': faker.city(),
        'address': faker.address(),
    }
    body.update(params)
    return body


def data_init():
    start_time = Utils.millisecond_time()
    for i in range(1, 100):
        body = get_body({'no': str(i)})
        result = es.index(index=test_index, doc_type='_doc', body=body)
        print(result)
    end_time = Utils.millisecond_time()
    total = end_time - start_time
    print(f"exec time: {total / 1000}")


def test_get():
    docs = es.search(index=test_index, body={
        'query': {
            'match_all': {}
        }
    })
    data = docs['hits']['hits']
    return data[0] if data else None


def test_insert():
    body = get_body()
    result = es.index(index=test_index, doc_type='_doc', body=body)
    print(result)


def test_update():
    global test_index, id, result

    item = test_get()
    id = item['_id']
    print('------ get1 --------')
    doc = es.get(test_index, id)
    print(doc)
    print('------ update --------')
    result = es.update(test_index, id, body={
        'doc': {
            'age': doc['_source']['age'] + 1
        }
    })
    print(result)
    print('-------get2-------')
    doc = es.get(test_index, id)
    print(doc)


# data_init()

# test_get()

# test_insert()

# test_update()
