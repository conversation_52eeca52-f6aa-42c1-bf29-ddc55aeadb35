import sys
import os
from faker import Faker
from dc.common.utils import Utils
from elasticsearch import Elasticsearch
from dc.conf.settings import get_settings
from dc.services.elasticsearch_service import ElasticsearchService

# conf = get_settings('elasticsearch')
# es = Elasticsearch("http://192.168.1.123:9200")

ess = ElasticsearchService()
es = ess.get_connect()
faker = Faker(locale='zh_CN')  # 配置为中文

test_index = 'dc_test2'


def test_update():
    global test_index, id, result
    test_index = 'dc_news_test'
    id = 'VpB2P4MBYWWw2XaaRfyF'
    es_index = 'dc_news_test'
    print('------ get1 --------')
    doc = es.get(es_index, id)
    print(doc)
    print('------ update --------')
    result = es.update(es_index, id, body={
        'doc': {
            'dc_site_name': '上海市人民政府2'
        }
    })
    print(result)
    print('-------get2-------')
    doc = es.get(es_index, id)
    print(doc)



def test_esid():
    body = {'title': '2021--11-01',
            'text': '',
            'dc_detail_url': 'https://www.shanghai.gov.cn/nw49248/20210423/6dbf9b3174074431a282e1fb6156ba60.html',
            'dc_site_name': '上海市人民政府'}

    result = es.index(index=test_index, doc_type='_doc', body=body)
    print(result)





def data_init():
    global body, result
    start_time = Utils.millisecond_time()
    for i in range(0, 1000):
        body = {'title': '2021--11-' + str(i),
                'text': '',
                'dc_detail_url': 'https://www.shanghai.gov.cn/nw49248/20210423/6dbf9b3174074431a282e1fb6156ba60.html',
                'dc_site_name': '上海市人民政府'}

        result = es.index(index=test_index, doc_type='_doc', body=body)
        print(result)
    end_time = Utils.millisecond_time()
    total = end_time - start_time
    print(f"exec time: {total / 1000}")
    exit(0)


def data_init2():
    start_time = Utils.millisecond_time()
    for i in range(1, 6):
        body = {
            'id': str(i),
            'title': faker.name(),
            'age': faker.random.randint(20, 30)
        }
        result = es.index(index=test_index, doc_type='_doc', body=body)
        print(result)
    end_time = Utils.millisecond_time()
    total = end_time - start_time
    print(f"exec time: {total / 1000}")
    exit(0)



def test_search():
    global result, body
    list = es.search(index=test_index)
    for row in list.get('hits').get('hits'):
        del row['text']
        print(row['_source'])
    result = es.search(index='sc-ktj_6')  # index：选择数据库
    # print(result)
    body = {
        'from': 0,  # 从0开始
        'size': 2  # 取2个数据。类似mysql中的limit 0, 20。 注：size可以在es.search中指定，也可以在此指定，默认是10
    }
    # 定义过滤字段，最终只显示此此段信息
    filter_path = ['hits.hits._source.ziduan1',  # 字段1
                   'hits.hits._source.ziduan2']  # 字段2
    # 搜索
    list = es.search(index='sc-ktj_6', body=body)
    for row in list.get('hits').get('hits'):
        print(row['_source']['title'])




def delete_many():
    # delete many
    query = {
        "query": {
            "term": {
                "price": {
                    "value": 399
                }
            }
        }
    }
    result = es.delete_by_query(index='product', body=query)
    print(f"delete result: {result}")




def test_sth():
    global body, result
    content_size = 3000  # 设置一页的数据量
    size_cont = content_size
    next_id = 0  # 初始化next_id，每次循环是从  此数据 之后的第1个数据开始
    while size_cont == content_size:
        body = {
            "query": {
                "range": {
                    "update_time": {
                        "gte": "2019-10-14"
                    }
                }
            },
            'sort': {'ziduan2': 'asc'},  # 以ziduan2为next_id，需要先对其进行排序
            'search_after': [next_id],  # 从此数据之后的第1个数据开始，但不包含此数据
            'size': content_size  # 指定当前页数据量
        }
        filter_path = [
            'hits.hits._source.ziduan1',
            'hits.hits._source.ziduan2'
        ]
        rt = es.search(index='pv1', body=body, filter_path=filter_path)['hits']['hits']
        size_cont = len(rt)  # 更新循环条件：若数据量不等于指定的数据量，说明遍历到最后的一页数据了
        for result in rt:
            try:
                app_date = result['_source']['ziduan1']
            except:
                continue
            try:
                ziduan2 = result['_source']['ziduan2']
                next_id = ziduan2  # 更新next_id
            except:
                app_text = ''



def upsert2222():
    # delete many
    body = {
        'doc': {
            "id": "1",
            "title": "李丽丽2",
            "age": 28
        },
        'doc_as_upsert': True
    }
    result = es.update(index=test_index, id='9Xi_9IMBYYbrmfudTv4-', body=body)
    print(f"update result: {result}")


def update_by_query():
    # delete many
    doc = {
        "id": "1",
        "title": "李丽丽2",
        "age": 28
    }

    body = {
        "query": {
            "terms": {
                "id": ["8", "9"]
            }
        },
        'from': 0,  # 从0开始
        'size': 20  # 取2个数据。类似mysql中的limit 0, 20。 注：size可以在es.search中指定，也可以在此指定，默认是10
    }
    # 搜索
    list = es.search(index=test_index, body=body)

    print(list)

    count = list['hits']['total']['value']
    print(f"count: {count}")

    for row in list.get('hits').get('hits'):
        print(row['_source'])
        print(row['_id'])

    # result = es.update(index=test_index, id='9Xi_9IMBYYbrmfudTv4-', body={'doc': doc, 'doc_as_upsert': True})
    # print(f"update result: {result}")


def update_by_query_new():
    # 定义查询条件和更新的脚本
    query = {
        "script": {
            "source": "ctx._source.title = '田慧2'",
            "lang": "painless"
        },
        "query": {
            "term": {
                "id": "4"
            }
        }
    }

    # 执行更新操作
    response = es.update_by_query(index=test_index, body=query)

    # 输出响应
    print(response)


def upsert_bak():
    # delete many

    body = {
        "query": {
            "terms": {
                "id": ["8", "9"]
            }
        },
        'from': 0,  # 从0开始
        'size': 20  # 取2个数据。类似mysql中的limit 0, 20。 注：size可以在es.search中指定，也可以在此指定，默认是10
    }
    # 搜索

    list = es.search(index=test_index, body=body)

    print(list)

    count = list['hits']['total']['value']
    print(f"count: {count}")

    for row in list.get('hits').get('hits'):
        print(row['_source'])
        print(row['_id'])
        body_update = {
            'doc': {
                "id": "1",
                "title": "李丽丽2",
                "age": 28
            },
            'doc_as_upsert': True
        }
        es.update(index=test_index, id=id, body=body_update)

    # result = es.update(index=test_index, id='9Xi_9IMBYYbrmfudTv4-', body={'doc': doc, 'doc_as_upsert': True})
    # print(f"update result: {result}")


def upsert(index, filter, body):
    # 搜索
    # filter = {
    #     "query": {
    #         "terms": {
    #             "id": ["8", "9"]
    #         }
    #     },
    #     'from': 0,  # 从0开始
    #     'size': 20  # 取2个数据。类似mysql中的limit 0, 20。 注：size可以在es.search中指定，也可以在此指定，默认是10
    # }
    list = es.search(index=index, body=filter)

    count = list['hits']['total']['value']
    print(f"count: {count}")

    update_results = []
    if count > 0:
        for row in list.get('hits').get('hits'):
            print(row['_source'])
            print(row['_id'])
            # body_update = {
            #     'doc': {
            #         "id": "1",
            #         "title": "李丽丽2",
            #         "age": 28
            #     },
            #     'doc_as_upsert': True
            # }
            id = row['_id']
            body2 = {
                'doc': body,
                'doc_as_upsert': True
            }
            result = es.update(index=index, id=id, body=body2)
            update_results.append(result)
    else:
        result = es.index(index=index, doc_type='_doc', body=body)
        update_results.append(result)

    return update_results


def test_sth2222():
    global filter, body, es, conn
    ## upsert
    filter = {
        "query": {
            "terms": {
                "id": ["16"]
            }
        },
        'size': 1
    }
    body = {
        "id": "16",
        "title": "王琴6",
        "age": 267
    }
    es = ElasticsearchService()
    # result = es.upsert(index=test_index, filter=filter, body=body)
    # print(result)
    conn = es.get_connect()
    zz = conn.search(body={}, index='yuqing')
    print(zz)
    # [{'_index': 'dc-test2', '_type': '_doc', '_id': 'U3jc-YMBYYbrmfudlv4F', '_version': 1, 'result': 'created', '_shards': {'total': 2, 'successful': 1, 'failed': 0}, '_seq_no': 7, '_primary_term': 1}]
    # [{'_index': 'dc-test2', '_type': '_doc', '_id': 'U3jc-YMBYYbrmfudlv4F', '_version': 2, 'result': 'updated', '_shards': {'total': 2, 'successful': 1, 'failed': 0}, '_seq_no': 8, '_primary_term': 1}]
    # update_by_query()


def search_all():
    # 定义查询
    query = {
        "query": {
            "match_all": {}
        }
    }

    # 执行查询
    response = es.search(index=test_index, body=query)

    # 输出响应
    for doc in response['hits']['hits']:
        print(doc)


# search_all()

# test_update()

# data_init2()

# data_init()

# test_search()

# delete_many()

# test_sth()

# test_sth2222()

# update_by_query_new()

test_esid()