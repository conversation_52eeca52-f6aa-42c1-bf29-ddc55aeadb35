from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, Foreign<PERSON>ey, \
    select, func, update, delete
from sqlalchemy.orm import declarative_base, relationship, backref, Session
from sqlalchemy.types import CHAR
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import scoped_session
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum
from urllib.parse import quote_plus as urlquote


# 声明一个连接
userName = 'root'
password = 'Db1@neiwang'
host = '*************'
port = 3306
database = 'doc'
engine = create_engine(f'mysql+pymysql://{userName}:{urlquote(password)}@{host}:{port}/{database}?charset=utf8', echo=True, future=True)
#sqlalchemy.future.engine.Engine

# ---------
SessionFactory = sessionmaker(bind=engine)
session = scoped_session(SessionFactory)

cursor = session.execute('select * from DC_SiteTask')
result = cursor.fetchall()
for i in result:
    print(i)

exit(0)

# ---------------

# 声明ORM的一个基类并建立映射关系
Base = declarative_base()

class SexEnum(IntEnum):
    MAN = 1
    WOMEN = 2


# name为在数据库表中的名称
# type为数据类型
# comment为注释
# nullable为不能为空
# default为默认字段
# unique为唯一约束
class DCSiteTask(Base):
    """采集器任务表"""
    __tablename__ = 'DC_SiteTask'
    id = Column(Integer, name='id', primary_key=True)
    siteListId = Column(Integer, name='siteListId')
    mainTaskId = Column(Integer, name='mainTaskId')
    baseUrl = Column(String(32), nullable=True, name='baseUrl', comment="官网地址")
    url = Column(String(32), nullable=True, name='url', comment="需要采集的url")
    title = Column(String(32), nullable=True, name='title', comment="文章标题")
    taskStatus = Column(TINYINT(unsigned=True), default=0, name='taskStatus', comment="任务状态,  0 未执行, 1 执行完成 , 2执行失败")
    taskAt = Column(DateTime, default=datetime.now(), name='taskAt', comment="执行任务时间")
    analysisStatus = Column(TINYINT(unsigned=True), default=0, name='analysisStatus', comment="分析状态 0 未执行, 1,已执行, 2执行失败")
    httpStatusCode = Column(TINYINT(unsigned=True), default=0, name='httpStatusCode', comment="如果执行, 则返回执行任务后的 status状态")
    mongoCollection = Column(String(32), nullable=True, name='mongoCollection', comment="mongo Collection")
    mongoKey = Column(String(32), nullable=True, name='mongoKey', comment="存入mongodb 的key")
    retryTime = Column(TINYINT(unsigned=True), default=0, name='retryTime', comment="重试次数")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now(), name='updatedAt', comment="更新时间")
    esIndex = Column(String(32), nullable=True, name='esIndex', comment="es-index")
    esId = Column(String(32), nullable=True, name='esId', comment="es->_id")
    #siteMainTask = relationship('DCSiteMainTask', backref='siteTask', uselist=False)  # 一对一


class DCSiteMainTask(Base):
    """主任务表"""
    __tablename__ = 'DC_SiteMainTask'
    id = Column(Integer, name='id', primary_key=True)
    siteListId = Column(Integer, name='siteListId')
    taskDate = Column(Integer, name='taskDate')
    url = Column(String(32), nullable=True, name='url', comment="需要采集的url")
    listUrl = Column(String(32), nullable=True, name='listUrl', comment="需要采集的url")
    httpStatusCode = Column(TINYINT(unsigned=True), default=0, name='httpStatusCode', comment="任务状态,  0 未执行, 1 执行完成 , 2执行失败")
    listUrlCount = Column(Integer, name='listUrlCount', default=0)
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now(), name='updatedAt', comment="更新时间")
    #siteTask = relationship("DCSiteTask", backref=backref('siteMainTask', unelist=False))  # 两种方法



# 根据用户ID查询信息(根据主键进行查询)
idIndex = 1
session = Session(bind=engine, future=True)
userObj = session.get(DCSiteTask, idIndex)  # 根据主键进行查询
userObjDetail = '{} => {}'.format(userObj.id, userObj.title)
print(userObjDetail)


