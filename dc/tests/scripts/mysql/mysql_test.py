import json
import sys
import os

from sqlalchemy import select
from sqlalchemy.engine import Row, Result, CursorResult

from dc.common.alchemy import AlchemyJsonEncoder
from dc.common.func import dict2json
from dc.common.utils import Utils
from dc.models.model import DCSiteTask, DCSiteMainTask, User
from dc.services.mysql_service import MySQLService

mysql = MySQLService()
session = mysql.create_session()

## 通过 sqlalchemy 对 mysql 进行访问
def execute_sql():
    # 原生 sql 执行
    cursor_result: CursorResult = session.execute("SELECT * FROM test_user limit 1")
    row: Row = session.execute("SELECT * FROM test_user limit 1").fetchone()
    rows: list[Row] = session.execute("SELECT * FROM test_user limit 1").fetchall()

    for r in rows:
        #for k in r.keys():
        #    print(k)
        pp = dict2json(r)
        print(pp)


    exit(0)
    row: Row
    row = results.fetchall()
    print(row.name)
    exit(0)

    x: Row
    for x in results:
        print(x['name'])
        print(x.name)
        x2 = str(x[3])  # 时间类型处理
        print(x2)

def add():
    # 新增数据
    user = User(
        name="dsfdsfdds"
    )
    # 添加到session
    session.add(user)  # 添加一个
    # session.add_all([userObj, userObj])  # 添加多个
    session.commit()

def query():
    # query: BaseQuery = User.query
    stmt = select(User).where(User.id.in_([34, 35]))
    for user in session.scalars(stmt):
        print(user.to_json())

execute_sql()

#add()

#query()