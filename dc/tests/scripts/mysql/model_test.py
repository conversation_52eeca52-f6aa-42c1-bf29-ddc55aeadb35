import sys
import os
from flask import Flask
from sqlalchemy.testing import in_

from dc.services.dc_service import DcService
from dc.services.logging_service import LoggingService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.models.model import DCSiteTaskHistory, DCSiteTask
from dc.services.mysql_service import MySQLService
from flask_sqlalchemy import BaseQuery, Pagination
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete, and_, or_, text
from sqlalchemy.engine import Row, ChunkedIteratorResult
from sqlalchemy.orm import declarative_base, relationship, backref, Session, Query, Load, defer, undefer
from dc.common.alchemy import query2dict, print_alchemy
from dc.exts import db
from dc.models.model_test import User
from faker import Faker
from sqlalchemy.orm import load_only

faker = Faker(locale='zh_CN')  # 配置为中文

mysql_service = MySQLService()
session = mysql_service.create_session()

def db_add():
    with app.app_context():
        """增加数据"""
        user = User(name='zs22', age=20)
        db.session.add(user)
        db.session.flush()
        print(user.to_json())
        db.session.commit()
        query: BaseQuery = User.query
        # query.get_or_404()


def db_query():
    with app.app_context():

        """增加数据"""
        query: BaseQuery = User.query
        user: User = query.first()
        print(user.to_json())

        # 分页
        # query: BaseQuery = query.order_by(User.id).all()
        # print(query2dict(query))

        page: Pagination = query.filter(User.id > 25, User.id < 32).order_by(User.id).paginate(1, 10)
        print(query2dict(page.items))
        for user in page.items:
            print(user.to_json())

        print('--------------')
        data = query.filter(User.name.like('王%'), User.name.like('林%')).all()
        print_alchemy(data)

        print('------and--------')
        data = query.filter(and_(User.name.like('王%'), User.name.like('林%'))).all()
        print_alchemy(data)

        print('------or--------')
        data = query.filter(or_(User.name.like('王%'), User.name.like('林%'))).all()
        print_alchemy(data)

        print('------in--------')
        data = query.filter(User.name.in_(["林岩", "杨军"])).all()
        print_alchemy(data)

        print('------offset--------')
        data = query.filter(User.id > 20).offset(2).limit(5)
        print_alchemy(data)


        db.session.commit()
        exit(0)

        User.query.filter(User.name == 'jack').first()
        User.query.filter_by(name='jack').first()

        # User.query.filter(User.name like('%ac%')).all()
        # User.query.filter(User.name in_(['jack', 'mary', 'bruce'])).all()

        # and 逻辑与过滤查询：

        # User.query.filter(and_(User.name == 'jack', User.gender == 1)).all()

        # or 逻辑或过滤查询：

        # User.query.filter(or_(User.name == 'jack', User.name == 'mary')).all()

        # 按照id升序
        # User.query.filter(User.gender == 1).order_by(User.id).all()
        # 按照id降序
        #User.query.filter(User.gender == 1).order_by(User.id.desc()).all()

        # limit
        # 限制数量查询：

        User.query.filter(User.gender == 1).limit(3).all()

        # group_by
        # 根据条件分组查询：

        from sqlalchemy import func
        # 使用with_entities()方法来获取要在结果中返回的列
        # lable 给字段起别名
        # User.query.with_entities(User, func.count(*).lable('num')).group_by(User.gender).all()

        # offset
        # 根据指定的偏移量查询：

        #User.query.offset(2).limit(5).all()

        # func.random
        # 随机获取查询：

        User.query.order_by(func.random()).limit(10)

        exit(0)


def db_query_columns():
    query: Query = session.query(User)
    #users = query.options(load_only("id", "name")).filter_by(age=20).all()
    users = query.with_entities(User.id, User.name).filter_by(age=20).all()
    print(users)

    users = session.query(User.id, User.name).filter_by(age=20).all()
    print(users)
    print('---1----')
    users = session.query(User).options(load_only("age","name")).filter_by(age=20).first()
    print(users.__dict__)
    #zzz = query2dict(users)
    #print(zzz)


# db_add()

#db_query2()

#db_query_columns()


#query: Query = session.query(DCSiteTask)
#first = query.first()
#df: dict = first.to_dict()
#df['taskId'] = df['id']
#del df['id']
#print(df)
#
#dch = DCSiteTaskHistory(**df)
#session.add(dch)
#session.commit()
#print(dch.id)


taskId = 1

logService = LoggingService('dc.log')
dcService = DcService(logger=logService, session=session)

task: DCSiteTask = session.get(DCSiteTask, taskId)
dcService.log_sitetask(task, demo="task pull--")