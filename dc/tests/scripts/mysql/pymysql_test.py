import json
import random
import string
import sys
import os
import traceback
from dc.models.model_test import User
from dc.services.check_analysis import CheckAnalysis
from dc.common.webdriver_helper import Web<PERSON>riverHelper
from dc.tests.browser import Browser

from dc.common.webdriver_helper import Web<PERSON><PERSON>Helper
from dc.tests.browser import Browser
import requests
import sqlalchemy
from dc.common.alchemy import AlchemyJsonEncoder
from sqlalchemy.orm.decl_api import DeclarativeMeta
from dc.conf.settings import get_settings
from dc.services.redis_service import RedisService
from dc.services.mongo_service import MongoService
from dc.services.logging_service import LoggingService
from dc.common.utils import Utils
from time import sleep
from dc.models.model import DCSiteTask, DCSiteList, DCSiteMainTask
from dc.services.mysql_service import MySQLService

import pymysql


host = '*************'
port = 3306
db = 'doc'
user = 'root'
password = 'Db1@neiwang'


# ---- 用pymysql 操作数据库
def get_connection():
    conn = pymysql.connect(host=host, port=port, db=db, user=user, password=password)
    return conn


def check_it():

    conn = get_connection()

    # 使用 cursor() 方法创建一个 dict 格式的游标对象 cursor
    cursor = conn.cursor(pymysql.cursors.DictCursor)

    # 使用 execute()  方法执行 SQL 查询
    cursor.execute("select * from test_user")

    # 使用 fetchone() 方法获取单条数据.
    data = cursor.fetchone()

    print(data)

    # 关闭数据库连接
    cursor.close()
    conn.close()


if __name__ == '__main__':
    check_it()