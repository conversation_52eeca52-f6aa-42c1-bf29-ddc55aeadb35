import json
import random
import string
import sys
import os
import traceback
from sqlalchemy.engine import Row
from dc.models.model_test import User
from dc.services.check_analysis import CheckAnalysis
from dc.common.webdriver_helper import WebDriverHelper
from dc.tests.browser import Browser
from dc.common.webdriver_helper import WebDriverHelper
from dc.tests.browser import Browser
import requests
import sqlalchemy
from dc.common.alchemy import AlchemyJsonEncoder
from sqlalchemy.orm.decl_api import DeclarativeMeta
from dc.conf.settings import get_settings
from dc.services.redis_service import RedisService
from dc.services.mongo_service import MongoService
from dc.services.logging_service import LoggingService
from dc.common.utils import Utils
from time import sleep
from dc.models.model import DCSiteTask, DCSiteList, DCSiteMainTask
from dc.services.mysql_service import MySQLService
from dc.models.model import Base
import uuid
from requests.packages.urllib3.exceptions import InsecureRequestWarning

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS += ':HIGH:!DH:!aNULL'
#requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS = 'DEFAULT:@SECLEVEL=1'

mysql = MySQLService()
session = mysql.create_session()

redisService = RedisService('redis')
ms = MongoService()

redis_prefix = 'laravel_database_'

redis_analyse_key = f'{redis_prefix}dc-site-task-analyse'
redis_task_key = '00-test-key'

logService = LoggingService('dc.log')

def get_content(url, site_list: DCSiteList) -> requests.Response:
    charset = 'utf-8'

    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        }
        r = requests.get(url, headers=headers, verify=False, timeout=(6.05, 27))
        r.encoding = "utf-8"
        status_code = r.status_code

        if site_list.detailDcMethod == 2:  # 高级
            webdriver_url = "http://*************:4444"
            driver = WebDriverHelper.get_webdriver(Browser.Chrome)
            #driver = WebDriverHelper.get_webdriver(Browser.Chrome)
            driver.implicitly_wait(10)
            driver.get(url)
            result = driver.page_source
            charset = CheckAnalysis.get_char_set(driver)
            driver.quit()
            return {'status_code': status_code, 'text': result, 'charset': charset}
        else:
            return {'status_code': status_code, 'text': r.text, 'charset': charset}
    except Exception as ex:
        logService.dcPullLog(f"get_content exception:" + traceback.format_exc())
        return {'status_code': 500, 'text': '', 'charset': charset}


def getPageFrequency(site_list: DCSiteList):
    """
    获取页面间隔jfuj
    :param site_list:
    :return:
    """
    type = site_list.pageFrequencyType
    time_sleep = 2
    if type == 0:
        time_sleep = site_list.pageFrequency
    elif type == 1:
        time_sleep = site_list.pageFrequency * 60
    elif type == 2:
        time_sleep = site_list.pageFrequency * 60 * 60

    logService.dcPullLog(f"pageFrequencyType: {type} pageFrequency： {site_list.pageFrequency}  sleep: {time_sleep}")
    return time_sleep


def handle():

    logService.dcPullLog('启动数据采集拉取服务...')
    max_retry_time = get_settings('dc.task-pull.max_retry_time', 3)
    timeout = get_settings('dc.task-pull.timeout', 20)
    sleep_time = get_settings('dc.task-pull.sleep', 5)
    redis = redisService.get_client()

    while True:

        try:

            dt = Utils.showTime2()
            logService.dcPullLog('============================================' + dt)

            print(redis.llen(redis_task_key))

            taskId = redis.rpop(redis_task_key)
            print(f'redis_task_key: {redis_task_key}  taskId: {taskId}')
            if taskId is None:
                sleep(sleep_time)
                logService.dcPullLog(f"taskId is empty, sleep {sleep_time} ...")
                continue

            #session.commit()
            row: Row = session.execute(f"SELECT * FROM test_user where id={taskId}").fetchone()
            print(row)
            user: User = session.get(User, taskId)
            if user is None:
                logService.dcPullLog(f"taskId:-{taskId}- task empty")
                sleep(3)
            else:
                print(user.to_json())

            #print(taskId)
            sleep(2)

        except Exception as ex:
            logService.info("pull exception:" + traceback.format_exc())


handle()
