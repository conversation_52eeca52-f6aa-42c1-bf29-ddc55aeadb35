import json
import random
import string
import sys
import os
import traceback
from sqlalchemy import text
from dc.models.model_test import User
from dc.services.check_analysis import CheckAnalysis
from dc.common.webdriver_helper import WebDriverHelper
from dc.tests.browser import Browser

from dc.common.webdriver_helper import WebDriverHelper
from dc.tests.browser import Browser
import requests
import sqlalchemy
from dc.common.alchemy import AlchemyJsonEncoder
from sqlalchemy.orm.decl_api import DeclarativeMeta
from dc.conf.settings import get_settings
from dc.services.redis_service import RedisService
from dc.services.mongo_service import MongoService
from dc.services.logging_service import LoggingService
from dc.common.utils import Utils
from time import sleep
from dc.models.model import DCSiteTask, DCSiteList, DCSiteMainTask, DCGzhInfo
from dc.services.mysql_service import MySQLService
from dc.models.model import Base
import uuid
from requests.packages.urllib3.exceptions import InsecureRequestWarning

requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS += ':HIGH:!DH:!aNULL'
#requests.packages.urllib3.util.ssl_.DEFAULT_CIPHERS = 'DEFAULT:@SECLEVEL=1'

mysql = MySQLService()
session = mysql.create_session()

redisService = RedisService('redis')
ms = MongoService()

redis_prefix = 'laravel_database_'

redis_analyse_key = f'{redis_prefix}dc-site-task-analyse'
redis_task_key = '00-test-key'
redis_task_key = 'laravel_database_dc-site-task'

logService = LoggingService('dc.log')


def handle():

    logService.dcPullLog('启动数据采集拉取服务...')
    max_retry_time = get_settings('dc.task-pull.max_retry_time', 3)
    timeout = get_settings('dc.task-pull.timeout', 20)
    sleep_time = get_settings('dc.task-pull.sleep', 10)
    redis = redisService.get_client()

    try:

        dt = Utils.showTime2()
        logService.dcPullLog('============================================' + dt)

        crawledAt = Utils.time_days(days=0, only_day=True)
        sql = f"status=1 and isSubscribe=1 and (crawledAt is null or crawledAt < '{crawledAt}')"
        print(sql)
        data: list[DCGzhInfo] = session.query(DCGzhInfo).where(text(sql)).all()

        for item in data:
            print(item.id, item.crawledAt)


    except Exception as ex:
        logService.info("pull exception:" + traceback.format_exc())


handle()
