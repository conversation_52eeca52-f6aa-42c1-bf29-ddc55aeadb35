from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete
from sqlalchemy.engine import Row, ChunkedIteratorResult
from sqlalchemy.orm import declarative_base, relationship, backref, Session
from sqlalchemy.types import CHAR
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum

# 声明一个连接
userName = 'root'
password = 'hbd123456'
host = '127.0.0.1'
port = 3306
database = 'testdb'
engine = create_engine(f'mysql+pymysql://{userName}:{password}@{host}:{port}/{database}?charset=utf8', echo=True, future=True)

# 声明ORM的一个基类并建立映射关系
Base = declarative_base()


class SexEnum(IntEnum):
    MAN = 1
    WOMEN = 2


# name为在数据库表中的名称
# type为数据类型
# comment为注释
# nullable为不能为空
# default为默认字段
# unique为唯一约束
class User(Base):
    """用户信息表格"""
    __tablename__ = 'account_user'
    id = Column(Integer, name='id', primary_key=True)
    user_name = Column(String(32), nullable=True, name='user_name', comment="user_name", unique=True)
    password = Column(String(512), nullable=True, name='password', comment="password")
    real_name = Column(String(16), nullable=False, name='real_name', comment="real_name")
    sex = Column(Enum(SexEnum), default=None, name='sex', comment="sex")
    age = Column(TINYINT(unsigned=True), default=0, name='age', comment="age")
    create_at = Column(DateTime, default=datetime.now(), name='create_at', comment="create_at")
    is_valid = Column(Boolean, default=True, name='is_valid', comment="is_valid")
    profile = relationship('UserProfile', backref='user', uselist=False)  # 一对一


class UserAddress(Base):
    """地址信息表格"""
    __tablename__ = 'account_user_address'
    id = Column(Integer, name='id', primary_key=True)
    area = Column(String(256), nullable=False, name='area', comment="area")
    phone_num = Column(CHAR(11), name='phone_num', comment="phone_num")
    remark = Column(String(512), name='remark', comment="remark")
    is_valid = Column(Boolean, default=True, name='is_valid', comment="is_valid")

    create_at = Column(DateTime, default=datetime.now(), name='create_at', comment="create_at")
    # user_id = Column(Integer, ForeignKey(User.id), comment="关联")  # 进行ORM关联一对多
    user_id = Column(Integer, ForeignKey(User.id, ondelete="CASCADE"), comment="关联")  # 进行ORM关联一对多，操作一：指定删除后操作

    # user = relationship("User", backref='address')  # 一对多经典
    # user = relationship("User", backref=backref('address', lazy='dynamic'))  # 可以执行子查询
    user = relationship("User", backref=backref('address', lazy='dynamic', cascade="all,delete"))  # 可以执行子查询 操作二：指定删除后操作


class UserProfile(Base):
    """用户详细信息表格"""
    __tablename__ = 'account_user_profile'
    id = Column(Integer, primary_key=True)
    hobby = Column(String(255), nullable=False, name='hobby', comment="hobby")
    user_id = Column(Integer, ForeignKey(User.id), comment="user_id")
    # user = relationship("User", backref=backref('profile', unelist=False))  # 两种方法

# 同步关系创建/删除数据库
#Base.metadata.create_all(engine)  # 创建表
#Base.metadata.drop_all(engine)  # 删除表


# 添加到session
session = Session(bind=engine, future=True)

def test_add():
    # 新增数据
    # 构建一个对象
    userObj = User(
        user_name='Jack' + str(datetime.now()),
        password='123456',
        real_name='杰克',
        sex=1
    )
    # 添加到session
    session = Session(bind=engine, future=True)
    session.add(userObj)  # 添加一个
    # session.add_all([userObj, userObj])  # 添加多个
    session.commit()

#test_add()

def test_add2():
    # 这个部分有点难 理清楚顺序
    # 根据一对一，一对多的关系添加数据
    # 首先是创建主表的一份数据
    userObj = User(
        user_name='Rose' + str(datetime.now()),
        password='123456',
        real_name='肉丝',
        sex=2
    )
    # 一个人可以对应多个地址
    userObj.address.append(
        UserAddress(user=userObj, area="地址01", phone_num="11111111111")
    )
    userObj.address.append(
        UserAddress(user=userObj, area="地址02", phone_num="11111111111")
    )
    session.add(userObj)  # 添加这个人的时候同时会添加2份数据
    profile = UserProfile(user=userObj, hobby='宝石')  # 一对一的详细信息
    session.add(profile)
    session.commit()  # 在提交之前会自动进行回滚  如果使用 session.begin() session.rollback()
    session.close()

#test_add2()

def search():
    idIndex = 1
    userObj = session.get(User, idIndex)  # 根据主键进行查询
    userObjDetail = '{} => {}'.format(userObj.user_name, userObj.real_name)
    print(userObjDetail)
    # 使用select查询全部/第一个
    stmt = select(User)
    rows = session.execute(stmt)  # 要全部
    print(rows)

    rowOne = session.execute(stmt).fetchone()  # 只要第一个
    print(rowOne)
    rowOne = session.execute(stmt).scalars().all()  # 只要第一个
    print(rowOne[0])


    maps = session.execute(stmt).mappings()  # 返回的是字典
    print(maps)
    rowOnlySome = session.execute(select(User.id, User.user_name))  # 只要指定的几个字段
    print(rowOnlySome)

    rowStillOne = session.execute(stmt).scalars().first()  # 只要第一个且为ORM的实体对象
    print(rowStillOne)
    # -----------
    # 使用SQLAlchemy进行条件查询
    # 使用where条件查询
    # User.id > 12
    # User.id.between(8,12)
    # User.id.is_(None)
    # User.id.is_not(None)
    # User.id.in_((9, 12))
    # or_(User.id == 9, User.id == 12)
    # User.id.like("Hello%")
    # User.id.like("Hello_")
    # where(xxxx).where(xxxx)可以连续或者and_(User.id == 9, User.password == 123456)
    # or_(and_(User.id == 9, User.password == 123456), and_(User.id == 9, User.password == 123456)) # 并集
    stmt = select(User).where(User.id == 1)
    rowWhere = session.execute(stmt).scalar_one_or_none()  # 没有就返回None

#search()

def test_update():
    # 修改数据
    # 修改单条信息
    # 拿到对应的-》修改属性-》添加进入-》提交事务
    oneObj = session.get(User, 1)
    oneObj.password = 1111
    session.add(oneObj)
    session.commit()

#test_update()


result = session.execute(select(User).order_by(User.id))
one = result.fetchone()
print(one)

result = session.execute(select(User)).scalars().first()
print(result)

stmt = select(User)

rows: ChunkedIteratorResult
rows = session.execute(stmt)  # 要全部
pp = rows.fetchmany(1)

maps = session.execute(stmt).mappings()  # 返回的是字典

exit(0)

#<sqlalchemy.engine.result.ChunkedIteratorResult object at 0x7fa5b79c2220>
#stmt = select(User).where(User.name.in_(["spongebob", "sandy"]))
stmt = select(User).where(User.id == 1)
for user in session.scalars(stmt):
    print(user.user_name)


