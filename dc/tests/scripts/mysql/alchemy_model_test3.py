import sys
import os
from flask import Flask
from sqlalchemy.testing import in_

from dc.services.mysql_service import MySQLService
from dc.tests.test_simple_basics import SimpleBasicsTestCase

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from flask_sqlalchemy import BaseQuery, Pagination
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete, and_, or_
from sqlalchemy.engine import Row, ChunkedIteratorResult
from sqlalchemy.orm import declarative_base, relationship, backref, Session, Query
from sqlalchemy.types import CHAR
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum
import random
import dc.configs
from dc.common.alchemy import query2dict, print_alchemy
from dc.exts import db
from dc.models.model import User
from faker import Faker
import unittest

faker = Faker(locale='zh_CN')  # 配置为中文

class TestClass3(SimpleBasicsTestCase):

    def test_db_query(self):
        """增加数据"""
        mysql_service = MySQLService()
        session = mysql_service.create_session()
        stmt = select(User).where(User.id.in_([23, 24]))
        for user in session.scalars(stmt):
            print(user.to_json())





