import os
import sys

import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.models.model import DCGzhWechatRelation

# mysql_service = MySQLService()
# session = mysql_service.create_session()
#
# w = 'wxid_4feo8h9fgofd22'
# gzh_id = 'gh_e3222cc0c7b91'
# h = session.query(DCGzhWechatRelation).where(DCGzhWechatRelation.wxId == w, DCGzhWechatRelation.gzhId == gzh_id).first()
# print(h)
# if h == None:
#     print('not have')
# else:
#     print(' have')


a = "aaa,bbb,ccc"
r = str.split(a, ',')
print(r, type(r))
