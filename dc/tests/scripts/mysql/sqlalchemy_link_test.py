# -*- coding:utf-8 -*-
# @Function  : mysql 数据库断开自动连接测试
# <AUTHOR> wjh
# @Time      : 2023/8/31
# Version    : 1.0

import logging
import time

from dc.models.model import DCSiteTask
from dc.services.mysql_service import MySQLService

mysql = MySQLService()
session = mysql.create_session()
idIndex = 435952

while True:
    try:
        logger = logging.getLogger('sqlalchemy')

        userObj: DCSiteTask = session.get(DCSiteTask, idIndex)  # 根据主键进行查询
        print(userObj.title)

    except Exception as ex:
        print(str(ex))
        session.rollback()

    finally:
        idIndex += 1
        time.sleep(5)
