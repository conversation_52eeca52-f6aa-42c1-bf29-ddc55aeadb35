import logging
import sys
import os

from flask_sqlalchemy import BaseQuery
from sqlalchemy import select
from sqlalchemy.orm import Query

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

from dc.models.model import DCSiteTask, DCSiteMainTask
from dc.services.mysql_service import MySQLService
from dc.models.model import User

mysql = MySQLService()
session = mysql.create_session()


logger = logging.getLogger('sqlalchemy')



# 根据用户ID查询信息(根据主键进行查询)
idIndex = 1
userObj = session.get(DCSiteTask, idIndex)  # 根据主键进行查询

# 新增数据
user = User(
    name="dsfdsfdds"
)

# 添加到session
session.add(user)  # 添加一个
# session.add_all([userObj, userObj])  # 添加多个
session.commit()

# query: BaseQuery = User.query
stmt = select(User).where(User.id.in_([34, 35]))
for user in session.scalars(stmt):
    print(user.to_json())


