import sys
import os
from flask import Flask
from sqlalchemy.testing import in_

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from flask_sqlalchemy import BaseQuery, Pagination
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete, and_, or_
from sqlalchemy.engine import Row, ChunkedIteratorResult
from sqlalchemy.orm import declarative_base, relationship, backref, Session, Query
from sqlalchemy.types import CHAR
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum
import random
import dc.configs
from dc.common.alchemy import query2dict, print_alchemy
from dc.exts import db
from dc.models.model import User
from faker import Faker
import unittest

app = Flask(__name__)
app.config.from_object(dc.configs)
db.init_app(app)

faker = Faker(locale='zh_CN')  # 配置为中文

class TestClass(unittest.TestCase):

    def db_add(self):
        with app.app_context():
            """增加数据"""
            user = User(name='zs22', age=20)
            db.session.add(user)
            db.session.flush()
            print(user.to_json())
            db.session.commit()
            query: BaseQuery = User.query
            # query.get_or_404()


    def db_query(self):
        with app.app_context():
            """增加数据"""
            query: BaseQuery = User.query
            user: User = query.first()
            print(user.to_json())


    def db_dict(self):
        ss = {
            'id':1,
            "name":'wjh'
        }
        #self.assertIs()


    def db_query2(self):
        with app.app_context():

            """增加数据"""
            query: BaseQuery = User.query
            user: User = query.first()
            print(user.to_json())

            exit(0)

            # 分页
            # query: BaseQuery = query.order_by(User.id).all()
            # print(query2dict(query))

            page: Pagination = query.filter(User.id > 25, User.id < 32).order_by(User.id).paginate(1, 10)
            print(query2dict(page.items))
            for user in page.items:
                print(user.to_json())

            print('--------------')
            data = query.filter(User.name.like('王%'), User.name.like('林%')).all()
            print_alchemy(data)

            print('------and--------')
            data = query.filter(and_(User.name.like('王%'), User.name.like('林%'))).all()
            print_alchemy(data)

            print('------or--------')
            data = query.filter(or_(User.name.like('王%'), User.name.like('林%'))).all()
            print_alchemy(data)

            print('------in--------')
            data = query.filter(User.name.in_(["林岩", "杨军"])).all()
            print_alchemy(data)

            print('------offset--------')
            data = query.filter(User.id > 20).offset(2).limit(5)
            print_alchemy(data)


            db.session.commit()
            exit(0)

            User.query.filter(User.name == 'jack').first()
            User.query.filter_by(name='jack').first()

            # User.query.filter(User.name like('%ac%')).all()
            # User.query.filter(User.name in_(['jack', 'mary', 'bruce'])).all()

            # and 逻辑与过滤查询：

            # User.query.filter(and_(User.name == 'jack', User.gender == 1)).all()

            # or 逻辑或过滤查询：

            # User.query.filter(or_(User.name == 'jack', User.name == 'mary')).all()

            # 按照id升序
            # User.query.filter(User.gender == 1).order_by(User.id).all()
            # 按照id降序
            #User.query.filter(User.gender == 1).order_by(User.id.desc()).all()

            # limit
            # 限制数量查询：

            User.query.filter(User.gender == 1).limit(3).all()

            # group_by
            # 根据条件分组查询：

            from sqlalchemy import func
            # 使用with_entities()方法来获取要在结果中返回的列
            # lable 给字段起别名
            # User.query.with_entities(User, func.count(*).lable('num')).group_by(User.gender).all()

            # offset
            # 根据指定的偏移量查询：

            #User.query.offset(2).limit(5).all()

            # func.random
            # 随机获取查询：

            User.query.order_by(func.random()).limit(10)

            exit(0)


