import time
import csv
import random
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait

search_info = {
    'city': {
        '全国': 100010000,
        '北京': 101010100,
        '上海': 101020100,
        '广州': 101280100,
        '深圳': 101280600,
        '杭州': 101210100,
        '天津': 101030100,
        '西安': 101110100,
        '苏州': 101190400,
        '武汉': 101200100,
        '厦门': 101230200,
        '长沙': 101250100,
        '成都': 101270100,
        '郑州': 101270100,
        '重庆': 101040100,
        '太原': 101100100,
    },
    'position': {
        '会计': 150301,
        '总账会计': 150311,
        '成本会计': 150310,
        '结算会计': 150304,
        '税务外勤会计': 150313,
        '建筑/工程会计': 150312,
        '审计': 150306,
        '税务': 150305,
    },
    'educational': {
        '不限': 0,
        '初中及以下': 209,
        '中砖/中级': 208,
        '高中': 206,
        '大专': 202,
        '本科': 203,
        '硕士': 204,
        '博士': 205
    }
}

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/89.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/89.0 Safari/537.36",
]


def get_sign():
    data = {
        "secret_id": "oduef8qw85fcu8yxtrfl",
        "secret_key": "9d7e4c5cmj7lc26fkeikuz240dpw854v"
    }

    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    response = requests.post(url='https://auth.kdlapi.com/api/get_secret_token', data=data, headers=headers)
    sign = None
    if response.status_code == 200:
        result = response.json()
        code = result.get('code')
        if code == 0:
            sign = result.get('data').get('secret_token')
    return sign


def get_proxies_from_kuaidaili():
    try:
        # response = requests.get(f"https://dps.kdlapi.com/api/getdps?"
        #                         f"secret_id=oduef8qw85fcu8yxtrfl&num=500&signature={get_sign()}")

        response = requests.get('https://dps.kdlapi.com/api/getdps/?secret_id=oduef8qw85fcu8yxtrfl&num=10&signature=9d7e4c5cmj7lc26fkeikuz240dpw854v&pt=1&format=json&sep=2')
        if response.status_code == 200:
            # username = "17810335597"
            # password = "1234Qwer"

            username = "d1719292114"
            password = "e4qc3efx"
            result = response.json()
            proxys = result.get('data').get('proxy_list')
            return [f'http://{username}:{password}@{proxy}' for proxy in proxys if proxy]
        else:
            print(f"Error: Failed to fetch proxies from kuaidaili API. Status code {response.status_code}")
            return []
    except Exception as e:
        print(f"Error: {e}")
        return []


# 从代理池中随机选择一个代理
def get_random_proxy(proxy_pool):
    return random.choice(proxy_pool) if proxy_pool else None


def remove_webdriver_trace(driver):
    # 执行 JavaScript 移除 WebDriver 检测痕迹
    with open("stealth.min.js") as f:
        js_code = f.read()
    driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {"source": js_code})
    # pass

# 设置Chrome代理
def set_proxy_for_chrome(proxy):
    options = Options()
    options.add_argument(f'--proxy-server={proxy}')
    options.add_argument(f"user-agent={random.choice(USER_AGENTS)}")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--ignore-certificate-errors")
    # 禁用 WebDriver 痕迹
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    return options


# 检查代理是否有效
def test_proxy(proxy):
    try:
        response = requests.get('https://www.zhipin.com', proxies={'http': proxy, 'https': proxy}, timeout=5)
        return response.status_code == 200
    except requests.RequestException:
        return False


# 设置爬虫
def scrape_jobs(city, position, educational, max_pages):
    # 获取代理池
    # proxy_pool = get_proxies_from_kuaidaili()
    # if not proxy_pool:
    #     print("没有获取到有效的代理IP，程序退出")
    #     return
    #
    # # 在代理池中选择一个有效代理
    # proxy = get_random_proxy(proxy_pool)
    # if proxy and not test_proxy(proxy):
    #     print(f"初始代理 {proxy} 无效，正在切换...")
    #     proxy = None  # 置空，以便后续轮换代理

    proxy = None

    city_code = search_info.get('city').get(city)
    position_code = search_info.get('position').get(position)
    educational_code = search_info.get('educational').get(educational)
    url = f'https://www.zhipin.com/web/geek/job?city={city_code}&position={position_code}&degree={educational_code}&jobType=1901'

    output_file = f'{city}_{position}_{educational}_job_info.csv'

    # 设置代理并初始化Chrome WebDriver
    driver = None
    while not driver:
        if proxy:
            options = set_proxy_for_chrome(proxy)
            try:
                service = Service(
                    "/usr/local/bin/chromedriver")
                driver = webdriver.Chrome(service=service, options=options)
                remove_webdriver_trace(driver)
                print(f"使用代理 {proxy} 启动浏览器")
            except Exception as e:
                print(f"代理 {proxy} 无效，正在切换代理。错误: {e}")
                proxy = get_random_proxy(proxy_pool)
                if not test_proxy(proxy):
                    print(f"代理 {proxy} 无效，再次切换...")
                continue
        else:
            proxy = None
            if test_proxy(proxy):
                options = set_proxy_for_chrome(proxy)
                service = Service(
                    "/usr/local/bin/chromedriver")
                driver = webdriver.Chrome(service=service, options=options)
                remove_webdriver_trace(driver)
                print(f"使用代理 {proxy} 启动浏览器")
            else:
                print(f"代理 {proxy} 无效，尝试下一个代理...")
                proxy = None
                continue

    width = random.randint(800, 1920)
    height = random.randint(600, 1080)
    driver.set_window_size(width, height)

    driver.get(url)
    driver.implicitly_wait(7)

    with open(output_file, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['企业名称', '职位名称', '薪资', '工作地点', '工作年限', '学历', '企业类型', '行业', '企业规模',
                         '职位关键字', '职位详情'])

        def job_info():
            lis = driver.find_elements(By.CSS_SELECTOR, '.job-card-wrapper')
            for li in lis:
                company_name = li.find_element(By.CLASS_NAME, 'company-name').text
                job_name = li.find_element(By.CLASS_NAME, 'job-name').text
                salary = li.find_element(By.CLASS_NAME, 'salary').text
                job_area = li.find_element(By.CLASS_NAME, 'job-area').text
                company_tag_list = li.find_element(By.CLASS_NAME, 'company-tag-list')
                industry, company_size = '', ''
                if company_tag_list:
                    company_tags = company_tag_list.find_elements(By.TAG_NAME, 'li')
                    industry = company_tags[0].text
                    company_size = company_tags[-1].text

                tag_list = li.find_element(By.CLASS_NAME, 'tag-list').text
                work_of_year, educational_requirements = '经验不限', '学历不限'
                if tag_list:
                    work_of_year, educational_requirements = tag_list.rsplit("\n", 1)

                href = li.find_element(By.CSS_SELECTOR, '.job-card-body a').get_attribute('href')
                original_window = driver.current_window_handle
                driver.execute_script("window.open(arguments[0]);", href)

                WebDriverWait(driver, 10).until(EC.number_of_windows_to_be(2))
                driver.switch_to.window(driver.window_handles[-1])

                detail_info, company_type, job_keyword_list = '', '', ''
                try:
                    detail_info = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CLASS_NAME, 'job-sec-text'))
                    ).text

                    company_type = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CLASS_NAME, 'company-type'))
                    ).text
                    if company_type:
                        _, company_type = company_type.rsplit("\n", 1)

                    company_name_all = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '.level-list-box li.company-name'))
                    ).text

                    if company_name_all:
                        _, company_name = company_name_all.rsplit("\n", 1)

                    job_keyword_list = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CLASS_NAME, 'job-keyword-list'))
                    ).text
                except Exception as ex:
                    print(f"Failed to extract details for {href}")
                writer.writerow([company_name, job_name, salary, job_area, work_of_year,
                                 educational_requirements, company_type, industry, company_size,
                                 job_keyword_list, detail_info])

                driver.close()
                driver.switch_to.window(original_window)

            try:
                driver.find_element(By.CLASS_NAME, 'ui-icon-arrow-right').click()
            except Exception as ex:
                print(f"No more pages or navigation failed. error:{ex}")

        for page in range(1, max_pages + 1):
            print(f"正在爬取第{page}页")
            time.sleep(random.uniform(1, 3))
            job_info()
        print("爬取完成")

    driver.quit()


scrape_jobs(city='北京', position='会计', educational='不限', max_pages=1)

# 在命令行执行:python3 boss6.py --city 北京 --position 会计 --educational 大专 --max_pages 1
# if __name__ == "__main__":
#     parser = argparse.ArgumentParser(description='Job scraping script')
#     parser.add_argument('--city', type=str, required=True, default='北京', help='City name')
#     parser.add_argument('--position', type=str, required=True, default='会计', help='Job position')
#     parser.add_argument('--educational', type=str, required=True, default='大专', help='Educational requirement')
#     parser.add_argument('--max_pages', type=int, required=True, default=1, help='Number of pages to scrape')
#
#     args = parser.parse_args()
#
#     scrape_jobs(city=args.city, position=args.position, educational=args.educational, max_pages=args.max_pages)
