"""
beanstalk producer
:author wjh
:date 2023-01-06
"""

import json
import random
import sys
import os
import time
import greenstalk
from dc.services.beanstalk_service import BeanstalkService

# client = greenstalk.Client(('192.168.1.133', 11300), use='dc', watch=['dc'])

while True:
    bs = BeanstalkService(name="beanstalk_126")
    client = bs.get_client(use='test', watch=['test'])
    msg = 'hello-' + str(random.randint(100, 999))
    delay = random.randint(10, 10000)
    print(f"msg: {msg}")
    print(f"delay: {delay}")
    client.put(msg, delay=delay)
    client.close()
    time.sleep(1)
