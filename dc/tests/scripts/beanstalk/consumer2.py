"""
beanstalk consumer
:author wjh
:date 2023-01-06
"""

import json
import random
import sys
import os
import time
import greenstalk
from dc.services.beanstalk_service import BeanstalkService

# client = greenstalk.Client(('192.168.1.133', 11300))
bs = BeanstalkService(name="beanstalk_126")
client = bs.get_client(use='test', watch=['test'])

while True:
    job = client.reserve()
    print(job.body)
    client.delete(job)
    # client.release(job)
    time.sleep(1)



