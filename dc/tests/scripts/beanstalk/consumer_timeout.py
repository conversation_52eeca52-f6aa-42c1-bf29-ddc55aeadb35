"""
beanstalk timeout 测试
:author wjh
:date 2023-01-06
"""

import json
import random
import sys
import os
import time
import greenstalk
from dc.services.beanstalk_service import BeanstalkService

# client = greenstalk.Client(('192.168.1.133', 11300))
bs = BeanstalkService()
client = bs.get_client(use='test', watch=['test'])

job = client.reserve()
print(job.body)
# client.delete(job)
# client.release(job)

stats = client.stats_job(job)
pri = int(stats['pri']) + 1
client.release(job, pri)
exit(0)

no = 0
while True:
    print('-' * 50)
    time.sleep(1)
    no += 1
    stats = client.stats_job(job)
    print(f"no: {no}")
    print(f"no: {no} job :{job.body}")
    print(f"no: {no} state:{stats}")


