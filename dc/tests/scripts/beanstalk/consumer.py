"""
beanstalk consumer
:author wjh
:date 2023-01-06
"""

import json
import random
import sys
import os
import time
import greenstalk
from dc.services.beanstalk_service import BeanstalkService

# client = greenstalk.Client(('192.168.1.133', 11300))
bs = BeanstalkService()
client = bs.get_client(use='test', watch=['test'])

while True:
    job = client.reserve()
    print(job.body)
    # client.delete(job)
    # client.release(job)
    no = 0
    while True:
        time.sleep(1)
        no += 1
        print(f"no: {no}")


