# -*- coding:utf-8 -*-
# @Function  : 监控报警服务测试
# <AUTHOR>
# @Time      : 2024/8/23
# Version    : 1.0

from datetime import datetime
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, JobExecutionEvent
from dateutil import utils
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor, ProcessPoolExecutor
from dc.services.logging_service import LoggingService
from dc.services.monitor_alert_service import MonitorAlertService

logger = LoggingService(logfile="monitor_alert_test.log")
mas = MonitorAlertService({'logger': logger})


def print_time():
    """ 输出当前时间 """
    current_time = datetime.now()
    formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
    logger.debug(f"当前时间:{formatted_time}")


# 数据存储
jobstores = {
    'default': SQLAlchemyJobStore(url='sqlite:///jobs.sqlite')
}


def log_monitor_job(**kwargs):
    """
    日志监控任务
    :param kwargs: 参数
    :return: 执行结果
    :rtype: bool
    """
    try:
        logger.debug(kwargs)
        print_time()
        mas.log_monitor_save()
        logger.info("Task log_monitor_job executed!")
        return True

    except Exception as ex:
        logger.error(f"Task log_monitor_job failed with exception:{str(ex)}")
        return False


def data_monitor_job(**kwargs):
    """
    数据监控任务
    :param kwargs: 参数
    :return: 执行结果
    :rtype: bool
    """
    try:
        logger.debug(kwargs)
        print_time()
        mas.data_monitor_save()
        logger.info("Task data_monitor_job executed!")
        return True

    except Exception as ex:
        logger.error(f"Task data_monitor_job failed with exception:{str(ex)}")
        return False


def log_test_job(**kwargs):
    """
    日志测试任务
    :param kwargs: 参数
    :return: 执行结果
    :rtype: bool
    """
    try:
        logger.debug(kwargs)
        print_time()
        mas.log_test_save("test log save")
        logger.info("Task log_test_job executed!")
        return True

    except Exception as ex:
        logger.error(f"Task data_monitor_job failed with exception:{str(ex)}")
        return False


def job_listener(event: JobExecutionEvent):
    """ 任务执行监听器 """
    if event.exception:
        logger.error(f"The job {event.job_id} failed with exception: {event.exception}")
    else:
        logger.info(f"The job {event.job_id} executed successfully")


# 任务执行器
executors = {
    'default': ThreadPoolExecutor(20),
    'processpool': ProcessPoolExecutor(5)
}

# scheduler = BlockingScheduler(jobstores=jobstores, executors=executors)
scheduler = BlockingScheduler(logger=logger)
# scheduler.add_job(log_monitor_job, trigger=IntervalTrigger(minutes=60), max_instances=1, kwargs={"method": "log_monitor_job", "max_instances": "1"})
# scheduler.add_job(data_monitor_job, trigger=IntervalTrigger(minutes=5), max_instances=1, kwargs={"method": "data_monitor_job", "max_instances": "1"})
scheduler.add_job(log_test_job, trigger=IntervalTrigger(seconds=5), max_instances=1, kwargs={"method": "data_monitor_job", "max_instances": "1"})
# scheduler.add_job(data_monitor_job, trigger=CronTrigger(minute='*/5'), max_instances=1, kwargs={"method": "data_monitor_job", "max_instances": "1"})
# scheduler.add_job(my_task, trigger=CronTrigger.from_crontab("*/1 * * * *"))

# 添加事件监听器
scheduler.add_listener(job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)

# 启动调度器
try:
    logger.info('scheduler start...')

    # 启动后立即执行所有任务
    # for job in scheduler.get_jobs():
    #     logger.info(f'start execute {job.name} ...')
    #     job.func()
    #     logger.info(f'finished execute {job.name}')

    # 启动调度器，但保持暂停状态
    scheduler.start()
    logger.info(f'scheduler started')

except (KeyboardInterrupt, SystemExit):
    logger.info("system exit")

except Exception as ex:
    logger.error(f"scheduler exception {str(ex)}")
