# -*- coding:utf-8 -*-
# @Function  :
# <AUTHOR>
# @Time      : 2024/8/26
# Version    : 1.0

import os.path
import time

from dc.services.mysql_service import MySQLService
from dc.models.model import *
from dc.services.logging_service import LoggingService
from dc.services.monitor_alert_service import MonitorAlertService
from dc.conf.settings import *


def print_time():
    current_time = datetime.now()
    formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
    logger.debug(f"当前时间:{formatted_time}")


logger = LoggingService(logfile='test_alert.log')
mas = MonitorAlertService({'logger': logger})

# mas.log_monitor_save()
mas.data_monitor_save()

exit(0)

for i in range(1, 30):
    zz = mas.can_trigger_alert(1, 10)
    print(zz)
    time.sleep(1)
    print_time()

exit(0)

# 调用函数判断当前时间是否在静默期
quiet_period = get_settings2('elk', 'quiet_period', [])
is_quiet_period = mas.is_quiet_period(quiet_period)

mysql = MySQLService()
session = mysql.create_session()


def test_get():
    alert: DMMonitorAlert = session.query(DMMonitorAlert).get(12)
    print(alert.to_dict())
    print(alert.task.to_dict())
    for detail in alert.details:
        print(detail.to_dict())


# test_get()


def test_add():
    a: DMMonitorAlert = DMMonitorAlert()
    a.task_id = 12
    a.alert_type = 1
    a.status = 1
    session.add(a)
    session.commit()


# test_add()

session.close()
