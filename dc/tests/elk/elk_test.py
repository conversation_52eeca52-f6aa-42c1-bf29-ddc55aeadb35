# -*- coding:utf-8 -*-
# @Function  : ELK 信息提示
# <AUTHOR> wjh
# @Time      : 2023/8/31
# Version    : 1.0

import json
import time
import traceback
from datetime import datetime, timedelta
from dc.models.model import ELKError
from dc.services.mysql_service import MySQLService
from dc.services.qianxun_service import QianxunService
from dc.services.weixin_service import WeixinService
from dc.services.logging_service import LoggingService
from dc.conf.defines import *
from confluent_kafka import Consumer, Producer, KafkaException, Message, KafkaError, TopicPartition
from jwtools.func import *
from dc.conf.settings import get_settings, RUN_MODE
from dc.services.kafka_service import KafkaService
from dc.services.elasticsearch_service import ElasticsearchService

# 定义时间范围的字符串
exec_start_time = "7:00:00"
exec_end_time = "21:00:00"
# mode = RUN_MODE
mode = 'prod'
elk_error_topic = f'^elk_log_{mode}_*'

logger = LoggingService('elk_alert.log')
kafka = KafkaService(run_model="prod")

ess = ElasticsearchService(name='elasticsearch_elk', run_model='prod')
es = ess.get_connect()

mysql = MySQLService(run_model='prod')
session = mysql.create_session()


def error_cb(err):
    logger.error(f"Error: {err}")


def stats_cb(err):
    # logger.info(f"stats: {err}")
    pass


def on_commit(err, tps):  # type: (KafkaError, list(TopicPartition)) -> None
    # logger.info(f"on_commit: {err}")
    pass


def execute_time_range(start_time_str, end_time_str) -> bool:
    current_time = datetime.datetime.now().time()

    # 将字符串转换为time对象
    start_time = datetime.datetime.strptime(start_time_str, "%H:%M:%S").time()
    end_time = datetime.datetime.strptime(end_time_str, "%H:%M:%S").time()

    if start_time <= current_time <= end_time:
        return True
    else:
        return False


def get_message(text):
    result = None
    try:
        result = json.loads(text)
    except:
        result = {}

    return result


def elk_consumer():
    consumer = kafka.get_consumer(extra={
        'group.id': 'consumer_group_elk_kafka_es',
        'auto.offset.reset': 'earliest',
        'error_cb': error_cb,
        'stats_cb': stats_cb,
        'on_commit': on_commit
    })

    consumer.subscribe([elk_error_topic])

    num = 0

    while True:

        try:

            msg: Message = consumer.poll(5.0)
            num += 1

            if msg is None:
                continue
            if msg.error():
                logger.error("Consumer error: {}".format(msg.error()))
                continue

            # print("Received message: {}".format(msg.value().decode('utf-8')))
            data = {
                'topic': msg.topic(),
                'partition': msg.partition(),
                'offset': msg.offset(),
                'key': msg.key(),
                'value': msg.value().decode('utf-8'),
                'timestamp': msg.timestamp(),
            }

            message: dict = json.loads(data.get('value'))
            message2: dict = get_message(message.get('message'))
            message['message2'] = message2
            json_decode = 0 if message2 is False else 1
            message['debug'] = {'json': json_decode}
            log_type = message.get('log', {}).get('topic')
            logger.info(data)

            # elasticsearch
            dt = time.strftime("%Y%m", time.localtime())
            # index_name = message.get('fields', {}).get('log_type') + f'_{dt}'   # elk_log_prod_php_202310
            index_name = message.get('fields', {}).get('log_type')  # elk_log_prod_php
            result = es.index(index=index_name, body=message)
            logger.info(result)

            # mysql
            try:
                # if not message2 or str(message2.get('levelname', '')).upper() == "ERROR":
                esid = result.get('_id')
                log_service = message.get('fields', {}).get('log_service')  # elk_log_prod_php
                log_time = message.get('@timestamp', {})
                dt_utc = datetime.datetime.strptime(log_time, "%Y-%m-%dT%H:%M:%S.%fZ")
                dt_local = dt_utc + timedelta(hours=8)
                log_time = dt_local.strftime("%Y-%m-%d %H:%M:%S")
                env = message.get('fields', {}).get('env')
                host = message.get('host', {}).get('name')

                elk_error = ELKError()
                elk_error.index = index_name
                elk_error.esid = esid
                elk_error.host = host
                elk_error.env = env
                elk_error.service = log_service
                elk_error.log_time = log_time
                elk_error.message = json.dumps(message)
                elk_error.message_func = message2.get('func')
                elk_error.message_lineno = message2.get('lineno')
                elk_error.message_pathname = message2.get('pathname')
                elk_error.message_time = message2.get('time')
                elk_error.message_levelno = message2.get('levelno')
                elk_error.message_levelname = message2.get('levelname')
                elk_error.message_process = message2.get('process')
                elk_error.message_message = message2.get('message')
                session.add(elk_error)
                session.commit()

            except Exception as ex:
                logger.error("elk mysql exception:" + str(ex))

            # send weixin
            # if execute_time_range(exec_start_time, exec_end_time):
            #     logger.info("include_time_range")
            #     if json_decode == 1 and message2.get('levelname') == 'ERROR':
            #         sign = mode
            #         dt = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            #         qx = QianxunService()
            #         msg = f"ELK 提示({sign})({log_type})：{message2.get('message')[0:200]} {dt}[恐惧][恐惧]"
            #         title = f"ELK 提示({sign})({log_type})：{message2.get('message')[0:100]} {dt}"
            #         body = f"ELK 提示({sign})({log_type})：{message2.get('message')[0:1000]} {dt}"
            #         qx.simple_send({
            #             'type': log_type,
            #             'msg': msg,
            #             'mail_title': title,
            #             'mail_body': body,
            #             'wxid': '20254966458@chatroom',
            #             'mail_to': '<EMAIL>;<EMAIL>'
            #         })
            #         logger.info(msg)

        except Exception as e:
            logger.error(traceback.format_exc())

        finally:
            # time.sleep(2)
            pass

    consumer.close()


elk_consumer()
