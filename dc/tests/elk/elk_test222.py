# -*- coding:utf-8 -*-
# @Function  : ELK 信息提示
# <AUTHOR> wjh
# @Time      : 2023/8/31
# Version    : 1.0

import json
import time
import traceback
from datetime import datetime, timedelta
from dc.models.model import ELKError
from dc.services.mysql_service import MySQLService
from dc.services.qianxun_service import QianxunService
from dc.services.weixin_service import WeixinService
from dc.services.logging_service import LoggingService
from dc.conf.defines import *
from confluent_kafka import Consumer, Producer, KafkaException, Message, KafkaError, TopicPartition
from jwtools.func import *
from dc.conf.settings import get_settings, RUN_MODE
from dc.services.kafka_service import KafkaService
from dc.services.elasticsearch_service import ElasticsearchService
from datetime import datetime

date_string = ""
parsed_date = datetime.strptime(str(date_string), "%Y-%m-%d")
