# -*- coding:utf-8 -*-
# @Function  :
# <AUTHOR>
# @Time      : 2024/8/26
# Version    : 1.0

import os.path
import time

from dc.services.mysql_service import MySQLService
from dc.models.model import *
from dc.conf.settings import *
from dc.services.logging_service import LoggingService

# send_email("简单的测试2", "简单测试一下2", "<EMAIL>, <EMAIL>")

channel = get_settings2('elk', 'alert_channel', {})
print(channel)
keys = channel.keys()
print(1 in keys)
print(channel.get(1, ''))
