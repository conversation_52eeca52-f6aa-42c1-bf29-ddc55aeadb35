import math
import os

from pptx import Presentation
from jwtools.io_util import *

from pptx.dml.color import RGBColor



# 定义需要替换的文案的一个数组
replace_words = [
    "{{title}}",  # 标题
    "{{company range}}",  # 申报公司范围
    "{{over time}}",  # 申报截止时间
    "{{interface unit}}",  # 归口单位
    "{{support ways}}",  # 支持方式
    "{{Policy Basis}}",  # 政策依据
]
# 定义一个需要替换其他的文案的数组
replace_other = "{{other}}"

# 定义一个需要替换的目标文案的数组  包含  支持领域 申报条件  组织方式 申报程序 注意事项 其他内容
replace_target = {
    "支持领域": "{{support_area}}",  # 支持领域
    "申报条件": "{{request_condition}}",  # 申报条件
    "组织方式": "{{organization_type}}",  # 组织方式
    "申报程序": "{{request_program}}",  # 申报程序
    "注意事项": "{{note}}",  # 注意事项
    "其他内容": "{{other_content}}",  # 其他内容
}
test_dict = {
    "标题": "{{title}}",
    "可申报企业范围": "{{company range}}",
    "申报截止时间": "{{over time}}",
    "归口单位": "{{interface unit}}",
    "支持方式": "{{support ways}}",
    "政策依据": "{{Policy Basis}}",
}

text = read_text_from_file("result.txt")
slice = text.split("###")
d = {}
others = []
for s in slice:
    # 去掉s结尾的\n
    s = s.rstrip("\n")
    # print(s)

    tmp = s.split("\n")
    tmp = [x for x in tmp if x != '']

    # print(tmp)
    if len(tmp) == 1:
        titleArr = tmp[0].split("：")
        if (len(titleArr) == 2) and (titleArr[0] == "标题"):
            print(titleArr[1])
            d["{{title}}"] = titleArr[1]
    else:
        for k, v in test_dict.items():
            if k == tmp[0].strip():
                # 拼接 以tmp 为数组的内容作为一个字符串
                d[v] = "".join(tmp[1:])
                continue
        for k, v in replace_target.items():
            if k == tmp[0].strip():
                others.append(k)
                others.append("".join(tmp[1:]))

d["others"] = others
print(len(d['others']), "*" * 50)
print(d['others'])
# 正文内容页总页数
total_page_num = 6
# 内容占位页
total_other_page_nums = math.ceil(len(d['others']) / 2) - 1
# 其他内容页起始位置
other_page_start = 3
# 其他内容删除位置
del_other_page_start = other_page_start + total_other_page_nums

prs = Presentation('template2023.pptx')
# copy倒数第二页的ppt 并增加一页相同的页放在最后一页前面


slides = prs.slides
print(del_other_page_start, total_page_num)
# 循环
if total_page_num > del_other_page_start:
    for i in range(del_other_page_start, total_page_num):
        rId = prs.slides._sldIdLst[i].rId
        prs.part.drop_rel(rId)
        del prs.slides._sldIdLst[i]

replace_other_place = 0

# 从幻灯片索引列表中删除
for slide in prs.slides:
    shapes = slide.shapes
    for shape in shapes:
        if shape.has_text_frame:
            tf = shape.text_frame
            for p in tf.paragraphs:
                r, g, b = 0, 0, 0
                #
                # print(p.font.color.rgb)

                # 获取文字阴影设置
                # shadow = run.font.shadow
                # break

                if p.text in replace_words:
                    print(p.text)
                    for run in p.runs:
                        try:
                            r, g, b = run.font.color.rgb
                        except AttributeError:
                            pass

                        size = run.font.size
                        name = run.font.name
                        bold = run.font.bold
                    print(r, g, b)
                    p.text = d[p.text]
                    # 设置字体颜色跟原来的字体颜色相同
                    p.font.color.rgb = RGBColor(r, g, b)
                    p.font.size = size
                    p.font.name = name
                    p.font.bold = bold
                    # p.font.shadow = shadow

                if p.text == replace_other:
                    if len(d['others']) < replace_other_place:
                        print(p.text)
                        for run in p.runs:
                            try:
                                r, g, b = run.font.color.rgb
                            except AttributeError:
                                pass

                            size = run.font.size
                            name = run.font.name
                            bold = run.font.bold
                        print(r, g, b)
                        print(replace_other_place)
                        p.text = d["others"][replace_other_place]
                        replace_other_place += 1
                        # 设置字体颜色跟原来的字体颜色相同
                        p.font.color.rgb = RGBColor(r, g, b)
                        p.font.size = size
                        p.font.name = name
                        p.font.bold = bold
                        # p.font.shadow = shadow

prs.save('newTemp.pptx')
