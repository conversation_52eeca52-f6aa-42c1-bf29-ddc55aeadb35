import sys
import os

from sqlalchemy import select
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.models.model import DCSiteTask, DCSiteMainTask, User
from dc.services.mysql_service import MySQLService
import unittest


class TestClass(unittest.TestCase):

    def setUp(self):
        print('---setUp---')

    def tearDown(self):
        print('---tearDown---')

    def test_app_exists(self):
        print('hello world')

    def test_app_exists2(self):
        print('hello world2')



