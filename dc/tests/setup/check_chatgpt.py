# -*- coding:utf-8 -*-
# @Function  : chatgpt 调用检测
# <AUTHOR> wjh
# @Time      : 2023/9/7
# Version    : 1.0

import random
import sys
import os
import time
from dc.services.chatgpt_service import ChatGPTService
from dc.services.aigc_service import AIGCService
from jwtools.dt import *
from jwtools.func import *

chatgpt = ChatGPTService()

print_line('chat3 check')
start = time_s(3)
data = chatgpt.chat("一只狗几条腿")
print(data)
print_vf(
    time_work(start)
)

print_line('chat4 check')
start = time_s(3)
data = chatgpt.chat4("一只鸡几条腿")
print(data)
print_vf(
    time_work(start)
)
