# -*- coding:utf-8 -*-
# @Function  : webdriver 调用本地方式检测，包括 Chrome、Firefox、Edge
# <AUTHOR> wjh
# @Time      : 2023/9/7
# Version    : 1.0

import sys
import os
from bs4 import BeautifulSoup, ResultSet
from selenium.webdriver.common.by import By
from selenium import webdriver
from time import sleep
from dc.common.utils import Utils
from dc.tests.browser import <PERSON><PERSON>er


def get_webdriver(browser: Browser):
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/geckodriver", options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Chrome(executable_path="/usr/local/bin/chromedriver", options=opt)


print('------------chrome-------------')
driver = get_webdriver(Browser.Chrome)
driver.implicitly_wait(10)
driver.get("https://www.baidu.com")
driver.implicitly_wait(10)
title = driver.title
print(title)
driver.quit()

print('------------firefox-------------')
driver = get_webdriver(Browser.Firefox)
driver.implicitly_wait(10)
driver.get("https://www.baidu.com")
driver.implicitly_wait(10)
title = driver.title
print(title)
driver.quit()
