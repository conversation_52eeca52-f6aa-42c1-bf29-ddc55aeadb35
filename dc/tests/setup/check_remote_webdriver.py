# -*- coding:utf-8 -*-
# @Function  : webdriver 调用remote方式检测，包括 Chrome、Firefox、Edge
# <AUTHOR> wjh
# @Time      : 2023/9/7
# Version    : 1.0

import sys
import os
import time
from dc.common.utils import Utils
from dc.tests.browser import <PERSON>rowser
from selenium import webdriver
from selenium.webdriver import DesiredCapabilities
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.remote_connection import RemoteConnection
from dc.common import utils

webdriver_url = "http://192.168.1.131:4444"


def get_remote_webdriver(browser: Browser):
    print('webdriver_url:' + webdriver_url)
    if browser == Browser.Firefox:
        opt = webdriver.FirefoxOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.FIREFOX, options=opt)
    elif browser == Browser.Edge:
        opt = webdriver.EdgeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.EDGE, options=opt)
    else:
        opt = webdriver.ChromeOptions()
        opt.add_argument("no-sandbox")
        opt.add_argument("--disable-extensions")
        opt.add_argument("--headless")
        return webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.CHROME, options=opt)


print('------------remote chrome-------------')
driver = get_remote_webdriver(Browser.Chrome)
driver.implicitly_wait(10)
driver.get("https://www.baidu.com")
driver.implicitly_wait(10)
title = driver.title
print(title)
driver.quit()

print('------------remote firefox-------------')
driver = get_remote_webdriver(Browser.Firefox)
driver.implicitly_wait(10)
driver.get("https://www.baidu.com")
driver.implicitly_wait(10)
title = driver.title
print(title)
driver.quit()
