import datetime
import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.check_analysis import CheckAnalysis


print(CheckAnalysis.deal_format_date('2024 年 02 月 28 日 9:00 '))
#
# # 创建一个 datetime 对象，表示某个特定时间点
# specific_datetime = datetime.datetime(2023, 9, 30)
#
# # 获取当前时间
# current_datetime = datetime.datetime.now()
#
# # 计算时间差
# time_difference = current_datetime - specific_datetime
#
# # 获取时间差的天数部分
# days_difference = time_difference.days
#
# print(f"与 {specific_datetime} 相差 {days_difference} 天")

#  将2023-11-11 或 2023-11-11 11:11:11 转换成datetime.datetime.now()的格式

#
# def determine_date_format(date_string):
#     # 尝试使用 "%Y-%m-%d" 格式解析日期字符串
#     try:
#         parsed_date = datetime.datetime.strptime(date_string, "%Y-%m-%d")
#         return parsed_date
#     except ValueError:
#         pass
#
#     # 尝试使用 "%Y-%m-%d %H:%M:%S" 格式解析日期字符串
#     try:
#         parsed_date = datetime.datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")
#         return parsed_date
#
#     except ValueError:
#         pass
#
#     # 如果都无法解析，则返回 None 或其他默认值，表示日期格式未知
#     return None
#
#
# def days_between(date_string):
#     # 输入的日期字符串
#     parsed_date = determine_date_format(date_string)
#
#     # 获取当前时间
#     current_datetime = datetime.datetime.now()
#
#     # 计算时间差
#     time_difference = current_datetime.date() - parsed_date.date()
#
#     # 获取时间差的天数部分
#     days_difference = time_difference.days
#
#     print(f"与 {date_string} 相差 {days_difference} 天")
#     return days_difference
#
#
# res = days_between("2023-9-26 23:11:11")
# print(res)
#
# a = ["aa", "bb", "cc"]
# b = ["bb", "cc", "dd"]
#
# print(set(a) - set(b))
