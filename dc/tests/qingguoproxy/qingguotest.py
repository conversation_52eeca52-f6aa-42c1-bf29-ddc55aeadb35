import time
from selenium import webdriver

targetURL = "http://myip.ipip.net"  # 访问的目标站点
proxyAddr = "tunnels.qg.net:19870"

# 记录开始时间
start_time = time.time()

# 创建Chrome浏览器驱动对象
option = webdriver.ChromeOptions()
option.add_argument("--start-maximized")
option.add_argument("--headless")
option.add_argument("--start-maximized")  # 窗口最大化运行
#option.add_argument('--proxy-server=%(server)s' % {"server": proxyAddr})
driver = webdriver.Chrome(options=option)
driver.get(targetURL)

print(driver.page_source)

# 记录结束时间
end_time = time.time()

# 计算执行时间
execution_time = end_time - start_time
print("执行时间：", execution_time, "秒")
driver.quit()
