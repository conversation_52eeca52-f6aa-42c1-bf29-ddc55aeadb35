import json
import random
import sys
import os
import logging

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.logging_service import LoggingService


log = LoggingService(logfile='xx/test.log')

log.info("this is test 这是一条 日志记录 信息")


def test_log():
    global name
    age = 23
    name = 'tom'
    msg = f'Happy birthday {age}, {name}!'
    log.info(msg)
    news = {'type': "微信公众账号",
            'mpname': "比特量化",
            'mpdata': {'account': "btcquant"}
            }
    # print(news);
    # print(type(news));
    json_encode = json.dumps(news)
    print(json_encode);
    print(type(json_encode));
    json_decode = json.loads(json_encode)
    print(json_decode);
    print(type(json_decode));


#test_log()