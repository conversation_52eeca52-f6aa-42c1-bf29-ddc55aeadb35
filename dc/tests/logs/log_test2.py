# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/7/10
# Version    : 1.0

"""
日志输出测试，ELK 测试
:author wjh
:date 2023-8-14
"""

import sys
import os
import time
import traceback

from dc.services.logging_service2 import LoggingService

logger = LoggingService('log_test2.log', formatter='')
# logger.info("dodo")


try:
    raise ValueError("this is value error")
except Exception as ex:
    logger.debug(f"debug exception:" + traceback.format_exc())
    logger.info(f"info exception:" + traceback.format_exc())
    logger.error(f"error exception:" + traceback.format_exc())
