"""
日志输出测试，ELK 测试
:author wjh
:date 2023-8-14
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.logging_service import LoggingService

logger = LoggingService('qianxun_send.log')

while True:
    dt = int(time.time())
    logger.warning('warning test ' + str(dt))
    logger.info('info test ' + str(dt))
    logger.error('error test ' + str(dt))
    time.sleep(10)
