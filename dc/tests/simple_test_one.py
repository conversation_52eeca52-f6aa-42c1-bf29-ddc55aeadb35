import random
import sys
import os

from sqlalchemy.orm import Query
from dc.services.mysql_service import MySQLService
from dc.common.convert_util import ConvertUtil

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.tests.test_simple_basics import SimpleBasicsTestCase
from flask_sqlalchemy import BaseQuery
from dc.models.model import User, User2
from flask import current_app
from dc.tests.test_basics import BasicsTestCase
from sqlalchemy import select

class SimpleTestOne(SimpleBasicsTestCase):


    def setUp(self):
        super().setUp()
        mysql_service = MySQLService()
        self.server = mysql_service
        self.session = mysql_service.create_session()

    def tearDown(self):
        super().tearDown()
        self.session.commit();

    def test_cc(self):
        #with app.app_context():
        query: Query = self.session.query(User2).filter_by(id=23)
        user: User2 = query.all()
        print(user.to_json())
        self.session.commit()

    def test_cc2(self):
        stmt = select(User2).where(User2.id.in_([23, 24]))
        for user in self.session.scalars(stmt):
            print(user.to_json())

    def test_cc3(self):
        stmt = select(User2).where(User2.id.in_([23, 24]))
        for user in self.session.scalars(stmt):
            print(user.to_json())

    def test_update(self):
        rage = {"age": random.randint(100, 999)}
        print(rage)
        self.session.query(User2).filter_by(id=62).update(rage)
        # self.session.commit()

    def test_search(self):
        zz = self.session.query(User2).filter_by(id=62).first()
        print(zz.to_json())

    def test_one(self):
        zz = self.session.execute("select * from test_user where id=62").fetchone()
        print(ConvertUtil.dict2json(zz))

    def test_one(self):
        zz = self.session.execute("select * from test_user where id=62").fetchone()
        json = ConvertUtil.row2json(zz)
        print(json)
        dd = ConvertUtil.row2dict(zz)
        print(dd)

    def test_one2(self):
        zz = self.session.execute("select * from test_user limit 2").fetchall()
        print(zz)
        dsds = ConvertUtil.rows2json(zz)
        print(dsds)
