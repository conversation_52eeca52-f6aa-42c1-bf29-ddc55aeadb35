import os
import comtypes.client


def ppt_to_pdf(input_file_path, output_file_path):
    # Ensure the output directory exists
    output_dir = os.path.dirname(output_file_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Load PowerPoint
    powerpoint = comtypes.client.CreateObject("Powerpoint.Application")

    # Open the input file
    ppt = powerpoint.Presentations.Open(input_file_path)

    # Save as PDF (formatType = 32 is for PDFs)
    ppt.SaveAs(output_file_path, 32)

    # Close the input file
    ppt.Close()

    # Close PowerPoint
    powerpoint.Quit()


ppt_to_pdf("D:/code/py/python-ijiwei-dc2/dc/tests/pdf/template2023.pptx",
           "D:/code/py/python-ijiwei-dc2/dc/tests/pdf/output.pdf")
