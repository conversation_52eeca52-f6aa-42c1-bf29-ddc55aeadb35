import flask
from flask import Blueprint, make_response, request
from dc.services.elk_service import ELKService

app_elk = Blueprint('app_elk', __name__)


@app_elk.route("error_count")
def errorCount():
    """ 错误数量分析 """
    service = ELKService()
    params = request.values.to_dict()
    return service.error_count(params)


@app_elk.route("error_type_count")
def errorTypeCount():
    """ 错误数量分析 """
    service = ELKService()
    params = request.values.to_dict()
    return service.error_type_count(params)


@app_elk.route("error_service_count")
def errorServiceCount():
    """ 服务错误分析 """
    service = ELKService()
    params = request.values.to_dict()
    return service.error_service_count(params)


@app_elk.route("elasticsearch_status")
def elasticsearchStatus():
    """ elasticsearch 状态监控 """
    service = ELKService()
    params = request.values.to_dict()
    return service.elasticsearch_status(params)


@app_elk.route("elasticsearch_count")
def elasticsearchCount():
    """ elasticsearch 数量统计 """
    service = ELKService()
    params = request.values.to_dict()
    return service.elasticsearch_count(params)


@app_elk.route("kafka_status")
def kafkaStatus():
    """ kafka 状态监控 """
    service = ELKService()
    params = request.values.to_dict()
    return service.kafka_status(params)


@app_elk.route("kafka_topics")
def kafkaTopics():
    """ kafka 主题监控 """
    service = ELKService()
    params = request.values.to_dict()
    return service.kafka_topics(params)


@app_elk.route("logMonitor/getAlertDetail")
def alertDetail():
    """ 监控报警详情 """
    service = ELKService()
    params = request.values.to_dict()
    return service.alert_detail(params)


@app_elk.route("logMonitor/list")
def alertList():
    """ 监控报警列表 """
    service = ELKService()
    params = request.values.to_dict()
    return service.alert_list(params)

