#! -*- encoding:utf-8 -*-
import json
from urllib import request

# 要访问的目标页面
targetUrl = "http://proxy.abuyun.com/switch-ip"
# targetUrl = "http://proxy.abuyun.com/switch-ip"
# targetUrl = "http://proxy.abuyun.com/current-ip"

# 代理服务器
proxyHost = "http-pro.abuyun.com"
proxyPort = "9010"

# 代理隧道验证信息
proxyUser = "H9FE43O59MXD3B2P"
proxyPass = "D4224AFA6DDC91E2"

proxyMeta = "http://%(user)s:%(pass)s@%(host)s:%(port)s" % {
    "host": proxyHost,
    "port": proxyPort,
    "user": proxyUser,
    "pass": proxyPass,
}

proxy_handler = request.ProxyHandler({
    "http": proxyMeta,
    "https": proxyMeta,
})

auth = request.HTTPBasicAuthHandler()
opener = request.build_opener(proxy_handler, auth, request.HTTPHandler)

# opener = request.build_opener(proxy_handler)

opener.addheaders = [("Proxy-Switch-Ip", "yes")]
request.install_opener(opener)
resp = request.urlopen("http://proxy.abuyun.com/switch-ip").read()

print(resp.decode('utf8'))

