# -*- coding:utf-8 -*-
# @Function  : 数据采集-文章拉取服务
# <AUTHOR> wjh
# @Time      : 2022-10-19
# Version    : 1.0

import json
import random
import traceback
from time import sleep

import requests
from bs4 import BeautifulSoup

from dc.services.k8s_service import K8sService
from dc.tests.browser import Browser

from dc.common.webdriver_util import get_remote_webdriver
from pymongo import ReturnDocument
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from sqlalchemy.pool import QueuePool

from dc.common.GetSeleniumDriver import GetSeleniumDriver
from dc.common.GetSeleniumFirefoxDriver import GetSeleniumFirefoxDriver
from dc.common.dc_util import get_url_type, get_url_type_by_file
from dc.common.functools_wraps import deprecated
from dc.common.qiniu_utils import *
from dc.common.utils import Utils
from dc.conf.settings import get_settings, get_settings2
from dc.models.model import DCSiteTask, DCSiteList
from dc.services.check_analysis import CheckAnalysis
from dc.services.logging_service import LoggingService
from dc.services.mongo_service import MongoService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


mysql = MySQLService()
redisService = RedisService('redis')
ms = MongoService()

redis_prefix = 'laravel_database_'
redis_analyse_key = f'{redis_prefix}dc-site-task-analyse'
redis_task_key = f'{redis_prefix}dc-site-task'
redis_task_blacklist = f'{redis_prefix}dc-site-task-blacklist'

logger = LoggingService('dc_task_pull.log')
ks = K8sService()

def get_content(url, site_list: DCSiteList, isfile: bool = True) -> requests.Response:
    charset = 'utf-8'

    try:

        if site_list.detailDcMethod == 2:  # 高级
            status_code = 200  # 暂时处理
            webdriver_url = get_settings2('webdriver', 'host')  # "http://*************:4444"
            # driver = WebDriverHelper.get_remote_webdriver(webdriver_url, Browser.Chrome)
            # driver = WebDriverHelper.get_webdriver(Browser.Chrome)
            firefox = False
            analysisMethod = Utils.get_extra(site_list.extra, 'analysisMethod', 0)
            firefox = analysisMethod == 1
            page_wait = Utils.get_extra(site_list.extra, 'pageWait', False)
            logger.debug(f'firefox {firefox} page wait: {page_wait} extra: {site_list.extra}')

            driver: WebDriver = None
            result = ""
            charset = ""

            try:
                logger.debug(f'firefox: {firefox}')
                if firefox:
                    # driver = GetSeleniumFirefoxDriver().driver
                    driver = get_remote_webdriver(webdriver_url, Browser.Firefox)
                else:
                    # driver = GetSeleniumDriver().driver
                    driver = get_remote_webdriver(webdriver_url, Browser.Chrome)

                driver.implicitly_wait(15)
                driver.get(url)
                if page_wait:
                    logger.debug(f'page wait -->> {page_wait}')
                    driver.find_element(By.XPATH, page_wait)

                result = driver.page_source
                charset = CheckAnalysis.get_char_set(driver)

            except Exception as ex:
                status_code = 500
                logger.error(f"get_content url: {url} exception: {str(ex)}")
            finally:
                if driver:
                    driver.quit()

            return {'status_code': status_code, 'text': result, 'charset': charset}
        else:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
            }
            r = requests.get(url, headers=headers, verify=False, timeout=(3.05, 27))
            # r.encoding = "utf-8"
            status_code = r.status_code
            # r_text = '' if isfile else r.text
            if isfile:
                r_text = ''
            else:
                soup = BeautifulSoup(r.content, 'lxml')
                charset = soup.original_encoding
                r_text = str(soup)

            return {'status_code': status_code, 'text': r_text, 'charset': charset}
    except Exception as ex:
        logger.error(f"get_content exception:" + str(ex))
        return {'status_code': 500, 'text': '', 'charset': charset}


def getPageFrequency(site_list: DCSiteList):
    """
    获取页面间隔
    :param site_list:
    :return:
    """
    return 1

    type = site_list.pageFrequencyType
    time_sleep = 2
    if type == 0:
        time_sleep = site_list.pageFrequency
    elif type == 1:
        time_sleep = site_list.pageFrequency * 60
    elif type == 2:
        time_sleep = site_list.pageFrequency * 60 * 60

    logger.info(f"pageFrequencyType: {type} pageFrequency： {site_list.pageFrequency}  sleep: {time_sleep}")
    return time_sleep


@deprecated()
def log_mysql_pool(pool: QueuePool):
    """
    log pool log
    :param pool:
    :return:
    """
    if isinstance(pool, QueuePool):
        pin = pool.checkedin()
        pout = pool.checkedout()
        size = pool.size()
        logger.debug(f"size:{size}    pin:{pin}      pout:{pout}")


def handle():
    logger.info('启动数据采集拉取服务...')
    max_retry_time = get_settings('dc.task-pull.max_retry_time', 3)
    # timeout = get_settings('dc.task-pull.timeout', 20)
    sleep_time = get_settings('dc.task-pull.sleep', 10)
    redis = redisService.get_client()
    session = None
    prefix = None

    while True:

        rand = random.randint(100000, 999999)
        prefix = f"[{rand}] "
        ks.liveness()

        try:

            session = mysql.create_session()
            taskId = redis.rpop(f'{redis_task_key}')
            if taskId is None:
                sleep(sleep_time)
                continue

            logger.info(f"{prefix} redis rpop (taskId): {taskId}")

            taskId = int(taskId)
            task: DCSiteTask = session.get(DCSiteTask, taskId)
            if task is None:
                logger.info(f"{prefix} taskId:{taskId} task empty")
                sleep(1)
                continue

            # 重复执行
            if task.taskStatus > 0:
                logger.info(f"{prefix} taskId:{taskId} task 重复执行")

            # retryTime
            if task.retryTime > max_retry_time:
                logger.info(f"{prefix} taskId: {taskId} retryTime {task.retryTime} > {max_retry_time}")
                continue

            # mongo
            builder = ms.get_collection('site-task')
            url = task.url
            logger.debug(f"{prefix} url: {url}")

            site_list: DCSiteList = session.query(DCSiteList).filter_by(id=task.siteListId).first()
            if site_list is None:
                logger.info(f'{prefix} taskId: {taskId} site_list is empty')
                continue

            logger.debug(f'{prefix} taskId: {taskId} detailDcMethod: {site_list.detailDcMethod}')

            # page time sleep
            page_time_sleep = getPageFrequency(site_list)
            logger.debug(f"{prefix} sleep: {page_time_sleep}")
            # sleep(page_time_sleep)

            # 附件处理采集
            ext = get_extension_from_url(task.url, '.html')
            is_html = is_valid_html_extension(ext)
            is_file = is_valid_attachment_extension(ext)

            response = get_content(url, site_list, is_file)
            logger.debug(f"{prefix} response status_code {response['status_code']}")

            status_code = response['status_code']
            md = {
                # '_id': mongo_key,
                'taskId': task.id,
                'mainTaskId': task.mainTaskId,
                'siteListId': task.siteListId,
                'url': url,
                'charset': response['charset'],
                'body': response['text'],
                'code': status_code,
                'coverImg': task.coverImg,
                'publicTime': task.publicTime,
                'createdAt': Utils.showDateTime(),
                'version': 2,
            }

            # logger.info(f'{prefix} mongo save:{md}')
            # result = builder.insert_one(md)
            result = builder.find_one_and_update({'taskId': task.id}, {'$set': md}, upsert=True, return_document=ReturnDocument.AFTER)
            mongo_key = str(result['_id'])
            logger.debug(f'{prefix} mongo save key:{mongo_key}')

            # update mysql
            task.taskStatus = 1 if status_code == 200 else 2
            task.httpStatusCode = status_code
            task.taskAt = Utils.showDateTime()
            task.mongoKey = mongo_key
            task.retryTime = task.retryTime + 1
            # $task->mongoCollection = '';

            # 附件处理 urlType
            task.urlType = get_url_type(is_html)
            logger.info(f'{prefix} urlType: {task.urlType} url: {task.url} extension: {ext}')

            session.add(task)
            session.commit()

            logger.info(f'{prefix} task save: 1')

            # update redis
            if task.taskStatus == 2:  # // 失败，重新进入队列
                redis.lpush(f'{redis_task_key}', task.id)
                logger.info(f'{prefix} redis push {redis_task_key}:{task.id}')
            else:  # // 处理数据分析
                dic: dict = task.to_dict()
                dic['detailDcMethod'] = site_list.detailDcMethod
                result = redis.lpush(redis_analyse_key, json.dumps(dic))
                logger.info(f'{prefix} redis push {redis_analyse_key} content: {json.dumps(dic)} result : {result}')

            session.close()

        except Exception as ex:
            redis.lpush(f'{redis_task_key}', taskId)
            logger.error(f'{prefix} exception redis push {redis_task_key}:{taskId} exception: {str(ex)}')

        finally:
            if session is not None:
                session.close()


handle()
