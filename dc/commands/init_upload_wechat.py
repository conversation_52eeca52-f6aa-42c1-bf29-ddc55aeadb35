import os
import sys

import xlrd

from dc.services.mysql_service import MySQLService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.gzh_service import GzhService
from dc.models.model import DCGzhInfo

write_mode = 'prod'
mysql = MySQLService(run_model=write_mode)
session = mysql.create_session()


def upload_wechat_follow_list(name):
    xls = xlrd.open_workbook(filename=name)
    sh = xls.sheets()[0]
    gzh_service = GzhService()
    i = 0
    k = 0
    for row in range(0, sh.nrows):
        i += 1
        tag = sh.cell(row, 1).value  # 第三列的数据
        name = sh.cell(row, 0).value  # 第三列的数据
        print(name, tag)
        have = session.query(DCGzhInfo).filter(DCGzhInfo.nickName == name). first()
        if have is None:
            k += 1
            gzh_service.search_and_follow(name, tag, wxid='', run_mode=write_mode)

    print(k)


upload_wechat_follow_list("./20231123gzh.xlsx")
