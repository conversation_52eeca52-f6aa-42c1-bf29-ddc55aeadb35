# -*- coding:utf-8 -*-
# @Function  : 数据采集-招聘详情页抓取
# <AUTHOR> lws
# @Time      : 2022-11-23
# Version    : 1.0

import time
import sys
import os
import json
import string
import zipfile
from selenium import webdriver
from selenium.webdriver import Keys, DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import MPCompanyInfo, MPCompanyRecruit
from dc.services.logging_service import LoggingService
from dc.commands.recruit_utils import *
from dc.conf.settings import *

mysql = MySQLService('mysql_company')
redisService = RedisService('redis')
logger = LoggingService('recruit_info.log')

redis_key = f'company_recruit_info_lists'

# 代理服务器
proxy: dict = get_settings2('recruit', 'proxy', {})
proxyHost, proxyPort, proxyUser, proxyPass = proxy.values()

proxy_url = f'--proxy-server=http://{proxyHost}:{proxyPort}'

# 写入主任务队列
def handel():

    r_client = None
    session = None

    try:
        params = sys.argv[1:]
        process_id = int(params[0])
        # proxy_auth_plugin_path = create_proxy_auth_extension(
        #     proxy_host=proxyHost,
        #     proxy_port=proxyPort,
        #     proxy_username=proxyUser,
        #     proxy_password=proxyPass)

        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        # chrome_options.add_extension(proxy_auth_plugin_path)
        chrome_options.add_argument(proxy_url)  # 代理池
        chrome_options.add_experimental_option("excludeSwitches", ["ignore-certificate-errors"])

        ca = DesiredCapabilities.CHROME
        ca["goog:loggingPrefs"] = {"performance": "ALL"}
        driver = webdriver.Chrome(options=chrome_options, desired_capabilities=ca)
        driver.implicitly_wait(10)
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
        })

        while True:
            r_client = redisService.get_client()
            redis_recruit_info = r_client.rpop(redis_key)
            if not redis_recruit_info:
                time.sleep(5)
                logger.debug('睡 5 秒')
                # continue
                break

            recruit_info = json.loads(redis_recruit_info)
            r_id = recruit_info['id']
            link = recruit_info['link']

            logger.info(f"r_id:{r_id} link: {link}")

            again_num = 0
            while again_num < 3:
                try:
                    driver.get(link)
                    driver.implicitly_wait(10)
                    driver.find_element('xpath', '//div[@class="job-apply-content"]')
                    break
                except:
                    again_num += 1
                    pass

            try:
                driver.find_element('xpath', '//div[@class="job-detail-operate"]').click()
            except:
                pass

            try:
                details = driver.find_element('xpath', '//dd[@data-selector="job-intro-content"]').text
            except:
                details = ''

            session = mysql.create_session()
            up_sql = f"update MP_CompanyRecruit set recruiterDetails = '{details}' where id = {r_id}"
            logger.info(f"up_sql: {up_sql}")
            try:
                session.execute(up_sql)
                session.commit()
                session.close()
            except Exception as ex:
                logger.error(f"session commit exception: {str(ex)}")
                if session is not None:
                    session.close()

    except Exception as ex:
        logger.error(f"exception: {str(ex)}")

    finally:
        if r_client:
            r_client.close()

        if session:
            session.close()



def getRecruitPage(driver, link):
    while True:
        try:
            driver.get(link)
            driver.find_element('xpath', '//dd[@data-selector="job-intro-content"]')
            break
        except:
            pass


handel()
