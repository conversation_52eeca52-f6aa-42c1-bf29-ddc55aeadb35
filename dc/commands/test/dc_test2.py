# -*- coding:utf-8 -*-
# @Function  : 数据采集-功能测试2
# <AUTHOR> wjh
# @Time      : 2022-10-19
# Version    : 1.0

import random

from dc.services.logging_service import LoggingService

logger = LoggingService('dc_test.log')

def handle():
    logger.info('启动功能测试...')

    while True:

        rand = random.randint(100000, 999999)
        prefix = f"[{rand}] "

        try:

            logger.info(f"{prefix} rand: {rand}")

        except Exception as ex:
            logger.error(f'{prefix} exception: {str(ex)}')


handle()
