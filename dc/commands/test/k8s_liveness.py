# -*- coding:utf-8 -*-
# @Function  : 活跃探针（Liveness Probe）
#               在不同运行环境下情况不同，如下：
#               crontab 中数据：
#               {
#                  "hostname": "ijiwei-dc2-test-bf6f64f88-x4bl7",
#                  "script_name": "k8s_liveness.py",
#                  "script_path": "/data/ijiwei-dc2/dc/commands/test/k8s_liveness.py",
#                  "process_name": "python3",
#                  "group_name": "python3",
#                  "full_process_name": "python3:python3"
#               }
#
#               supervisor 中数据：
#               {
#                  "hostname": "ijiwei-dc2-test-bf6f64f88-x4bl7",
#                  "script_name": "k8s_liveness.py",
#                  "script_path": "/data/ijiwei-dc2/dc/commands/test/k8s_liveness.py",
#                  "process_name": "00",
#                  "group_name": "k8s-liveness",
#                  "full_process_name": "k8s-liveness:00"
#               }
#
# <AUTHOR> wjh
# @Time      : 2025-01-02
# Version    : 1.0

import time

from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService

logger = LoggingService(logfile="k8s_liveness.log")
ks = K8sService(logger=logger)

while True:
    try:
        ks.liveness()
        logger.info(f"liveness")

    except Exception as ex:
        logger.error(f"exception: {str(ex)}")

    finally:
        time.sleep(10)
