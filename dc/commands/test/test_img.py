import re
import urllib.request
import requests
from selenium import webdriver
import time
import os.path
import traceback
from urllib.parse import urlparse, urlunparse
import hashlib
from selenium.webdriver.chrome.options import Options


def webshot(url, saveImgName):
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    driver = webdriver.Chrome(options=options)
    # driver.maximize_window()
    driver.set_window_size(width=1005, height=500, windowHandle='current')

    # 返回网页的高度的js代码
    js_height = "return document.body.clientHeight"
    pic_name = saveImgName
    link = url
    # driver.get(link)
    try:
        driver.get(link)
        k = 1
        height = driver.execute_script(js_height)
        while True:
            if k * 500 < height:
                js_move = "window.scrollTo(0,{})".format(k * 500)
                print(js_move)
                driver.execute_script(js_move)
                time.sleep(0.2)
                height = driver.execute_script(js_height)
                k += 1
            else:
                break
        scroll_width = driver.execute_script('return document.body.parentNode.scrollWidth')
        scroll_height = driver.execute_script('return document.body.parentNode.scrollHeight')
        driver.set_window_size(scroll_width, scroll_height)
        driver.get_screenshot_as_file(pic_name + ".png")

        print("Process {} get one pic !!!".format(os.getpid()))
        time.sleep(0.1)
    except Exception as e:
        print(pic_name, e)


def test(url):
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    driver = webdriver.Chrome(options=options)

    link = url
    try:
        driver.get(link)
        print(driver.page_source)

        links = driver.find_elements('tag name', 'a')
        for href in links:
            a_url = href.get_attribute('href')
            # print(a_url)
            if re.findall(".xlsx", str(a_url)):
                try:
                    # href.click()
                    driver.get(a_url)
                    # downloadFile('', a_url)
                    print("完成")
                    time.sleep(3)
                except:
                    print(f"{traceback.format_exc()}")
    except Exception as e:
        print(e)


# 下载文件
def downloadFile(name, url):
    r = requests.get(url, stream=True)
    length = float(r.headers['content-length'])
    f = open('D:\\tste1.xlsx', 'wb')
    count = 0
    count_tmp = 0
    time1 = time.time()
    for chunk in r.iter_content(chunk_size=512):
        if chunk:
            f.write(chunk)
            count += len(chunk)
            if time.time() - time1 > 2:
                p = count / length * 100
                speed = (count - count_tmp) / 1024 / 1024 / 2
                count_tmp = count
                # print(name + ': ' + formatFloat(p) + '%' + ' Speed: ' + formatFloat(speed) + 'M/S')
                time1 = time.time()
    f.close()


# 文件保存目录
# file_dir = os.path.join(os.path.dirname((os.path.abspath(__file__))), "files")
# if (not os.path.exists(file_dir)):
#     os.mkdir(file_dir)


def test1():
    down_link = ['http://www.bjhr.gov.cn/zwgk/tzgg/202212/P020221225480802611093.xlsx']
    # download_dir = "/Users/"

    options = webdriver.ChromeOptions()

    prefs = {'profile.default_content_settings.popups': 0, 'download.default_directory': 'D:\\'}
    options.add_experimental_option('prefs', prefs)

    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-gpu')

    # options.add_experimental_option("prefs", {
    #     "download.default_directory": download_dir,
    #     "download.prompt_for_download": False,
    #     "download.directory_upgrade": True,
    #     "plugins.always_open_pdf_externally": True
    # })
    # arr = re.findall('.png', "http://www.bjshy.gov.cn/web/ywdt84/dtxx94/1301136/2022122919362637321.jpg")
    # print(arr)
    # exit()
    driver = webdriver.Chrome(chrome_options=options)

    driver.get("http://www.bjhr.gov.cn/zwgk/tzgg/202212/./P020221228500382301579.xls")
    # driver.get("https://zyk.bjhd.gov.cn/jbdt/auto10489_51767/zfwj_57152/202009/P020200927625351492667.ofd")
    # header = {
    #     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.82 Safari/537.36'}
    #
    # pic = requests.get("http://www.bjshy.gov.cn/web/ywdt84/dtxx94/1301136/2022122919362637321.jpg", verify=False, headers=header)
    # print(pic.status_code)
    # exit()
    # 获取图片保存的路径
    # path = "D:\\"
    # base_url = path + "\image{}.jpg".format((6 + 1))
    # with open(base_url, 'wb') as f:
    #     f.write(pic.content)

    # for web in down_link:
    #     driver.get("https://amr.hunan.gov.cn/amr/zwx/xxgkmlx/zcfgx/gfxwjx/202205/24468656/files/08a9c933171d4e9a9bbd8c526e81921f.pdf")
        # data = urllib.request.urlopen("http://zscqj.beijing.gov.cn//zscqj/picture/0/915a1bc3070f4081b3d0380dec71aec5.png")
        # data = urllib.request.urlopen(request).read()
        # f = open('D:\\test.png', 'wb')
        # f.write(data)
        # f.close()

        # print(driver.page_source)

        # time.sleep(5)  # wait for the download to end, a better handling it's to check if the file exists
    time.sleep(5)
    driver.quit()


if __name__ == '__main__':
    url = "http://www.bjshy.gov.cn/web/ywdt84/gggs/index.html"

    result = urlparse(url)
    # print(result.scheme, result[0], result.netloc, result[1], result.path, result[2], sep='\n')
    r_path = result.path
    # print(r_path.split("/")[0:4])
    # print(("/").join(r_path.split("/")[0:4]))

    base_url1 = ("/").join(r_path.split("/")[0:4])

    print(urlunparse([str(result.scheme), str(result.netloc), str(base_url1) + "/", '', '', '']))
    exit()
    # m = hashlib.md5()
    # m.update(url.encode('utf-8'))
    # print(m.hexdigest())
    # exit()
    t = time.time()
    # 两个参数，前面url，后面保存地址
    test1()
    # test("http://www.bjhr.gov.cn/zwgk/tzgg/202212/t20221225_2883880.html")
    webshot('http://zscqj.beijing.gov.cn/zscqj/zwgk/zcfg/gfxwj/325993668/index.html', 'D:\\tstImg1')
    # print("操作结束，耗时：{:.2f}秒".format(float(time.time() - t)))
