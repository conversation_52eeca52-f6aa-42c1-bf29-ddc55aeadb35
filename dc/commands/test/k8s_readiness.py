# -*- coding:utf-8 -*-
# @Function  : 就绪探针（Readiness Probe）
# <AUTHOR> wjh
# @Time      : 2025-01-02
# Version    : 1.0

import time
from datetime import datetime

from dc.common.func import get_text_md5
from dc.common.functools_wraps import get_session
from dc.common.k8s_util import *
from dc.models.model_k8s import K8sLiveness
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService

logger = LoggingService(logfile="k8s_readiness.log")
ks = K8sService(logger=logger)

while True:
    try:
        ks.liveness()
        logger.info(f"readiness")

    except Exception as ex:
        logger.error(f"exception: {str(ex)}")

    finally:
        time.sleep(10)
