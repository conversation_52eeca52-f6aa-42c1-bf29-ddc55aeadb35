import sys
import os

from sqlalchemy.orm import Query

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))

from flask_sqlalchemy import BaseQuery
from sqlalchemy import select
from dc.models.model import DCSiteTask, DCSiteList, DCSiteMainTask
from dc.models.model_test import User, User2
from dc.app import create_app, db, app

"""
命令测试，仅供测试
2022-10-19
"""

def handle():
    query: Query = User.query
    user = query.first()
    print(user.to_json())


def handle2():
    with app.app_context():
        query: BaseQuery = User2.query
        user = query.first()
        query.filter_by()
        print(user.to_json())


handle2()
