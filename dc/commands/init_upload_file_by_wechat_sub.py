import os
import sys

import xlrd

from dc.services.mysql_service import MySQLService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.gzh_service import GzhService
from dc.services.weixin_service import WeixinService
from dc.models.model import DCGzhInfo, DCGzhWechat, DCGzhWechatRelation

write_mode = 'prod'
mysql = MySQLService(run_model=write_mode)
session = mysql.create_session()


def upload_wechat_follow_list(name):
    xls = xlrd.open_workbook(filename=name)
    sh = xls.sheets()[0]
    gzh_service = GzhService()
    gzhs = []
    for row in range(0, sh.nrows):
        name = sh.cell(row, 0).value  # 第三列的数据
        gzhs.append(name)

    # scan wechat follow list , check if it in gzhs , try to write to db
    wechats: [DCGzhWechat] = session.query(DCGzhWechat).filter(DCGzhWechat.isUsed == 1).all()
    for wechat in wechats:
        s = WeixinService(wx_id=wechat.wxId)
        p = {
            'wxpid': s.pid,
            'base_url': s.base_url
        }
        subs = s.req_gzh(p)
        for sub in subs['result'][wechat.wxId]:
            if len(sub) < 2:
                continue
            if sub[2] in gzhs:
                # 查询是否已经被关注
                have: DCGzhInfo = session.query(DCGzhInfo).filter(DCGzhInfo.gzhId == sub[0]).first()
                if have:
                    continue
                g = DCGzhInfo(
                    gzhId=sub[0],
                    gzhNo=sub[1],
                    nickName=sub[2],
                )
                session.add(g)
                relation: DCGzhWechatRelation = session.query(DCGzhWechatRelation).filter(
                    DCGzhWechatRelation.wxId == sub[0],
                    DCGzhWechatRelation.gzhId == wechat.wxId).first()
                if relation is None:
                    relation = DCGzhWechatRelation()
                    relation.gzhId = sub[0]
                    relation.wxId = wechat.wxId
                    session.add(relation)

        session.commit()


upload_wechat_follow_list("./20231123gzh.xlsx")
session.close()
