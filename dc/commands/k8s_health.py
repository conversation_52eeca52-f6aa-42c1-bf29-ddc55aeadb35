# -*- coding:utf-8 -*-
# @Function  : k8s_check_health
# <AUTHOR> wjh
# @Time      : 2025-01-08
# Version    : 1.0

import argparse
import random
import sys

from dc.common.k8s_util import get_hostname
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService

logger = LoggingService(logfile="k8s_health.log")
ks = K8sService(logger=logger)


def parse_args():
    """ 设置参数解析 """
    parser = argparse.ArgumentParser(description="Health Check Script")

    # 定义命名参数
    parser.add_argument('--script_path', required=False, type=str, help="Path to the script")
    parser.add_argument('--script_name', required=True, type=str, help="Script name")
    parser.add_argument('--failed_percent', required=True, type=float, help="The failure probability (0-1)")

    # 解析参数
    return parser.parse_args()


def parse_args_with_dict():
    args = parse_args()
    return vars(args)


if __name__ == "__main__":

    try:
        # 解析命令行传递的参数
        # args = parse_args()
        args = parse_args_with_dict()
        logger.info(f"args: {args}")

        # 获取传递的参数值
        # script_path = str(args.script_path).strip()
        # assert script_path, "script_path is empty"
        assert args['script_name'], "script_name is empty"
        assert args['failed_percent'], "failed_percent is empty"

        hostname = get_hostname()
        args['hostname'] = hostname

        # 执行健康检查
        exit_code = ks.livenessProbe(**args)
        logger.info(f"hostname: {hostname} script_name:{args['script_name']} exit_code: {exit_code}")
        sys.exit(exit_code)

    except Exception as ex:
        logger.error(f"exception: {str(ex)}")
        sys.exit(-1)
