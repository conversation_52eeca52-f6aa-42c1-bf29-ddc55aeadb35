# -*- coding:utf-8 -*-
# @Function  : 数据采集-招聘数据去重过滤
# <AUTHOR> lws
# @Time      : 2022-10-19
# Version    : 1.0

import time
import sys
import os
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import MPCompanyRecruit, MPCompanyRecruitOriginal
from dc.services.logging_service import LoggingService
from dc.commands.recruit_utils import *

mysql = MySQLService('mysql_company')
redisService = RedisService('redis')
client1 = redisService.get_client()
logger = LoggingService('recruit_filter.log')

redis_key = f'company_recruit_lists'
redis_statistics_key = f'company_recruit_statistics_lists'


# 写入主任务队列
def handel():
    while True:

        r_client = None
        session = None

        try:
            r_client = redisService.get_client()
            company_id = r_client.rpop(redis_key)
            if not company_id:
                logger.debug('sleep 5 秒')
                time.sleep(5)
                continue

            session = mysql.create_session()
            ret_filter = session.query(MPCompanyRecruit).filter(MPCompanyRecruit.companyId == company_id).all()

            filter_list = []
            for filter_info in ret_filter:
                tmp1 = filter_info.__dict__
                filter_list.append(tmp1['jobId'])

            ret_original = session.query(MPCompanyRecruitOriginal).filter(
                MPCompanyRecruitOriginal.companyId == company_id).all()
            for original_info in ret_original:
                tmp = original_info.__dict__
                if tmp['jobId'] in filter_list:
                    continue

                requireEduLevel_str = tmp['requireEduLevel'].strip()
                if not requireEduLevel_str:
                    requireEduLevel_str = getEduLevel(tmp['labels'])

                df = {'jobId': tmp['jobId'],
                      'jobKind': tmp['jobKind'],
                      'salary': tmp['salary'],
                      'requireWorkYears': tmp['requireWorkYears'],
                      'dq': tmp['dq'],
                      'refreshTime': tmp['refreshTime'],
                      'title': tmp['title'],
                      'link': tmp['link'],
                      'requireEduLevel': requireEduLevel_str,
                      'labels': tmp['labels'],
                      'recruiterId': tmp['recruiterId'],
                      'recruiterName': tmp['recruiterName'],
                      'recruiterTitle': tmp['recruiterTitle'],
                      'createAt': tmp['createAt'],
                      'companyId': tmp['companyId']}

                dch = MPCompanyRecruit(**df)
                session.add(dch)
                filter_list.append(tmp['jobId'])
            r_client.lpush(redis_statistics_key, company_id)
            session.commit()
            session.close()

        except Exception as ex:
            logger.error(f"exception: {str(ex)}")

        finally:
            if r_client:
                r_client.close()

            if session:
                session.close()

def getEduLevel(labels):
    eduLevel = ''
    if not labels:
        return eduLevel

    labels_arr = labels.split(',')
    if 'MBA/EMBA' in labels_arr:
        eduLevel = 'MBA/EMBA'
    elif '中专' in labels_arr:
        eduLevel = '中专'
    elif '中专/中技' in labels_arr:
        eduLevel = '中专/中技'
    elif '初中' in labels_arr:
        eduLevel = '初中'
    elif '博士' in labels_arr:
        eduLevel = '博士'
    elif '大专' in labels_arr:
        eduLevel = '大专'
    elif '大专及以上' in labels_arr:
        eduLevel = '大专及以上'
    elif '学历不限' in labels_arr:
        eduLevel = '学历不限'
    elif '本科' in labels_arr:
        eduLevel = '本科'
    elif '本科及以上' in labels_arr:
        eduLevel = '本科及以上'
    elif '硕士' in labels_arr:
        eduLevel = '硕士'
    elif '硕士及以上' in labels_arr:
        eduLevel = '硕士及以上'
    elif '统招本科' in labels_arr:
        eduLevel = '统招本科'
    elif '高中' in labels_arr:
        eduLevel = '高中'

    return eduLevel


handel()

