import sys
import os
import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import DCSiteTask


def again():
    current_time = datetime.datetime.now()
    two_hours_ago = current_time - datetime.timedelta(hours=2)
    five_hours_ago = current_time - datetime.timedelta(hours=5)
    end_time = two_hours_ago.strftime('%Y-%m-%d %H:%M:%S')
    start_time = five_hours_ago.strftime('%Y-%m-%d %H:%M:%S')

    # start_time = '2023-09-18 00:00:00'
    # end_time = '2023-09-18 15:20:00'

    mysql = MySQLService()
    redis = RedisService()
    session = mysql.create_session()
    client1 = redis.get_client()

    ret = session.query(DCSiteTask).filter(DCSiteTask.taskStatus == 1, DCSiteTask.analysisStatus.in_([0, 2]),
                                           DCSiteTask.id > 3500000, DCSiteTask.updatedAt >= start_time,
                                           DCSiteTask.updatedAt <= end_time).all()

    if not ret:
        print('无数据')
        exit()

    ids = []
    for info in ret:
        tmp = info.to_dict()
        ids.append(tmp['id'])

    if not ids:
        print('无数据')
        exit()

    if len(ids) == 1:
        up_sql1 = f"update DC_SiteTask set retryTime = 0, taskStatus = 0 where id = {ids[0]}"
    else:
        ids_tuple = tuple(ids)
        up_sql1 = f"update DC_SiteTask set retryTime = 0, taskStatus = 0 where id in {ids_tuple}"
    print(up_sql1)
    session.execute(up_sql1)
    session.commit()
    session.close()

    for tmp_id in ids:
        client1.rpush('laravel_database_dc-site-task', tmp_id)


again()
