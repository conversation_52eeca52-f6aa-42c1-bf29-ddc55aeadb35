"""
微信公众号采集-未成功解析文章重新进入队列解析
2024-5-21
"""

import json
import random
import time
import traceback

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhArticle
from dc.services.beanstalk_service import BeanstalkService
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService

logger = LoggingService('dc_gzh_analyse_reinit.log')
mysql = MySQLService()
bs = BeanstalkService()
ks = K8sService()

except_sleep = 10


def handle():
    """
    公众号历史文章队列
    :return:
    """
    logger.info('启动公众号-历史文章队列 服务...')

    session = None
    prefix = None
    page = 1
    page_size = 4000

    while True:

        ks.liveness()   # 探针
        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        from datetime import datetime, timedelta
        current_time = datetime.now()
        three_days_ago = current_time - timedelta(days=1)

        try:

            dt = Utils.showTime2()
            # logger.info(f'{prefix} ============================================' + dt)

            beanstalk = bs.get_client(use=DC_GZH_ANALYSE, watch=[DC_GZH_ANALYSE])
            session = mysql.create_session()
            # data: list[DCGzhArticle] = session.query(DCGzhArticle).where(DCGzhArticle.analysisResult == 2, DCGzhArticle.createAt > '2024-05-25 00:00:00').order_by(DCGzhArticle.id).all()
            data: list[DCGzhArticle] = session.query(DCGzhArticle).where(DCGzhArticle.isAnalysis == 1, DCGzhArticle.analysisResult == 2, DCGzhArticle.analysisRetryTimes < 3, DCGzhArticle.createAt > three_days_ago).order_by(DCGzhArticle.id).all()
            # data: list[DCGzhArticle] = session.query(DCGzhArticle).where(DCGzhArticle.id == 2725458).order_by(DCGzhArticle.id).all()
            logger.info(f'{prefix} where count: {len(data)}')
            if len(data) == 0:
                logger.debug(f"{prefix} 不存在数据，continue")
                time.sleep(60 * 10)
                continue

            for article in data:
                logger.debug(f"{prefix} article:{article.id}")
                article.analysisQueue = 1
                article.isAnalysis = 0
                article.analysisRetryTimes = article.analysisRetryTimes + 1
                article.analysisRetryAt = Utils.showDateTime()
                session.add(article)
                session.commit()

                logger.info(f"{prefix} commit article:{article.id}")

                # article2 = session.query(DCGzhArticle).get(article.id)
                # logger.info(f'{prefix} article2: {article2.id}  isAnalysis: {article2.isAnalysis} analysisQueue: {article2.analysisQueue}')

                # beanstalk
                logger.info(f'{prefix} beanstalk put: {article.id}')
                beanstalk.put(body=json.dumps({'id': article.id, 'createdAt': Utils.showDateTime()}))

                time.sleep(0.1)

            session.commit()
            session.close()

        except Exception as ex:
            logger.error(f"{prefix} exception:" + traceback.format_exc())
            time.sleep(except_sleep)

        finally:
            page += 1

            if session is not None:
                session.close()


handle()
