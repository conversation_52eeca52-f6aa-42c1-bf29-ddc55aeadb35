"""
微信公众号采集-历史文章拉取服务
2022-10-19
"""

import random
import time
import traceback

from sqlalchemy import text

from dc.common.utils import Utils
from dc.conf.settings import get_settings
from dc.models.model import DCGzhArticle, DCGzhInfo, DCGzhWechat
from dc.services.gzh_service import GzhService
from dc.services.logging_service import LoggingService
from dc.services.mongo_service import MongoService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.weixin_service import WeixinService

logger = LoggingService('dc_gzh_history.log')
mysql = MySQLService()
redisService = RedisService('redis')
ms = MongoService()
wx = WeixinService(logger=logger)


settings = get_settings('weixin_gzh')
list_sleep = settings.get('list_sleep', 5)
list_update_days = settings.get('list_update_days', 2)
list_update_hours = settings.get('list_update_hours', 6)
list_update_stop = settings.get('list_update_stop', 3)

time_stop = Utils.time_days(list_update_stop, only_day=True)
time_stop = Utils.str2timestamp(time_stop)
logger.info(f"time_stop: {time_stop}")

enable_gzh_yds = False   # 启用阅读数获取
except_sleep = 10
redis = redisService.get_client()

def show_article(**kwargs):
    """
    进行文章信息处理
    :param kwargs: 参数配置
    :return: None
    """
    print('------show_article----')

    ainfo = kwargs.get('article')
    account_info: dict = kwargs.get('account')
    base_info: dict = kwargs.get('base')
    params: dict = kwargs.get('params')
    gzhid = params.get('gzhid')
    wxid = params.get('wxid')
    wxpid = params.get('wxpid')
    base_url = params.get('base_url')
    logger.info(ainfo)

    session = mysql.create_session()
    url_md5 = wx.calc_url_md5(ainfo['ContentUrl'])
    url_md5_old = wx.calc_url_md5_old(ainfo['ContentUrl'])
    logger.info(f"url_md5: {url_md5} url_md5_old: {url_md5_old} url: {ainfo['ContentUrl']}")
    article = session.query(DCGzhArticle).filter_by(url_md5=url_md5).first()
    if article is not None:  # 文章存在
        logger.info(f"文章存在，忽略 {article.id}")
    else:   # 文章不存在，创建
        article = DCGzhArticle()
        logger.info(f"文章不存在，创建")

        article.url = ainfo['ContentUrl']
        article.url_md5 = url_md5
        article.cover = ainfo['CoverImgUrl']
        article.title = ainfo['Title']
        article.gzhId = account_info['UserName']
        article.wxId = wxid
        article.gzhId = gzhid
        CreateTime = base_info["CreateTime"]
        UpdateTime = base_info["UpdateTime"]
        article.wx_create_time = Utils.timestamp2str(CreateTime)
        article.wx_pub_time = Utils.timestamp2str(UpdateTime)
        article.status = 1
        article.createAt = Utils.showDateTime()

        # 阅读数量
        # if enable_gzh_yds:
        #     yds = wx.req_gzh_yds({
        #         "wxpid": wxpid,
        #         "base_url": base_url,
        #         "url": article.url,
        #         "desc": "文章阅读数"
        #     })
        #
        #     article.readAt = Utils.showDateTime()
        #     article.readNum = Utils.dict_get(yds, ['result', 'read_num'], 0) if yds.get('status') == 200 else None
        #     article.praiseNum = Utils.dict_get(yds, ['result', 'old_like_num'], 0) if yds.get('status') == 200 else None
        #     article.readStatus = 1 if yds.get('status') == 200 else 2
        #     logger.info(f"获取文章阅读数，{yds}")
        #
        #     time.sleep(1.5)

        logger.info(f"插入文章：{article.to_dict()}")
        session.add(article)
        session.commit()

        logger.info(f"插入文章id：{article.id}")

    session.commit()
    session.close()


def handle():

    logger.info('启动公众号-历史文章拉取 服务...')
    logger.info(f'settings: {settings}')

    session = None
    prefix = None

    gzh_service = GzhService()

    while True:

        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        try:

            dt = Utils.showTime2()
            logger.info(f'{prefix} ============================================' + dt)

            session = mysql.create_session()
            # pullAt = Utils.time_days(days=list_update_days, only_day=False)
            pullAt = Utils.time_delta(hours=list_update_hours)
            where = f"status=1 and crawlStatus=1 and (pullAt is null or pullAt < '{pullAt}')"
            # where = f"status=1 and crawlStatus=1 and (pullAt is null)"
            logger.info(f'{prefix} where: {where}')
            data: list[DCGzhInfo] = session.query(DCGzhInfo).where(text(where)).order_by(DCGzhInfo.pullAt).limit(10).all()
            logger.info(f'{prefix} where count: {data}')
            if len(data) == 0:
                logger.info(f"{prefix} 不存在数据，continue")
                time.sleep(20)
                continue

            for row in data:

                try:
                    row.pullStatus = 1

                    logger.info(f"{prefix}-{row.to_json()}")
                    wechat: DCGzhWechat = wx.get_wechat_by_gzh(row.gzhId)
                    if wechat is None:
                        logger.info(f"{prefix} 没有可用的微信，忽略")
                        continue

                    wechat_info = wechat.to_dict() if wechat else {}
                    logger.info(f"{prefix} wechat: {wechat_info}")
                    wx.req_gzh_history({
                        "base_url": wechat.machineIp if wechat else wx.wechat.machineIp,
                        "wxpid": wechat.machinePid if wechat else wx.wechat.machinePid,
                        "wxid": wechat.wxId if wechat else wx.wechat.wxId,
                        "gzhid": row.gzhId,
                        "time_stop": time_stop,
                        "list_sleep": list_sleep,
                        "desc": "获取历史文章",
                        "prefix": prefix
                    }, callback=show_article)

                except Exception as ex2:
                    row.pullStatus = 2
                    row.pullResult = traceback.format_exc()
                    logger.error(f"{prefix}- pullStatus 2 --------- " + traceback.format_exc())

                finally:
                    row.pullAt = Utils.showDateTime()
                    session.add(row)
                    session.commit()

                    # 更新文章数量
                    gzh_service.gzh_article_count(row.gzhId)

                    logger.info(f"{prefix}- 更新公众号信息 {row.id}")

            session.close()

        except Exception as ex:
            logger.error(f"{prefix} pull exception:" + traceback.format_exc())
            logger.error(f"{prefix} exception sleep: {except_sleep}")
            time.sleep(except_sleep)

            # 消息提醒
            # qx = QianxunService()
            # dt = Utils.showDateTime()
            # msg = f"获取公众号历史文章异常 {dt}"
            # title = f"获取公众号历史文章异常 {dt}"
            # body = f"获取公众号历史文章异常 {dt} {traceback.format_exc()}"
            # qx.simple_send({
            #     'type': Utils.get_wechat_type(__file__),
            #     'msg': msg,
            #     'mail_title': title,
            #     'mail_body': body,
            # })

        finally:
            if session is not None:
                session.close()
            # logger.info(f"{prefix} finally ++++")


handle()
