# -*- coding:utf-8 -*-
# @Function  : k8s_cleanup k8s 清理操作
# <AUTHOR> wjh
# @Time      : 2025-01-08
# Version    : 1.0

import argparse
import random
import sys

from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService

logger = LoggingService(logfile="k8s_cleanup.log")
ks = K8sService(logger=logger)


def parse_args():
    """ 设置参数解析 """
    parser = argparse.ArgumentParser(description="Cleanup on Pod termination")
    parser.add_argument('--action', type=str, required=True, help="The action to perform")
    parser.add_argument('--message', type=str, required=False, help="Shutdown message")
    return parser.parse_args()


if __name__ == "__main__":

    try:
        # 解析命令行传递的参数
        args = parse_args()
        logger.info(f"args: {args}")

        # 获取传递的参数值
        action = str(args.action).strip()
        message = str(args.message).strip()
        assert action, "action is empty"
        # assert message, "message is empty"

        # 将 Namespace 转换为字典
        args_dict = vars(args)

        # 执行清理操作
        exit_code = ks.cleanup(**args_dict)
        logger.info(f"exit_code: {exit_code}")
        sys.exit(exit_code)

    except Exception as ex:
        logger.error(f"exception: {str(ex)}")
        sys.exit(-1)
