# -*- coding:utf-8 -*-
# @Function  : dcgzh_push 公众号文章推送 (redis)
# <AUTHOR> wjh
# @Time      : 2025-06-11
# Version    : 1.0

import json
import random
import time

from apscheduler.schedulers.background import BackgroundScheduler

from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhInfo
from dc.services.gzh_service_v2 import GzhServiceV2
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.weixin_service_v2 import WeixinServiceV2

logger = LoggingService('dc_gzh_push_service.log')
mysql = MySQLService()
ks = K8sService()
rs = RedisService(db=6)
redis = rs.get_client()
wx = WeixinServiceV2()

settings = get_settings('weixin_gzh')
PUSH_LOG_MAX=50000

"""
{
  "biz": "MjM5MzMwNjM0MA==",
  "cover": "https://mmbiz.qpic.cn/mmbiz_jpg/HlLt5rdYSZhpEYU3cTHX46vJE4MpJbVYKOSnUArbEviastvUlAdcicpLKbFycEIrY5pFYe8YnZ0piaIiaibO5wfO0bA/640?wxtype=jpeg&wxfrom=0",
  "digest": "",
  "ghid": "gh_cbbad4c1d33c",
  "insert_time": "2025-06-13 12:29:20",
  "name": "中国证券报",
  "play_url": "",
  "pub_time": "2025-06-13 12:26:26",
  "title": "黄金原油飙涨！亚太股市普跌",
  "url": "http://mp.weixin.qq.com/s?__biz=MjM5MzMwNjM0MA==&mid=2651294388&idx=1&sn=92d5e56db08efaf23e24f228ce9fc005&chksm=bc4f2c30836180e2068db846124e77e29925f6a3552559b6b8885ee2e4732b9f658228817f72&scene=0&xtrack=1#rd"
}
"""

def push_log_job(**kwargs):
    """
    日志监控任务
    :param kwargs: 参数
    :return: 执行结果
    :rtype: bool
    """
    try:
        logger.info(kwargs)
        log_length = redis.hlen(DC_GZH_PUSH_LOG)
        logger.info(f"push_log_job redis key: {DC_GZH_PUSH_LOG} length: {log_length}")
        if log_length > PUSH_LOG_MAX:
            redis.delete(DC_GZH_PUSH_LOG)
            logger.info(f"push_log_job redis key: {DC_GZH_PUSH_LOG} length: {log_length} delete")

        return True

    except Exception as ex:
        logger.error(f"push_log_job failed with exception:{str(ex)}")
        return False

def handle():
    logger.info('启动公众号-文章推送 服务...')

    session = None
    prefix = None

    # scheduler
    scheduler = BackgroundScheduler()
    scheduler.add_job(push_log_job, 'interval', minutes=10, max_instances=1, kwargs={'name': 'push_log_job'})
    scheduler.start()

    while True:

        ks.liveness()  # 探针
        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        try:

            session = mysql.create_session()

            # 检查键
            key_type = redis.type(DC_GZH_PUSH)
            ll = redis.llen(DC_GZH_PUSH)
            logger.debug(f"{prefix} redis key：{DC_GZH_PUSH} type: {key_type} length: {ll}")

            # 处理
            item = redis.lpop(DC_GZH_PUSH)
            if item is None:
                logger.debug(f"{prefix} 不存在数据，continue")
                time.sleep(60)
                continue

            item = json.loads(item)
            logger.info(f"{prefix} redis item: {item}")

            try:

                # 检查公众号
                gzh = session.query(DCGzhInfo).filter(DCGzhInfo.gzhId == item['ghid'], DCGzhInfo.status == 1).first()
                if gzh is None:
                    logger.info(f"{prefix} 公众号 {item['ghid']} 不存在，continue")
                    continue

                # 文章处理
                url_md5 = wx.calc_url_md5(item['url'])
                result = redis.hexists(DC_GZH_PUSH_LOG, f"{url_md5}")
                if result:
                    logger.info(f"{prefix} redis exists: {url_md5} continue")
                    continue

                item.setdefault("wxid", None)
                push_data = [item]
                logger.info(f"{prefix} push data: {push_data}")
                result = GzhServiceV2.wechat_push(push_data, logger=logger)
                logger.info(f"{prefix} result: {result}")

                redis.hset(DC_GZH_PUSH_LOG, f"{url_md5}", "1")

                # beanstalk
                logger.debug(f'{prefix} redis hset: {url_md5}')

            except Exception as ex:
                logger.error(f'{prefix} exception: {str(ex)}')

            session.close()


        except Exception as ex:
            logger.error(f"{prefix} pull exception:" + str(ex))
            time.sleep(30)

        finally:
            if session is not None:
                session.close()


handle()
