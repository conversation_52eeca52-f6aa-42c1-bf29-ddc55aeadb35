"""
微信公众号采集-文章分析
2022-10-19
"""

import json
import random
import time
import traceback

import greenstalk

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhArticle, DCGzhInfo, DCGzhWechat
from dc.services.beanstalk_service import BeanstalkService
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.services.weixin_service_v2 import WeixinServiceV2

logger = LoggingService('dc_gzh_job.log')
mysql = MySQLService()
wx = WeixinServiceV2(logger=logger)
bs = BeanstalkService()
ks = K8sService()

test_index = 'wechat'

settings = get_settings('weixin_gzh')
list_sleep = settings.get('list_sleep', 5)
list_update_days = settings.get('list_update_days', 2)
list_update_hours = settings.get('list_update_hours', 6)
list_update_stop = settings.get('list_update_stop', 3)

time_stop = Utils.time_days(list_update_stop, only_day=True)
time_stop = Utils.str2timestamp(time_stop)
logger.info(f"time_stop: {time_stop}")


def show_article(**kwargs):
    """
    进行文章信息处理
    :param kwargs: 参数配置
    :return: None
    """
    print('------show_article----')

    ainfo = kwargs.get('article')
    account_info: dict = kwargs.get('account')
    base_info: dict = kwargs.get('base')
    params: dict = kwargs.get('params')
    gzhid = params.get('gzhid')
    wxid = params.get('wxid')
    wxpid = params.get('wxpid')
    base_url = params.get('base_url')
    logger.info(ainfo)

    session = mysql.create_session()
    url_md5 = wx.calc_url_md5(ainfo['ContentUrl'])
    url_md5_old = wx.calc_url_md5_old(ainfo['ContentUrl'])
    logger.info(f"url_md5: {url_md5} url_md5_old: {url_md5_old} url: {ainfo['ContentUrl']}")
    article = session.query(DCGzhArticle).filter_by(url_md5=url_md5).first()
    if article is not None:  # 文章存在
        logger.info(f"文章存在，忽略 {article.id}")
    else:   # 文章不存在，创建
        article = DCGzhArticle()
        logger.debug(f"文章不存在，创建")

        article.url = ainfo['ContentUrl']
        article.url_md5 = url_md5
        article.cover = ainfo['CoverImgUrl']
        article.title = ainfo['Title']
        article.gzhId = account_info['UserName']
        article.wxId = wxid
        article.gzhId = gzhid
        CreateTime = base_info["CreateTime"]
        UpdateTime = base_info["UpdateTime"]
        article.wx_create_time = Utils.timestamp2str(CreateTime)
        article.wx_pub_time = Utils.timestamp2str(UpdateTime)
        article.status = 1
        article.createAt = Utils.showDateTime()

        # 阅读数量
        # if enable_gzh_yds:
        #     yds = wx.req_gzh_yds({
        #         "wxpid": wxpid,
        #         "base_url": base_url,
        #         "url": article.url,
        #         "desc": "文章阅读数"
        #     })
        #
        #     article.readAt = Utils.showDateTime()
        #     article.readNum = Utils.dict_get(yds, ['result', 'read_num'], 0) if yds.get('status') == 200 else None
        #     article.praiseNum = Utils.dict_get(yds, ['result', 'old_like_num'], 0) if yds.get('status') == 200 else None
        #     article.readStatus = 1 if yds.get('status') == 200 else 2
        #     logger.info(f"获取文章阅读数，{yds}")
        #
        #     time.sleep(1.5)

        # logger.info(f"插入文章：{article.to_dict()}")
        session.add(article)
        session.commit()

        logger.info(f"插入文章：{article.id}")

    session.commit()
    session.close()

def handle():
    """
    公众号-文章分析
    :return:
    """
    logger.info('启动公众号-历史文章列表获取任务...')
    settings = get_settings('weixin_gzh')
    analyse_sleep = settings.get('analyse_sleep', 2)
    dc_config = settings.get('dc_config', [])
    content_xpath = dc_config.get('content_xpath')

    session = None
    prefix = None
    max_reserves = 3
    timeout = 300

    while True:

        ks.liveness()   # 探针
        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        try:

            session = mysql.create_session()
            beanstalk = bs.get_client(use=DC_GZH, watch=[DC_GZH])

            job = beanstalk.reserve(timeout=timeout)
            job_id = job.id
            stats = beanstalk.stats_job(job)
            logger.debug(f"{prefix} beanstalk stats: {stats}")
            reserves = int(stats['reserves'])
            if reserves > max_reserves:
                logger.info(f"{prefix} beanstalk reserves: {reserves} over {max_reserves} delete: job_id:{job_id}")
                beanstalk.delete(job)
                continue

            logger.info(f"{prefix} beanstalk reserve: job_id:{job_id} body:{job.body}")
            job_data = json.loads(job.body)
            gzh_id = job_data['id']
            gzh: DCGzhInfo = session.query(DCGzhInfo).get(gzh_id)
            logger.info(f"{prefix} job_id:{job_id} article:{gzh_id}")
            if gzh is None:
                logger.info(f"{prefix} gzh is empty, beanstalk delete: job_id:{job_id} gzh:{gzh_id}, continue")
                beanstalk.delete(job)
                continue

            if gzh.crawlStatus == 0:
                logger.info(f"{prefix} crawlStatus is 0, beanstalk delete: job_id:{job_id} gzh:{gzh_id}, continue")
                beanstalk.delete(job)
                continue

            wechat: DCGzhWechat = wx.get_wechat_by_gzh(gzh.gzhId)
            if wechat is None:
                logger.info(f"{prefix} 没有可用的微信，忽略，sleep 100秒")
                time.sleep(100)
                continue

            wechat_info = wechat.to_dict() if wechat else {}
            logger.debug(f"{prefix} wechat: {wechat_info}")
            wx.req_gzh_history({
                "base_url": wechat.machineIp if wechat else wx.wechat.machineIp,
                "wxpid": wechat.machinePid if wechat else wx.wechat.machinePid,
                "wxid": wechat.wxId if wechat else wx.wechat.wxId,
                "gzhid": gzh.gzhId,
                "time_stop": time_stop,
                "list_sleep": list_sleep,
                "desc": "获取历史文章",
                "prefix": prefix
            }, callback=show_article)

            # beanstalk
            beanstalk.delete(job)
            logger.info(f"{prefix} beanstalk delete3: job_id:{job_id} gzh:{gzh_id}")

        except greenstalk.TimedOutError as ex:
            logger.info(f"{prefix} greenstalk timeout")
            pass

        except Exception as ex:
            logger.error(f"{prefix} pull exception:" + str(ex))

            # beanstalk release
            pri = int(stats['pri']) + 1
            beanstalk.release(job, pri)
            logger.info(f"{prefix} beanstalk release: {job.id}")
            time.sleep(analyse_sleep)

        finally:
            if session is not None:
                session.close()


handle()
