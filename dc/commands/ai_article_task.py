# -*-coding:utf-8-*-
import time
import sys
import os
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService
from dc.models.model import AIWriteArticle, AIWriteReference

"""
[功能]：Ai写文章任务
[作者]：lws
[日期]：2023-05-20
"""

mysql = MySQLService()
redisService = RedisService('redis')
logService = LoggingService('ai_article_task.log')

redis_prefix = "laravel_database_"
redis_task_list = f"{redis_prefix}write_article_list"
redis_task_set = f"{redis_prefix}write_article_set"
redis_reference_list = f"{redis_prefix}reference_list"


# 处理任务
def handel():
    while True:
        client1 = redisService.get_client()
        articleId = client1.rpop(redis_task_list)
        if not articleId:
            time.sleep(5)
            client1.close()
            continue

        isArticleId = client1.sismember(redis_task_set, articleId)
        if not isArticleId:
            client1.close()
            continue

        client1.srem(redis_task_set, articleId)

        session = mysql.create_session()
        aiArticle: AIWriteArticle = session.query(AIWriteArticle).filter(AIWriteArticle.id == articleId).first()
        if not aiArticle:
            logService.dcPullLog(f"文章任务【{articleId}】不存在")
            session.close()
            client1.close()
            continue

        aiInfo = aiArticle.to_dict()
        if aiInfo['status'] in [0, 2, 3, 4]:
            logService.dcPullLog(f"文章任务【{articleId}】状态异常，状态为【{aiInfo['status']}】")
            session.close()
            client1.close()
            continue

        aiArticle.status = 2
        aiArticle.updatedAt = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        session.add(aiArticle)
        session.commit()

        referenceData = session.query(AIWriteReference).filter(AIWriteReference.articleId == articleId,
                                                               AIWriteReference.status == 0).all()
        if not referenceData:
            client1.lpush(redis_reference_list, json.dumps({'taskId': articleId}))
            logService.dcPullLog(f"文章任务【{articleId}】没有需要执行参考资料")
            session.close()
            client1.close()
            continue

        for referenceInfo in referenceData:
            referenceInfo = referenceInfo.to_dict()
            client1.lpush(redis_reference_list, json.dumps(referenceInfo))
        client1.lpush(redis_reference_list, json.dumps({'taskId': articleId}))
        session.close()
        client1.close()


handel()
