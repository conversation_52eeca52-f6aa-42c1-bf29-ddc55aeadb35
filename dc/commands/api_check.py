"""
api 访问时间检测
:author wjh
:date 2022-11-15
"""

import sys
import os
import time
import traceback

import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.common.func import md5
from dc.services.qianxun_service import QianxunService
from dc.services.logging_service import LoggingService
from dc.conf.settings import get_settings2, get_settings
from dc.common.utils import Utils
from dc.common import utils

# apis = [
#     {
#         'name': 'python uwsgi 接口',
#         'url': 'http://192.168.1.125/dc/status',
#         'params': {},
#         'timeout': 2000,
#         'demo': '备注',
#     },
#     {
#         'name': 'soya 接口',
#         'url': 'http://192.168.1.125/soya/apps/doc/server/?action=test.status',
#         'params': {},
#         'timeout': 2000,
#         'demo': '备注',
#     }
# ]
logger = LoggingService('api_check.log')
qx = QianxunService()

settings = get_settings('api_check')
apis = settings.get('apis')
check_sleep_time = settings.get('check_sleep_time', 60)  # 每次检查接口状态时间间隔  秒
api_sleep_time = settings.get('api_sleep_time', 2)  # 单个api检测间隔时间 秒

logger.info(f"settings: {settings}")


def get_request_time(url: str) -> int:
    """
    获取链接访问时间
    :param url:
    :return:
    """
    try:
        time1 = Utils.millisecond_time()
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        }
        r = requests.get(url, headers=headers)
        r.encoding = 'utf-8'
        # logger.info(r.text)
        time2 = Utils.millisecond_time()
        exec_time = time2 - time1
        logger.info(f"url: {url} status_code: {r.status_code}")
        logger.info(f"url: {url} exec_time: {exec_time}")
        assert r.status_code == 200, 'status_code is not 200'
        return exec_time
    except Exception as ex:
        logger.error(f"exception: {traceback.format_exc()}")
        return -1


i = 0
while True:
    i = i + 1
    dt = Utils.showDateTime()
    logger.info(f"---{i}--{dt}--")

    for api in apis:

        try:
            url = api.get('url')
            name = api.get('name')
            if url is None:
                logger.info(f"url is empty, continue")
                continue

            exec_time = get_request_time(url)
            timeout = api.get('timeout', 3000)
            logger.info(f'exec_time: {exec_time} timeout: {timeout}')
            title = f"接口访问超时:{name}"
            msg = f"接口访问超时:{name} {url}"
            if exec_time == -1 or exec_time > timeout:
                logger.info('出现问题---!!!!!!!!!!!!!')
                qx.simple_send({
                    'type': md5(url)[0:6],
                    'msg': msg,
                    'mail_title': title,
                    'mail_body': msg,
                })
        except Exception as ex:
            logger.error(f"exception: {traceback.format_exc()}")
        finally:
            time.sleep(api_sleep_time)

    time.sleep(check_sleep_time)
