"""
微信公众号采集-公众号阅读数量
2022-10-19
"""

import json
import random
import time
import traceback

from sqlalchemy import text

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhArticle
from dc.services.beanstalk_service import BeanstalkService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.services.weixin_service import WeixinService

logger = LoggingService('dc_gzh_read.log')
mysql = MySQLService()
wx = WeixinService(logger=logger)
bs = BeanstalkService()
beanstalk = bs.get_client(use=DC_GZH_READ, watch=[DC_GZH_READ])
except_sleep = 10

def handle():
    """
    公众号阅读数量
    :return:
    """
    logger.info('启动公众号-阅读数量 服务...')
    settings = get_settings('weixin_gzh')
    read_sleep = settings.get('read_sleep', 2)
    read_update_days = settings.get('read_update_days', 3)
    read_update_interval = settings.get('read_update_interval', 1)

    session = None
    prefix = None

    while True:

        rand = random.randint(100000, 999999)
        prefix = f"[{rand}] "

        wx_create_time = Utils.time_days(read_update_days, only_day=True)
        readAt = Utils.time_days(read_update_interval)

        try:

            dt = Utils.showTime2()
            logger.info(f'{prefix} ============================================' + dt)
            logger.info(f'{prefix} settings {settings}')

            session = mysql.create_session()
            sql_filter = f"status=1 and (wx_create_time > '{wx_create_time}' and (readQueueAt is null or readQueueAt < '{readAt}'))"
            logger.info(f"{prefix} sql_filter: {sql_filter}")
            data: list[DCGzhArticle] = session.query(DCGzhArticle).where(text(sql_filter)).order_by(DCGzhArticle.readQueueAt).limit(20).all()
            if len(data) == 0:
                logger.info(f"{prefix} 不存在数据，continue")
                time.sleep(10)

            for article in data:
                logger.info(f"{prefix} article:{article.id} readQueueAt:{article.readQueueAt}")
                pri = 10000 if article.readQueueAt is None else 65536
                article.readQueueAt = Utils.showDateTime()
                article.readQueue = 1
                session.add(article)
                session.commit()

                # beanstalk
                beanstalk.put(body=json.dumps({'id': article.id, 'createdAt': Utils.showDateTime()}), priority=pri)
                logger.info(f"{prefix} beanstalk put:{article.id} pri:{pri}")

                article2 = session.query(DCGzhArticle).get(article.id)
                logger.info(f"{prefix} article2: {article2.id} readQueueAt:{article2.readQueueAt}")
                session.commit()

                # time.sleep(0.1)

            session.commit()
            session.close()

        except Exception as ex:
            logger.error(f"{prefix} pull exception:" + traceback.format_exc())
            logger.error(f"{prefix} exception sleep: {except_sleep}")
            time.sleep(except_sleep)

        finally:
            if session is not None:
                session.close()
            logger.info(f"{prefix} finally ++++")


handle()
