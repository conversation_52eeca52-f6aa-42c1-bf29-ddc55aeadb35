import re
import traceback

import requests
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import sys
import os
import threading

from selenium.webdriver.common.by import By

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.models.model import DCCalendar, DCStockQA, DCStockInfo
from dc.services.check_analysis import CheckAnalysis
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from dc.services.aigc_service import AIGCService
from seleniumwire import webdriver as wwdriver  # 导入 Selenium Wire

mysql1 = MySQLService()
log_service = LoggingService('concept_stock.log')
redis_client = RedisService()
gpt = AIGCService()

stock_market_list = "stock_market_list"

header = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0",
}

answer = ['尊敬的投资者，您好', '尊敬的投资者您好', '投资者您好', '尊敬的投资人，您好', '非常感谢您的建议', '您好，非常感谢您对XXXX的关注和建议', '（来自: 投资者关系互动平台）',
          '尊敬的投资者，感谢您对公司的关注', '您好，感谢您的关注', '感谢您关注我公司', '谢谢您的问题', '谢谢您的建议', '谢谢您的提示和关注', '也敬请您关注和期待', '谢谢', '谢谢您的关注',
          '感谢您对XXXX的关注和支持', '感谢您对公司的关注', '谢谢您对公司的关注', '感谢您的支持与关注', '感谢关注', '谢谢关注', '感谢您提出的宝贵建议', '再次感谢您的关注和支持',
          '(来自：深交所互动易)', '（来自: 上证 E 互动）', '感谢您的关注与建议', '祝您投资顺利', '感谢您的提问', '请参照公司之前的回复', '感谢您对公的关注，祝您生活愉快',
          '祝您生活愉快', '感谢您对公司的关注和建议', '祝您投资愉快', '敬请关注', '感谢您的持续关注', '感谢您对公司的关心', '尊敬的投资者', '您好', '你好']

question = ["您好董秘", "董秘您好", "董秘你好", "董秘，您好", "尊敬的董秘，你好", "尊敬的董秘你好", "尊敬的董秘您好", "领导您好", "董秘好", "谢谢啦", "不好意思",
            "尊敬的董秘：您好", "请问:董秘", "公司你好", "（来自: 投资者关系互动平台）", "(来自：深交所互动易)", "（来自: 上证 E 互动）", "谢谢", "您好", "你好"]

list3 = ["目前", "正常", "预计", "订单", "暂不", "计划", "投产", "出货", "建成", "不存在", "调试", "试产", "现有", "产能",
         "充足", "将于", "达产", "推进", "爬坡", "具备", "量产", "流片", "处于", "能见度", "扩产", "尚未", "验证",
         "送样", "试生产", "小规模", "进入", "正式", "批量", "供货", "可用于", "正在", "对接", "争取", "力争",
         "少量", "交付", "有望", "增长", "供应", "成功", "饱满", "产业链", "推出", "试运行", "已", "向" "由"]

list4 = [",", "，", "。", "?", "？", ":", "：", "!", "！", " ", ";", "；"]

list5 = ["你好", "您好", "感谢", "谢谢", "祝您", "祝你", "敬请", "尊敬的", "\(来自", "你的回答", "您的回答", "董秘", "您的提问", "不好意思", "公司你好",
         "请参照公司之前的回复", "深交所互动易"]

# 基金代码
code_list1 = set()
code_list2 = set()
code_list3 = set()
# 基金池
codes = []
# 每次取两小时数据
curr_time = time.time() - 7200
curr_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(curr_time))
history_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(curr_time - 3600))


#  处理问题答案内容
def filterInfo(content, type1, list2):
    # for l2 in list2:
    #     content = content.replace(l2, '')

    filter_str = ["领导，您好", "领导您好", "领导，你好", "领导你好", "答：", "答:"]
    for filter_str1 in filter_str:
        content = content.replace(filter_str1, '')

    content_tmp = content
    for l4 in list4:
        content_tmp = content_tmp.replace(l4, '，')

    content_arr = content_tmp.split('，')

    list_tmp = []
    for l1 in content_arr:
        for l5 in list5:
            if not re.findall(l5, l1):
                continue

            for l3 in list3:
                if re.findall(l3, l1):
                    content = content.replace(l5, '')
                    continue

            list_tmp.append(l1)

    for l2 in list_tmp:
        content = content.replace(l2, '')

    left_str = content[0:1]
    while left_str in list4:
        content = content[1:]
        left_str = content[0:1]

    right_str = content[-1:]
    while right_str in list4:
        content = content.replace('\n', '')
        content = content.strip()
        content = content[0:-1]
        right_str = content[-1:]

    content = content.strip()

    end_sign = "?" if type1 == 1 else "。"
    content = content + end_sign

    r = re.compile(r'([，！？。；.,/#!$^&*;:{}=_`~()-])[，！？。；.,/#!$^&*;:{}=_`~()-]+')
    content = r.sub(r'\1', content)

    return content


# text = "感谢您关注我公司。公司与中石油、七台河城投公司共同成立的合资公司目前正在办理土地审批手序。"
# s = SnowNLP(filterInfo(text, 2, answer))
# print(s.summary(limit=1))
# exit()


# 处理标题
def dealTitle(content='', stock_name1=''):
    # content1 = f'''
    # 请提取新闻标题，“{content}”，保留25个字以内
    # '''
    # title2 = gpt.chat(content1)
    # if title2 != "broken":
    #     arr1 = title2.split('：')
    #     title2 = arr1[0] if len(arr1) == 1 else arr1[1]
    #     right_str = title2[-1:]
    #     while right_str in list4:
    #         title2 = title2[0:-1]
    #         right_str = title2[-1:]
    #     return stock_name1 + "：" + title2

    content_tmp = content
    l1 = [",", "。", "?", "？", ";", "；", "!", "！"]
    for l11 in l1:
        content_tmp = content_tmp.replace(l11, '，')

    arr = content_tmp.split('，')
    num = num1 = 0
    title1 = arr[0]
    for words in arr:
        for keyword in list3:
            if re.findall(keyword, words):
                num += 1
        if num > num1:
            title1 = words
            num1 = num
        num = 0

    if title1 == '':
        return ''

    arr_title1 = content.split(f"{title1}")
    tmp_title = arr_title1[1] if len(arr_title1) > 1 else ''
    tmp_title = tmp_title.strip()
    title11 = title1 + tmp_title

    filter_arr = [stock_name1 + "的", stock_name1, '控股公司的', '控股公司', '控股子公司的', '控股子公司', '子公司的', '子公司',
                  '公司的', '公司', '目前']
    for filter_a in filter_arr:
        title11 = title11.replace(filter_a, '')

    title11_tmp = title11
    for l11 in l1:
        title11_tmp = title11_tmp.replace(l11, '，')
    arr1 = title11_tmp.split('，')

    len1 = len(arr1[0])
    all_len = 25 - len1
    for k, v in enumerate(arr1):
        if k == 0:
            continue

        if all_len - len(v) - 1 > 0:
            len1 = len1 + len(v) + 1
            all_len = all_len - len(v) - 1
        else:
            break

    left_str = title11[0:1]
    ll4 = [",", "，", "。", "?", "？", ":", "：", "!", "！", " ", ";", "；", "、"]

    while left_str in ll4:
        title11 = title11[1:]
        left_str = title11[0:1]

    title11 = title11[0:len1]
    title11 = stock_name1 + "：" + title11
    title11 = title11[0:30] if len(title11) > 30 else title11

    right_str = title11[-1:]
    while right_str in ll4:
        title11 = title11[0:-1]
        right_str = title11[-1:]

    return title11


# str1 = "您好，报告期各期末，公司股东权益分别为13,807.40万元、17,031.24万元、21,473.60万元和25,798.58万元。报告期内，公司经营情况良好，利润规模逐年增加，公司股东权益逐年增长。谢谢。"
# answer_str = filterInfo(str1, 2, answer)
# print(answer_str)
# asd = dealTitle(answer_str, "剋及公司")
# print(asd)
# exit()


# 处理文章摘要
def dealSummary(content=''):
    # content1 = f'''
    # 请提取文章摘要，“{content}”，保留90个字以内
    # '''
    # summary = gpt.chat(content1)
    # if summary != "broken":
    #     arr1 = summary.split('：')
    #     summary = arr1[0] if len(arr1) == 1 else arr1[1]
    #     right_str = summary[-1:]
    #     while right_str in list4:
    #         summary = summary[0:-1]
    #         right_str = summary[-1:]
    #     return summary + "。"

    arr = content.split('。')
    num = num1 = 0
    summary1 = arr[0]
    for words in arr:
        for keyword in list3:
            if re.findall(keyword, words):
                num += 1
        if num > num1:
            summary1 = words
            num1 = num
        num = 0

    if summary1 == '':
        return ''

    arr_summary1 = content.split(f"{summary1}")
    tmp_summary = arr_summary1[1] if len(arr_summary1) > 1 else ''
    summary11 = summary1 + tmp_summary
    summary11 = summary11.strip()

    right_str = summary11[-1:]
    while right_str in list4:
        summary11 = summary11[0:-1]
        right_str = summary11[-1:]

    summary_ret = summary11[0:90] + "。"

    r = re.compile(r'([，！？。；.,/#!$^&*;:{}=_`~()-])[，！？。；.,/#!$^&*;:{}=_`~()-]+')
    summary_ret = r.sub(r'\1', summary_ret)

    return summary_ret


# print(dealTitle('尊敬的投资者你好，公司四川募投项目正在进行土建收尾工作和设备的安装调试，项目正式投产时间请关注公司披露的定期报告和临时公告相关内容，谢谢', '华为证券'))
# exit()
# print(filterInfo("董秘您好，公司你好，贵公司现在跟浙江华坤道威是什么关系。其主要是做哪方面技术业务的，谢谢?(来自：深交所互动易)", 1, question))
# print(filterInfo("您好！感谢您对公司的关注。2023年以来公司股东中国中化集团未减持公司股份，以前年度减持系其根据自身经营需求做出减持的决策，不影响双方在钾肥及其他领域的合作。多年来，公司始终与中国中化集团保持良好合作关系，有力拓展了公司钾肥销售。 (来自：深交所互动易)", 2, answer))
# exit()


# 获取同花顺数据
# def getTHSInfo(stock_codes):
#     log_service.dcPullLog('拉取股票行情信息')
#     chrome_options1 = Options()
#     chrome_options1.add_argument('--headless')
#     chrome_options1.add_argument('--no-sandbox')
#     chrome_options1.add_argument('--disable-gpu')
#     driver1 = webdriver.Chrome(options=chrome_options1)
#     driver1.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
#         'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
#     })
#
#     for stock_code in stock_codes:
#         log_service.dcPullLog(f"拉取股票信息：{stock_code}")
#         while True:
#             try:
#                 driver1.get(f"https://d.10jqka.com.cn/v2/realhead/hs_{stock_code}/last.js")
#                 info1 = driver1.find_element("xpath", "/html").text
#                 if len(info1) > 48:
#                     log_service.dcPullLog(f"拉取股票信息成功：{stock_code}")
#                     break
#                 else:
#                     log_service.dcPullLog(f"拉取股票信息重试：{stock_code}")
#                     time.sleep(10)
#             except:
#                 log_service.dcPullLog(f"打开页面异常：{stock_code}")
#                 time.sleep(10)
#                 pass
#
#         if len(info1) > 48:
#             log_service.dcPullLog(f"处理股票信息：{stock_code}")
#             info1 = info1[48:]
#             info1 = info1[:-2]
#             info1 = json.loads(info1)
#
#             if not info1['3541450']:
#                 continue
#
#             if info1['stockStatus'] == '停牌':
#                 up_sql = f"update DC_Stock_QA set is_pull = 2 where stock_code = {stock_code} and is_pull = 1"
#                 session.execute(up_sql)
#                 session.commit()
#                 session.close()
#             session = mysql1.create_session()
#
#             all_price = "{:.2f}".format(float(info1['3541450']) / 100000000)
#             up_sql = f"update DC_Stock_QA set capitalization = {all_price}, stock_price = {info1['10']}, ratio = {info1['199112']}, is_pull = 2 where is_pull = 1 and stock_code = {stock_code}"
#             session.execute(up_sql)
#             session.commit()
#             session.close()
#             log_service.dcPullLog(f"处理股票信息成功：{stock_code}")
#     driver1.close()


# getTHSInfo(
#     {'002549', '002289'})
# exit()



def get_body(driver, page):
    # 访问请求
    for request in driver.requests:
        if request.response:
            # print(
                # request.path,
                # request.response.status_code,
                # request.response.headers['Content-Type']
            # )
            if request.path.endswith('getNewSearchR.shtml') and int(request.params.get('page', -1)) == page:
                result = request.response.body
                if result:
                    result = result.decode('utf-8')
                    return result

    return False


def get_page_content(url, page):
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-gpu')
    wdriver = wwdriver.Chrome(executable_path="/usr/local/bin/chromedriver", options=chrome_options)
    wdriver.implicitly_wait(10)
    wdriver.get(url)

    print(f"page: {page}")
    page_num = wdriver.find_element(By.XPATH, '//div[@class="m-pagination-group"]/input')
    jump = wdriver.find_element(By.XPATH, '//button[@data-page-btn="jump"]')
    page_num.send_keys(page)
    jump.click()
    time.sleep(1)

    wdriver.find_element(By.XPATH, f'//li[@class="active"]/a[@data-page-index="{page - 1}"]')
    bb = get_body(wdriver, page - 1)
    wdriver.quit()

    return json.loads(bb)


#  ir.p5w.net  投资者互动平台
def getP5w():
    filter_list = getHistoryData(source=2)
    flag = False
    for pageNo in range(0, 21):
        log_service.dcPullLog('getP5w' + str(pageNo))
        # params = {"isPagination": 1, "keyWords": "", "companyCode": "", "companyBaseinfoId": "", "page": pageNo,
        #           "rows": 5}
        # res = requests.post("https://ir.p5w.net/interaction/getNewSearchR.shtml", headers=header, params=params,
        #                     verify=False)
        # res.encoding = "UTF-8"
        # if res.status_code != 200:
        #     log_service.dcPullLog('getP5w' + str(pageNo) + "获取数据失败")
        #     continue
        #
        # result1 = json.loads(res.text)

        result1 = get_page_content('https://ir.p5w.net/question/', pageNo + 1)
        if not result1:
            log_service.dcPullLog('getP5w' + str(pageNo) + "获取数据失败")
            continue

        data1 = result1['rows']
        session = mysql1.create_session()
        for info in data1:
            if info['pid'] in filter_list:
                continue

            if info['companyCode'] not in codes:
                continue

            # if curr_date > info['replyerTimeStr']:
            #     flag = True
            #     break

            answer_str = filterInfo(info['replyContent'], 2, answer)
            question_str = filterInfo(info['content'], 1, question)
            title_str = dealTitle(answer_str, info['companyShortname'])
            if len(title_str) < 6 or len(question_str) <= 1 or len(answer_str) <= 1:
                continue

            unique_id = CheckAnalysis.getMd5(info['content'].strip() + str(info['companyCode']))
            try:
                insert_info = {"original_question": info['content'], "original_answer": info['replyContent'],
                               "answer_date": info['replyerTimeStr'][0:10],
                               "unique_str": info['pid'], "stock_code": info['companyCode'],
                               "stock_name": info['companyShortname'], "ratio": '0.00',
                               "source": 2, "title": title_str,
                               "question": question_str, "answer": answer_str,
                               "capitalization": '0.00', "stock_price": '0.00',
                               'create_at': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                               'update_at': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                               'summary': dealSummary(answer_str), 'tags': '', 'unique_question': unique_id}
                dch = DCStockQA(**insert_info)
                session.add(dch)
                code_list2.add(str(info['companyCode']))
                filter_list.append(info['pid'])
                session.commit()
            except Exception as ex:
                log_service.info('getP5w' + str(pageNo) + "存在插入数据失败情况" + unique_id)
                log_service.info(f"失败数据问题 {unique_id}：【" + info['content'] + "】")
                log_service.info(f'getP5w {unique_id}' + traceback.format_exc())
                pass
        session.close()
        if flag:
            break


# getP5w()
# exit()


#  sns.sseinfo.com  上证e互动
def getEHuDong():
    filter_list = getHistoryData(source=3)
    flag = False
    # for page_no in range(1, 1001):
    for page_no in range(1, 51):
        log_service.dcPullLog('getEHuDong' + str(page_no))
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-gpu')
        driver = webdriver.Chrome(executable_path="/usr/local/bin/chromedriver", options=chrome_options)
        driver.implicitly_wait(10)
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
        })

        driver.get(
            f"http://sns.sseinfo.com/ajax/feeds.do?rnd=0.20088653929574685&type=11&pageSize=10&lastid=-1&show=1&page={page_no}")
        xpath_obj = driver.find_elements('xpath', '//div[@class="m_feed_item"]')

        session = mysql1.create_session()
        for obj in xpath_obj:
            uniquid = obj.get_attribute("id")
            if uniquid in filter_list:
                continue

            company_code = driver.find_element('xpath',
                                               f"//div[@id='{uniquid}']//div[@class='m_feed_detail m_qa_detail']//div["
                                               f"@class='m_feed_txt']/a").text
            question1 = driver.find_element('xpath',
                                            f"//div[@id='{uniquid}']//div[@class='m_feed_detail m_qa_detail']//div["
                                            f"@class='m_feed_txt']").text
            answer1 = driver.find_element('xpath',
                                          f"//div[@id='{uniquid}']//div[@class='m_feed_detail m_qa']//div["
                                          f"@class='m_feed_txt']").text
            answer_time = driver.find_element('xpath',
                                              f"//div[@id='{uniquid}']//div[@class='m_feed_detail m_qa']//div["
                                              f"@class='m_feed_from']/span").text
            answer_user = driver.find_element('xpath',
                                              f"//div[@id='{uniquid}']//div[@class='m_feed_detail m_qa']//div["
                                              f"@class='m_feed_face']/p").text

            company_code = re.search("\\d{6}", company_code).group()
            if company_code not in codes:
                continue

            if not re.findall("前", answer_time):
                flag = True
                break

            if re.findall("小时前", answer_time):
                num = re.search("\\d{1,2}", answer_time).group()
                if int(num) > 2:
                    flag = True
                    break

            answer_str = filterInfo(answer1, 2, answer)
            replace_str = f':{answer_user}({company_code})'
            question1 = question1.replace(replace_str, '')
            unique_id = CheckAnalysis.getMd5(question1.strip() + str(company_code))
            question_str = filterInfo(question1, 1, question)
            title_str = dealTitle(answer_str, answer_user)
            if len(title_str) < 6 or len(question_str) <= 1 or len(answer_str) <= 1:
                continue
            try:
                insert_info = {"original_question": question1, "original_answer": answer1,
                               "answer_date": time.strftime("%Y-%m-%d", time.localtime(time.time())),
                               "unique_str": uniquid, "stock_code": company_code,
                               "stock_name": answer_user, "ratio": '0.00',
                               "source": 3, "title": title_str,
                               "question": question_str, "answer": answer_str,
                               "capitalization": '0.00', "stock_price": '0.00',
                               'create_at': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                               'update_at': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                               'summary': dealSummary(answer_str), 'tags': '', 'unique_question': unique_id}
                dch = DCStockQA(**insert_info)
                session.add(dch)
                code_list3.add(str(company_code))
                filter_list.append(uniquid)
                session.commit()
            except:
                log_service.info('getEHuDong' + str(page_no) + "存在插入数据失败情况" + unique_id)
                log_service.info("失败数据问题：【" + question1 + "】")
                pass
        session.close()
        if flag:
            break


# getEHuDong()
# exit()


#  irm.cninfo.com.cn  互动易
def getHuDongYi():
    filter_list = getHistoryData(source=1)
    flag = False
    # for page_no in range(1, 1001):
    for page_no in range(1, 51):
        log_service.dcPullLog('getHuDongYi' + str(page_no))
        session = mysql1.create_session()
        params1 = {"pageNo": page_no, "pageSize": 10, "searchTypes": 11, "market": "", "industry": "", "stockCode": ""}
        res = requests.post("http://irm.cninfo.com.cn/newircs/index/search", headers=header, params=params1)
        res.encoding = "UTF-8"
        if res.status_code != 200:
            log_service.dcPullLog('getHuDongYi' + str(page_no) + "获取数据失败")
            continue

        result1 = json.loads(res.text)
        for info1 in result1['results']:
            if info1['stockCode'] not in codes:
                continue

            if info1['esId'] in filter_list:
                continue

            answer_str = filterInfo(info1['attachedContent'], 2, answer)
            answer_time = int(info1['attachedPubDate'][0:10])
            # if answer_time < curr_time:
            #     flag = True
            #     break

            unique_id = CheckAnalysis.getMd5(info1['mainContent'].strip() + str(info1['stockCode']))
            question_str = filterInfo(info1['mainContent'], 1, question)
            title_str = dealTitle(answer_str, info1['companyShortName'])
            if len(title_str) < 6 or len(question_str) <= 1 or len(answer_str) <= 1:
                continue

            try:
                insert_info = {"original_question": info1['mainContent'], "original_answer": info1['attachedContent'],
                               "answer_date": time.strftime("%Y-%m-%d", time.localtime(answer_time)),
                               "unique_str": info1['esId'], "stock_code": info1['stockCode'],
                               "stock_name": info1['companyShortName'], "ratio": '0.00',
                               "source": 1, "title": title_str,
                               "question": question_str, "answer": answer_str,
                               "capitalization": '0.00', "stock_price": '0.00',
                               'create_at': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                               'update_at': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                               'summary': dealSummary(answer_str), 'tags': '', 'unique_question': unique_id}
                dch = DCStockQA(**insert_info)
                session.add(dch)
                code_list1.add(str(info1['stockCode']))
                filter_list.append(info1['esId'])
                session.commit()
            except:
                log_service.info('getHuDongYi' + str(page_no) + "存在数据插入失败情况" + unique_id)
                log_service.info("失败数据问题：【" + info1['mainContent'] + "】")
                pass
        session.close()
        if flag:
            break


# getHuDongYi()
# exit()


def getHistoryData(source=1):
    session = mysql1.create_session()
    ret_filter = session.query(DCStockQA).filter(DCStockQA.source == source, DCStockQA.create_at >= history_date).all()

    filter_list = []
    for filter_info in ret_filter:
        tmp1 = filter_info.__dict__
        filter_list.append(tmp1['unique_str'])

    return filter_list


def getStockInfo():
    session = mysql1.create_session()
    code_list4 = session.query(DCStockInfo).all()

    for code_list4 in code_list4:
        tmp1 = code_list4.__dict__
        codes.append(tmp1['stock_code'])

    log_service.dcPullLog("获取基础基金信息：" + str(len(codes)) + "条")


# getP5w()
# exit()

log_service.dcPullLog('开始执行')
t0 = threading.Thread(target=getStockInfo)
t0.start()
t0.join()
log_service.dcPullLog('获取股票基础信息完成')

t = threading.Thread(target=getHuDongYi)
t.start()
t1 = threading.Thread(target=getEHuDong)
t1.start()
t2 = threading.Thread(target=getP5w)
t2.start()

t.join()
log_service.dcPullLog('任务一加入')
t1.join()
log_service.dcPullLog('任务二加入')
t2.join()
log_service.dcPullLog('任务三加入')
log_service.dcPullLog('获取问答信息完成')
code_list = code_list1 | code_list2 | code_list3
log_service.dcPullLog('合并股票信息完成')

redis_c = redis_client.get_client()
for code_num in code_list:
    redis_c.lpush(stock_market_list, code_num)

exit()
# getTHSInfo(code_list)
# exit(1111)


#  获取日历信息
def getCalendar():
    r = requests.get("https://api.apihubs.cn/holiday/get?size=500&year=2023", headers=header)
    r.encoding = "UTF-8"

    if r.status_code != 200:
        print("获取数据失败")
        exit()

    result = json.loads(r.text)
    data = result['data']['list']

    mysql = MySQLService()
    session = mysql.create_session()
    for day in data:
        df = {'year': str(day['year']),
              'month': str(day['month']),
              'date': str(day['date']),
              'yearweek': str(day['yearweek']),
              'yearday': str(day['yearday']),
              'lunar_year': str(day['lunar_year']),
              'lunar_month': str(day['lunar_month']),
              'lunar_date': str(day['lunar_date']),
              'lunar_yearday': str(day['lunar_yearday']),
              'week': day['week'],
              'weekend': day['weekend'],
              'workday': day['workday'],
              'holiday': str(day['holiday']),
              'holiday_or': str(day['holiday_or']),
              'holiday_overtime': str(day['holiday_overtime']),
              'holiday_today': day['holiday_today'],
              'holiday_legal': day['holiday_legal'],
              'holiday_recess': day['holiday_recess']}
        dch = DCCalendar(**df)
        session.add(dch)
    session.commit()
    session.close()
