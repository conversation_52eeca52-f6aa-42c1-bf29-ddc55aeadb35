# -*- coding:utf-8 -*-
# @Function  : ELK 信息提示
# <AUTHOR> wjh
# @Time      : 2023/8/31
# Version    : 1.0

import json
import time
import traceback
from datetime import datetime, timedelta

from dc.models.model import ELKError
from dc.services.mysql_service import MySQLService
from dc.services.qianxun_service import QianxunService
from dc.services.weixin_service import WeixinService
from dc.services.logging_service import LoggingService
from dc.conf.defines import *
from confluent_kafka import Consumer, Producer, KafkaException, Message, KafkaError, TopicPartition
from jwtools.func import *
from dc.conf.settings import get_settings, RUN_MODE
from dc.services.kafka_service import KafkaService
from dc.services.elasticsearch_service import ElasticsearchService

import time
import schedule
from confluent_kafka.admin import AdminClient, ConfigResource
from confluent_kafka import Consumer, KafkaException
from elasticsearch import Elasticsearch

# Kafka 配置
kafka_config = {
    'bootstrap.servers': '120.79.71.191:9092',
    'security.protocol': 'SASL_PLAINTEXT',
    'sasl.mechanisms': 'PLAIN',
    'sasl.username': 'elk',
    'sasl.password': 'elk@q6wcbkj',
    'group.id': 'monitor_group',
    'auto.offset.reset': 'earliest'
}
# Elasticsearch 配置
es = Elasticsearch(['*************************************************'])


# 初始化 Kafka 客户端
def init_kafka_consumer():
    return Consumer(kafka_config)


# 获取 Kafka topic 的消费情况
def monitor_kafka_topics(consumer):
    consumer.subscribe(['elk_log_prod_python', 'elk_log_prod_php'])  # 替换为你的 topic
    topic_partition_count = {}
    try:
        while True:
            msg = consumer.poll(timeout=10.0)
            if msg is None:
                break
            if msg.error():
                if msg.error().code() == KafkaError._PARTITION_EOF:
                    continue
                else:
                    print(msg.error())
                    break
            topic = msg.topic()
            partition = msg.partition()
            topic_partition_count[topic] = topic_partition_count.get(topic, {})
            topic_partition_count[topic][partition] = topic_partition_count[topic].get(partition, 0) + 1
    except KafkaException as e:
        print(f"Error monitoring Kafka topics: {str(e)}")
    finally:
        consumer.close()
    for topic, partitions in topic_partition_count.items():
        partition_sum = sum(partitions.values())
        print(f"Topic: {topic}, Total Messages: {partition_sum}, Per Partition: {partitions}")


# 获取 Elasticsearch 索引的文档数量
def monitor_elasticsearch_indices():
    indices = ['your_index']  # 替换为你的索引
    query = {
        "query": {
            "match_all": {}
        }
    }
    for index in indices:
        count = es.count(index=index, body=query)['count']
        print(f"Index: {index}, Document Count: {count}")


# 定时任务
def job():
    consumer = init_kafka_consumer()
    monitor_kafka_topics(consumer)
    # monitor_elasticsearch_indices()


# 每隔 10 分钟执行一次任务
schedule.every(20).seconds.do(job)

if __name__ == "__main__":
    while True:
        schedule.run_pending()
        time.sleep(1)
