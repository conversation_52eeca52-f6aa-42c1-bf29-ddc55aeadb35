# -*- coding:utf-8 -*-
# @Function  : ELK 日志消费服务
# <AUTHOR>
# @Time      : 2024/8/7
# Version    : 1.0

import re
from datetime import datetime, timedelta

from confluent_kafka import Message
from jwtools.func import *

from dc.commands.elk.elk_utils import *
from dc.models.model import ELKError
from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.kafka_service import KafkaService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService

run_mode = 'prod'
topics = parse_arguments()
print(f"the topics is: {topics}")
if not topics:
    print('topic is not allowed empty')
    time.sleep(10)
    exit(0)

logfile = f"elk_consumer_{topics[0]}.log" if topics else 'elk_consumer.log'
# group_id = topics[0] if topics else 'consumer_group_elk_kafka_es'
group_id = 'consumer_group_elk_kafka_es'

logger = LoggingService(logfile)
kafka = KafkaService(run_model=run_mode)
ess = ElasticsearchService(name='elasticsearch_elk', run_model=run_mode)
es = ess.get_connect()
mysql = MySQLService(run_model=run_mode)
session = mysql.create_session()

# 错误类型定义
patterns = [
    (1, r'org\.openqa\.selenium\.NoSuchSessionException'),
    (2, r'raise Exception\(\'response is null\'\)'),
    (3, r'task is failed after \d+ times'),
    (4, r'TimeoutError: timed out'),
    (5, r'greenstalk\.NotFoundError'),
    (6, r'Read timed out'),
    (7, r'net::ERR_CONNECTION_TIMED_OUT'),
    (8, r'Max retries exceeded with url'),
    (9, r'打开网页失败, 失败原因'),
    (10, r'Message: Could not start a new session'),
    (11, r'Incorrect string value'),
    (12, r'Message: unknown error: net::ERR_NAME_NOT_RESOLVED'),
    (13, r"Can't connect to MySQL server on")
]


def get_error_type(elkError: ELKError):
    """ 获取错误类型 """
    # 遍历数组，匹配输入字符串
    for number, pattern in patterns:
        if re.search(pattern, elkError.message_message):
            return number

    return 0


def error_cb(err):
    """ kafka 错误回调 """
    logger.error(f"Error: {err}")


try:
    consumer = kafka.get_consumer(extra={
        'group.id': group_id,
        'auto.offset.reset': 'earliest',
        'error_cb': error_cb,
        # 'stats_cb': stats_cb,
        # 'on_commit': on_commit
    })

    consumer.subscribe(topics)

    while True:

        try:
            msg: Message = consumer.poll(5.0)
            if msg is None:
                continue

            if msg.error():
                logger.error("Consumer error: {}".format(msg.error()))
                continue

            # Received message
            data = {
                'topic': msg.topic(),
                'partition': msg.partition(),
                'offset': msg.offset(),
                'key': msg.key(),
                'value': msg.value().decode('utf-8'),
                'timestamp': msg.timestamp(),
            }

            message: dict = json.loads(data.get('value'))
            message2: dict = get_message(message.get('message'))
            message['message2'] = message2
            # json_decode = 0 if message2 is False else 1
            # message['debug'] = {'json': json_decode}
            # log_type = message.get('log', {}).get('topic')
            logger.info(data)

            # Elasticsearch indexing
            index_name = message.get('fields', {}).get('log_type')  # 例如： elk_log_prod_php
            result = es.index(index=index_name, body=message)
            logger.info(result)

            # MySQL logging for errors
            try:

                # 未正确识别错误 或 错误级别为 ERROR 数据
                if not message2 or str(message2.get('levelname', '')).upper() == "ERROR":
                    esid = result.get('_id')
                    log_service = message.get('fields', {}).get('log_service')  # elk_log_prod_php
                    log_time = message.get('@timestamp', {})
                    dt_utc = datetime.datetime.strptime(log_time, "%Y-%m-%dT%H:%M:%S.%fZ")
                    dt_local = dt_utc + timedelta(hours=8)
                    log_time = dt_local.strftime("%Y-%m-%d %H:%M:%S")
                    env = message.get('fields', {}).get('env')
                    host = message.get('host', {}).get('name')

                    # elk error
                    elk_error = ELKError()
                    elk_error.index = index_name
                    elk_error.esid = esid
                    elk_error.host = host
                    elk_error.env = env
                    elk_error.service = log_service
                    elk_error.log_time = log_time
                    elk_error.message = json.dumps(message)
                    elk_error.message_func = message['message2'].get('func')
                    elk_error.message_lineno = message['message2'].get('lineno')
                    elk_error.message_pathname = message['message2'].get('pathname')
                    elk_error.message_time = message['message2'].get('time')
                    elk_error.message_levelno = message['message2'].get('levelno')
                    elk_error.message_levelname = message['message2'].get('levelname')
                    elk_error.message_process = message['message2'].get('process')
                    elk_error.message_message = message['message2'].get('message')
                    elk_error.error_type = get_error_type(elk_error)
                    session.add(elk_error)
                    session.commit()

            except Exception as ex:
                logger.error("elk mysql exception:" + str(ex))
                session.rollback()

        except Exception as ex:
            logger.error("elk exception:" + str(ex))
            time.sleep(10)

except Exception as ex:
    logger.error("exception:" + str(ex))

finally:
    if consumer:
        consumer.close()


