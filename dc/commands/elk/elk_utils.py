# -*- coding:utf-8 -*-
# @Function  : ELK 日志消费服务
# <AUTHOR>
# @Time      : 2024/8/7
# Version    : 1.0

import argparse
import datetime
import json


def parse_arguments():
    """ parse arguments for topic"""
    parser = argparse.ArgumentParser(description="Parse topics from command line")
    parser.add_argument('--topic', type=str, help="Comma separated list of topics")
    args = parser.parse_args()
    if args.topic:
        topics = args.topic.split(',')
    else:
        topics = []
    return topics


def execute_time_range(start_time_str, end_time_str) -> bool:
    current_time = datetime.datetime.now().time()

    # 将字符串转换为time对象
    start_time = datetime.datetime.strptime(start_time_str, "%H:%M:%S").time()
    end_time = datetime.datetime.strptime(end_time_str, "%H:%M:%S").time()

    if start_time <= current_time <= end_time:
        return True
    else:
        return False


def get_message(text):
    """ get message """
    result = None
    try:
        result = json.loads(text)
    except:
        result = {}

    return result
