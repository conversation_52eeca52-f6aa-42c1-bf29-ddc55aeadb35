# -*- coding:utf-8 -*-
# @Function  : 贸易管制任务入口文件，顺序执行队列处理、关键词匹配和日报生成
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @Time      : 2025/7/30
# Version    : 1.0

"""
    顺序执行贸易管制相关任务：
    1. trade_control_queue.py - 创建贸易管制数据队列，并更新ES数据为已处理
    2. trade_control_match_keywords.py - 从队列中取出数据，进行关键词匹配
    3. trade_control_daily_report.py - 生成每日报告
"""

import sys
import os
import time
from datetime import datetime

# 添加当前目录到Python路径，确保可以导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from dc.services.logging_service import LoggingService
from trade_control_queue import main as queue_main
from trade_control_match_keywords import main as match_main
from trade_control_daily_report import main as report_main

# 创建日志服务
logger = LoggingService('dc_trade_control_task.log', formatter='string')


def run_trade_control_queue():
    """
    执行贸易管制队列处理
    """
    logger.info("=" * 50)
    logger.info("开始执行贸易管制队列处理...")
    logger.info(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    try:
        queue_main()
        logger.info("贸易管制队列处理执行完成！")
        return True
    except Exception as e:
        logger.error(f"贸易管制队列处理执行失败: {str(e)}")
        logger.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return False


def run_trade_control_match_keywords():
    """
    执行贸易管制关键词匹配
    """
    logger.info("=" * 50)
    logger.info("开始执行贸易管制关键词匹配...")
    logger.info(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    try:
        match_main()
        logger.info("贸易管制关键词匹配执行完成！")
        return True
    except Exception as e:
        logger.error(f"贸易管制关键词匹配执行失败: {str(e)}")
        logger.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return False


def run_trade_control_daily_report():
    """
    执行贸易管制日报生成
    """
    logger.info("=" * 50)
    logger.info("开始执行贸易管制日报生成...")
    logger.info(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    try:
        report_main()
        logger.info("贸易管制日报生成执行完成！")
        return True
    except Exception as e:
        logger.error(f"贸易管制日报生成执行失败: {str(e)}")
        logger.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return False


def main():
    """
    主函数：顺序执行三个贸易管制任务
    """
    logger.info("=" * 60)
    logger.info("贸易管制任务开始执行")
    logger.info(f"贸易管制任务开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    start_time = time.time()
    
    # 执行队列处理
    logger.info("步骤1: 执行贸易管制队列处理")
    if not run_trade_control_queue():
        logger.error("队列处理失败，停止后续执行")
        return False
    
    # 步骤2: 执行关键词匹配
    logger.info("步骤2: 执行贸易管制关键词匹配")
    if not run_trade_control_match_keywords():
        logger.error("关键词匹配失败，停止后续执行")
        return False
    
    # 步骤3: 执行日报生成
    logger.info("步骤3: 执行贸易管制日报生成")
    if not run_trade_control_daily_report():
        logger.error("日报生成失败")
        return False
    
    # 计算总执行时间
    end_time = time.time()
    total_time = end_time - start_time
    
    logger.info("=" * 60)
    logger.info("所有贸易管制任务执行完成！")
    logger.info(f"总执行结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"总执行时间: {total_time:.2f} 秒")
    logger.info("=" * 60)
    
    return True


if __name__ == "__main__":
    success = main()
    if success:
        print("贸易管制任务执行成功！")
        sys.exit(0)
    else:
        print("贸易管制任务执行失败！")
        sys.exit(1)
