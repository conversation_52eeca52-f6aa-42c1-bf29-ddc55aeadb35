# -*- coding:utf-8 -*-
# @Function  : 从贸易管制数据队列中取出数据，并对该数据匹配关键词
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @Time      : 2025/7/22
# Version    : 1.0

"""
    1、从数据库中取出公司id及其对应的所有关键词信息
    2、从Redis队列中取出es_id，如果Redis为空则匹配结束
    3、根据es_id到ES中取出数据
    4、遍历每一个子关键词，对ES数据匹配关键词；对附件和原文分别匹配，类型为报告、新闻和视频
    5、匹配到结果生成报告记录
    6、将报告记录存入数据库
"""
from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from dc.services.mysql_service import MySQLService
from sqlalchemy.exc import SQLAlchemyError
from dc.commands.trade_control_report.match_keywords_summary_by_ai import invoke
from dc.models.model_mp import TradeControlCompany, TradeControlCompanyKeywords, TradeControlKeywordsRelation, TradeControlMonitorReport
import re

logger = LoggingService('dc_trade_control_match_keywords.log', formatter='string')

redisService = RedisService('redis')
client = redisService.get_client()
redis_key = 'dc_trade_control_queue'

ess = ElasticsearchService()
es = ess.get_connect()
es_index = 'dc_tradecontrol'

mysql = MySQLService('mysql_company')


def get_company_keywords():
    """
    查询数据库，获取企业关键词及其子关键词，同时保存customer_id
    :return: {customer_id: {keyword: {'company_id': None, 'keywordShow': None, 'childwords': []}}}
    """
    result = {}
    try:
        with mysql.create_session() as session:
            # 查询customer_id
            customer_id_rows = session.query(TradeControlCompany.customer_id) \
                .filter(TradeControlCompany.companyStatus == 1) \
                .all()
            customer_ids = [row.customer_id for row in customer_id_rows]

            if not customer_ids:
                return result

            # 批量查询关键词并按customer_id分组
            customer_keywords = {}
            for record in session.query(
                    TradeControlCompanyKeywords.customer_id,
                    TradeControlCompanyKeywords.keyword,
                    TradeControlCompanyKeywords.companyId
            ).filter(TradeControlCompanyKeywords.customer_id.in_(customer_ids)).all():
                cid, kw, company_id = record
                customer_keywords.setdefault(cid, []).append((kw, company_id))

            # 提取所有关键词
            all_keywords = [kw for cid in customer_keywords for kw, _ in customer_keywords[cid]]
            if not all_keywords:
                logger.info("未查询到关键词！")
                return result

            # 批量查询子关键词并分组
            keyword_children = {}
            for record in session.query(
                    TradeControlKeywordsRelation.keyword,
                    TradeControlKeywordsRelation.keywordShow,
                    TradeControlKeywordsRelation.childWord
            ).filter(TradeControlKeywordsRelation.keyword.in_(all_keywords)).all():
                kw, show, child = record
                keyword_children.setdefault(kw, []).append((show, child))

            # 组装结果
            for cid, keywords in customer_keywords.items():
                result[cid] = {
                    kw: {
                        "company_id": company_id,
                        "keywordShow": keyword_children[kw][0][0],
                        "childwords": [child for _, child in keyword_children[kw]]
                    }
                    for kw, company_id in keywords
                    if kw in keyword_children
                }

    except SQLAlchemyError as e:
        logger.error(f"数据库查询错误: {str(e)}")
        return result
    except Exception as e:
        logger.error(f"处理数据时发生错误: {str(e)}")
        return result

    return result


def get_id_from_redis():
    """从Redis队列取出一个es_id"""
    es_id = client.rpop(redis_key)
    if es_id:
        logger.info(f"从Redis队列获取到es_id: {es_id}")
        return es_id
    else:
        logger.info("Redis队列为空，匹配结束！")
        return None


def get_data_from_es(es_id):
    """根据es_id从ES中获取数据内容"""
    try:
        data = es.get(index=es_index, id=es_id)
        logger.info(f"成功获取ES文档: {es_id}")
        return data['_source']
    except Exception as e:
        logger.error(f"获取ES文档{es_id}失败: {str(e)}")
        return None


def summary_by_ai(article_content, keyword):
    """
    调用AI模型对页面内容进行总结
    :author yaoyuanhao
    :date 2025-7-22
    :param article_content: 需要总结的内容
    :param keyword: 关键词
    :return: ai总结的内容
    """
    logger.info("调用AI进行内容总结")
    # prompt = "对下面文本内容进行简要总结，并将总结内容翻译成中文。最后只输出总结内容的中文版，输出格式为：“*定位摘要*：中文总结内容。”。文本内容为："
    prompt = (f"关键词{keyword}是否作为一个单独的词出现在下面文本中且文本中的意思和关键词的意思相同，如果都不成立只回答否，不需要看下面的文字。"
              f"如果都成立则对下面文本内容进行简要总结，并将总结内容翻译成中文。最后只输出总结内容的中文版，输出格式为：“*定位摘要*：中文总结内容。”。文本内容为：")
    query = prompt + article_content
    return invoke(query, 300)


def is_similar_conclusion(conclusion_a, conclusion_b):
    """
    使用AI判断两个总结内容是否说的是同一件事。
    :author yaoyuanhao
    :date 2025-7-22
    :param conclusion_a: 总结内容a
    :param conclusion_b: 总结内容b
    :return: True/False
    """
    logger.info("调用AI进行去重！")
    prompt = f"判断下面两段内容是否说的是同一件事，只回答是或否：\nA: {conclusion_a}\nB: {conclusion_b}"
    result = invoke(prompt, 20)
    if '是' in result and '不是' not in result:
        return True
    return False


def match_keyword_attachements(article_content, keyword):
    """
    对附件内容进行匹配关键词，若匹配到关键词则返回匹配到关键词的页码和当前页内容总结，自动去重
    :author yaoyuanhao
    :date 2025-7-22
    :param article_content: 附件中所有页面内容
    :param keyword: 关键词
    :return: 匹配到且去重后的结果
    """
    if not article_content or not keyword:
        return None

    keyword = keyword.strip()  # 去除keyword前后空格

    pages = article_content.split('Page ')              # 按照Page进行分页操作
    matched_pages = []
    if len(pages) == 1:
        if keyword.lower() in pages[0].lower():
            matched_pages.append((1, pages[0]))
    else:
        for i, page_content in enumerate(pages[1:], 1):        # 进行关键词匹配，收集匹配位置的页码和页面内容
            if keyword.lower() in page_content.lower():
                matched_pages.append((i, page_content))

    results = []
    unique_conclusions = []
    for page_num, page_content in matched_pages:
        conclusion = summary_by_ai(page_content, keyword)
        if conclusion == "否":           # 如果AI判断关键词不成立，则跳过该页面
            continue
        is_duplicate = False                    # AI去重
        for uc in unique_conclusions:
            if is_similar_conclusion(conclusion, uc):
                is_duplicate = True
                break
        if not is_duplicate:
            unique_conclusions.append(conclusion)
            results.append({
                "page": page_num,
                "conclusion": conclusion
            })
            logger.info("已处理+1！")

    return results


def match_keyword_text(article_content, keyword, range=300):
    """
    对新闻内容进行匹配关键词，获取关键词前后各range个字符的内容，并进行AI总结，自动去重，position为出现次数
    :author yaoyuanhao
    :date 2025-7-22
    :param article_content: 新闻内容
    :param keyword: 关键词
    :param range: 关键词前/后的字符个数
    :return: 匹配到且去重后的结果
    """
    if not article_content or not keyword:
        return None

    keyword = keyword.strip()  # 去除keyword前后空格

    content_lower = article_content.lower()
    keyword_lower = keyword.lower()
    matched_contents = []
    unique_conclusions = []  # 用于去重

    # 查找所有匹配位置
    start_pos = 0
    occurrence_count = 1  # 出现次数计数器
    while True:
        pos = content_lower.find(keyword_lower, start_pos)  # 找到关键词所在位置
        if pos == -1:
            break

        start = max(0, pos - range)
        end = min(len(article_content), pos + len(keyword) + range)
        nearby_content = article_content[start:end]  # 截取关键词前后内容
        conclusion = summary_by_ai(nearby_content, keyword)
        if conclusion == "否":           # 如果AI判断关键词不成立，则跳过该页面
            start_pos = pos + range
            continue
        is_duplicate = False  # AI去重逻辑
        for uc in unique_conclusions:
            if is_similar_conclusion(conclusion, uc):
                is_duplicate = True
                break
        if not is_duplicate:
            unique_conclusions.append(conclusion)
            matched_contents.append({
                "conclusion": conclusion,
                "position": occurrence_count
            })
        logger.info("已处理：%s", occurrence_count)
        occurrence_count += 1
        start_pos = pos + range

    if matched_contents:
        return {
            "page": 0,
            "conclusions": matched_contents
        }
    return None


def create_monitor_report(ES_content, part_of_record):
    """
    生成监测报告记录，即存入MP_TradeControlMonitorReport数据表中的记录
    :author yaoyuanhao
    :date 2025-7-22
    :param ES_content: es中的数据内容
    :param part_of_record: 部分监控记录
    :return: 完整的监控记录
    """

    return {
        "companyId": part_of_record['company_id'],
        "customer_id": part_of_record['customer_id'],
        "MonitorDate": ES_content['createdAt'],
        "keyword": part_of_record['keyword'],
        "keywordShow": part_of_record['keywordShow'],
        "keywordRelation": part_of_record['childword'],
        "newsId": part_of_record['es_id'],
        "title": ES_content['title_zh'],
        "content": part_of_record['conclusion'],
        "newsSource": ES_content['dc_site_name'],
        "page": part_of_record['page'],
        "type": part_of_record['upload_type'],
        "url": ES_content['dc_detail_url']
    }


def save_report_to_db(session, report_data):
    """
        将报告记录存储到数据库中
    """
    try:
        report = TradeControlMonitorReport(
            companyId=report_data["companyId"],
            customer_id=report_data["customer_id"],
            MonitorDate=report_data["MonitorDate"],
            keyword=report_data["keyword"],
            keywordShow=report_data["keywordShow"],
            keywordRelation=report_data["keywordRelation"],
            newsId=report_data["newsId"],
            title=report_data["title"],
            content=report_data["content"],
            newsSource=report_data["newsSource"],
            page=report_data["page"],
            type=report_data["type"],
            url=report_data["url"],
        )
        session.add(report)
        session.commit()
        logger.info("监控记录保存成功！")
    except Exception as e:
        companyId = report_data["companyId"]
        keyword = report_data["keyword"]
        newsId = report_data["newsId"]
        logger.error(f"监控记录：{companyId}-{keyword}-{newsId}保存失败！: {str(e)}")
        raise


def main():
    session = mysql.create_session()
    key_words_structure = get_company_keywords()
    while True:
        es_id = get_id_from_redis()
        if not es_id:
            logger.info("匹配结束！")
            break

        ES_content = get_data_from_es(es_id)
        if not ES_content:
            logger.info(f"ES:{es_id}数据为空！")
            continue

        if 'text' not in ES_content:
            logger.info(f"ES:{es_id}数据中不存在text内容！")
            continue
        article_content = ES_content['text']

        attachments = ES_content.get('files_v2', [])
        attachment_texts = [
            item['file_v2_original']
            for item in attachments
            if 'file_v2_original' in item
        ]
        # 判断上传类型
        upload_type_val = ES_content.get('upload_type')
        upload_type = str(upload_type_val) if upload_type_val else ('2' if attachment_texts else '1')

        # 遍历所有关键词，进行匹配
        for customer_id, keywords in key_words_structure.items():
            for keyword, info in keywords.items():
                company_id = info["company_id"]
                keyword_show = info["keywordShow"]
                child_words = info["childwords"]
                for child_word in child_words:
                    try:
                        if attachments and upload_type == '2':          # 如果存在附件并且类型为报告，按照“报告”类型进行匹配
                            for text in attachment_texts:
                                match_results = match_keyword_attachements(text, child_word)
                                if not match_results:
                                    continue

                                for match_result in match_results:      # 将匹配结果存入数据库
                                    part_of_report = {'company_id': company_id,
                                                      'customer_id': customer_id,
                                                      'keyword': keyword,
                                                      'keywordShow': keyword_show,
                                                      'childword': child_word,
                                                      'es_id': es_id,
                                                      'upload_type': upload_type,
                                                      'conclusion': match_result['conclusion'],
                                                      'page': match_result['page']}
                                    report_data = create_monitor_report(ES_content, part_of_report)
                                    save_report_to_db(session, report_data)
                                break   # 成功匹配到一个附件之后跳出循环
                        else:                                           # 其余新闻和视频均匹配text内容
                            match_result = match_keyword_text(article_content, child_word, 500)
                            if not match_result:
                                continue

                            for match_result_item in match_result['conclusions']:
                                part_of_report = {'company_id': company_id,
                                                  'customer_id': customer_id,
                                                  'keyword': keyword,
                                                  'keywordShow': keyword_show,
                                                  'childword': child_word,
                                                  'es_id': es_id,
                                                  'upload_type': upload_type,
                                                  'conclusion': match_result_item['conclusion'],
                                                  'page': match_result_item['position']}
                                report_data = create_monitor_report(ES_content, part_of_report)       # 生成存储记录
                                save_report_to_db(session, report_data)                 # 存储到数据库中
                    except Exception as e:
                        logger.error(f"监测记录：{customer_id}-{keyword}-{child_word}匹配关键词异常，跳过处理！: {str(e)}")
                        continue

    session.close()
    es.close()
    client.close()

# 示例用法
if __name__ == "__main__":
    # main()
    session = mysql.create_session()
    key_words_structure = get_company_keywords()  # 获取所有关键词
    print(key_words_structure)

    es_id_test = "G1Nmh5gB1IhhYq_NkrYQ"     # 包含files_v2
    ES_content = get_data_from_es(es_id_test)
    if not ES_content:
        print(f"ES:{es_id_test}数据为空！")

    if 'text' not in ES_content:
        print(f"ES:{es_id_test}数据中不存在text内容！")
    article_content = ES_content['text']

    attachments = ES_content.get('files_v2', [])
    attachment_texts = [
        item['file_v2_original']
        for item in attachments
        if 'file_v2_original' in item
    ]
    # 判断上传类型
    upload_type_val = ES_content.get('upload_type')
    upload_type = str(upload_type_val) if upload_type_val else ('2' if attachment_texts else '1')

    # 遍历所有关键词，进行匹配
    for customer_id, keywords in key_words_structure.items():
        for keyword, info in keywords.items():
            company_id = info["company_id"]
            keyword_show = info["keywordShow"]
            child_words = info["childwords"]
            for child_word in child_words:
                try:
                    if attachments and upload_type == '2':  # 如果存在附件并且类型为报告，按照“报告”类型进行匹配
                        for text in attachment_texts:
                            match_results = match_keyword_attachements(text, child_word)
                            if not match_results:
                                continue

                            for match_result in match_results:  # 将匹配结果存入数据库
                                part_of_report = {'company_id': company_id,
                                                  'customer_id': customer_id,
                                                  'keyword': keyword,
                                                  'keywordShow': keyword_show,
                                                  'childword': child_word,
                                                  'es_id': es_id_test,
                                                  'upload_type': upload_type,
                                                  'conclusion': match_result['conclusion'],
                                                  'page': match_result['page']}
                                report_data = create_monitor_report(ES_content, part_of_report)
                                save_report_to_db(session, report_data)
                            break  # 成功匹配到一个附件之后跳出循环
                    else:  # 其余新闻和视频均匹配text内容
                        for match_result_item in match_result['conclusions']:
                            part_of_report = {'company_id': company_id,
                                              'customer_id': customer_id,
                                              'keyword': keyword,
                                              'keywordShow': keyword_show,
                                              'childword': child_word,
                                              'es_id': es_id_test,
                                              'upload_type': upload_type,
                                              'conclusion': match_result_item['conclusion'],
                                              'page': match_result_item['position']}
                            report_data = create_monitor_report(ES_content, part_of_report)  # 生成存储记录
                            save_report_to_db(session, report_data)  # 存储到数据库中
                except Exception as e:
                    logger.error(f"监测记录：{customer_id}-{keyword}-{child_word}匹配关键词异常，跳过处理！: {str(e)}")
                    continue

    session.close()
    es.close()
    client.close()

    # summary_prompt = "如果都成立则下面文本内容进行简要总结，并将总结内容翻译成中文。最后只输出总结内容的中文版，输出格式为：“*定位摘要*：中文总结内容。”。文本内容为："
    # keyword1 =  "global politics"
    # keyword = "中国"
    # article_content = """中国移动通信集团有限公司（China Mobile Communications Group Co., Ltd.）是中国最大的移动通信运营商之一，成立于2000年，总部位于北京。作为全球领先的移动通信公司，中国移动在全国范围内提供移动语音、数据和多媒体服务。公司致力于推动数字化转型和智能化发展，积极布局5G网络、物联网和云计算等新兴领域。"""
    # prompt = (f"关键词{keyword}是否作为一个单独的词出现在下面文本中且文本中的意思和关键词的意思相同，如果都不成立只回答否，不需要看下面的文字。"
    #           f"如果都成立则下面文本内容进行简要总结，并将总结内容翻译成中文。最后只输出总结内容的中文版，输出格式为：“*定位摘要*：中文总结内容。”。文本内容为：")
    # query = prompt + article_content
    # result = invoke(query, 300)
    # # print(article_content)
    # print(result)
