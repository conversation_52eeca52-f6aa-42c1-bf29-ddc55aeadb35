# -*- coding:utf-8 -*-
# @Function  : 从贸易管制数据队列中取出数据，并对该数据匹配关键词
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @Time      : 2025/7/16
# Version    : 1.0

"""
    1、先获取 MonitorDate 有监测记录的company_id和customer_id。
    2、根据company_id统计所有的关键词。
    3、根据company_id统计新闻的数量和报告数量，相加得到总的信息数量。
    4、报告日期为（当前日期减1天），createAt时间为当前日期。
    5、将上述数据写入数据表中
"""
from you_get.extractors.le import video_info

from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from datetime import datetime
from datetime import timedelta
from dc.models.model_mp import TradeControlCompany, TradeControlMonitorReport, TradeControlDailyReport



# MySQL连接
mysql = MySQLService('mysql_company')

logger = LoggingService('trade_control_daily_reports.log', formatter='string')


def getCustomerId(session, date):
    """
    获取某个日期有监测记录的所有 customer_id
    """
    customer_ids = (
        session.query(TradeControlMonitorReport.customer_id)
        .filter(TradeControlMonitorReport.MonitorDate == date)
        .distinct()
        .all()
    )
    logger.info(f"获取{date}有监测记录的customer_id")
    return [row[0] for row in customer_ids]


def getCompanyIdOfCustomerId(session, customerIds):
    """
    获取每个 customer_id 对应的 company_id
    :return: {customer_id: companyId }
    """
    if not customerIds:
        return {}
    result = (
        session.query(TradeControlCompany.customer_id, TradeControlCompany.companyId)
        .filter(TradeControlCompany.customer_id.in_(customerIds))
        .all()
    )
    return {row[0]: row[1] for row in result}


def getKeywordsOfCompany(session, customerIds, date):
    """
    获取每个 customer_id 在指定日期涉及的所有关键词，拼接成逗号分隔的字符串
    :return：{customer_id: "关键词1,关键词2,...", ...}
    """
    if not customerIds:
        return {}
    result = (
        session.query(TradeControlMonitorReport.customer_id, TradeControlMonitorReport.keyword)
        .filter(TradeControlMonitorReport.MonitorDate == date)
        .filter(TradeControlMonitorReport.customer_id.in_(customerIds))
        .all()
    )
    keywords = {}
    for customer_id, keyword in result:
        keywords.setdefault(customer_id, set()).add(keyword)
    logger.info(f"获取{date}有监测记录的company_id对应的关键词")
    return {cid: ','.join(sorted(kw_set)) for cid, kw_set in keywords.items()}


def getNewsReportsVideosTotalCount(session, customerIds, date):
    """
    获取每个 customer_id 某天新闻和报告的数量（type=1,2, 3），根据newsId去重
    返回：{customer_id: {'news_count': 新闻数量,'report_count': 报告数量,'video_count': 视频数量,'total_count': 总数量}}
    """
    if not customerIds:
        return {}
    from sqlalchemy import func, case
    result = (
        session.query(
            TradeControlMonitorReport.customer_id,
            func.count(func.distinct(case([(TradeControlMonitorReport.type == 1, TradeControlMonitorReport.newsId)], else_=None))).label('news_count'),
            func.count(func.distinct(case([(TradeControlMonitorReport.type == 2, TradeControlMonitorReport.newsId)], else_=None))).label('report_count'),
            func.count(func.distinct(case([(TradeControlMonitorReport.type == 3, TradeControlMonitorReport.newsId)], else_=None))).label('video_count'),
        )
        .filter(TradeControlMonitorReport.MonitorDate == date)
        .filter(TradeControlMonitorReport.customer_id.in_(customerIds))
        .group_by(TradeControlMonitorReport.customer_id)
        .all()
    )
    logger.info(f"获取{date}有监测记录的customer_id对应新闻数量、报告数量、视频数量和总数（根据newsId去重）")
    return {
        row.customer_id: {
            'news_count': row.news_count,
            'report_count': row.report_count,
            'video_count': row.video_count,
            'total_count': row.news_count + row.report_count + row.video_count
        }
        for row in result}


def build_daily_report_dict(session, date):
    """
    构建日报字典
    :param session: 数据库session
    :param date: 统计日期（datetime.date类型）
    :return: {customer_id: [company_id, total_count, news_count, report_count, keywords, reportDate, createDate], ...}
    """
    result = {}
    # 获取date日期存在检测报告的用户ID
    customer_ids = getCustomerId(session, date)
    if not customer_ids:
        return result

    keywords_dict = getKeywordsOfCompany(session, customer_ids, date)
    count_dict = getNewsReportsVideosTotalCount(session, customer_ids, date)
    company_id_dict = getCompanyIdOfCustomerId(session, customer_ids)

    for cid in customer_ids:
        count_data = count_dict.get(cid, {})
        news_count = count_data.get('news_count', 0)
        report_count = count_data.get('report_count', 0)
        video_count = count_data.get('video_count', 0)
        total_count = count_data.get('total_count', 0)
        keywords = keywords_dict.get(cid, "")               # 将匹配到的关键词拼接成字符串
        company_id = company_id_dict.get(cid, None)       # 获取该公司id对应的company_id
        result[cid] = {
            'company_id': company_id,
            'total_count': total_count,
            'news_count': news_count,
            'report_count': report_count,
            'video_count': video_count,
            'keywords': keywords,
            'report_date': date  # 注释说明为昨天的日期，保持原逻辑
        }
    logger.info(f"构建{date}的日报字典：{result}")
    return result


def save_daily_report_to_db(session, report):
    """
    将daily_report存储到MP_TradeControlDailyReport表中，存在则更新，不存在则插入
    """
    try:
        count = 0
        for customer_id, values in report.items():
            company_id = values['company_id']
            total_count = values['total_count']
            news_count = values['news_count']
            report_count = values['report_count']
            video_count = values['video_count']
            keywords = values['keywords']
            report_date = values['report_date']
            # 先查
            obj = session.query(TradeControlDailyReport).filter(
                TradeControlDailyReport.customer_id == customer_id,
                TradeControlDailyReport.reportDate == report_date
            ).first()
            if obj:
                # 有则更新
                obj.companyId = company_id
                obj.totalCount = total_count
                obj.newsCount = news_count
                obj.reportCount = report_count
                obj.videoCount = video_count
                obj.keywords = keywords
            else:
                # 无则插入
                obj = TradeControlDailyReport(
                    companyId=company_id,
                    customer_id=customer_id,
                    totalCount=total_count,
                    newsCount=news_count,
                    reportCount=report_count,
                    videoCount=video_count,
                    keywords=keywords,
                    reportDate=report_date
                )
                session.add(obj)
                count += 1
        logger.info(f"共有{count}条记录！")
        session.commit()
        logger.info(f"成功保存日报！")
    except Exception as e:
        session.rollback()
        print(f'插入日报表失败，已回滚，错误信息：{e}')
        logger.error(f'插入日报表失败，已回滚，错误信息：{e}')
        raise

def main():
    session = mysql.create_session()
    date_of_last_day = (datetime.now().date() - timedelta(days=1))
    s = "2025-07-30"
    d: datetime.date = datetime.strptime(s, "%Y-%m-%d").date()
    daily_report = build_daily_report_dict(session, d)       # 传入的date是昨天的日期
    save_daily_report_to_db(session, daily_report)
    session.close()


if __name__ == '__main__':
    main()
