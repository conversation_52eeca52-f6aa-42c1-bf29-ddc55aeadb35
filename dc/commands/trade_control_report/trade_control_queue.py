# -*- coding:utf-8 -*-
# @Function  : 创建贸易管制数据队列，并更新ES数据为已处理
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @Time      : 2025/7/9
# Version    : 1.0

"""
    1、读取ES数据库中的数据
    2、将ES数据的es_id存入Redis队列
    3、统一更新ES数据内容，打上已处理标记 isAddToQueue
"""

from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from elasticsearch import helpers
import time
from datetime import datetime, timedelta

logger = LoggingService('dc_trade_control_queue.log', formatter='string')

redisService = RedisService('redis')
client = redisService.get_client()
redis_key = 'dc_trade_control_queue'

ess = ElasticsearchService()
es = ess.get_connect()
es_index = 'dc_tradecontrol'


def es_data(batch_size=50):
    """
    查询ES中的数据，返回es_id
    :author ya<PERSON><PERSON><PERSON>
    :date 2025-7-9
    :param batch_size: 批处理大小
    :return: es_id列表
    """
    # 计算时间范围：北京时间昨天0点到今天0点
    end_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_time = end_time - timedelta(days=1)

    start_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    end_str = end_time.strftime("%Y-%m-%d %H:%M:%S")

    logger.info(f"查询时间范围：{start_str} 到 {end_str}")
    # 查询未处理的数据
    query_body = {
        "size": batch_size,
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "is_translated": 1
                        }
                    },
                    {
                        "range": {
                            "createdAt": {
                                "gte": start_str,
                                "lt": end_str
                            }
                        }
                    }
                ],
                "should": [
                    {
                        "bool": {
                            "must_not": {
                                "exists": {
                                    "field": "isAddToQueue"
                                }
                            }
                        }
                    },
                    {
                        "term": {
                            "isAddToQueue": 0
                        }
                    }
                ],
                "minimum_should_match": 1
            }
        }
    }
    result = es.search(index=es_index, body=query_body)
    data = [item['_id'] for item in result['hits']['hits']]
    return data


def update_result(actions):
    """
    批量更新ES数据为已处理
    :author yaoyuanhao
    :date 2025-7-9
    :param actions: 批量更新操作
    :return: none
    """
    success, failed = helpers.bulk(es, actions)
    if failed:
        logger.error("以下内容更新失败")
        for fail in failed:
            logger.error(f"失败详情: {fail}")
    else:
        logger.info("批量更新成功")
        es.indices.refresh(index=es_index)
        time.sleep(1)


def main():
    logger.info("开始处理ES数据...")
    while True:
        es_ids = es_data()          # 读取ES中未处理的数据
        if not es_ids:
            logger.info("没有新数据需要处理，处理结束!")
            break

        actions = []
        for es_id in es_ids:
            try:
                logger.info(f"当前记录数据ID: {es_id}")
                client.lpush(redis_key, es_id)
                action = {                  # 标记ES数据为已处理
                    "_op_type": "update",
                    "_index": es_index,
                    "_id": es_id,
                    "doc": {"isAddToQueue": "1"}
                }
                actions.append(action)
            except Exception as e:
                logger.error(f"处理ES数据{es_id}时发生错误：{e}")

        if actions:
            update_result(actions)
        else:
            logger.info("本批次无可更新数据")

    es.close()
    client.close()

if __name__ == "__main__":
    main()

