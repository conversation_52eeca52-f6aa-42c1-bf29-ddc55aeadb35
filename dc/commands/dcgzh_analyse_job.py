"""
微信公众号采集-文章分析
2022-10-19
"""

import json
import random
import time
import traceback

import greenstalk
import requests
from pymongo import ReturnDocument
from selenium.common import NoSuchElementException
from selenium.webdriver.common.by import By

from dc.common.dc_helper import Dc<PERSON><PERSON>per
from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhArticle, DCGzhInfo, DCGzhTag
from dc.services.beanstalk_service import BeanstalkService
from dc.services.content_filter_service import ContentFilterService
from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.k8s_service import K8sService
from dc.services.kafka_service import KafkaService
from dc.services.logging_service import LoggingService
from dc.services.mongo_service import MongoService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.weixin_service_v2 import WeixinServiceV2
from dc.common.webdriver_util import *
from dc.tests.browser import Browser
from dc.services.qiniu_service import QiniuService

logger = LoggingService('dc_gzh_analyse_job.log')
slogger = LoggingService('dc_gzh_selenium.log')
klogger = LoggingService('dc_opinion_wechat_kafka.log', formatter='string')
mysql = MySQLService()
redisService = RedisService('redis')
ms = MongoService()
wx = WeixinServiceV2(logger=logger)
ks = K8sService()
bs = BeanstalkService()
beanstalk = bs.get_client(use=DC_GZH_ANALYSE, watch=[DC_GZH_ANALYSE])

ess = ElasticsearchService()
es = ess.get_connect()

test_index = 'wechat'

redis = redisService.get_client()

redisService1 = RedisService('redis', db=6)
client2 = redisService1.get_client()

kafka = KafkaService()
qs = QiniuService(name='bianjifagao')

settings = get_settings('webdriver')
webdriver_url = settings.get('host')



def delivery_report(err, msg):
    try:
        if err is not None:
            # print("Message delivery failed: {}".format(err))
            klogger.error("Message delivery failed: {}".format(err))
        else:
            data = {
                'topic': msg.topic(),
                'partition': msg.partition(),
                'offset': msg.offset(),
                'key': msg.key(),
                'value': msg.value().decode('utf-8'),
                'timestamp': msg.timestamp(),
            }
            topic = msg.topic()
            item: dict = json.loads(data['value'])
            esid = item.get(KAFKA_TOPIC_ESID_KDY)
            sourceCategory = item.get('_sourceCategory', '')
            klogger.info(f"kafka topic: {topic} esid: {esid} sourceCategory:{sourceCategory}")
    except Exception as ex:
        print(f"delivery_report: {traceback.format_exc()}")


def handle():
    """
    公众号-文章分析
    :return:
    """
    logger.info('启动公众号-文章分析服务...')
    settings = get_settings('weixin_gzh')
    analyse_sleep = settings.get('analyse_sleep', 2)
    bianjifagao_index = settings.get('bianjifagao_index', 'dc_bianjifagao')
    bianjifagao_tags = settings.get('bianjifagao_tags', [])
    dc_config = settings.get('dc_config', [])
    content_xpath = dc_config.get('content_xpath')
    logger.debug(f'settings: {settings}')
    logger.debug(f'content_xpath: {content_xpath}')

    session = None
    prefix = None
    timeout = 300

    while True:

        ks.liveness()   # 探针
        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "
        gzh_images = []

        try:

            job = beanstalk.reserve(timeout=timeout)
            job_id = job.id
            logger.info(f"{prefix} beanstalk reserve: job_id:{job_id} body:{job.body}")
            job_data = json.loads(job.body)
            article_id = job_data['id']

            stats = beanstalk.stats_job(job)
            logger.info(f"stats: {stats}")
            reserves = int(stats['reserves'])
            if reserves > 3:
                logger.info(f"{prefix} reserves > 3, beanstalk delete: job_id:{job_id} article:{article_id}, continue")
                beanstalk.delete(job)
                continue

            logger.info(f"{prefix} job_id:{job_id} article:{article_id}")
            if article_id is None:
                logger.info(f"{prefix} article_id is empty, beanstalk delete: job_id:{job_id} article:{article_id} , continue")
                beanstalk.delete(job)
                continue

            session = mysql.create_session()
            article: DCGzhArticle = session.query(DCGzhArticle).get(article_id)
            if article is None:
                logger.info(f"{prefix} article is none, beanstalk delete: job_id:{job_id} article:{article_id} , continue")
                beanstalk.delete(job)
                continue

            if article.isAnalysis == 1:
                logger.info(f"{prefix} isAnalysis=1, beanstalk delete2: job_id:{job_id} article:{article_id}，continue")
                beanstalk.delete(job)
                continue

            content = None
            html_content = None
            deleted = False  # 文章是否删除

            try:
                logger.info(f"{prefix} title: {article.title} url: {article.url}")
                # logger.info(f"{prefix} url: {article.url}")

                # html_content = DcHelper.get_html(article.url)
                # content = DcHelper.get_content(html_content, content_xpath, By.XPATH)

                # driver = get_remote_webdriver(webdriver_url, Browser.Chrome)
                driver = get_remote_webdriver(webdriver_url, Browser.Firefox, options={
                    # 'arguments': ['--proxy-server=http://f753.kdltps.com:15818']
                })
                driver.get(article.url)
                html_content = driver.page_source
                content = driver.find_element(By.XPATH, content_xpath).text
                content = format_content(content)

                gzh_images_elements = driver.find_elements(By.XPATH, content_xpath + '//img')
                gzh_images = qs.get_gzh_images_by_webelement(gzh_images_elements)

            except NoSuchElementException as ex:    # 内容删除
                xpath = '//body'
                element = driver.find_element(By.XPATH, xpath)
                deleted = element.text.startswith('该内容已被发布者删除')
                if deleted:
                    logger.error(f"{prefix} article id: {article.id} deleted: {deleted} 该内容已被发布者删除")
                else:
                    logger.error(f"{prefix} article id: {article.id} xpath 匹配失败 {content_xpath} url: {article.url}")
                    logger.error(f"{prefix} article id: {article.id} text:{element.text}")
                    logger.error(f"{prefix} article id: {article.id} html:{driver.page_source}")

            except Exception as ex:
                # logger.error(f"{prefix} article id： {article.id} url: {article.url} ex: {traceback.format_exc()}")
                logger.error(f"{prefix} article id： {article.id} url: {article.url} ex: {str(ex)}")

            finally:
                driver.quit()

            gzh: DCGzhInfo = session.query(DCGzhInfo).filter(DCGzhInfo.gzhId == article.gzhId).first()
            # tags
            gzh_tags = session.query(DCGzhTag).filter(DCGzhTag.gzhId == gzh.gzhId).all()
            tags = [tag.tagName for tag in gzh_tags]
            tags_bianjifagao = [tag.tagName for tag in gzh_tags if tag.tagName.startswith("本土IC-微信") or tag.tagName.startswith("企业微信") or tag.tagName.startswith("本土IC-学研") or tag.tagName.startswith("IC-机构") or tag.tagName.startswith("重点-外媒")]

            url_md5 = article.url_md5
            if url_md5 is None:
                url_md5 = wx.calc_url_md5(article.url)

            esId = None
            if content is not None:  # 获取到文章内容

                # es 处理
                ad = article.to_dict()

                body = {
                    'id': article.id,
                    'wxId': article.wxId,
                    'title': article.title,
                    'content': content,
                    'cover': article.cover,
                    'url': article.url,
                    'url_md5': url_md5,
                    'public_time': ad['wx_pub_time'],
                    'readNum': article.readNum,
                    'praiseNum': article.praiseNum,
                    'gzhId': article.gzhId,
                    'gzhNickName': gzh.nickName if gzh else None,
                    'createAt': ad['createAt'],
                    'createdAt': ad['createAt'],    # 数据兼容
                    'created_time': time.time_ns(),
                    'tags': tags,
                }

                filter = {
                    "query": {
                        "terms": {
                            "url_md5": [url_md5]
                        }
                    },
                    'size': 1
                }

                #result = es.index(index=test_index, body=body)
                es_body = body
                es_body['isRepeat'] = 0
                result = ess.upsert(index=test_index, filter=filter, body=es_body)
                logger.info(f"{prefix} text: {result}")
                esId = result[0]['_id']

                # 编辑发稿
                if tags_bianjifagao:
                    # 如果是编辑发稿的话,需要先检查文章是否有关键词内容, 如果没有 直接可以退出
                    cf = ContentFilterService()
                    if cf.word_filter(cf.words, body['content']):

                        # 图片处理
                        qiniu_images = []
                        if gzh_images:
                            qiniu_images = qs.get_gzh_qiniu_images_by_gzh_images(gzh_images, article.url)

                        bianjifagao_body = {
                            'title': body['title'],
                            'url': body['url'],
                            'url_md5': body['url_md5'],
                            'site_name': body['gzhNickName'],
                            'author': body['gzhNickName'],
                            'abstract': None,
                            'domain': None,
                            'public_time': body['public_time'],
                            # 'esId': body['url'],
                            'content': body['content'],
                            'createAt': body['createAt'],
                            'tags': body['tags'],
                            'isPublished': 0,
                            'isPass': 0,
                            'isWaiting': 0,
                            'isDeal': 0,
                            'is_translated': 0,  # 是否翻译完成
                            'images_v2': qiniu_images,
                            '_dc_type': 2,
                        }
                        bianjifagao_result = ess.upsert(index=bianjifagao_index, filter=filter, body=bianjifagao_body)
                        logger.info(f"{prefix} text: {bianjifagao_result}")
                        bianjifagao_esId = bianjifagao_result[0]['_id']
                        logger.info(f"{prefix} bianjifagao_esId: {bianjifagao_esId}")
                    else:
                        logger.info(f"{prefix} : bianjifagao content has none keywords ,job_id:{job_id} article:{article_id}")

                # kafka 数据变更队列
                p = kafka.get_producer()
                p.produce(KAFKA_TOPIC_DC_WECHAT, json.dumps({KAFKA_TOPIC_ESID_KDY: esId, **es_body}), key=esId, callback=delivery_report)
                # p.poll(0)
                p.flush()

                # mongo 处理（已经取消）
                # body['body'] = driver.page_source
                # body['body'] = html_content
                # mongo = MongoService()
                # coll = mongo.get_collection('wechat')
                # doc = coll.find_one_and_update(filter={"url_md5": url_md5}, update={'$set': body}, upsert=True, return_document=ReturnDocument.AFTER)

            article.isAnalysis = 1
            # article.analysisResult = 2 if content is None else 1
            article.analysisResult = 3 if deleted else (2 if content is None else 1)
            article.analysisAt = Utils.showDateTime()
            article.esIndex = 'wechat'
            article.esId = esId if esId else None
            # article.mongoCollection = 'wechat'
            # article.mongoKey = str(doc['_id']) if doc else None
            session.add(article)
            session.commit()

            client2.lpush('dc_article_weight', json.dumps({'taskId': article_id, 'type': 2}))
            client2.close()

            time.sleep(1)

            session.close()

            # beanstalk
            try:
                beanstalk.delete(job)
                logger.info(f"{prefix} beanstalk delete3: job_id:{job_id} article:{article_id}")

            except Exception as ex:
                pass

        except greenstalk.TimedOutError as ex:
            logger.info(f"{prefix} greenstalk timeout")

        except greenstalk.NotFoundError as ex:
            logger.info(f"{prefix} greenstalk not fond")

        except Exception as ex:
            logger.error(f"{prefix} exception:" + str(ex))
            time.sleep(analyse_sleep)

        finally:
            if session is not None:
                session.close()


handle()
