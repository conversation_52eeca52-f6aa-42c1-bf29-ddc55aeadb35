# -*- coding:utf-8 -*-
# @Function  : dcgzh_push 公众号文章推送 (mysql)
# <AUTHOR> wjh
# @Time      : 2025-06-11
# Version    : 1.0

import random
import time

from sqlalchemy import text

from dc.common.utils import Utils
from dc.conf.defines import DC_GZH_PUSH
from dc.conf.settings import get_settings
from dc.models.model import DCGzhInfo, DCGzhPush
from dc.services.gzh_service import GzhService
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService

logger = LoggingService('dc_gzh_push_mysql.log')
mysql = MySQLService()
ks = K8sService()
rs = RedisService()
redis = rs.get_client()

settings = get_settings('weixin_gzh')


def handle():

    logger.info('启动公众号-历史文章拉取 服务...')

    session = None
    prefix = None

    while True:

        ks.liveness()   # 探针
        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        try:

            session = mysql.create_session()
            pullAt = Utils.time_delta(hours=1)
            where = f"insert_time >= '{pullAt}'"
            data: list[DCGzhPush] = session.query(DCGzhPush).where(text(where)).order_by(DCGzhPush.insert_time).all()
            logger.info(f'{prefix} where count: {len(data)}')
            if len(data) == 0:
                logger.debug(f"{prefix} 不存在数据，continue")
                time.sleep(20)
                continue

            for row in data:

                try:

                    result = redis.hexists(DC_GZH_PUSH, f"{row.id}")
                    if result:
                        logger.info(f"{prefix} redis hexists: {row.id} continue")
                        continue

                    item = row.to_dict()
                    item.setdefault("wxid", "")
                    push_data = [item]
                    logger.info("push data: " + f'{push_data}')
                    result = GzhService.wechat_push(push_data)
                    logger.info(f"wechat_push result: {result}")

                    redis.hset(DC_GZH_PUSH, f"{row.id}", "1")

                    # beanstalk
                    logger.info(f'{prefix} redis hset: {row.id}')

                except Exception as ex:
                    logger.info(f'{prefix} exception: {str(ex)}')

            session.close()


        except Exception as ex:
            logger.error(f"{prefix} pull exception:" + str(ex))
            time.sleep(30)

        finally:
            if session is not None:
                session.close()

        time.sleep(180)

handle()
