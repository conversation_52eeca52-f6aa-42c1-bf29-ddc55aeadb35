"""
通过局域网ip扫描获取微信服务器信息，并写入数据库
:author wjh
:date 2022-12-21
"""

import json
import socket
import sys
import threading
import queue
import os
import time
import traceback
from concurrent.futures.thread import ThreadPoolExecutor
import requests

from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.models.model import DCWechatMachine
from dc.common.utils import Utils
from dc.conf.settings import get_settings2

logger = LoggingService('dc_wechat_machine.log')
ks = K8sService()

start_time = Utils.time_second()    # 开始时间
stop_time = start_time + 2 * 60 * 60   # 停止时间


def req_url(url):
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        }
        r = requests.get(url, headers=headers, timeout=0.5)
        r.encoding = 'utf-8'
        data: dict = json.loads(r.content)
        assert r.status_code == 200
        assert data.get('status', 200) == 404, 'status is error'
        return True
    except Exception as ex:
        return False


def scan(tp_no):
    """
    局域网扫描
    :author wjh
    :date 2022-12-21
    :param tp_no: 线程编号
    :return:
    """
    while not q.empty():
        params: dict = q.get()
        host = params.get('host')
        port = params.get('port')
        api = params.get('api')
        # c = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # c.settimeout(0.5)
        url = f'http://{host}:{port}'
        data = req_url(url)
        if data:
            print("%s:%d is open---" % (host, port))
            ip_list.append(params)
        else:
            pass
            # print("%s:%d is close" % (host, port))

        # c.close()


def init_params(**kwargs):
    """
    初始ip扫描段信息
    :param kwargs:
    :return:
    """
    # ranges = ['192.168.1.', '192.168.2.', '192.168.10.']
    ranges = get_settings2('weixin_gzh', 'machine_list', [])
    port = get_settings2('weixin_gzh', 'machine_port', 23230)
    for r in ranges:
        for ip in range(1, 254):
            host = f"{r}{ip}"
            # port = 23230
            machineIp = f"http://{host}:{port}/api"
            q.put({'host': host, 'port': port, 'machineIp': machineIp})


def update_machine_list(iplist=[]):
    """
    更新ip信息
    :param iplist:
    :return:
    """
    if not iplist:
        return
    mysql_service = MySQLService()
    session = mysql_service.create_session()
    hosts = [c.get('machineIp') for c in iplist]
    logger.info(f"hosts: {hosts}")

    session.execute("update DC_WechatMachine set status = 2 where machineIp not in ('%s')" % "', '".join(hosts))
    for value in iplist:
        h: DCWechatMachine = session.query(DCWechatMachine).where(DCWechatMachine.machineIp == value['machineIp']).first()
        if h is None:
            logger.info(f"create: {value}")
            h = DCWechatMachine()
            h.machineIp = value['machineIp']
            h.status = 1
        else:
            logger.info(f"update: {value}")
            h.status = 1

        session.add(h)
    session.commit()
    session.close()


def test():
    port = 23230
    ip_list = [{'host': '*************', 'port': port}, {'host': '*************', 'port': port}, {'host': '*************', 'port': port}, {'host': '*************', 'port': port}, {'host': '************', 'port': port}, {'host': '************', 'port': port},
               {'host': '************', 'port': port}, {'host': '************', 'port': port}, {'host': '************', 'port': port}, {'host': '************', 'port': port}, {'host': '*************', 'port': port}]
    update_machine_list(ip_list)


def get_result(future):
    """
    显示线程调用结果
    :author wjh
    :date 2022-12-21
    :param future:
    :return:
    """
    print(threading.current_thread().name + '运行结果：' + str(future.result()))


time_sleep = 2 * 60    # 等待时间 秒
no = 0
while True:
    try:
        ks.liveness()   # 探针

        # stop_time
        current_time = Utils.time_second()
        logger.debug(f"[{no}] current_time: {current_time} stop_time: {stop_time}")
        if current_time > stop_time:
            logger.debug(f"[{no}] current_time: {current_time} stop_time: {stop_time} break")
            break

        # 业务处理
        logger.debug(f"[{no}] begin...")

        ip_list = []
        thread_num = 10
        q = queue.Queue()
        init_params()

        logger.debug(f"thread_num: {thread_num}")

        pool = ThreadPoolExecutor(max_workers=10, thread_name_prefix='测试线程')
        for i in range(0, 10):
            future1 = pool.submit(scan, i)
            future1.add_done_callback(get_result)

        pool.shutdown()

        logger.info(f"ip_list: {ip_list}")
        update_machine_list(ip_list)

        logger.debug(f"[{no}] end")

    except Exception as ex:
        logger.error(f"[{no}] exception:{str(ex)}")

    finally:
        no += 1
        logger.debug(f"[{no}] sleep:{time_sleep}")
        time.sleep(time_sleep)
