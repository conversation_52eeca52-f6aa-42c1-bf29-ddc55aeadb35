"""
微信公众号采集-历史文章拉取服务
2022-10-19
"""
import json
import random
import time
import traceback

from sqlalchemy import text

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhInfo
from dc.services.beanstalk_service import BeanstalkService
# from dc.services.gzh_service import GzhService
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService

logger = LoggingService('dc_gzh.log')
mysql = MySQLService()
bs = BeanstalkService()
ks = K8sService()

settings = get_settings('weixin_gzh')
list_sleep = settings.get('list_sleep', 5)
list_update_days = settings.get('list_update_days', 2)
list_update_hours = settings.get('list_update_hours', 6)
list_update_stop = settings.get('list_update_stop', 3)

time_stop = Utils.time_days(list_update_stop, only_day=True)
time_stop = Utils.str2timestamp(time_stop)
logger.info(f"time_stop: {time_stop}")

enable_gzh_yds = False   # 启用阅读数获取
except_sleep = 10


def handle():

    logger.info('启动公众号-历史文章拉取 服务...')
    # logger.info(f'settings: {settings}')

    session = None
    prefix = None

    # gzh_service = GzhService()

    while True:

        ks.liveness()   # 探针
        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        try:

            beanstalk = bs.get_client(use=DC_GZH, watch=[DC_GZH])

            session = mysql.create_session()
            # pullAt = Utils.time_days(days=list_update_days, only_day=False)
            pullAt = Utils.time_delta(hours=list_update_hours)
            where = f"status=1 and crawlStatus=1 and (pullAt is null or pullAt < '{pullAt}')"
            data: list[DCGzhInfo] = session.query(DCGzhInfo).where(text(where)).order_by(DCGzhInfo.pullAt).limit(100).all()
            logger.info(f'{prefix} where：{where}')
            logger.info(f'{prefix} count: {len(data)}')
            if len(data) == 0:
                logger.debug(f"{prefix} 不存在数据，continue")
                time.sleep(20)
                continue

            for row in data:

                try:
                    row.pullStatus = 1
                    row.pullAt = Utils.showDateTime()
                    session.add(row)
                    session.commit()

                    # beanstalk
                    logger.info(f'{prefix} beanstalk put: {row.id}')
                    beanstalk.put(body=json.dumps({'id': row.id, 'createdAt': Utils.showDateTime()}))

                except Exception as ex2:
                    row.pullStatus = 2
                    row.pullResult = traceback.format_exc()
                    logger.error(f"{prefix}- pullStatus 2 --------- " + str(ex2))
                    # logger.debug(f"{prefix}- pullStatus 2 --------- " + traceback.format_exc())
                    session.add(row)
                    session.commit()

            session.close()

        except Exception as ex:
            logger.error(f"{prefix} pull exception:" + str(ex))
            time.sleep(except_sleep)

        finally:
            if session is not None:
                session.close()


handle()
