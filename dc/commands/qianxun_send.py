import json
import random
import sys
import os
import time
import traceback
from time import sleep
import requests
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.conf.settings import get_settings
from dc.services.mail_service import MailService
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from dc.common.utils import Utils
from dc.tests.browser import Browser
from dc.common import utils
from urllib.parse import urlparse, parse_qs, parse_qsl
from urllib import request
from dc.services.qianxun_service import QianxunService

"""
千寻微信框架-通过 redis 获取消息发送
2022-10-19
"""


wechat_alert_key = f'wechat_alert'

redisService = RedisService('redis', db=1)
redis = redisService.get_client()
logService = LoggingService('qianxun_send.log')

qx = QianxunService()

wechat_alert_key = f'wechat_alert'
wechat_alert_check_key = f'wechat_alert_check'

settings = get_settings('qianxun')
qianxun_channel = settings.get('channel', [])   # 开启通道
sleep_time_check = settings.get('check_sleep', 10 * 60)   # 每个消息类型检测间隔 秒
sleep_time = settings.get('time_sleep', 5)   # 间隔 秒
aims = settings.get('aims', None)   # 间隔 秒
mail_to_default: str = settings.get('mail_to', None)   # 间隔 秒
mail_to_default: list = mail_to_default.split(';')

start_time = Utils.time_second()    # 开始时间
stop_time = start_time + 3 * 60 * 60   # 停止时间

# sleep_time = 5
# sleep_time_check = 10 * 60
prefix = None

while True:

    rand = random.randint(100000, 999999)
    prefix = f"[{rand}] "

    try:

        # stop_time
        current_time = Utils.time_second()
        logService.debug(f"{prefix} current_time: {current_time} stop_time: {stop_time}")
        if current_time > stop_time:
            logService.debug(f"{prefix} current_time: {current_time} stop_time: {stop_time} break")
            break

        # 业务处理
        dt = Utils.showTime2()
        # logService.info(f'{prefix} ============================================' + dt)
        # logService.info(f'settings: {settings}')
        # logService.info(f'qianxun_channel: {qianxun_channel} sleep_time_check: {sleep_time_check} sleep_time: {sleep_time}')

        data = redis.rpop(f'{wechat_alert_key}')
        if data is None:
            # logService.debug(f"{prefix} redis message is empty, sleep {sleep_time} ...")
            sleep(sleep_time)
            continue

        # message = {
        #     "wxid": "25322860479@chatroom",
        #     "msg": "[@,wxid=all,nick=,isAuto=true]\n测试召唤全体\n测试[emoji=D83D][emoji=DE01]"
        # }

        data: dict = json.loads(data)
        check_key = f"{wechat_alert_check_key}_{data.get('type',1000)}"
        result = redis.exists(check_key)
        logService.debug(f"{prefix} check_key {check_key} check result: {result}")
        if result:
            logService.debug(f"{prefix} check {check_key} exists !!!!!!")
            continue

        set_result = redis.set(check_key, 1, sleep_time_check)

        channel = data.get('channel', 'all')
        mail_title = data.get('mail_title', None)
        mail_body = data.get('mail_body', None)
        mail_to: str = data.get('mail_to', None)
        if mail_to:
            mail_to = mail_to_default + mail_to.split(';')
        else:
            mail_to = mail_to_default

        # 过滤 wuzhou
        mail_to = [addr.strip() for addr in mail_to if not addr.strip().startswith('wuzhou')]
        mail_to = list(set(mail_to))

        mail_cc = data.get('mail_cc', None)

        if channel in ['all', 'wechat']:  # 微信
            if 'wechat' in qianxun_channel:
                message = data.get('data')
                wxid = message.get('wxid')
                if len(wxid) == 0:
                    message['wxid'] = aims
                logService.info(f"{prefix} wechat send: {message}")
                result = qx.send_text(message)
                logService.info(f"{prefix} wechat send result: {result}")
            else:
                logService.info(f"{prefix} wechat channel closed")

        if channel in ['all', 'mail']:  # 邮件
            if 'mail' in qianxun_channel:
                mail = MailService(logger=logService)
                email = {
                    'mail_title': mail_title,
                    'mail_body': mail_body,
                    'mail_to': mail_to,
                    'mail_cc': mail_cc,
                }
                logService.info(f"{prefix} mail send: {email}")
                result = mail.send(email)
                logService.info(f"{prefix} mail send result: {result}")
            else:
                logService.info(f"{prefix} mail channel closed")

        sleep(sleep_time)
        # update redis
        # if task.taskStatus == 2:  # // 失败，重新进入队列
        #     redis.lpush(f'{redis_task_key}', task.id)
        #     logService.dcPullLog(f'{prefix} redis push {redis_task_key}:{task.id}')
        #

    except Exception as ex:
        #redis.lpush(f'{redis_task_key}', taskId)
        logService.error(f"{prefix} pull exception:" + traceback.format_exc())

    finally:
        # logService.info(f"{prefix} finally ++++")
        pass








