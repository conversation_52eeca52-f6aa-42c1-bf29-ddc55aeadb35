import json
import os
import sys


sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.redis_service import RedisService


redisService = RedisService('redis', run_model='test', db=6)
client = redisService.get_client()
article_id = 4000000
while True:
    if article_id > 4010569:
        break
    r = client.lpush('dc_article_weight', json.dumps({'taskId': article_id, 'type': 1}))
    print(r)
    article_id += 1
