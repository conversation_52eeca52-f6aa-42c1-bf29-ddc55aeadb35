# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 文章队列消费 文章热度初始化
# <AUTHOR> wang<PERSON>
# @Time      : 2023/9/27
# Version    : 1.0
import datetime
import json
import logging
import os
import sys
import time
import traceback
# from datetime import datetime
from time import sleep

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService
from dc.services.ai_base_service import AIBaseService
from dc.services.elasticsearch_service import ElasticsearchService
from dc.conf.settings import *

from dc.models.model import DCArticle, DCHotArticleWordsWeight, DCSiteList, DCSiteTask, DCGzhArticle, DCGzhInfo


def determine_date_format(date_string):
    # 尝试使用 "%Y-%m-%d" 格式解析日期字符串
    try:
        parsed_date = datetime.datetime.strptime(date_string, "%Y-%m-%d")
        return parsed_date
    except ValueError:
        pass

    # 尝试使用 "%Y-%m-%d %H:%M:%S" 格式解析日期字符串
    try:
        parsed_date = datetime.datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")
        return parsed_date

    except ValueError:
        pass

    # 如果都无法解析，则返回 None 或其他默认值，表示日期格式未知
    return None


def days_between(date_string):
    # 输入的日期字符串
    parsed_date = determine_date_format(date_string)

    # 获取当前时间
    current_datetime = datetime.datetime.now()

    # 计算时间差
    time_difference = current_datetime.date() - parsed_date.date()

    # 获取时间差的天数部分
    days_difference = time_difference.days

    return days_difference


mysql = MySQLService()
logger = LoggingService(logfile='hot_word_update_24.log')

session = mysql.create_session()
esService = ElasticsearchService()

redisService = RedisService('redis', db=6)
redis_client = redisService.get_client()

# 获取1天内所有的文章数据
end_date = datetime.datetime.now().date()  # 当前日期
start_date = end_date - datetime.timedelta(days=1)  # 前一周日期
articles: [DCArticle] = session.query(DCArticle).filter(
    DCArticle.articlePublishTime >= start_date).all()

words = []
for article in articles:
    for w in article.hotWords.split(','):
        words.append(w)

print(words)
print("文章数量", len(articles))

# 获取所有的关键词数据
wordsScoreList: [DCHotArticleWordsWeight] = session.query(DCHotArticleWordsWeight).filter(
    DCHotArticleWordsWeight.status == 2, DCHotArticleWordsWeight.word.in_(words)).all()

# 根据 wordsScoreList 提取 word 为key ,weight为value 组成一个dict
wordsScoreDict = {}
for wordsScore in wordsScoreList:
    wordsScoreDict[wordsScore.word] = wordsScore.weight

print("关键词数量:", len(wordsScoreDict))

# 重新计算新的文章得分 每1000个提交一次
i = 0  # 计数器 每1000个更新提交一次
esUpdate = []

esUpdateUnique = []
# 获取热词的配置
# unique_param = redis_client.get('hot_unique_param')
# if unique_param is None or unique_param == "":
#     unique_param = get_settings('hot_unique_param')
#     redis_client.set('hot_unique_param', unique_param)

param = redis_client.get('hot_param')
if param is None or param == "":
    param = get_settings('hot_param')
    redis_client.set('hot_param', param)


for article in articles:
    # 根据article hotWords 字段计算 hotScore  hotWords字段为以,分割的字符串, 每个字符串代表一个关键词
    hotScore = 0
    hotWords = article.hotWords.split(',')
    for hotWord in hotWords:
        hotScore += wordsScoreDict.get(hotWord, 0)

    W = hotScore * 0.8
    I = (article.baseScore + article.timeScore) * 0.2
    hotWeight = W + I
    if article.type == 3:
        hotWeight *= float(param)
    # 更新文章热度
    if hotWeight == article.hotWeight24h:
        continue
    article.hotWeight24h = hotWeight
    session.add(article)
    esUpdate.append({"_id": article.newEsId, "new_score": hotWeight})
    i += 1
    if i % 1000 == 0:
        # 数据库批量提交
        # es 批量更新
        esService.update_multiple_docs_score(docs=esUpdate, key='hotWeight24h')
        esUpdate = []
        session.commit()

esService.update_multiple_docs_score(docs=esUpdate, key='hotWeight24h')
session.commit()
