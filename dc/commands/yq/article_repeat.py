# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 文章去重
# <AUTHOR> wjh
# @Time      : 2023/9/27
# Version    : 1.0
import os
import sys
import re
import traceback

from dc.services.k8s_service import K8sService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.mysql_service import MySQLService
from dc.services.logging_service import LoggingService
from dc.services.elasticsearch_service import ElasticsearchService
from datetime import datetime, timedelta
from dc.models.model import DCArticle
from dc.services.ai_base_service import AIBaseService
from dc.services.zhipu_service import ZhipuService
import dc.services.jiwei_ai.abstract as jiwei_ai

ess = ElasticsearchService()
logService = LoggingService(logfile='article_repeat.log')
es_index_name = 'dc_hotarticle'
zhipu = ZhipuService()
ks = K8sService()

fail_words = ['无法提取', '政治内容', '聊天机器人', '很抱歉', '根据以下模板', '150字', '有关政治', '需要我回答', '您可以根据以下提示', '一名人工智能助手', '政治相关',
              '一个人工智能助手']
response_filter_words = ['标题：', '标题:', '标题', '文章摘要：', '文章摘要:', '文章摘要', '文章概述:', '本文主要介绍了', '该篇文章摘要如下:', '本文介绍了']
response_filter_patterns = ['^以下是.*?内容的示例:', '^以下是.*?[文章|报告]的摘要[:，]', '^以下是.*?文章内容:', '^关注下方公众号.*?数字不断递增的魅力！']
input_filter_patterns = [
    r"大家好.*?入市需谨慎！",
    r"独角兽智库.*?文末有二维码",
    r"感知芯视界.*?扫码报名参加",
    r"全球AI动态日报.*?敬请期待！",
    r"点击上方蓝字.*?职位 #",
    "集微网·爱集微APP.*?原创内容\n公众号",
    "来源：雪球App.*?\d{9,10}）",
    "购买该报告.*?（#换成@）",
    "求是缘半导体联盟.*?原创内容\n公众号",
    "价值线.*?价值线导读",
    "点击标题下.*?\n公众号",
    "点击↑红刊投服平台.*?不迷路",
    "长按扫码.*?投资可预见的未来",
    "^半导体行业观察.*?\n公众号",
    "^点击上方.*?一起进步呀~",
    "^点击上方.*?关注我们！"
]
abstract_len = 150
sub_len = 300


# 通过正则过滤部分内容
def remove_paragraph(text, patterns):
    for pattern in patterns:
        # 定义正则表达式模式
        o = re.compile(pattern, re.DOTALL)
        # 使用正则表达式替换匹配的段落为空字符串
        text = o.sub('', text)
    return text


# 提取文章的简介
def get_abstract(content: str):
    # 去除两边的空格
    content = content.strip()
    # 过滤掉文章中废话开场白
    content = remove_paragraph(content, input_filter_patterns)
    if len(content) >= abstract_len:
        prompt = "你是一名专业编辑，请用一段话概括文章的摘要，用作网页meta的description,不超过{}字。直接返回结果，不需要其他介绍，使用中文回答，正确使用标点符号，。以下是文章内容。{}".format(
            abstract_len, content[:1300])
        result = zhipu_chat(prompt)
        num = 0
        while result == "broken":  # 问不出结果会自动反复提交上一个问题，直到有结果为止。
            result = zhipu_chat(prompt)  # 重复提交问题
            num += 1
            if num > 3:
                logService.dcPullLog(f'智普提取摘要失败')
                break
        if result == 'broken':
            s = content[:sub_len]
        else:
            # 判断是否回答的失败
            mark = all(fail_word not in result for fail_word in fail_words)
            if not mark:
                s = content[:sub_len]
            else:
                s = result
    else:
        s = content
    s = s.strip()
    return filter_abstract(s)


# 优化文章简介
def filter_abstract(abstract: str):
    s = extract_text_before_last_symbol(abstract)
    # 删除 \n, \\n, \\\n, /n, //n
    ans = re.sub(r'(\\{0,2}n|/n)', '', s)
    ans = ans.replace('\n', '')
    # 将 \" 替换为 "
    ans = ans.replace('\\"', '"').replace("\\'", "'")
    ans = ans.replace(r'\"', '"').replace(r"\'", "'")
    ans = ans.replace(',', '，')
    ans = ans.replace(';', '；')
    ans = ans.replace('!', '！')
    ans = ans.replace('?', '？')
    # 删除部分违规字符
    ans = remove_prefixes(ans)
    return ans


# 处理文章简介突然结束的问题
def extract_text_before_last_symbol(input_string):
    pattern = r'[.!?。？！;；]+'
    matches = list(re.finditer(pattern, input_string))
    if matches:
        last_match = matches[-1]
        return input_string[:last_match.start()] + last_match.group()
    else:
        return input_string


# 移除一些不可以出现的字符
def remove_prefixes(input_string):
    input_string = remove_paragraph(input_string, response_filter_patterns)
    pattern = '|'.join(map(re.escape, response_filter_words))
    result_string = re.sub(pattern, '', input_string)
    return result_string


def zhipu_chat(prompt: str):
    try:
        response = zhipu.invoke([
            {"role": "user", "content": prompt}
        ])
        if response and (response.get('code', 0) == 200):
            abstract = ''
            for r in response.get('data').get('choices'):
                abstract += r.get('content', '').strip('"')
            return abstract
        else:
            return 'broken'
    except Exception as exc:
        return "broken"


def handle():
    try:
        ks.liveness()   # 探针

        # 获取当前时间
        now = datetime.now()
        start_of_day = now - timedelta(hours=24)
        # 将起始时间转换为字符串
        start_of_day_str = start_of_day.strftime("%Y-%m-%d %H:%M:%S")
        es = ess.get_connect()
        # 查询今天的新数据和已经通过的数据
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "range": {
                                "public_time": {
                                    "gte": start_of_day_str
                                }
                            }
                        },
                        {
                            "terms": {
                                "is_repeat": [0, 1]
                            }
                        }
                    ]
                }
            },
            "sort": {
                "hotWeight": "desc"
            }
        }

        source = ['title', 'text', 'is_repeat', 'hotWeight', 'abstract', 'type']
        size = 10000
        response = es.search(index=es_index_name, _source=source, body=query, size=size)
        hits = response["hits"]["hits"]
        c = len(hits)
        logService.dcPullLog(f'文章查询数量count:{c}')

        if c == 0:
            return
        ai = AIBaseService()
        mysql = MySQLService()
        session = mysql.create_session()
        start_num = 0
        offset_num = 100
        while c > start_num:
            end_num = start_num + offset_num
            data_list = hits[start_num:end_num]
            es_data = []
            ids_all = set()
            ids_empty = set()
            weight_map = {}
            weight_map_24h = {}
            weight_map_unique = {}
            type_map = {}
            abstract_map = {}
            content_map = {}
            repeat_map = {}
            new_count = 0
            # 将文章数据的权重、简介、文章内容记录成字典，方便之后使用
            for hit in data_list:
                tmp = {}
                text = hit['_source'].get('text', '')
                if not text:
                    ids_empty.add(hit['_id'])
                    continue
                tmp['es_id'] = hit['_id']
                tmp['value'] = hit['_source']['title'] + text
                ids_all.add(hit['_id'])
                weight_map[hit['_id']] = hit['_source'].get('hotWeight', 0)
                weight_map_24h[hit['_id']] = hit['_source'].get('hotWeight24h', 0)
                weight_map_unique[hit['_id']] = hit['_source'].get('hotWeightUnique', 0)
                type_map[hit['_id']] = hit['_source'].get('type', 0)
                abstract_map[hit['_id']] = hit['_source'].get('abstract', '')
                content_map[hit['_id']] = text
                repeat_map[hit['_id']] = hit['_source'].get('is_repeat', 0)
                es_data.append(tmp)
                if hit['_source'].get('is_repeat', 0) == 0:
                    new_count += 1
            # 如果没有新增的数据，直接退出
            if not new_count:
                logService.dcPullLog(f"{start_num}-{end_num}未查询到新的文章数据")
                start_num = end_num
                continue
            logService.dcPullLog(f"before duplication:{len(es_data)}")
            # 比对结果按分组返回
            res_data = ai.article_duplication(es_data)
            logService.dcPullLog(f"after duplication:{len(res_data)}")
            ids_choice = set()
            # 处理数据，选出每组文章中hotWeight最高的，将其id放入ids_choice,其余的id放入ids_delete
            for child_data in res_data:
                tmp_weight_24h = -1
                tmp_weight_unique = -1
                tmp_id = None
                for one in child_data:
                    # if type_map[one['es_id']] == 3:
                    #     tmp_id = one['es_id']
                    #     break
                    if weight_map_unique[one['es_id']] > tmp_weight_unique or (
                            (weight_map_unique[one['es_id']] == tmp_weight_unique) and (
                            weight_map_24h[one['es_id']] > tmp_weight_24h)):
                        tmp_weight_24h = weight_map_24h[one['es_id']]
                        tmp_weight_unique = weight_map_unique[one['es_id']]
                        tmp_id = one['es_id']
                ids_choice.add(tmp_id)
            ids_delete = (ids_all - ids_choice) | ids_empty
            query = {
                "script": {
                    "source": "ctx._source.is_repeat = params.is_repeat",
                    "lang": "painless",
                    "params": {
                        "is_repeat": 1  # 更新的状态值
                    }
                },
                "query": {
                    "terms": {
                        "_id": []
                    }
                }
            }

            # 将ids_choice中的ES数据更新为is_repeat=1，并且没有简介的，生成简介
            if ids_choice:
                list_ids = list(ids_choice)
                mysql_update_ids = []
                for es_id in list_ids:
                    if not abstract_map.get(es_id, '') or repeat_map.get(es_id, 0) != 1:
                        doc_update = {
                            "doc": {
                                "is_repeat": 1
                            }
                        }
                        if repeat_map.get(es_id, 0) != 1:
                            mysql_update_ids.append(es_id)
                        if not abstract_map.get(es_id, ''):
                            # ab = get_abstract(content_map.get(es_id, '')
                            ab = jiwei_ai.invoke(content_map.get(es_id, ''), sub_len)
                            doc_update['doc']['abstract'] = ab
                            logService.dcPullLog(f'提取摘要-{es_id}:{ab}')
                        # 使用update API执行更新操作
                        es.update(index=es_index_name, id=es_id, body=doc_update)
                # mysql也更新
                if mysql_update_ids:
                    condition = DCArticle.newEsId.in_(mysql_update_ids)
                    article_list: DCArticle = session.query(DCArticle).filter(
                        condition).params(value=mysql_update_ids).all()
                    for article in article_list:
                        article.isRepeat = 1
                    session.commit()
            # 将ids_delete中的数据更新为is_repeat=2
            if ids_delete:
                list_ids = list(ids_delete)
                query['script']['params']['is_repeat'] = 2
                query['query']['terms']['_id'] = list_ids
                # 更新ES
                response = es.update_by_query(index=es_index_name, body=query)
                condition = DCArticle.newEsId.in_(list_ids)
                # 更新mysql
                article_list: DCArticle = session.query(DCArticle).filter(
                    condition).params(value=list_ids).all()
                for article in article_list:
                    article.isRepeat = 2
                session.commit()
            start_num = end_num
        es.close()
        return
    except:
        es.close()
        logService.dcPullLog(f'文章去重失败，失败原因：【{traceback.format_exc()}】')


handle()
