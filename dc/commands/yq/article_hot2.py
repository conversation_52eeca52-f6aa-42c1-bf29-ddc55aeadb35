# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 文章队列消费 文章热度初始化
# <AUTHOR> wang<PERSON>
# @Time      : 2023/9/27
# Version    : 1.0

import json
import logging
import os
import sys
import time
import traceback
from datetime import datetime
from time import sleep

from dc.services.k8s_service import K8sService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.conf.settings import *
from dc.services.zhipu_service import ZhipuService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService
from dc.services.ai_base_service import AIBaseService
from dc.services.elasticsearch_service import ElasticsearchService
from dc.conf.defines import *
from dc.models.model import DCArticle, DCHotArt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DCGzhInfo
import dc.services.jiwei_ai.industry_article as industry_article

# read_mode = RUN_MODE  # 'prod'
# write_mode = RUN_MODE  # 'test'

mysql = MySQLService()
session = mysql.create_session()

mysql2 = MySQLService()
session2 = mysql2.create_session()

redisService = RedisService('redis', db=6)
dc_article_weight = 'dc_article_weight'
logger = LoggingService(logfile='article_hot2.log')

redis_client = redisService.get_client()
ai = AIBaseService()
esService = ElasticsearchService()
esService2 = ElasticsearchService()
zhipu = ZhipuService()
ks = K8sService()

def determine_date_format(date_string):
    # 尝试使用 "%Y-%m-%d" 格式解析日期字符串
    try:
        parsed_date = datetime.strptime(str(date_string), "%Y-%m-%d")
        return parsed_date
    except ValueError:
        pass

    # 尝试使用 "%Y-%m-%d %H:%M:%S" 格式解析日期字符串
    try:
        parsed_date = datetime.strptime(str(date_string), "%Y-%m-%d %H:%M:%S")
        return parsed_date

    except ValueError:
        pass

    # 如果都无法解析，则返回 None 或其他默认值，表示日期格式未知
    return None


def days_between(date_string):
    # 输入的日期字符串
    parsed_date = determine_date_format(date_string)

    # 获取当前时间
    current_datetime = datetime.now()

    # 计算时间差
    time_difference = current_datetime.date() - parsed_date.date()

    # 获取时间差的天数部分
    days_difference = time_difference.days

    return days_difference


def between_hot_time(date_string):
    # 解析日期字符串为datetime对象
    parsed_date = determine_date_format(date_string)
    if parsed_date is None:
        return False  # 日期格式无法解析，不在热门时间内

    # 提取时间部分
    time_part = parsed_date.time()

    # 定义热门时间范围
    hot_start_time = 1  # 凌晨1点
    hot_end_time = 6  # 凌晨6点

    # 判断时间是否在热门时间范围内
    if hot_start_time <= time_part.hour < hot_end_time:
        return True
    else:
        return False


while True:
    # 如果当前时间为00:00到00:10之间  则跳过执行 并睡眠5秒
    if datetime.now().hour == 0 and datetime.now().minute < 10:
        sleep(60)
    try:
        session.flush()
        session2.flush()
        # 检查session的连接是否已经断开, 断开了就重新连接
        if not session.is_active:
            session = mysql.create_session()
        if not session2.is_active:
            session2 = mysql2.create_session()
        task = redis_client.rpop(dc_article_weight)
        ks.liveness()   # 探针

        if task is None:
            sleep(5)
            continue

        logger.info(f"redis {task}")
        task = json.loads(task)
        # print(task)

        # 获取task 信息
        if task.get("type") == 1:
            index = 'dc_yq'
            taskInfo: DCSiteTask = session.query(DCSiteTask).filter(DCSiteTask.id == task['taskId']).first()
            # 如果舆情的索引不是微信的或不是舆情的则跳过
            if taskInfo.esIndex != "dc_yq":
                logger.info(f"taskInfo.esIndex != dc_yq,{taskInfo.esId}", task)
                continue
            # 获取 site 信息
            siteInfo: DCSiteList = session.query(DCSiteList).filter(DCSiteList.id == taskInfo.siteListId).first()
            # 根据url判断是否重复
            have: DCSiteTask = session.query(DCSiteTask).filter(DCSiteTask.url == taskInfo.url, DCSiteTask.id < taskInfo.id).first()
            if have is not None:
                logger.info(f"have is not None, {taskInfo.esId}", task)
                sleep(5)
                continue

            task_es_id = taskInfo.esId

        if task.get('type') == 2:
            index = 'dc_wechat'
            taskInfo: DCGzhArticle = session.query(DCGzhArticle).filter(DCGzhArticle.id == task['taskId']).first()
            # 如果舆情的索引不是微信的或不是舆情的则跳过
            if taskInfo.esIndex != "wechat":
                logger.info(f"taskInfo.esIndex != wechat {taskInfo.esId}", task)
                continue

            # 获取 site 信息
            siteInfo: DCGzhInfo = session.query(DCGzhInfo).filter(DCGzhInfo.gzhId == taskInfo.gzhId).first()
            # 根据url判断是否重复
            have: DCGzhArticle = session.query(DCGzhArticle).filter(DCGzhArticle.url == taskInfo.url,
                                                                    DCGzhArticle.id < taskInfo.id).first()
            if have is not None:
                logger.info(f"have is not None,{taskInfo.esId}", task)
                sleep(5)
                continue

            task_es_id = taskInfo.esId

        if task.get('type') == 3:
            index = 'dc_yq'
            task_es_id = task['taskId']

        # 获取article去重结果
        have: DCArticle = session2.query(DCArticle).filter(DCArticle.esId == task_es_id).first()
        if have is not None:
            logger.info(f"have is not None,{task_es_id}", task)
            sleep(5)
            continue

        baseScore = 1
        # 基础分值设置
        try:
            if siteInfo.isHotModule is not None and siteInfo.isHotModule == 1:
                # 基础得分为1.2
                baseScore = 1.2
        except:
            pass

        text_field = 'content' if task.get("type") == 2 else 'text'
        # 从es里获取文章详情
        if task_es_id:
            try:
                esContent = esService.get_connect().get(id=task_es_id, index=index)
                content = esContent['_source'][text_field]
            except:
                # 重新填充回队列
                redis_client.lpush(dc_article_weight, f"{task}")
                logger.error("article_hot_params_err", {task_es_id}, {index})
                continue
        else:
            logger.error("article_hot_esId_err", task['taskId'])
            continue
        # 文章内容小于40个字 自动过滤掉
        if len(content) < 100:
            logger.info(f"文章内容小于100个字 自动过滤掉,{task_es_id}", task)
            continue

        if esContent['_source']['title'] == "":
            logger.info(f"标题为空 ,{task_es_id}", task)
            continue
        filter_words = ["现货促销", '下单即发', '即时库存', '欢迎选购', '现货现发', '入群']
        for filter_word in filter_words:  # 过滤掉关键词
            if filter_word in esContent['_source']['title']:
                logger.info(f"过滤掉关键词 {filter_word} ,{task_es_id}", task)
                continue

        if task.get('type') != 3:
            # 检查是否是半导体行业的相关新闻
            # prompt = "以下是一篇文章，{}。这段文字描述的是与半导体、芯片、集成电路产业链（设计、制造、封测、装备、材料、EDA/IP等）及其重点应用领域（手机、服务器、汽车、IoT等）相关的内容吗？只回答：是、否，不需要其他字体。".format(
            #     content[:1000])
            try:
                # response = zhipu.invoke([
                #     {"role": "user", "content": prompt}
                # ])
                # if response['code'] != 200:
                #     # 重新填充回队列
                #     redis_client.lpush(dc_article_weight, task)
                #     logger.error("article_hot_zhipu_err", f"exception: {traceback.format_exc()}")
                #     continue
                # if "否" in response['data']['choices'][0]['content']:
                #     logger.info(f"response is 否,{task_es_id} ,type:{task.get('type')}")
                #     continue

                response = industry_article.is_industry_article(content)
                if response['ret'] != 0:
                    redis_client.lpush(dc_article_weight, f"{task}")
                    logger.error("article_hot_zhipu_err", f"exception: {traceback.format_exc()}")
                    continue
                if response['data'] != '行业':
                    logger.info(f"response is 否,{task_es_id} ,type:{task.get('type')}")
                    continue
                logger.info(f"response is 是,{task_es_id} ,type:{task.get('type')}")
            except:
                # 重新填充回队列
                redis_client.lpush(dc_article_weight, f"{task}")
                logger.error("article_hot_zhipu_err", f"exception: {traceback.format_exc()}")
                continue

        # 写入新的es key为hotArticle
        newIndex = "dc_hotarticle"
        filter = {
            "query": {
                "terms": {
                    "_id": [task_es_id]
                }
            },
            'size': 1
        }
        newEsRecord = esContent['_source']

        newEsRecord['is_repeat'] = 0
        # 拆词
        words = ai.separate_word(content=content)
        if words == "":
            logger.info(f"words = '',{task_es_id}", task)
            continue
        # 将words的这个dict 重新组成一个 已返回的key 为元素的素组
        allWordsArray = [w.get('k') for w in words]
        logger.info(",".join(allWordsArray))

        # 拆词得分
        wordsScoreList: [DCHotArticleWordsWeight] = session2.query(DCHotArticleWordsWeight).filter(
            DCHotArticleWordsWeight.word.in_(allWordsArray)).all()
        wordScore = 0
        calculateWords = []
        preCalculateWords = []
        for w in wordsScoreList:
            if w.status == 1:
                preCalculateWords.append(w)
                continue
            if w.status == -1:
                continue
            if len(calculateWords) >= 20:
                continue
            calculateWords.append(w)
        for w in calculateWords:
            wordScore += w.weight + baseScore

        if len(calculateWords) == 0:
            logger.info(f"after filter! words = '',{task_es_id}", task)
            continue

        # 把wordScoreList中的 word字段提取出来成为一个数组
        wordsInDB = [w.word for w in wordsScoreList]
        # 将words的这个dict 重新组成一个 已返回的key 为元素的素组
        wordsArray = [w.word for w in calculateWords]
        # 将wordsArray 转换成以逗号分割的字符串
        wordsStr = ",".join(wordsArray)
        # print(wordsStr)
        logger.info(f"{wordsStr},{task_es_id}")

        # 文章质量
        articleQuality = ai.text_quality(content=content[:200])
        # articleQuality = 30
        # 语法得分
        languageScore = ai.error_correction(content=content[:200])
        # languageScore = 50
        # 时间得分
        timeScore = 0

        # 来源得分
        hotScore = 0
        if task.get('type') != 3:
            match siteInfo.level:
                case 1:
                    hotScore = 6
                case 2:
                    hotScore = 10
                case 3:
                    hotScore = 20
                case 4:
                    hotScore = 30

        # 计算文章得分入库
        W = wordScore * 0.8
        publicTime = esContent['_source'].get('public_time', esContent['_source']['analysis_time']) if task.get(
            "type") != 2 else esContent['_source']['public_time']
        if between_hot_time(publicTime):
            timeScore = 20
        I = (articleQuality + languageScore + timeScore + hotScore) * 0.2

        if publicTime == "" or publicTime == datetime(1970, 1, 1, 0, 0, 0):
            T = days_between(taskInfo.createdAt) if task.get('type') != 3 else 0
        else:
            T = days_between(publicTime)
        G = 1.2
        hotWeight = (W + I) / ((T + 1) ** G)
        newEsRecord['text'] = newEsRecord[text_field]
        # 删除newEsRecord中的 content字段 若果这个字段存在的话
        newEsRecord['public_time'] = publicTime
        if 'content' in newEsRecord:
            del newEsRecord['content']
        if 'wx_pub_time' in newEsRecord:
            del newEsRecord['wx_pub_time']
        newEsRecord['hotWeight'] = hotWeight
        newEsRecord['hotWeightUnique'] = hotWeight
        newEsRecord['type'] = task.get("type")
        newEsRecord['hotWeight24h'] = 0
        currentTime = datetime.now()
        formatted_date = currentTime.strftime("%Y-%m-%d %H:%M:%S")
        newEsRecord['createdAt'] = formatted_date
        if task.get("type") == 2:
            newEsRecord['analysis_time'] = newEsRecord['createAt']
        # 写入数据库及es
        # 删除 newEsRecord['tags']
        if 'tags' in newEsRecord:
            del newEsRecord['tags']  # 删除newEsRecord中的 tags字段 若果这个字段存在的话
        hotUnique = hotWeight
        if task.get("type") == 3:
            unique_param = redis_client.get('hot_unique_param')
            if unique_param is None or unique_param == "":
                unique_param = get_settings('hot_unique_param')
                redis_client.set('hot_unique_param', unique_param)

            param = redis_client.get('hot_param')
            if param is None or param == "":
                param = get_settings('hot_param')
                redis_client.set('hot_param', param)
            # 计算爱集微文章额外的加权参数 及排重加权参数
            hotUnique *= float(unique_param)
            hotWeight *= float(param)
            newEsRecord['hotWeight'] = hotWeight
            newEsRecord['hotWeightUnique'] = hotUnique

        newEsResult = esService2.upsert(index=newIndex, body=newEsRecord, filter=filter)
        newEsId = newEsResult[0]['_id']
        logger.info(f"update es {newEsId}")

        newArticleRecord = DCArticle(
            baseScore=articleQuality + languageScore + hotScore,
            articleQualityScore=articleQuality,
            languageScore=languageScore,
            timeScore=timeScore,
            sourceScore=hotScore,
            hotWords=wordsStr,
            wordScore=wordScore,
            esId=task_es_id,
            newEsId=newEsId,
            articlePublishTime=publicTime,
            hotWeight=hotWeight,
            hotWeightUnique=hotUnique,
            type=task.get("type"),
            isRepeat=0
        )

        session2.add(newArticleRecord)
        # session2.commit()

        # 把wordArray与have的交集刨除 并写入入库DCHotArticleWordsWeight库中
        # 再把更新的DCHotArticleWordsWeight中 wordArray与have的交集更新weight+1

        newWordsArray = set(allWordsArray) - set(wordsInDB)
        newWordRecords = []
        if isinstance(publicTime, str):
            publicTime = datetime.strptime(publicTime, "%Y-%m-%d %H:%M:%S").date()

        dayIndex = datetime.weekday(publicTime)
        dayColumn = "day{}Weight".format(dayIndex + 1)
        # 更新DCHotArticleWordsWeight中交集部分的weight
        for w in calculateWords:
            w.weight += baseScore
            dayColumnValue = getattr(w, dayColumn)
            setattr(w, dayColumn, dayColumnValue + baseScore)
            session2.add(w)
        for w in preCalculateWords:
            w.weight += baseScore
            dayColumnValue = getattr(w, dayColumn)
            setattr(w, dayColumn, dayColumnValue + baseScore)
            session2.add(w)
        # up_sql = f'UPDATE `DC_HotArticleWordsWeight` SET weight = weight +{baseScore}, {dayColumn} = {dayColumn}+{
        # baseScore} where id = ' \ f'"{w.id}"' session.execute(up_sql)

        # 插入   刨除交集部分并写入入库DCHotArticleWordsWeight库中
        if len(newWordsArray) != 0:
            for w in newWordsArray:
                newWordRecord = DCHotArticleWordsWeight()
                newWordRecord.word = w
                newWordRecord.weight = baseScore
                newWordRecord.status = 1
                setattr(newWordRecord, dayColumn, 1)
                session2.add(newWordRecord)
        session2.commit()
        session.commit()
        logger.info("session commit")

    except Exception as e:
        sleep(1)
        logger.error("article_hot_error", f"exception: {traceback.format_exc()}")
        exit()
