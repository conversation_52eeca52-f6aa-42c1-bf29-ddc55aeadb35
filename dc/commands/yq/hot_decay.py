# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 文章热度衰减
# <AUTHOR> liwenshuangcd
# @Time      : 2023/9/27
# Version    : 1.0

import datetime
import os
import sys
import traceback
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.mysql_service import MySQLService
from dc.services.logging_service import LoggingService

mysql = MySQLService()
logger = LoggingService('dc_hot_decay.log')


def init_weight():
    current_datetime = datetime.datetime.now()
    # 获取当前日期是星期几（0表示星期一，1表示星期二，以此类推）
    current_day_of_week = current_datetime.weekday()

    filedes = ['day1Weight', 'day2Weight', 'day3Weight', 'day4Weight', 'day5Weight', 'day6Weight', 'day7Weight']
    filed_key = current_day_of_week % 7
    filed = filedes[filed_key]

    try:
        session = mysql.create_session()

        up_sql = f'UPDATE `DC_HotArticleWordsWeight` SET weight = weight - {filed}'
        logger.info(f"up_sql: {up_sql}")
        session.execute(up_sql)
        session.commit()

        up_sql1 = f'UPDATE DC_HotArticleWordsWeight SET {filed} = 0'
        logger.info(f"up_sql: {up_sql1}")
        session.execute(up_sql1)
        session.commit()

        session.close()
        logger.info('热词处理成功')
    except:
        logger.error(f'热词处理失败，失败原因：【{traceback.format_exc()}】')


init_weight()

