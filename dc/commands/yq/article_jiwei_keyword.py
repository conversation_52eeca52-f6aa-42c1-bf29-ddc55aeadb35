# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 集微网新闻标签热词
# <AUTHOR> wjh
# @Time      : 2023/10/07
# Version    : 1.0

import datetime
import json
import os
import sys
import traceback
import requests
from sqlalchemy import desc
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.mysql_service import MySQLService
from dc.services.logging_service import LoggingService
from dc.models.model import DCHotArticleWordsWeight
from dc.conf.settings import get_settings

mysql = MySQLService()
log = LoggingService('dc_jiwei_keyword.log')


def init_keyword():
    current_datetime = datetime.datetime.now()
    # 获取当前日期是星期几（0表示星期一，1表示星期二，以此类推）
    current_day_of_week = current_datetime.weekday()

    filedes = ['day1Weight', 'day2Weight', 'day3Weight', 'day4Weight', 'day5Weight', 'day6Weight', 'day7Weight']
    filed_key = current_day_of_week % 7
    filed = filedes[filed_key]

    try:
        session = mysql.create_session()

        filed1 = getattr(DCHotArticleWordsWeight, filed)
        ret = session.query(DCHotArticleWordsWeight).order_by(desc(filed1)).first()
        max_weight = 0.00
        if ret:
            max_weight = float(ret.to_dict()[filed])

        if max_weight == 0.00:
            log.dcPullLog('当前最大权重为0，不需要进行更新')
            exit()

        # 请求接口获取关键词
        jiwei_net_domain = get_settings('jiwei_net_domain')
        url = jiwei_net_domain + '/api/sentiment/newskeywords'
        log.info(f"jiwei url: {url}")

        params = {'source': 'pc', 'time': current_datetime.strftime('%Y-%m-%d')}
        res = requests.post(url, params=params)
        res.encoding = "UTF-8"

        log.info(f"jiwei response: {res.text}")
        res_info = json.loads(res.text)

        if res.status_code != 200 or res_info['errno'] != 0:
            log.error(f'获取数据接口请求异常，状态码：{res.status_code}')
            exit()

        tags = res_info['data']
        for keyword in tags:
            info = session.query(DCHotArticleWordsWeight).filter(DCHotArticleWordsWeight.word == keyword).first()
            if info:
                info = info.to_dict()
                num = float(info['weight']) - float(info[filed]) + max_weight
                up_sql = f'UPDATE `DC_HotArticleWordsWeight` SET weight = {num}, {filed} = {max_weight} where word = ' \
                         f'"{keyword}"'
                session.execute(up_sql)

                log.info(f"up_sql: {up_sql}")

            else:
                df = {'word': keyword,
                      filed: max_weight,
                      'weight': max_weight}

                dch = DCHotArticleWordsWeight(**df)
                session.add(dch)
        session.commit()
        session.close()
        log.info('处理成功')
    except:
        if session:
            session.close()

        log.error(f'处理失败，失败原因：【{traceback.format_exc()}】')


init_keyword()
