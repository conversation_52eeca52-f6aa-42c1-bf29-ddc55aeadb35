# -*- coding:utf-8 -*-
# @Function  : 舆情订阅 文章热度衰减
# @Time      : 2023/11/09
# Version    : 1.0
import time
from datetime import datetime, timedelta
import os
import sys
import traceback
import requests
from bs4 import BeautifulSoup
import nltk  #用于自然语言处理
from elasticsearch import Elasticsearch, helpers

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))))
from dc.services.logging_service import LoggingService
from dc.services.elasticsearch_service import ElasticsearchService

logger = LoggingService('dc_article_check.log')
es = ElasticsearchService()


def jaccard_similarity(s1, s2):
    lev_distance = nltk.edit_distance(s1, s2)
    return 1 - (lev_distance / max(len(s1), len(s2)))


now = datetime.now()
start_of_time = now - timedelta(hours=2)
end_of_time = now - timedelta(hours=8)
# 将起始时间转换为字符串
start_of_time = start_of_time.strftime("%Y-%m-%d %H:%M:%S")
end_of_time = end_of_time.strftime("%Y-%m-%d %H:%M:%S")
es_client = es.get_connect()
step = 500
for i in range(0, 20):
    start = i * step
    query = {
        "_source": ["title", "hotWeight", "analysis_time", "dc_detail_url", "is_repeat", "type", "url"],
        "query": {
            "bool": {
                "filter": {
                    "range": {
                        "analysis_time": {
                            "gte": end_of_time,
                            "lte": start_of_time
                        }
                    }
                }
            }
        },
        "sort": {
            "analysis_time": {
                "order": "desc"
            }
        },
        "from": start,
        "size": step
    }
    search_res = es_client.search(index="dc_hotarticle", body=query)
    search_info = search_res['hits']['hits']
    if not search_info:
        break

    es_ids = []
    for info in search_info:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        }
        
        url = info['_source']['dc_detail_url'] if info['_source']['type'] != 2 else info['_source']['url']
        try:
            r = requests.get(info['_source']['dc_detail_url'], headers=headers, verify=False, timeout=(3.05, 27))
            status_code = r.status_code
            if status_code != 200:
                continue

            soup = BeautifulSoup(r.content, 'lxml')
            if info['_source']['title'] in soup.title.text or soup.title.text in info['_source']['title']:
                continue

            similarity = jaccard_similarity(info['_source']['title'], soup.title.text)
            if similarity < 0.3:
                es_ids.append({"_id": info['_id'], "is_repeat": 5})
            time.sleep(1)
        except:
            continue

        # if info['_source']['title'] not in soup.title.text and soup.title.text not in info['_source']['title']:
        # if info['_source']['title'] not in r_text:
        #     es_ids.append(info['_id'])

    if es_ids:
        actions = []
        # 构建要更新的文档操作列表
        for doc in es_ids:
            action = {
                '_op_type': 'update',
                '_index': 'dc_hotarticle',
                '_id': doc['_id'],
                'doc': {
                    'is_repeat': 5
                }
            }
            actions.append(action)

        # 发送请求
        helpers.bulk(es_client, actions=actions)
