# -*-coding:utf-8-*-
import logging
import sys
import os
import threading
import uuid
import traceback
import time
from typing import List

from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService

path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(path)

from dc.models.model import DCSiteTask, DCSiteMainTask, DCSiteList
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.dc_web_analyzer_service import AnalyzerService
from dc.common.dc_web_analyzer_util import AnalyzerUtil
from dc.common.dc_web_analyzer_work import AnalyzerWork

ks = K8sService()

class AnalyzerTask:
    """
    网页分析任务脚本
    """
    MAIN_TASK_KEY = "laravel_database_dc-main-task"  # redis读取key
    SITE_TASK_KEY = "laravel_database_dc-site-task"  # redis写入key
    site_list_id_set = set()  # 如果多个任务site_id相同，则需要阻塞线程(加锁)
    N = 4  # 开启线程并发的数量
    FAIL_TRY = 3  # 失败后重新尝试次数
    logger = LoggingService(logfile='dc_web_analyzer.log')

    def __init__(self):
        self.redis_service = RedisService()
        self.mysql_service = MySQLService()
        self.session = None
        self.pool = None
    def do(self):
        web_driver = None
        main_task = None
        uid = uuid.uuid4().hex  # 日志唯一id标注
        site_list_id = -1
        main_task_id = -1
        try:
            self.pool = self.redis_service.get_client_pool()
            if self.pool.llen(AnalyzerTask.MAIN_TASK_KEY) == 0:
                return
            main_task_id = self.pool.rpop(
                AnalyzerTask.MAIN_TASK_KEY)
            if not main_task_id:
                AnalyzerTask.log("no main_task_id in redis", uid)
                return
            try:
                main_task_id = int(main_task_id)
            except:
                AnalyzerTask.log("main_task_id illegal", uid)
                return
            self.session = self.mysql_service.create_session()
            main_task = self.get_DCSiteMainTask_by_id(main_task_id)
            if not main_task:
                AnalyzerTask.log("not found main_task", uid)
                return
            site_list_id = main_task.siteListId
            site_list = self.get_DCSiteList_by_id(site_list_id)
            if not site_list:
                AnalyzerTask.log("not found site_list", uid)
                return
            cnt = 0
            while site_list_id in AnalyzerTask.site_list_id_set:  # 如果当前有相同的site_id正在处理，则阻塞当前线程
                if cnt > 128:  # 如果长时间阻塞，则直接关闭该线程
                    return
                cnt += 1
                AnalyzerUtil.random_sleep(2, 3)
            self.session.commit()  # 刷新数据表
            AnalyzerTask.site_list_id_set.add(site_list_id)  # 加锁
            AnalyzerTask.log("start main_task_id:%d site_id:%d task" % (main_task_id, site_list_id), uid)
            old_site_task = self.get_DCSiteTasks_by_siteListId(site_list_id, main_task.keyWord)  # 获取历史数据
            # 爬取数据
            worker = AnalyzerWork(dc_site_list=site_list,
                                  dc_site_main_task=main_task,
                                  old_dc_site_tasks=old_site_task)
            web_driver = worker.driver
            worker.work()
            # 数据倒置
            site_task_list = worker.crawl_data[::-1]
            self.add_DCSiteTasks(site_task_list)
            # 完成
            main_task.httpStatusCode = 200
            main_task.listUrlCount = len(site_task_list)
            self.session.commit()
            for site_task in site_task_list:
                self.pool.lpush(
                    AnalyzerTask.SITE_TASK_KEY, site_task.id)
            AnalyzerTask.log("the main_task_id:%d site_id:%d task is successful. the length of new data is %f " % (
            main_task_id, site_list_id, len(site_task_list)), uid)
            self.session.close()
            AnalyzerTask.site_list_id_set.remove(site_list_id)  # 解锁
        except Exception:
            if site_list_id in AnalyzerTask.site_list_id_set:
                AnalyzerTask.site_list_id_set.remove(site_list_id)
            try:
                if web_driver:
                    web_driver.quit()
            except:
                pass
            try:
                if self.session:
                    self.session.close()
                if self.pool:
                    self.pool.close()
            except:
                pass
            try:
                if main_task:
                    main_task.httpStatusCode = 300
                    main_task.listUrlCount = 0
                    self.session.commit()
            except:
                pass
            AnalyzerTask.log("the task is failed ", uid)
            flag = False
            for cnt in range(AnalyzerTask.FAIL_TRY):
                if self.fail_try(site_list_id, uid, cnt):
                    flag = True
                    break
            if not flag:
                tb = traceback.format_exc()
                AnalyzerTask.fail_log("the main_task_id:%d site_id:%d task is failed after 3 times " % (
                    main_task_id, site_list_id) + tb, uid)
        finally:
            try:
                if web_driver:
                    web_driver.quit()
            except:
                pass
            try:
                if self.session:
                    self.session.close()
                if self.pool:
                    self.pool.close()
            except:
                pass

    def fail_try(self, main_task_id, uid, try_cnt):
        web_driver = None
        main_task = None
        site_list_id = -1
        try:
            self.pool = self.redis_service.get_client_pool()
            self.session = self.mysql_service.create_session()
            main_task = self.get_DCSiteMainTask_by_id(main_task_id)
            if not main_task:
                AnalyzerTask.log("not found main_task", uid)
                return
            site_list_id = main_task.siteListId
            site_list = self.get_DCSiteList_by_id(site_list_id)
            if not site_list:
                AnalyzerTask.log("not found site_list", uid)
                return
            cnt = 0
            while site_list_id in AnalyzerTask.site_list_id_set:  # 如果当前有相同的site_id正在处理，则阻塞当前线程
                if cnt > 128:  # 如果长时间阻塞，则直接关闭该线程
                    return
                cnt += 1
                AnalyzerUtil.random_sleep(2, 3)
            self.session.commit()  # 刷新数据表
            AnalyzerTask.site_list_id_set.add(site_list_id)  # 加锁
            AnalyzerTask.log("start main_task_id:%d site_id:%d task" % (main_task_id, site_list_id), uid)
            old_site_task = self.get_DCSiteTasks_by_siteListId(site_list_id, main_task.keyWord)  # 获取历史数据
            # 爬取数据
            worker = AnalyzerWork(dc_site_list=site_list,
                                  dc_site_main_task=main_task,
                                  old_dc_site_tasks=old_site_task)
            web_driver = worker.driver
            worker.work()
            # 数据倒置
            site_task_list = worker.crawl_data[::-1]
            self.add_DCSiteTasks(site_task_list)
            # 完成
            main_task.httpStatusCode = 200
            main_task.listUrlCount = len(site_task_list)
            self.session.commit()
            for site_task in site_task_list:
                self.pool.lpush(
                    AnalyzerTask.SITE_TASK_KEY, site_task.id)
            AnalyzerTask.log("the main_task_id:%d site_id:%d task is successful. the length of new data is %f " % (
            main_task_id, site_list_id, len(site_task_list)), uid)
            self.session.close()
            AnalyzerTask.site_list_id_set.remove(site_list_id)  # 解锁
            return True
        except Exception:
            if site_list_id in AnalyzerTask.site_list_id_set:
                AnalyzerTask.site_list_id_set.remove(site_list_id)
            try:
                if web_driver:
                    web_driver.quit()
            except:
                pass
            try:
                if self.session:
                    self.session.close()
                if self.pool:
                    self.pool.close()
            except:
                pass
            try:
                if main_task:
                    main_task.httpStatusCode = 300
                    main_task.listUrlCount = 0
                    self.session.commit()
            except:
                pass
            AnalyzerTask.log(f"the task is failed and try {try_cnt + 1} times ", uid)
            return False
        finally:
            try:
                if web_driver:
                    web_driver.quit()
            except:
                pass
            try:
                if self.session:
                    self.session.close()
                if self.pool:
                    self.pool.close()
            except:
                pass

    @staticmethod
    def log(msg: str, uid: str):
        # info = "[" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time())) + " uid:" + uid + "] "
        # with open(path + '/logs/dc_web_analyzer_task_out.log', 'a') as f:
        #     f.write(info + msg + "\n")
        AnalyzerTask.logger.info(f"{msg},{uid}")

    @staticmethod
    def fail_log(msg: str, uid: str):
        # info = "[" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time())) + " uid:" + uid + "] "
        # with open(path + '/logs/dc_web_analyzer_task_fail.log', 'a') as f:
        #     f.write(info + msg + "\n")
        AnalyzerTask.logger.error(f"{msg},{uid}")

    def get_DCSiteMainTask_by_id(self, id: int) -> DCSiteMainTask:
        """
        根据id获取主任务
        :param id:
        :return:
        """
        ret = self.session.query(DCSiteMainTask).filter(
            DCSiteMainTask.id == id).first()
        return ret

    def get_DCSiteTasks_by_siteListId(self, siteListId: int, keyword: str = "") -> List[DCSiteTask]:
        """
        根据siteListId获取历史的所有相关子任务(用于去重)
        :param keyword: 关键词
        :param siteListId: list id
        :return:
        """
        if not keyword:
            ret = self.session.query(DCSiteTask).filter(DCSiteTask.siteListId == siteListId).all()
        else:
            ret = self.session.query(DCSiteTask).filter(DCSiteTask.siteListId == siteListId,
                                                        DCSiteTask.keyword == keyword).all()
        return ret

    def add_DCSiteTasks(self, tasks: list[DCSiteTask]) -> None:
        """
        添加一个新的子任务，并推送到redis中
        :param task:
        :return:
        """
        self.session.add_all(tasks)

    def get_DCSiteList_by_id(self, id: int) -> DCSiteList:
        """
        根据id获取sitelist
        :param id: sitelist的id
        :return:
        """
        ret = self.session.query(DCSiteList).filter(DCSiteList.isUsed == 1, DCSiteList.status == 1,
                                                    DCSiteList.id == id).first()
        return ret

    def filter_links(self, site_id: int, new_site_tasks: list[DCSiteTask]) -> list[DCSiteTask]:
        """
        将新爬取到的数据与数据库中的数据比对，根据url，清除已经有的数据
        :param site_id: 当前子任务的site_id
        :param new_site_tasks: 新爬取到的数据
        :return:
        """
        old_site_task = self.get_DCSiteTasks_by_siteListId(site_id)
        old_url_set = set()
        for element in old_site_task:
            old_url_set.add(element.url)
        res = []
        for element in new_site_tasks:
            if element.url not in old_url_set:
                res.append(element)
        return res

    def __del__(self):
        # try:
        #     self.mysql_session.close()
        # except:
        #     pass
        try:
            self.pool.quit()
            self.pool.close()
        except:
            pass


class AnalyzerThread(threading.Thread):
    """
    多线程
    """

    def __init__(self, threadName):
        super(AnalyzerThread, self).__init__(name=threadName)

    def run(self):
        task = AnalyzerTask()
        while 1:
            ks.liveness()   # 探针

            AnalyzerUtil.random_sleep(2, 4)
            task.do()
            AnalyzerUtil.random_sleep(2, 4)


if __name__ == "__main__":
    # 开启N个线程
    for i in range(AnalyzerTask.N):
        AnalyzerThread("AnalyzerThread:" + str(i)).start()
