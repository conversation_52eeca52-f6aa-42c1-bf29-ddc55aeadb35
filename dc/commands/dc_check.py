# -*- coding:utf-8 -*-
# @Function  : 功能检测
# <AUTHOR> wjh
# @Time      : 2024-11-13
# Version    : 1.0

import json
import random
import time
import traceback

from sqlalchemy import text

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhInfo
from dc.services.beanstalk_service import BeanstalkService
from dc.services.gzh_service import GzhService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService

logger = LoggingService('dc_check.log')
mysql = MySQLService()


def handle():
    logger.info('检测程序...')

    while True:

        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        try:
            logger.info(f'randdom: {prefix}')
            time.sleep(60)

        except Exception as ex:
            logger.error(f"{prefix} pull exception:" + traceback.format_exc())


handle()
