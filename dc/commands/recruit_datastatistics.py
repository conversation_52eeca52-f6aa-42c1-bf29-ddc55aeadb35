# -*- coding:utf-8 -*-
# @Function  : 数据采集-招聘数据统计
# <AUTHOR> lws
# @Time      : 2022-10-19
# Version    : 1.0

import datetime
import json
import os
import re
import sys
import time
from dateutil.relativedelta import relativedelta
from dc.services.mysql_service import MySQLService
from dc.services.logging_service import LoggingService
from dc.services.redis_service import RedisService
from dc.models.model import MPCompanyRecruit, MPCompanyRecruitStatistics
from dc.commands.recruit_utils import *

mysql = MySQLService('mysql_company')
logger = LoggingService('recruit_datastatistics.log')
redisService = RedisService('redis')

redis_statistics_key = f'company_recruit_statistics_lists'


def handel():

    while True:

        r_client = None
        session = None

        try:
            r_client = redisService.get_client()
            company_id = r_client.rpop(redis_statistics_key)
            if not company_id:
                time.sleep(5)
                logger.debug('睡 5 秒')
                continue

            last_year = (datetime.datetime.now() + relativedelta(years=-1)).strftime("%Y%m%d%H%M%S")
            session = mysql.create_session()
            ret2 = session.query(MPCompanyRecruit).filter(MPCompanyRecruit.companyId == company_id,
                                                          MPCompanyRecruit.refreshTime >= last_year).all()
            if not ret2:
                session.close()
                continue

            list2 = []
            recruiterNum = 0
            salaryNum = 0
            salarySum = 0
            cityDistribution = {}
            jobDistribution = {}
            experienceDistribution = {}
            educationDistribution = {}
            salaryDistribution = {}
            for recruitInfo in ret2:
                tmp1 = recruitInfo.to_dict()
                if tmp1['jobId'] in list2:
                    continue

                list2.append(tmp1['jobId'])
                recruiterNum += 1

                if tmp1['salary']:
                    #  算平均薪资
                    salaryArr = re.search("\\d{1,3}-\\d{1,3}", tmp1['salary'])
                    salaryDis = 0
                    if salaryArr:
                        salaryArr1 = salaryArr[0].split('-')
                        salaryStart = int(salaryArr1[0])
                        salaryEnd = int(salaryArr1[1])

                        if re.search("天|日", tmp1['salary']):
                            salarySum += ((salaryStart + salaryEnd) / 2 * 21 / 1000)
                            salaryDis = salaryStart * 21 / 1000
                        elif re.search("月", tmp1['salary']):
                            salarySum += ((salaryStart + salaryEnd) / 2 / 1000)
                            salaryDis = salaryStart / 1000
                        else:
                            salarySum += ((salaryStart + salaryEnd) / 2)
                            salaryDis = salaryStart
                        salaryNum += 1

                    salaryArr2 = re.search("\\d{1,5}元/[天|日]", tmp1['salary'])
                    if salaryArr2:
                        salaryArr3 = re.search("\\d{1,5}", tmp1['salary'])
                        salarySum += (int(salaryArr3[0]) * 21 / 1000)
                        salaryDis = int(salaryArr3[0]) * 21 / 1000
                        salaryNum += 1

                    salaryArr4 = re.search("\\d{1,8}元/月", tmp1['salary'])
                    if salaryArr4:
                        salaryArr5 = re.search("\\d{1,8}", tmp1['salary'])
                        salarySum += (int(salaryArr5[0]) / 1000)
                        salaryDis = int(salaryArr5[0]) / 1000
                        salaryNum += 1

                    #  计算薪资分布
                    if tmp1['salary'] in ['面议', '薪资面议']:
                        if '面议' in salaryDistribution:
                            salaryDistribution['面议'] += 1
                        else:
                            salaryDistribution['面议'] = 1

                    if salaryDis < 3:
                        if '3k以下' in salaryDistribution:
                            salaryDistribution['3k以下'] += 1
                        else:
                            salaryDistribution['3k以下'] = 1

                    if 3 <= salaryDis < 5:
                        if '3-5k' in salaryDistribution:
                            salaryDistribution['3-5k'] += 1
                        else:
                            salaryDistribution['3-5k'] = 1

                    if 5 <= salaryDis < 10:
                        if '5-10k' in salaryDistribution:
                            salaryDistribution['5-10k'] += 1
                        else:
                            salaryDistribution['5-10k'] = 1

                    if 10 <= salaryDis < 20:
                        if '10-20k' in salaryDistribution:
                            salaryDistribution['10-20k'] += 1
                        else:
                            salaryDistribution['10-20k'] = 1

                    if 20 <= salaryDis < 40:
                        if '20-40k' in salaryDistribution:
                            salaryDistribution['20-40k'] += 1
                        else:
                            salaryDistribution['20-40k'] = 1

                    if 40 <= salaryDis < 60:
                        if '40-60k' in salaryDistribution:
                            salaryDistribution['40-60k'] += 1
                        else:
                            salaryDistribution['40-60k'] = 1

                    if 60 <= salaryDis:
                        if '60k以上' in salaryDistribution:
                            salaryDistribution['60k以上'] += 1
                        else:
                            salaryDistribution['60k以上'] = 1

                #  地区
                if tmp1['dq']:
                    dqArr1 = tmp1['dq'].split('-')
                    if dqArr1[0] in cityDistribution:
                        cityDistribution[dqArr1[0]] += 1
                    else:
                        cityDistribution[dqArr1[0]] = 1

                #  工作经验
                if tmp1['requireWorkYears']:
                    if tmp1['requireWorkYears'] in experienceDistribution:
                        experienceDistribution[tmp1['requireWorkYears']] += 1
                    else:
                        experienceDistribution[tmp1['requireWorkYears']] = 1

                #  岗位名称
                if tmp1['title']:
                    if tmp1['title'] in jobDistribution:
                        jobDistribution[tmp1['title']] += 1
                    else:
                        jobDistribution[tmp1['title']] = 1

                #  学历要求
                if tmp1['requireEduLevel']:
                    if tmp1['requireEduLevel'] in ['大专及以上', '大专', '中专/中技', '高中', '初中', '中专']:
                        if '大专及以下' in educationDistribution:
                            educationDistribution['大专及以下'] += 1
                        else:
                            educationDistribution['大专及以下'] = 1

                    if tmp1['requireEduLevel'] == '学历不限':
                        if '学历不限' in educationDistribution:
                            educationDistribution['学历不限'] += 1
                        else:
                            educationDistribution['学历不限'] = 1

                    if tmp1['requireEduLevel'] in ['本科', '统招本科', '本科及以上']:
                        if '本科' in educationDistribution:
                            educationDistribution['本科'] += 1
                        else:
                            educationDistribution['本科'] = 1

                    if tmp1['requireEduLevel'] in ['硕士', '硕士及以上']:
                        if '硕士' in educationDistribution:
                            educationDistribution['硕士'] += 1
                        else:
                            educationDistribution['硕士'] = 1

                    if tmp1['requireEduLevel'] == '博士':
                        if '博士' in educationDistribution:
                            educationDistribution['博士'] += 1
                        else:
                            educationDistribution['博士'] = 1

                    if tmp1['requireEduLevel'] == 'MBA/EMBA':
                        if 'MBA/EMBA' in educationDistribution:
                            educationDistribution['MBA/EMBA'] += 1
                        else:
                            educationDistribution['MBA/EMBA'] = 1

            info: MPCompanyRecruitStatistics = session.query(MPCompanyRecruitStatistics).filter(
                MPCompanyRecruitStatistics.companyId == company_id).first()
            if info:
                info.companyId = company_id
                info.recruiterNum = recruiterNum
                info.avgSalary = '%.2f' % (salarySum / salaryNum * 1000) if salarySum > 0 else salarySum * 1000
                info.jobDistribution = json.dumps(jobDistribution)
                info.salaryDistribution = json.dumps(salaryDistribution)
                info.educationDistribution = json.dumps(educationDistribution)
                info.experienceDistribution = json.dumps(experienceDistribution)
                info.cityDistribution = json.dumps(cityDistribution)
            else:
                df = {'companyId': company_id,
                      'recruiterNum': recruiterNum,
                      'avgSalary': '%.2f' % (salarySum / salaryNum * 1000) if salarySum > 0 else salarySum * 1000,
                      'jobDistribution': json.dumps(jobDistribution),
                      'salaryDistribution': json.dumps(salaryDistribution),
                      'educationDistribution': json.dumps(educationDistribution),
                      'experienceDistribution': json.dumps(experienceDistribution),
                      'cityDistribution': json.dumps(cityDistribution),
                      'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}
                info = MPCompanyRecruitStatistics(**df)

            session.add(info)
            session.commit()
            session.close()

        except Exception as ex:
            logger.error(f"exception: {str(ex)}")

        finally:
            if r_client:
                r_client.close()

            if session:
                session.close()

handel()
