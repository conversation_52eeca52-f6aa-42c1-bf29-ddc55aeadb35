# -*-coding:utf-8-*-
import re
import time
import sys
import os
import json
import string
import zipfile
import traceback
from selenium import webdriver
from selenium.webdriver import Keys, DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from urllib import request

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import MPCompanyInfo, MPCompanyRecruitOriginal, MPCompanyRecruitAlias, BossRecruit

"""
[功能]：数据采集-招聘数据采集
[作者]：lws
[日期]：2022-10-19
"""

mysql = MySQLService('mysql_company')
redisService = RedisService('redis')

# 代理服务器
proxyHost = "http-pro.abuyun.com"
proxyPort = "9010"

# 代理隧道验证信息
channel = [
    {'proxyUser': 'H586W877TXY2KY3P', 'proxyPass': '5B11CE1E1C7F2FF0'},
    {'proxyUser': 'H9FE43O59MXD3B2P', 'proxyPass': 'D4224AFA6DDC91E2'},
    {'proxyUser': 'HRT5DIT52XUM910P', 'proxyPass': 'D131E0D056CD3E0E'},
    {'proxyUser': 'H392HIU7812V155P', 'proxyPass': '34D41FBAC90B3D56'},
    {'proxyUser': 'H64Y0Z113J3U052P', 'proxyPass': 'BC8491E69515DA4D'},
    {'proxyUser': 'HTMW013NTR02OT0P', 'proxyPass': '082967F829F03E73'},
    {'proxyUser': 'H689XS56V267S97P', 'proxyPass': '86AEAE18851493CC'},
    {'proxyUser': 'H4H6X269FV842E3P', 'proxyPass': '7DCC64F4B209CF10'},
    {'proxyUser': 'H4777M95GS7236FP', 'proxyPass': '2194A21C15B15944'},
    {'proxyUser': 'H8D9U6S122IH17LP', 'proxyPass': 'FF8437617D914AD0'},
]


# 写入主任务队列
def handel():
    # session = mysql.create_session()
    # ret1 = session.query(MPCompanyInfo).filter(MPCompanyInfo.status == 1).all()

    # r_client = redisService.get_client()
    # print(r_client.llen('boss_recruit_company_list'))
    # exit()

    # for info in ret1:
    #     tmp = info.to_dict()
    #     company = tmp['name']
    #     companyId = tmp['id']
    #     print(companyId)
    #     print(company)
    #
    #     # ret2 = session.query(MPCompanyRecruitAlias).filter(MPCompanyRecruitAlias.companyId == companyId).first()
    #     # if ret2:
    #     #     ret2_arr = ret2.to_dict()
    #     #     company_alias = ret2_arr['companyAlias'].split(",")
    #     # else:
    #     #     company_alias = []
    #
    #     list1 = {"name": company, "id": companyId}
    #     r_client.lpush('boss_recruit_company_list', json.dumps(list1))
    #
    # exit()
    while True:
        r_client = redisService.get_client()
        redis_company_info = r_client.rpop("boss_recruit_company_list")
        if not redis_company_info:
            time.sleep(5)
            print('睡 5 秒')
            continue

        # 循环执行
        # r_client.lpush("boss_recruit_company_list", redis_company_info)

        company_info = json.loads(redis_company_info)
        company = company_info['name']
        companyId = company_info['id']
        if not company or not companyId:
            continue

        print(companyId)
        print(company)
        # company = "北京百度网讯科技有限公司"  # company_info['name']
        # companyId = 9999  # company_info['id']

        params = sys.argv[1:]
        process_id = int(params[0])

        # switchIp(process_id)
        # exit()

        # for i in range(1, 50):
        #     proxyMeta = "http://%(user)s:%(pass)s@%(host)s:%(port)s" % {
        #         "host": proxyHost,
        #         "port": proxyPort,
        #         "user": channel[process_id]['proxyUser'],
        #         "pass": channel[process_id]['proxyUser'],
        #     }
        #
        #     proxy_handler = request.ProxyHandler({
        #         "http": proxyMeta,
        #         "https": proxyMeta,
        #     })
        #
        #     opener = request.build_opener(proxy_handler)
        #
        #     opener.addheaders = [("Proxy-Switch-Ip", "yes")]
        #     request.install_opener(opener)
        #     resp = request.urlopen("http://proxy.abuyun.com/switch-ip").read()
        #
        #     print(resp)
        #
        # exit()

        proxy_auth_plugin_path = create_proxy_auth_extension(
            proxy_host=proxyHost,
            proxy_port=proxyPort,
            proxy_username=channel[process_id]['proxyUser'],
            proxy_password=channel[process_id]['proxyPass'])

        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_extension(proxy_auth_plugin_path)
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_experimental_option("excludeSwitches", ["--ignore-certificate-errors"])

        ca = DesiredCapabilities.CHROME
        ca["goog:loggingPrefs"] = {"performance": "ALL"}
        driver = webdriver.Chrome(options=chrome_options, desired_capabilities=ca)
        driver.implicitly_wait(10)
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
        })

        # for i in range(1, 100):
        #     time.sleep(2)
        #     print(i)
        #     driver.get("http://proxy.abuyun.com/switch-ip")
        #     print(driver.page_source)
        #
        #     # driver.get("https://2022.ip138.com/")
        #     # print(driver.page_source)
        #     time.sleep(2)
        #
        # driver.close()
        # exit()

        getFirstPage(driver, process_id)

        while True:
            try:
                input_obj = driver.find_element('xpath', '//input[@class="ipt-search"]')
                input_obj.send_keys(company)
                time.sleep(1)
                input_obj.send_keys(Keys.ENTER)
                driver.find_element('xpath', '//div[@class="job-search-wrapper"]')
                break
            except:
                getFirstPage(driver, process_id)

        time.sleep(3)

        for i in range(1, 11):
            logs = driver.get_log("performance")
            for log1 in logs:
                message = log1['message']
                msg_dict = json.loads(message)
                try:
                    req_id = msg_dict['message']['params']['requestId']
                    if msg_dict['message']['params']['request'] is None:
                        continue
                    if msg_dict['message']['params']['request']['url'] is None:
                        continue
                    u = msg_dict['message']['params']['request']['url']
                    arr = re.findall("https://www.zhipin.com/wapi/zpgeek/search/joblist.json", u)
                    if arr:
                        flag = 0
                        while flag < 10:
                            try:
                                print('req_id:' + req_id)
                                print('u:' + u)
                                # driver.find_element('xpath', '//div[@class="job-search-wrapper"]')
                                time.sleep(2)
                                content = driver.execute_cdp_cmd('Network.getResponseBody', {'requestId': req_id})
                                break
                            except:
                                print(flag)
                                flag += 1
                                # switchIp(process_id)
                        print(content)
                        print('==================================')
                        if 'body' not in content:
                            continue

                        info1 = json.loads(content['body'])
                        if 'zpData' not in info1:
                            continue
                        try:
                            session = mysql.create_session()
                            jobInfos = info1['zpData']['jobList']
                            for jobArr in jobInfos:
                                print('----------------------------------------')
                                print(jobArr)
                                print(jobArr['securityId'])
                                print('----------------------------------------')
                                df = {'securityId': jobArr['securityId'],
                                      'encryptBossId': jobArr['encryptBossId'],
                                      'bossName': jobArr['bossName'],
                                      'bossTitle': jobArr['bossTitle'],
                                      'encryptJobId': jobArr['encryptJobId'],
                                      'jobName': jobArr['jobName'],
                                      'lid': jobArr['lid'],
                                      'salaryDesc': jobArr['salaryDesc'],
                                      'jobLabels': ','.join(jobArr['jobLabels']),
                                      'jobValidStatus': jobArr['jobValidStatus'],
                                      'skills': ','.join(jobArr['skills']),
                                      'jobExperience': jobArr['jobExperience'],
                                      'jobDegree': jobArr['jobDegree'],
                                      'cityName': jobArr['cityName'],
                                      'areaDistrict': jobArr['areaDistrict'],
                                      'businessDistrict': jobArr['businessDistrict'],
                                      'jobType': int(jobArr['jobType']),
                                      'encryptBrandId': jobArr['encryptBrandId'],
                                      'brandName': jobArr['brandName'],
                                      'brandStageName': jobArr['brandStageName'],
                                      'brandIndustry': jobArr['brandIndustry'],
                                      'brandScaleName': jobArr['brandScaleName'],
                                      'welfareList': ','.join(jobArr['welfareList']),
                                      'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                                      'companyId': companyId}
                                dch = BossRecruit(**df)
                                session.add(dch)
                            session.commit()
                            session.close()
                        except:
                            print(f"{traceback.format_exc()}")
                except:
                    continue

            try:
                pageLinks = driver.find_elements('xpath', '//div[@class="options-pages"]/a')
                if not pageLinks:
                    getFirstPage(driver, process_id)
                    break

                for button in pageLinks:
                    if button.text == str(i + 1):
                        while True:
                            try:
                                time.sleep(1)
                                button.click()
                                driver.find_element('xpath', '//div[@class="job-search-wrapper"]')
                                break
                            except:
                                # switchIp(process_id)
                                pass
                        break
            except:
                driver.close()
                break

            time.sleep(3)
        #
        # if company_alias and not companyAlias:
        #     session = mysql.create_session()
        #     aliasArr: MPCompanyRecruitAlias = session.query(MPCompanyRecruitAlias).filter(MPCompanyRecruitAlias.companyId == companyId).first()
        #     if not aliasArr:
        #         df = {'companyId': companyId,
        #               'companyAlias': ",".join(company_alias),
        #               'name': company,
        #               'status': 1,
        #               'updateAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
        #               'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}
        #         info = MPCompanyRecruitAlias(**df)
        #         session.add(info)
        #         session.commit()
        #     session.close()

        driver.close()


def create_proxy_auth_extension(proxy_host, proxy_port,
                                proxy_username, proxy_password,
                                scheme='http', plugin_path=None):
    if plugin_path is None:
        plugin_path = r'D:/{}_{}@http-pro.abuyun.com_9010.zip'.format(proxy_username, proxy_password)

    manifest_json = """
    {
        "version": "1.0.0",
        "manifest_version": 2,
        "name": "Abuyun Proxy",
        "permissions": [
            "proxy",
            "tabs",
            "unlimitedStorage",
            "storage",
            "<all_urls>",
            "webRequest",
            "webRequestBlocking"
        ],
        "background": {
            "scripts": ["background.js"]
        },
        "minimum_chrome_version":"22.0.0"
    }
    """

    background_js = string.Template(
        """
        var config = {
            mode: "fixed_servers",
            rules: {
                singleProxy: {
                    scheme: "${scheme}",
                    host: "${host}",
                    port: parseInt(${port})
                },
                bypassList: ["foobar.com"]
            }
          };

        chrome.proxy.settings.set({value: config, scope: "regular"}, function() {});

        function callbackFn(details) {
            return {
                authCredentials: {
                    username: "${username}",
                    password: "${password}"
                }
            };
        }

        chrome.webRequest.onAuthRequired.addListener(
            callbackFn,
            {urls: ["<all_urls>"]},
            ['blocking']
        );
        """
    ).substitute(
        host=proxy_host,
        port=proxy_port,
        username=proxy_username,
        password=proxy_password,
        scheme=scheme,
    )

    with zipfile.ZipFile(plugin_path, 'w') as zp:
        zp.writestr("manifest.json", manifest_json)
        zp.writestr("background.js", background_js)

    return plugin_path


def getFirstPage(driver, process_id):
    while True:
        try:
            switchIp(process_id)
            driver.get("https://www.zhipin.com/?city=100010000&ka=city-sites-100010000")
            driver.find_element('xpath', '//input[@class="ipt-search"]')
            break
        except:
            switchIp(process_id)
            pass


def switchIp(process_id):
    proxyMeta = "http://%(user)s:%(pass)s@%(host)s:%(port)s" % {
        "host": proxyHost,
        "port": proxyPort,
        "user": channel[process_id]['proxyUser'],
        "pass": channel[process_id]['proxyPass'],
    }

    proxy_handler = request.ProxyHandler({
        "http": proxyMeta,
        "https": proxyMeta,
    })

    auth = request.HTTPBasicAuthHandler()
    opener = request.build_opener(proxy_handler, auth, request.HTTPHandler)

    opener.addheaders = [("Proxy-Switch-Ip", "yes")]
    request.install_opener(opener)
    resp = request.urlopen("http://proxy.abuyun.com/switch-ip").read()
    time.sleep(1)
    print(resp.decode('utf8'))


handel()
