import logging
import os
import sys
import time
from time import sleep

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService
from dc.models.model import AIWriteArticle, AIWriteSection, AIWriteReference
from dc.services.chatgpt_service import ChatGPTService
from dc.services.aigc_service import AIGCService

mysql = MySQLService()
redisService = RedisService('redis', db=0)
aigc_redis_key = 'laravel_database_aigc_task_list'
logger = LoggingService(logfile='aigc.log')

gptService = ChatGPTService()
aigcService = AIGCService()
part_total = 1500

gpt3_token_limit_dict = {
    "section_total_cut": 1800
}

gpt4_token_limit_dict = {
    "section_total_cut": 5500
}


def aigc_for_writing():
    session = mysql.create_session()
    # redis client
    redis_client = redisService.get_client()
    # pop  key aigc_redis_key from redis_client
    while True:
        try:
            session.flush()
            # 检查session的连接是否已经断开, 断开了就重新连接
            if not session.info:
                session = mysql.create_session()

            print(session.info)
            task_id = redis_client.rpop(aigc_redis_key)
            # 判断task_id 是否为空 如果为空 则继续pop
            if task_id is None:
                # 休息3秒
                print('no task :' + str(time.time()))

                sleep(3)
                continue

            # task_id = 25
            print('redis pop res :' + str(task_id))

            # 文章正文内容整合
            task_content = ''
            # get task info from mysql table AIWriteArticle
            task_info: AIWriteArticle = session.query(AIWriteArticle).filter(AIWriteArticle.id == task_id).first()
            if task_info.status != 2:
                logger.info("err : 文章状态不对", task_info.id)
                sleep(5)

                continue
            print(task_info.id)
            # get min task list from mysql table AIWriteSection
            section_list: [AIWriteSection] = session.query(AIWriteSection).filter(
                AIWriteSection.articleId == task_info.id).all()

            #  如果有需要处理的列表逻辑
            if len(section_list) == 0:
                logger.info('aigc_for_writing err: no section list')
                task_info.status = 5
                session.add(task_info)
                session.commit()
                continue

            for k, section in enumerate(section_list):
                section_content = ''
                # get section list from mysql table AIWriteReference
                reference_list: [AIWriteReference] = session.query(AIWriteReference) \
                    .filter(AIWriteReference.sectionId == section.id,
                            AIWriteReference.articleId == task_info.id).all()
                if len(reference_list) != 0:
                    # section result
                    section_content = reference_content_merge(reference_list, task_info)

                # section reference 拼接 section_content
                section_content = section.referenceText + "。" + section_content
                # # 根据section content 切割有效的长度
                # section_content = aigcService.cut_content_by_token_len(section_content, 1800)
                # 根据section result 生成section 的结果 并更新数据库
                # section.result = gptService.chat(generate_section_request_prompt(section_content, section),
                #                                 max_tokens=1900)
                section_content = aigcService.cut_content_by_token_len(section_content, 3500)
                if k + 1 == len(section_list):
                    section.result = gptService.chat4(
                        generate_section_request_prompt(section_content, section, task_info, True))
                else:
                    section.result = gptService.chat4(
                        generate_section_request_prompt(section_content, section, task_info))
                # 判断section result是否为空
                if section.result == "broken":
                    task_info.status = 5
                    session.add(task_info)
                    session.commit()

                    break
                # section.result = gptService.chat(generate_section_request_prompt(section_content, section))
                task_content += section.result

                # task_content_len = aigcService.token_len(task_content)
                # # 重新梳理内容
                # if task_content_len < 4000 :
                #     task_content = gptService.chat4(merge_section_prompt(task_content))
                session.add(section)

            if task_info.status == 5:
                continue
            # 根据文章正文内容生成摘要
            task_content = aigcService.cut_content_by_token_len(task_content, 3100)
            task_info.intro = fix_result(
                gptService.chat(generate_abstract_request_prompt(task_content, article=task_info), max_tokens=700))
            # 根据文章摘要内容生成标题
            task_info.title = fix_result(
                gptService.chat(generate_title_request_prompt(task_content, task_info), max_tokens=200))
            # 根据标题内容生成短标题
            task_info.subTitle = fix_result(
                gptService.chat(generate_abbreviation_request_prompt(task_info.title, article=task_info),
                                max_tokens=200))
            # 任务处理完成
            task_info.status = 3
            session.add(task_info)
            session.commit()
            print(str(task_info.id) + '处理完成')
            # 生成正文
        except Exception as e:
            logger.error('aigc_for_writing err:', e)
            task_info.status = 5
            try:
                session.add(task_info)
                session.commit()
            except Exception as e:
                redis_client.lpush(aigc_redis_key, str(task_info.id))




def reference_content_merge(reference_list: [AIWriteReference], task_info: AIWriteArticle):
    """
    gpt处理并合并一个列表内的内容
    """
    # 初始化处理完成的字符串
    ref_part = 1500 / len(reference_list)
    content = ''
    for reference in reference_list:
        print(reference.id)
        if reference.content == '':
            continue
        # 使用gpt接口处理内容
        reference.content = aigcService.cut_content_by_token_len(reference.content, 1800)
        request_prompt = generate_request_prompt(reference.content, task_info)
        response = gptService.chat(request_prompt, max_tokens=1800)
        content += response
    # 返回处理完成的字符串
    return content


def generate_request_prompt(content: str, task_info: AIWriteArticle):
    """
    生成接口请求的内容
    """
    string = content + ", 提取上述内容中的相关 '" + task_info.subject + "' 的内容"
    return string


def generate_title_request_prompt(content, article: AIWriteArticle):
    """
    生成标题prompt
    """
    string = content + ", 总结上述内容, 生成标题,最多200个字"
    # article.language = 2 用英文输出
    if article.language == 2:
        string += ", 用英文返回"
    else:
        string += ", 用中文返回"
    return string


def generate_abstract_request_prompt(content, article: AIWriteArticle):
    """
    生成摘要prompt
    """
    string = content + ", 总结上述内容, 生成摘要 最多250个字"
    # article.language = 2 用英文输出
    if article.language == 2:
        string += ", 用英文返回"
    else:
        string += ", 用中文返回"
    return string


def generate_abbreviation_request_prompt(content, article: AIWriteArticle):
    """
    生成缩写prompt
    """
    string = content + ", 总结上述内容, 生成缩写 ,最多保留50个字以内"
    # article.language = 2 用英文输出
    if article.language == 2:
        string += ", 用英文返回"
    else:
        string += ", 用中文返回"
    return string


def generate_section_request_prompt(content, section: AIWriteSection, article: AIWriteArticle, summarize=False):
    """
    生成section prompt
    """
    # string = "你是一名资深编辑 需要写作当前的文章章节,文章的主题是:" + article.subject + "根据写作要求:" + section.ask + " 来撰写章节,  章节的提纲是" \
    #          + section.outline + ",参考内容为:'" + content + "', 字数控制在" + str(
    #     section.characterNumMin) + "-" + str(
    #     section.characterNumMax) + "字"

    string = "你是一名资深编辑 需要写作当前的这篇新闻章节,新闻的主题是:" + article.subject

    if article.sideType == 3:
        string += ", 以负面消极的角度来写作"
    if section.ask != '':
        string += ", 根据写作要求:" + section.ask + " 来撰写内容, "
    if section.outline != '':
        string += ", 本章节的提纲是" + section.outline
    if content != "。":
        string += ",参考内容为: \'" + content + "\' "

    string += ", 字数控制在" + str(
        section.characterNumMin) + "-" + str(
        section.characterNumMax) + "字"
    # if article.sideType == 3:
    #     string += ", 需要有非正常"
    string += ", 用新闻稿的方式  不要段落标题、不要说明"
    if not summarize:
        string += "、no summarize"
    # article.language = 2 用英文输出
    if article.language == 2:
        string += ", 用英文返回"
    else:
        string += ", 用中文返回"

    return string


def merge_section_prompt(task_content: str):
    """
    合并章节prompt
    """
    string = "你是一名资深编辑 重新梳理一下当前的内容,让它成为一个完整的新闻稿 ,内容为:'" + task_content + "'"
    return string


def fix_result(content):
    arr = content.split("：")
    if len(arr) > 1:
        result = " ".join(arr[1:])
    else:
        result = " ".join(arr[0:])

    return result


aigc_for_writing()
