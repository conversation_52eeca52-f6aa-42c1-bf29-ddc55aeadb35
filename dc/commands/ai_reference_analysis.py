# -*-coding:utf-8-*-
import time
import sys
import os
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from urllib.parse import urlparse

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService
from dc.services.aigc_service import AIGCService
from dc.services.dc_service import DcService
from dc.models.model import AIWriteReference, AIWriteArticle

"""
[功能]：撰稿材料解析
[作者]：lws
[日期]：2023-05-12
"""

mysql = MySQLService()
redisService = RedisService('redis')
logService = LoggingService('ai_reference_analysis.log')

redis_prefix = 'laravel_database_'
redis_reference_list = f'{redis_prefix}reference_list'
redis_aigc_task_list = f'{redis_prefix}aigc_task_list'


# 解析文章
def handel():
    while True:
        client1 = redisService.get_client()
        info = client1.rpop(redis_reference_list)
        client1.close()
        if not info:
            logService.dcPullLog('未获取到需要分析数据，休眠5秒')
            time.sleep(5)
            continue

        referenceInfo = json.loads(info)

        # 确认一个任务执行完成
        if 'taskId' in referenceInfo:
            dealTaskStatus(referenceInfo['taskId'])
            continue

        if referenceInfo['status'] != 0:
            continue

        #  附件或音频
        if referenceInfo['type'] == 1:
            analysisFile(referenceInfo)
        else:  # 网页链接
            analysisWebpage(referenceInfo)


# 附件解析
def analysisFile(referenceInfo):
    content = ''
    status = 2
    aiServer = AIGCService()
    extension = referenceInfo['address'].split('.')[-1]
    if extension in ['doc', 'docx', 'xls', 'xlt', 'xlsx', 'xlsm', 'ppt', 'pptx', 'pdf', 'excel']:
        try:
            content = aiServer.extract_text(referenceInfo['address'])
            status = 1
        except:
            logService.error(f"文本参考资料【{referenceInfo['id']}】处理异常")

    if extension in ['mp3', 'm4a', 'wav', 'wma', 'mp2', 'flac', 'midi', 'ra', 'ape', 'aac', 'cda', 'mov']:
        try:
            content = aiServer.speech_to_text(referenceInfo['address'])
            status = 1
        except:
            logService.error(f"音频参考资料【{referenceInfo['id']}】处理异常")

    analysisResult(referenceInfo['id'], content, status)


# 解析网页
def analysisWebpage(referenceInfo):
    res = urlparse(referenceInfo['address'])
    netloc = res.netloc
    if netloc == 'mp.weixin.qq.com':
        dcSer = DcService()
        content = dcSer.get_weixin_content(url=referenceInfo['address'])
        status = 1 if content else 2
    else:
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-gpu')
        driver = webdriver.Chrome(options=chrome_options)
        driver.implicitly_wait(10)
        try:
            driver.get(referenceInfo['address'])
            if netloc == 'www.laoyaoba.com':
                content = driver.find_element('tag name', 'article').text
            else:
                content = driver.find_element('xpath', '/html').text
            status = 1
        except:
            status = 2
            content = ''
            logService.error(f"网页参考资料【{referenceInfo['id']}】处理异常")

    analysisResult(referenceInfo['id'], content, status)


# 处理主任务完成情况
def dealTaskStatus(taskId):
    session = mysql.create_session()
    while True:
        referenceInfo = session.query(AIWriteReference).filter(AIWriteReference.articleId == taskId,
                                                               AIWriteReference.status == 0).first()
        if not referenceInfo:
            client1 = redisService.get_client()
            client1.lpush(redis_aigc_task_list, taskId)
            logService.dcPullLog(f'解析完成，文章id【{taskId}】,插入队列成功')

            # aiArticle: AIWriteArticle = session.query(AIWriteArticle).filter(AIWriteArticle.id == taskId).first()
            # aiArticle.status = 3
            # aiArticle.updatedAt = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            # session.add(aiArticle)
            # session.commit()

            session.close()
            client1.close()
            break

        time.sleep(5)


# 添加数据
def analysisResult(referenceId, content, status):
    session = mysql.create_session()
    try:
        aiReference: AIWriteReference = session.query(AIWriteReference).filter(
            AIWriteReference.id == referenceId).first()
        aiReference.status = status
        aiReference.updatedAt = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        aiReference.content = content.replace("\n", "").replace("\t", "").replace("\r", "").replace(" ", "")
        session.add(aiReference)
        session.commit()
    except:
        aiReference: AIWriteReference = session.query(AIWriteReference).filter(
            AIWriteReference.id == referenceId).first()
        aiReference.status = 2
        aiReference.updatedAt = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        session.add(aiReference)
        session.commit()
    finally:
        session.close()


handel()
