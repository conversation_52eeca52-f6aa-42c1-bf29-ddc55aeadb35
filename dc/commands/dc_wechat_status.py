"""
公众号采集-微信登录状态更新
:author wjh
:date 2022-12-21
"""

import os
import sys
import time
import traceback
from dc.services.gzh_service import GzhService
from dc.services.gzh_service_v2 import GzhServiceV2
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.common.utils import Utils

logger = LoggingService(logfile='dc_wechat_status.log')
ks = K8sService()

start_time = Utils.time_second()    # 开始时间
stop_time = start_time + 2 * 60 * 60   # 停止时间

time_sleep = 60
no = 0
while True:
    try:
        ks.liveness()   # 探针

        # stop_time
        current_time = Utils.time_second()
        logger.debug(f"[{no}] current_time: {current_time} stop_time: {stop_time}")
        if current_time > stop_time:
            logger.debug(f"[{no}] current_time: {current_time} stop_time: {stop_time} break")
            break

        # 业务处理
        logger.debug(f"[{no}] begin...")
        res = GzhServiceV2.check_wechat_by_config()
        logger.info("check wechat status:"+f'{res}')

        logger.debug(f"[{no}] end")
    except Exception as ex:
        logger.error(f"[{no}] exception:{str(ex)}")

    finally:
        no += 1
        logger.debug(f"[{no}] sleep:{time_sleep}")
        time.sleep(time_sleep)
