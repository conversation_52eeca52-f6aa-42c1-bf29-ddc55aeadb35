"""
从 mysql 读取公众号信息进行公众号关注操作
:author wjh
:date 2022-11-26
"""
import socket
import sys
import threading
import queue
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.models.model import DCWechatMachine


def scan():
    while not q.empty():
        params: dict = q.get()
        host = params.get('host')
        port = params.get('port')
        c = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        c.settimeout(0.5)
        if c.connect_ex((host, port)) == 0:
            print("%s:%d is open---" % (host, port))
            ip_list.append(params)
        else:
            pass
            # print("%s:%d is close" % (host, port))

        c.close()


def init_params(**kwargs):
    ranges = ['192.168.1.', '192.168.2.', '192.168.10.']
    for r in ranges:
        for ip in range(1, 254):
            host = f"{r}{ip}"
            port = 23230
            q.put({'host': host, 'port': port})


def update_machine_list(iplist=[]):
    if not iplist:
        return
    mysql_service = MySQLService()
    session = mysql_service.create_session()
    hosts = ["http://" + c.get('host') + ":23230/api" for c in iplist]
    session.execute("update DC_WechatMachine set status = 2 where machineIp not in ('%s')" % "', '".join(hosts))
    for value in hosts:
        h: DCWechatMachine = session.query(DCWechatMachine).where(DCWechatMachine.machineIp == value).first()
        if h is None:
            session.add(DCWechatMachine(machineIp=value, status=1))
        else:
            h.status = 1
            session.add(h)
    session.commit()
    session.close()


def test():
    ip_list = [{'host': '*************', 'port': 23230}, {'host': '*************', 'port': 23230},
               {'host': '*************', 'port': 23230}, {'host': '*************', 'port': 23230},
               {'host': '************', 'port': 23230}, {'host': '************', 'port': 23230},
               {'host': '************', 'port': 23230}, {'host': '************', 'port': 23230},
               {'host': '************', 'port': 23230}, {'host': '************', 'port': 23230},
               {'host': '*************', 'port': 23230}]
    update_machine_list(ip_list)


if __name__ == "__main__":
    # test()
    # exit()
    ip_list = []
    thread_num = 10
    q = queue.Queue()
    init_params()

    for i in range(int(thread_num)):
        t = threading.Thread(target=scan)
        t.start()
        t.join()  # 子线程全部运行完了结束进程，以免线程卡死

    print(ip_list)
    update_machine_list(ip_list)
