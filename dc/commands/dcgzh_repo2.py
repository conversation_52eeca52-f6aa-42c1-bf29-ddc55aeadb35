"""
根据关键字从微信搜索中搜索公众号，写入数据库
:author wjh
:date 2022-11-26
"""

import datetime
import json
import os
import sys
import time
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.gzh_service import GzhService
from dc.models.model import DCGzhArticle, DCGzhInfo, DCGzhTag, DCGzhWechat, DCGzhWechatRelation, DCGzhImport
from dc.services.mysql_service import MySQLService
from dc.services.logging_service import LoggingService
from dc.common.utils import Utils
from dc.services.weixin_service import WeixinService
from dctest.conf.settings import settings as test_settings, get_settings

# 参见
# https://www.showdoc.cc/1965672579479613

gzh_settings = get_settings('gzh')
base_url = gzh_settings.get('host')

# base_url = 'http://*************:23230/api'
base_url = 'http://*************:23230/api'
# base_url = 'http://*************:23230/api'
login_info = {}  # 当前登录信息

log = LoggingService(logfile='gzh_db.log')
log2 = LoggingService(logfile='gzh_db2.log')
wx = WeixinService()
mysql = MySQLService(name='mysql_wjh')

gzh_service = GzhService()

data = wx.req_login({"base_url": base_url, "desc": "获取登录信息"})
login_info: dict = data.get('result')[0] if data.get('status') == 200 else None
# print(login_info)
log.info(Utils.json_dumps_pretty(login_info))
wxid = login_info.get('wxid')
wxpid = login_info.get('wxpid')


def func_gzh(**kwargs):
    """
    公众号处理
    :param kwargs:
    :return:
    """
    log.info(kwargs)

    item: dict = kwargs.get('item')
    params: dict = kwargs.get('params')
    business_type = params.get('business_type')

    source = item.get('source')
    principalPart = source.get('title')
    jumpInfo = item['jumpInfo']
    keyword = params.get('keyword')
    gzhId = jumpInfo['userName']

    session = mysql.create_session()
    gzh = session.query(DCGzhImport).filter_by(gzhId=gzhId).first()
    if gzh is None:
        gzh = DCGzhImport()

    gzh.gzhId = gzhId
    gzh.gzhNo = jumpInfo['aliasName']
    gzh.nickName = jumpInfo['nickName']
    gzh.signature = jumpInfo['signature']
    gzh.content = json.dumps(item)
    gzh.principalPart = principalPart
    gzh.keyword = keyword
    gzh.businessType = 1
    gzh.createdAt = datetime.now()
    gzh.updatedAt = datetime.now()
    gzh.status = 1
    session.add(gzh)
    session.commit()

def func_article(**kwargs):
    """
    文章处理
    :param kwargs:
    :return:
    """
    log.info(kwargs)

    item: dict = kwargs.get('item')
    params: dict = kwargs.get('params')
    business_type = params.get('business_type')

    title = item.get('title')
    date = Utils.timestamp2str(item.get('date'))
    gzhId = item.get('srcUserName')
    desc = item.get('desc')
    doc_url = item.get('doc_url')
    source = item.get('source')
    nickName = source.get('title')
    keyword = params.get('keyword')

    log2.info([gzhId, nickName, title, doc_url])

    session = mysql.create_session()
    gzh = session.query(DCGzhImport).filter_by(gzhId=gzhId).first()
    if gzh is None:
        gzh = DCGzhImport()

    gzh.gzhId = gzhId
    # gzh.gzhNo = jumpInfo['aliasName']
    gzh.nickName = nickName
    # gzh.signature = jumpInfo['signature']
    # gzh.content = json.dumps(item)
    # gzh.principalPart = principalPart
    gzh.keyword = keyword
    gzh.businessType = 2
    gzh.createdAt = datetime.now()
    gzh.updatedAt = datetime.now()
    gzh.status = 1
    session.add(gzh)
    session.commit()


func = {
    1: func_gzh,
    2: func_article
}


def callback(**kwargs):
    log.info(kwargs)
    params: dict = kwargs.get('params')
    business_type = params.get('business_type')
    call = func.get(business_type)
    call(**kwargs)


def gzh_search(params):
    """
    微信搜索 公众号，文章...
    :author wjh
    :date 2022-11-10
    :return:
    """
    keyword = params.get('keyword')
    page_stop = params.get('page_stop', 10)
    business_type = params.get('business_type', 1)
    doc_sort_type = params.get('doc_sort_type', 1)
    gzhid = params.get('gzhid')
    wxpid = params.get('wxpid')
    prefix = params.get('prefix', '')

    time_stop = int(params.get('time_stop'))  # 数据截止时间，时间戳
    list_sleep = int(params.get('list_sleep'))  # 公众号历史文章列表，拉取间隔
    if list_sleep < 3:
        list_sleep = 10

    page: int = 1
    item_total: int = 0
    next_offset = None
    while True:

        log.info(
            f"\r\n {prefix}*********************************************************************************\r\n")
        log.info(f"{prefix}         page: {page}    next_offset: {next_offset}")
        if next_offset:
            dk = {
                "mode": 1000,
                "data": {
                    "keyword": keyword,
                    "BusinessType": business_type,
                    "docSortType": doc_sort_type,
                    "next_params": next_offset,
                    "wxpid": wxpid
                }
            }
        else:
            dk = {
                "mode": 1000,
                "data": {
                    "keyword": keyword,
                    "BusinessType": business_type,
                    "docSortType": doc_sort_type,
                    "wxpid": wxpid
                }
            }

        result = wx.req_call(dk, params)
        # self.log.info(result)
        # if result is None:
        #     self.log.info('返回内容为空，重新执行----->')
        #     raise Exception('返回内容为空--- 异常了')
        #     continue

        jj = json.loads(result)

        log.info("result:")
        # log.info(Utils.json_dumps_pretty(result))

        log.info(f'\r\n {prefix} +++++++++++++++++++++++++++++++++++++++')

        types = jj['result']['result']['data']
        if types is None:
            log.info(f"{prefix} {page} 最后一页，循环结束")
            break

        item_total_wx = None
        for type in types:
            item_total_wx = type['totalCount']
            item_count = type['count']
            item_total += type['count']
            log.info(f"page: {page} item_count:{item_count} ")
            log.info(f"item_total_wx:{item_total_wx} item_total: {item_total} ")
            # log.info(f"next_params: {jj['result']['next_params']}")
            items = type['items']
            for item in items:
                log.info(item)
                callback(item=item, params=params)

        # 记录数判断
        if item_total >= item_total_wx:
            log.info(f"{prefix} {page} 最后一页2，循环结束")
            break

        # next page
        next_offset = jj['result']['next_params']

        page = page + 1
        if page > page_stop:
            log.info(f"{prefix} page:{page} 达到最大页码要求 {page_stop}，循环结束")
            break

        time.sleep(list_sleep)


keywords = [
    # "发布",
    # "半导体",
    # "芯片",
    # "光刻",
    # "微电子",
    # "集成电路",
    # "晶圆",
    # "IC",
    # "半导体封测",
    "汽车芯片"

]


"""
“0”: “全部”,
“1”: “公众号”,
“2”: “文章”,
“7”: “视频号”,
“9”: “直播”,
“16384”: “新闻”,
“12582912”: “视频”,
“262208”: “小程序”,
“8192”: “微信指数”
"""

for keyword in keywords:
    params = {
        # 'gzhid': 1,
        'wxpid': wxpid,
        'keyword': keyword,
        'business_type': 2,
        'doc_sort_type': 2,
        'prefix': '',
        'time_stop': 2,
        'page_stop': 10000,
        'list_sleep': 10,
        "base_url": base_url,
    }

    gzh_search(params)
