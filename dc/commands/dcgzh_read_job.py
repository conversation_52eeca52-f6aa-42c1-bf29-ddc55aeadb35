"""
微信公众号采集-公众号阅读数量
2022-10-19
"""

import json
import random
import time
import traceback

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhArticle
from dc.services.beanstalk_service import BeanstalkService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.services.weixin_service import WeixinService

logger = LoggingService('dc_gzh_read_job.log')
mysql = MySQLService()
wx = WeixinService(logger=logger)
bs = BeanstalkService()
beanstalk = bs.get_client(use=DC_GZH_READ, watch=[DC_GZH_READ])


def handle():
    """
    公众号阅读数量
    :return:
    """
    logger.info('启动公众号-阅读数量 服务...')
    settings = get_settings('weixin_gzh')
    read_sleep = settings.get('read_sleep', 2)
    read_update_days = settings.get('read_update_days', 3)
    read_update_interval = settings.get('read_update_interval', 1)

    session = None
    prefix = None

    while True:

        rand = random.randint(100000, 999999)
        prefix = f"[{rand}] "

        wx_create_time = Utils.time_days(read_update_days, only_day=True)
        readAt = Utils.time_days(read_update_interval)

        try:

            dt = Utils.showTime2()
            logger.info(f'{prefix} ============================================' + dt)
            # logger.info(f'{prefix} settings {settings}')

            # beanstalk reserve
            job = beanstalk.reserve()
            logger.info(f"{prefix} beanstalk reserve: {job.body}")
            stats = beanstalk.stats_job(job)
            logger.info(f"{prefix} beanstalk stats: {stats}")

            job_data = json.loads(job.body)
            article_id = job_data['id']

            logger.info(f"{prefix} article: {article_id}")

            session = mysql.create_session()
            article: DCGzhArticle = session.query(DCGzhArticle).get(article_id)
            if article is None:
                logger.info(f"{prefix} article_id: {article_id} 不存在数据，continue")

                # beanstalk
                beanstalk.delete(job)
                logger.info(f"beanstalk delete: {article_id}")
                continue

            wechat = wx.get_wechat_by_gzh(article.gzhId)
            wxpid = wechat.machinePid if wechat and wechat.machinePid else None

            if wxpid is None:
                logger.info(f"{prefix} wxpid is empty [{article.id}]")
                article.readAt = Utils.showDateTime()
                article.readNum = None
                article.praiseNum = None
                article.readResult = 'wxpid is empty'
                article.readStatus = 2
                logger.info(f"{prefix} wxpid is empty，{article.id}")

            else:
                # 阅读数量
                yds = wx.req_gzh_yds({
                    "wxpid": wxpid,
                    "base_url": wechat.machineIp,
                    "url": article.url,
                    "desc": "文章阅读数"
                })

                article.readAt = Utils.showDateTime()
                article.readNum = Utils.dict_get(yds, ['result', 'read_num'], 0) if yds.get('status') == 200 else None
                article.praiseNum = Utils.dict_get(yds, ['result', 'old_like_num'], 0) if yds.get('status') == 200 else None
                article.readStatus = 1 if yds.get('status') == 200 else 2
                logger.info(f"{prefix} 获取文章阅读数，{yds}")

            session.add(article)
            session.commit()
            session.close()
            time.sleep(read_sleep)

            # beanstalk
            beanstalk.delete(job)
            logger.info(f"beanstalk delete: {job}")

        except Exception as ex:
            logger.error(f"{prefix} exception:" + traceback.format_exc())

            # beanstalk release
            pri = int(stats['pri']) + 1
            beanstalk.release(job, pri)
            logger.info(f"{prefix} beanstalk release: {job.id}")

            logger.info(f"{prefix} exception sleep: {read_sleep}")
            time.sleep(read_sleep)

        finally:
            if session is not None:
                session.close()


handle()
