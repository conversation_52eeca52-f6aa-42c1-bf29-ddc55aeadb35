"""
微信公众号采集-公众号阅读数量 加入队列
2022-10-19
"""

import json
import random
import time
import traceback

from sqlalchemy import text

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.models.model import DCGzhArticle
from dc.services.beanstalk_service import BeanstalkService
from dc.services.k8s_service import K8sService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.services.weixin_service_v2 import WeixinServiceV2

logger = LoggingService('dc_gzh_analyse.log')
mysql = MySQLService()
wx = WeixinServiceV2(logger=logger)
bs = BeanstalkService()
ks = K8sService()

except_sleep = 10


def handle():
    """
    公众号历史文章队列
    :return:
    """
    logger.info('启动公众号-历史文章队列 服务...')
    settings = get_settings('weixin_gzh')

    session = None
    prefix = None

    while True:

        ks.liveness()   # 探针
        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        try:

            beanstalk = bs.get_client(use=DC_GZH_ANALYSE, watch=[DC_GZH_ANALYSE])
            session = mysql.create_session()
            sql_filter = f"status=1 and isAnalysis=0 and analysisQueue=0"
            data: list[DCGzhArticle] = session.query(DCGzhArticle).where(text(sql_filter)).order_by(DCGzhArticle.id).limit(20).all()
            if len(data) == 0:
                logger.debug(f"{prefix} 不存在数据，continue")
                time.sleep(10)
                continue

            for article in data:
                logger.info(f"{prefix} article update: {article.id}")
                article.analysisQueue = 1
                session.add(article)
                session.commit()

                # beanstalk
                logger.info(f'{prefix} beanstalk put: {article.id}')
                beanstalk.put(body=json.dumps({'id': article.id, 'createdAt': Utils.showDateTime()}), ttr=120)
                time.sleep(0.1)

            session.commit()
            session.close()

        except Exception as ex:
            logger.error(f"{prefix} pull exception:" + traceback.format_exc())
            time.sleep(except_sleep)

        finally:
            if session is not None:
                session.close()


handle()
