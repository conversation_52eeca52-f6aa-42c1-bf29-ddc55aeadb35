# -*- coding:utf-8 -*-
# @Function  : 数据采集-文章分析服务
# <AUTHOR> lws
# @Time      : 2023-07-17
# Version    : 1.0

import re
import time
import sys
import os
import json
import traceback
import requests
from bson import ObjectId
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from sqlalchemy.orm import load_only
from datetime import datetime

from dc.common.qiniu_utils import calculate_md5, url_path_join
from dc.conf.settings import get_settings
from dc.services.k8s_service import K8sService
from dc.services.mongo_service import MongoService
from dc.services.mysql_service import MySQLService
from dc.services.qiniu_service import QiniuService
from dc.services.redis_service import RedisService
from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.logging_service import LoggingService
from dc.models.model import MPCompanyAntitrust, MPCompanyInfo
from dc.services.check_analysis import CheckAnalysis
from dc.conf.defines import *
from dc.services.kafka_service import KafkaService
from dc.services.content_filter_service import ContentFilterService
from dc.common.config_util import ConfigUtil
from dc.common.dc_util import check_trade_control, check_attachment
from dc.common.webdriver_util import get_remote_webdriver
from dc.conf.settings import get_settings2
from dc.tests.browser import Browser

mysql = MySQLService()
mgs = MongoService()
ess = ElasticsearchService()
redisService = RedisService('redis')
client1 = redisService.get_client()

redisService1 = RedisService('redis', db=6)
client2 = redisService1.get_client()

logger = LoggingService('dc_task_analysis.log')
logger2 = LoggingService('dc_analysis_ai.log')
klogger = LoggingService('dc_opinion_task_kafka.log', formatter='string')
kafka = KafkaService()
ks = K8sService()

redis_prefix = 'laravel_database_'
site_list_hash = f'{redis_prefix}dc-site-list-hash'
redis_analyse_key = f'{redis_prefix}dc-site-task-analyse'
redis_weight_list = 'dc_article_weight'
save_path = "/uploads/chanquan/"


# 写入主任务队列
def handel():
    while True:
        info = client1.lpop(redis_analyse_key)
        ks.liveness()

        if not info:
            print('无数据，休眠10秒')
            time.sleep(10)
            continue

        analyse_length = redisService.get_length(redis_analyse_key)
        logger.info(f"{redis_analyse_key} length: {analyse_length}")
        logger.info(f"redis lpop: {redis_analyse_key} content: {info}")

        # task_info = {
        #     "id": 11566655,
        #     "siteListId": 785,
        #     "mainTaskId": 4533616,
        #     "baseUrl": "http://newssearch.chinadaily.com.cn",
        #     "url": "https://cn.chinadaily.com.cn/a/202410/28/WS671ef293a310b59111da022d.html",
        #     "title": "菜鲜蟹肥 市场旺！订单式种植助农户增收",
        #     "taskStatus": 1,
        #     "taskAt": "2024-10-28 10:35:13",
        #     "analysisStatus": 0,
        #     "httpStatusCode": 200,
        #     "mongoCollection": "",
        #     "mongoKey": "671ef8611d1eedc9ca0ca118",
        #     "retryTime": 1,
        #     "createdAt": "2024-10-28 10:35:00",
        #     "updatedAt": "2024-10-28 10:35:13",
        #     "esIndex": "dc_yq",
        #     "esId": "",
        #     "ruleId": "null",
        #     "publicTime": "null",
        #     "coverImg": "",
        #     "keyword": "车",
        #     "detailDcMethod": 2
        # }

        task_info = json.loads(info)
        task_id = task_info['id']

        if task_info['taskStatus'] != 1:
            up_site_task('', '', task_id, 2)
            logger.info(f"子任务id: {task_id} 分析状态异常")
            continue

        try:
            m_client = mgs.get_collection('site-task')
            mongo_info = m_client.find_one({'_id': ObjectId(task_info['mongoKey'])})
        except Exception as ex:
            up_site_task('', '', task_id, 2)
            logger.info(f"子任务id: {task_id} mongo链接异常，对应mongoKey: {task_info['mongoKey']} exception: {str(ex)}")
            continue

        if not mongo_info or not mongo_info['body']:
            up_site_task('', '', task_id, 2)
            logger.info(f"子任务id: {task_id} mongo数据异常，对应mongoKey: {task_info['mongoKey']}")
            continue

        if mongo_info['code'] != 200:
            up_site_task('', '', task_id, 2)
            logger.info(f"子任务id: {task_id} mongo数据异常,状态码非200，对应mongoKey: {task_info['mongoKey']}")
            continue

        analysis(task_info, mongo_info)


# 获取页面响应数据
def analysis(task_info, mongo_info):
    site_id = task_info['siteListId']
    task_id = task_info['id']
    url_type = task_info['urlType']

    driver = None

    site_info_redis = client1.hmget(site_list_hash, task_info['siteListId'])
    if not site_info_redis:
        up_site_task('', '', task_id, 2)
        logger.info(f"子任务: {task_info['id']} 对应数据源: {task_info['siteListId']} 缓存数据异常，解析失败")
        return False

    # site_info = {
    #     "sourceCategory": "dc_yq",
    #     "siteName": "快科技",
    #     "analysisMethod": 0,
    #     "isQuickShort": 2,
    #     "isKeyWord": 2,
    #     "detailDcMethod": 2,
    #     "sourceName": "快科技-最新",
    #     "ruleItem": "{\"2064\":{\"8011\":{\"id\":8011, \"siteListId\":954, \"siteRuleId\":2064, \"columnTitle\":\"标题\", \"columnKey\":\"title\", \"crawlRuleType\":1, \"crawlRule\":\"//*[@id=\\\"thread_subject\\\"]\", \"startFlag\":\"\", \"endFlag\":\"\", \"columnRegex\":\"\", \"columnDefault\":\"\", \"status\":1, \"createdAt\":\"2024-03-11 15:52:43\", \"updatedAt\":\"2024-03-11 15:59:10\", \"analysisType\":1, \"path\":\"\", \"pathLevel\":0}, \"8012\":{\"id\":8012, \"siteListId\":954, \"siteRuleId\":2064, \"columnTitle\":\"发布日期\", \"columnKey\":\"public_time\", \"crawlRuleType\":1, \"crawlRule\":\"//div[@class=\\\"news_bt1_left\\\"]\", \"startFlag\":\"\", \"endFlag\":\"\", \"columnRegex\":\"\\\\d{4}-\\\\d{1,2}-\\\\d{1,2}\\\\s\\\\d{2}:\\\\d{2}:\\\\d{2}\", \"columnDefault\":\"\", \"status\":1, \"createdAt\":\"2024-03-11 15:52:43\", \"updatedAt\":\"2024-03-11 15:59:10\", \"analysisType\":1, \"path\":\"\", \"pathLevel\":0}, \"8013\":{\"id\":8013, \"siteListId\":954, \"siteRuleId\":2064, \"columnTitle\":\"正文\", \"columnKey\":\"text\", \"crawlRuleType\":1, \"crawlRule\":\"//div[@class=\\\"news_info\\\"]\", \"startFlag\":\"\", \"endFlag\":\"\", \"columnRegex\":\"\", \"columnDefault\":\"\", \"status\":1, \"createdAt\":\"2024-03-11 15:52:43\", \"updatedAt\":\"2024-03-11 15:59:10\", \"analysisType\":1, \"path\":\"\", \"pathLevel\":0}}}",
    #     "isHotModule": 2,
    #     "tags": ""
    # }

    site_info = json.loads(site_info_redis[0])
    if not site_info:
        up_site_task('', '', task_id, 2)
        logger.info(f"子任务: {task_info['id']} 对应数据源: {task_info['siteListId']} 缓存数据异常，解析失败")
        return False

    logger.info(f"redis hmget {site_list_hash} {task_info['siteListId']} content: {json.dumps(site_info)}")

    # 判断是否贸易管制
    is_trade_control = check_trade_control(site_info['sourceCategory'])
    is_attachment = check_attachment(site_info['sourceCategory'])

    if url_type == 1:   # 页面采集
        if site_info['detailDcMethod'] == 2:    # 详情采集方式 高级模式（浏览器模式）
            ym = time.strftime('%Y%m', time.localtime(time.time())) + "/"
            # 火狐或谷歌模式
            try:
                webdriver_url = get_settings2('webdriver', 'host', None)
                if site_info['analysisMethod'] == 1:
                    # opt = webdriver.FirefoxOptions()
                    # opt.add_argument("--headless")
                    # # opt.add_argument('--disable-gpu')
                    # driver = webdriver.Firefox(options=opt)
                    driver = get_remote_webdriver(webdriver_url=webdriver_url, browser=Browser.Firefox)
                else:
                    # chrome_options = Options()
                    # chrome_options.add_argument('--headless')
                    # chrome_options.add_argument('--no-sandbox')
                    # chrome_options.add_argument('--disable-gpu')
                    prefs = {'profile.default_content_settings.popups': 0,
                             'download.default_directory': "/data" + save_path + ym}
                    # prefs = {'profile.default_content_settings.popups': 0,
                    #          'download.default_directory': "D:\\"}
                    # chrome_options.add_experimental_option('prefs', prefs)
                    # driver = webdriver.Chrome(options=chrome_options)
                    driver = get_remote_webdriver(webdriver_url=webdriver_url, browser=Browser.Chrome, options={
                        'experimental_options': {'prefs': prefs}
                    })

                driver.implicitly_wait(10)
                driver.get(task_info['url'])

            except Exception as ex:
                up_site_task('', '', task_id, 2)
                logger.error(f"任务: {task_info['id']} 打开网页失败, 失败原因：{str(ex)}")
                return False

            # 快照
            if site_info['isQuickShort'] == 1:
                quick_short(driver, site_info['analysisMethod'], task_info, ym)

            # 解析对应数据
            analysis_ret = CheckAnalysis.browser_analysis(driver, task_info, site_info, mongo_info['body'])
            logger.info(f"browser_analysis: {json.dumps({'task_info': task_info,'site_info': site_info})} result: {json.dumps(analysis_ret)}")

        else:   # 详情采集方式（普通模式）
            # 解析对应数据
            analysis_ret = CheckAnalysis.ordinary_analysis(site_info['ruleItem'], mongo_info['body'], task_info)
            logger.info(f"ordinary_analysis: {json.dumps({'site_info_ruleItem': site_info['ruleItem'], 'task_info': task_info})} result: {json.dumps(analysis_ret)}")

    else:   # 详情采集方式（附件模式）
        if is_attachment:
            analysis_ret = CheckAnalysis.attachment_analysis(site_info['ruleItem'], mongo_info['body'], task_info)
            logger.info(f"attachment_analysis: {json.dumps({'site_info_ruleItem': site_info['ruleItem'], 'task_info': task_info})} result: {json.dumps(analysis_ret)}")

    if analysis_ret['analysis_status'] == 2:
        up_site_task('', '', task_id, 2)
        logger.info(f"子任务: {task_id} 解析数据失败  status: {analysis_ret['analysis_status']}")
        return False

    # 附件处理
    analysis_ret['info']['url_type'] = url_type
    if not is_attachment:
        analysis_ret['info'].pop('files_v2', None)
        analysis_ret['info'].pop('files_v2_hrefs', None)

    # 如果是编辑发稿的话,需要先检查文章是否有关键词内容, 如果没有 直接可以退出
    cf = ContentFilterService()
    if site_info['sourceCategory'] == "dc_bianjifagao":

        # 如果是巨潮网络的 过滤关键词
        if site_info['sourceName'] == "巨潮资讯-深沪京公告":
            if not check_content_have_words_expression(analysis_ret['info']['text']):
                up_site_task('', site_info['sourceCategory'], task_id, 1, analysis_ret['rule_id'])
                return False
        else:
            if not cf.word_filter(cf.words, analysis_ret['info']['text']):
                # 退出前更新状态
                up_site_task('', site_info['sourceCategory'], task_id, 1, analysis_ret['rule_id'])
                return False

    # print(analysis_ret)
    es_id = save_es(analysis_ret['info'], analysis_ret['rule_id'], task_info, site_info)
    if es_id:
        analysis_status = analysis_ret['analysis_status']
        rule_id = analysis_ret['rule_id']
        up_site_task(es_id, site_info['sourceCategory'], task_id, analysis_status, rule_id)
    else:
        up_site_task('', '', task_id, 2)
        logger.info(f"子任务: {task_id} 写入es失败")

    # 处理反垄断数据
    if site_id in [377, 378, 379, 380]:
        saveCompanyAntitrust(analysis_ret['info'], es_id, site_info, task_info, driver)

    if driver:
        driver.quit()


def up_site_task(es_id, es_index, task_id, analysis_status=0, rule_id=0):
    """ 更新任务状态 """
    try:
        session = mysql.create_session()
        up_sql = f"update DC_SiteTask set esIndex = '{es_index}', ruleId = {rule_id}, esId = '{es_id}', " \
                 f"analysisStatus = {analysis_status} where id = {task_id}"
        session.execute(up_sql)
        session.commit()
        session.close()
    except Exception as ex:
        logger.error(f"子任务: {task_id} 更新执行状态失败，失败原因：{str(ex)}")


# 生成快照
def quick_short(driver, isFirefox, task_info, ym):
    pic_name = save_path + ym + CheckAnalysis.getMd5(task_info['url'])
    # pic_name = "a.png"
    local_file = "/data" + pic_name + ".png"

    # qiniu
    qs = QiniuService()
    md5 = calculate_md5(task_info['url'])
    image_save_path = url_path_join(qs.config["document_name"], "screenshot", md5, ".png")
    if isFirefox:
        # local_file = "/data" + pic_name + ".png"
        driver.get_screenshot_as_file(local_file)

        # qiniu
        ret, info = qs.put_file(local_file, image_save_path)
        if info and info.status_code == 200:
            logger.info(f"快照上传七牛云成功 {info} {info}")
        else:
            logger.error(f"快照上传七牛云失败 {info} {info}")
    else:
        # driver.set_window_size(width=1005, height=500, windowHandle='current')
        # 处理保存快照,返回网页的高度的js代码
        js_height = "return document.body.clientHeight"
        try:
            k = 1
            height = driver.execute_script(js_height)
            while True:
                if k * 500 < height:
                    js_move = "window.scrollTo(0,{})".format(k * 500)
                    driver.execute_script(js_move)
                    time.sleep(0.2)
                    height = driver.execute_script(js_height)
                    k += 1
                else:
                    break

            scroll_width = driver.execute_script('return document.body.parentNode.scrollWidth')
            scroll_height = driver.execute_script('return document.body.parentNode.scrollHeight')
            driver.set_window_size(scroll_width, scroll_height)
            # local_file = "/data" + pic_name + ".png"
            driver.get_screenshot_as_file(local_file)
            # driver.get_screenshot_as_file("D:\\a.png")

            # qiniu
            ret, info = qs.put_file(local_file, image_save_path)
            if info and info.status_code == 200:
                logger.info(f"快照上传七牛云成功 {info} {info}")
            else:
                logger.error(f"快照上传七牛云失败 {info} {info}")

        except Exception as ex:
            logger.error(f"子任务: {task_info['id']} 生成快照失败, 失败原因：{str(ex)}")


# 反垄断数据
def saveCompanyAntitrust(info, esId, site_info, task_info, driver):
    mysql1 = MySQLService('mysql_company')
    session1 = mysql1.create_session()
    if info['siteListId'] == 377:
        text1 = info['text']
        arr = text1.split("/n")
        for tmp1 in arr:
            arr1 = tmp1.split("\n")
            if len(arr1) < 2:
                arr1 = tmp1.split()
                if len(arr1) < 2:
                    continue

            if arr1[1] == "案件名称":
                continue

            deal_arr = dealConclusionDate(arr1[3], info['pinjieyong'])
            df = {'title': arr1[1],
                  'source': "反垄断局",
                  'pubTime': "",
                  'conclusionDate': deal_arr[0].strip(),
                  'url': info['dc_detail_url'],
                  'companyInfo': arr1[2],
                  'isUnconditional': 1,
                  'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                  'esId': esId,
                  'year': deal_arr[1],
                  'month': deal_arr[2],
                  'esIndex': site_info['sourceCategory']}
            dch = MPCompanyAntitrust(**df)
            session1.add(dch)
        session1.commit()
    elif info['siteListId'] == 378:
        # chrome_options1 = Options()
        # chrome_options1.add_argument('--headless')
        # chrome_options1.add_argument('--no-sandbox')
        # chrome_options1.add_argument('--disable-gpu')
        # driver3 = webdriver.Chrome(options=chrome_options1)

        info['dc_detail_url'] = f"{task_info['url']}"
        info['dc_site_name'] = site_info['siteName']
        info['mainTaskId'] = task_info['mainTaskId']
        info['siteListId'] = task_info['siteListId']
        info['taskId'] = task_info['id']
        info['analysis_time'] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        info['version'] = 2

        # driver.get(task_info['url'])
        xpath = driver.find_elements('xpath', "//div[@class='zt_xilan_07']//table//tr[1]/following-sibling::tr")
        # driver.implicitly_wait(10)

        for x in xpath:
            td1 = x.find_element('xpath', './td[1]')
            if td1.text == '序号':
                continue
            td2 = x.find_element('xpath', './td[2]')
            td3 = x.find_element('xpath', './td[3]')
            td4 = x.find_element('xpath', './td[4]')
            conclusionDate2 = td4.text.strip()

            info['title'] = td2.text
            info['content'] = td3.text
            info['public_time'] = CheckAnalysis.deal_format_date(conclusionDate2)[0]

            es = ess.get_connect()
            res = es.index(index=site_info['sourceCategory'], body=info)
            es.close()

            df = {'title': td2.text,
                  'source': "反垄断执法二司",
                  'pubTime': "",
                  'conclusionDate': conclusionDate2,
                  'url': task_info['url'],
                  'companyInfo': td3.text,
                  'isUnconditional': 1,
                  'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                  'esId': res['_id'] if res['_id'] else '',
                  'year': conclusionDate2[0:4],
                  'month': conclusionDate2[5:].split('月')[0].strip('0'),
                  'esIndex': site_info['sourceCategory']}
            dch = MPCompanyAntitrust(**df)
            session1.add(dch)
        session1.commit()
        # driver3.quit()
    else:
        arr_time = info['public_time'].split("-")
        df = {'title': info['title'],
              'source': "反垄断局" if info['siteListId'] == 379 else "反垄断执法二司",
              'pubTime': info['public_time'],
              'conclusionDate': "",
              'url': info['dc_detail_url'],
              'companyInfo': getCompanyName(info['content'], session1),
              'isUnconditional': 2,
              'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
              'esId': esId,
              'year': arr_time[0],
              'month': arr_time[1].strip("0"),
              'esIndex': site_info['sourceCategory']}
        dch = MPCompanyAntitrust(**df)
        session1.add(dch)
        session1.commit()
    session1.close()


# 获取企业名称
def getCompanyName(body, session1):
    # mysql1 = MySQLService('mysql_company')
    # session1 = mysql1.create_session()

    companyNames = client1.get("company_name_list")
    if companyNames:
        companyNames = json.loads(companyNames)
    else:
        companyInfos = session1.query(MPCompanyInfo).options(load_only(MPCompanyInfo.id, MPCompanyInfo.name)).all()
        for companyInfo in companyInfos:
            tmp = companyInfo.__dict__
            companyNames.append(tmp['name'])
        client1.set("company_name_list", json.dumps(companyNames), 86400)

    arr1 = []
    for companyName in companyNames:
        arr = re.search(companyName, body)
        if arr:
            arr1.append(arr[0])

    return '、'.join(arr1) if arr1 else ''


# 处理反垄断日期
def dealConclusionDate(date_str, year):
    date_str = date_str.strip()
    date_str = date_str.replace('年', '-')
    date_str = date_str.replace('月', '-')
    date_str = date_str.replace('日', '')

    try:
        if datetime.strptime(date_str, "%Y-%m-%d"):
            date_str1 = datetime.strptime(date_str, "%Y-%m-%d")
            format_date = datetime.strftime(date_str1, "%Y年%#m月%#d日")
            year1 = date_str1.strftime("%Y")
            month1 = date_str1.strftime("%#m")
    except:
        if datetime.strptime(date_str, "%m-%d"):
            date_str1 = datetime.strptime(date_str, "%m-%d")
            format_date1 = datetime.strftime(date_str1, "%#m月%#d日")
            if re.search("\\d{4}", year):
                arr = re.search("\\d{4}", year)
                format_date = str(arr[0]) + "年" + format_date1
                year1 = arr[0]
                month1 = date_str1.strftime("%#m")
        else:
            format_date = '1970年1月1日'
            year1 = 1970
            month1 = 1

    return [format_date, year1, month1]


def delivery_report(err, msg):
    try:
        if err is not None:
            # print("Message delivery failed: {}".format(err))
            klogger.error("Message delivery failed: {}".format(err))
        else:
            data = {
                'topic': msg.topic(),
                'partition': msg.partition(),
                'offset': msg.offset(),
                'key': msg.key(),
                'value': msg.value().decode('utf-8'),
                'timestamp': msg.timestamp(),
            }
            topic = msg.topic()
            item: dict = json.loads(data['value'])
            esid = item.get(KAFKA_TOPIC_ESID_KDY)
            sourceCategory = item.get('_sourceCategory', '')
            klogger.info(f"kafka topic: {topic} esid: {esid} sourceCategory:{sourceCategory}")
    except Exception as ex:
        print(f"delivery_report: {traceback.format_exc()}")


# 保存数据到es
def save_es(info, rule_id, task_info, site_info):
    if site_info.get('tags') is not None:
        tags = site_info.get('tags').split(',')
    else:
        tags = ''

    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    info['tags'] = tags
    info['dc_detail_url'] = f"{task_info['url']}"
    info['dc_site_name'] = f"{site_info['siteName']}"
    info['mainTaskId'] = task_info['mainTaskId']
    info['siteListId'] = task_info['siteListId']
    info['taskId'] = task_info['id']
    info['analysis_time'] = current_time
    info['version'] = 2
    info['ruleId'] = rule_id
    info['publicTime'] = info['publicTime'] if 'publicTime' in info else ''
    info['coverImg'] = info['coverImg'] if 'coverImg' in info else ''
    # info['search_keyword'] = [task_info['keyword']] if task_info['keyword'] else []
    info.setdefault('createdAt', current_time)  # 增加创建时间
    info.setdefault('title', task_info['title'])  # 如果详情没有标题（附件），使用列表标题
    if site_info['sourceCategory'] == "dc_policy":
        info['area'] = getPolicyInfo(info['title'] + site_info['siteName'], 7, 'ner')
        info['industry'] = getPolicyInfo(info['title'] + info['text'], 6, 'ner')
        info['informationtype'] = getInformationType(info['title']) if getInformationType(info['title']) != '其他' else ''

    if site_info['sourceCategory'] == 'dc_yq':
        info['isRepeat'] = 0

    if site_info['sourceCategory'] == "dc_bianjifagao":
        info['isPublished'] = 0  # 发布状态
        info['isPass'] = 0  # pass
        info['isWaiting'] = 0  # 待定
        info['isDeal'] = 0  # 处理状态
        info['publishID'] = 0  # 发布出去的id
        info['createAt'] = info['analysis_time']
        info['content'] = info['text']
        info['_dc_type'] = 1
        # 删除 info字典 中的 text
        info.pop('text') if 'text' in info else None
        info['is_translated'] = 0  # 是否翻译完成

    if site_info['sourceCategory'] == "dc_tradecontrol":  # 贸易管制
        info['isPublished'] = 0  # 发布状态
        info['isPass'] = 0  # pass
        info['isWaiting'] = 0  # 待定
        info['isDeal'] = 0  # 处理状态
        info['is_translated'] = 0  # 是否翻译完成

    try:
        es = ess.get_connect()
        is_exists = es.indices.exists(index=site_info['sourceCategory'])
        if not is_exists:
            res = es.index(index=site_info['sourceCategory'], body=info)
            es.close()

            # kafka 数据变更队列
            es_id = res['_id']
            p = kafka.get_producer()
            p.produce(KAFKA_TOPIC_DC_ALL,
                      json.dumps({KAFKA_TOPIC_ESID_KDY: es_id, "_sourceCategory": site_info['sourceCategory'], **info}),
                      key=es_id, callback=delivery_report)
            p.flush()

            client2.lpush(redis_weight_list, json.dumps({'taskId': task_info['id'], 'type': 1}))
            client2.close()
            logger.info(f"redis lpush {redis_weight_list} content: {json.dumps({'taskId': task_info['id'], 'type': 1})}")

            return res['_id']

        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "match": {
                                "siteListId": task_info['siteListId']
                            }
                        },
                        {
                            "match": {
                                "dc_detail_url.keyword": task_info['url']
                            }
                        }
                    ]
                }
            }
        }
        search_res = es.search(index=site_info['sourceCategory'], body=query)
        search_info = search_res['hits']['hits']
        if not search_info:
            res = es.index(index=site_info['sourceCategory'], body=info)
            es.close()

            # kafka 数据变更队列
            es_id = res['_id']
            p = kafka.get_producer()
            p.produce(KAFKA_TOPIC_DC_ALL,
                      json.dumps({KAFKA_TOPIC_ESID_KDY: es_id, "_sourceCategory": site_info['sourceCategory'], **info}),
                      key=es_id, callback=delivery_report)
            p.flush()

            client2.lpush(redis_weight_list, json.dumps({'taskId': task_info['id'], 'type': 1}))
            client2.close()
            logger.info(f"redis lpush {redis_weight_list} content: {json.dumps({'taskId': task_info['id'], 'type': 1})}")

            return res['_id']

        es_id = search_info[0]['_id']
        es_index = search_info[0]['_index']
        update = {
            "doc": info
        }
        es.update(index=es_index, id=es_id, body=update)
        es.close()

        # kafka 数据变更队列
        p = kafka.get_producer()
        p.produce(KAFKA_TOPIC_DC_ALL,
                  json.dumps({KAFKA_TOPIC_ESID_KDY: es_id, "_sourceCategory": site_info['sourceCategory'], **info}),
                  key=es_id, callback=delivery_report)
        p.flush()

        client2.lpush(redis_weight_list, json.dumps({'taskId': task_info['id'], 'type': 1}))
        client2.close()
        logger.info(f"redis lpush {redis_weight_list} content: {json.dumps({'taskId': task_info['id'], 'type': 1})}")

        return es_id

        # if task_info['keyword'] == '':
        # return es_id

        # keyword = search_info[0]['_source']['search_keyword'] if 'search_keyword' in search_info[0]['_source'] else []
        # keyword.append(task_info['keyword'])
        #
        # update = {
        #     "doc": {
        #         "search_keyword": keyword,
        #     }
        # }
        #
        # es.update(index=es_index, id=es_id, body=update)
        # return es_id
    except Exception as ex:
        logger.error(f"子任务: {task_info['id']} 写入es失败原因：{str(ex)}")
        return ''


# 获取区域、行业
def getPolicyInfo(content, model_id, type1):
    info = []
    params = {"model_id": model_id, "data": content, "type": type1}
    if model_id == 7:
        url = get_settings('getArea')
    elif model_id == 6:
        url = get_settings('getIndustry')

    try:
        res = requests.post(url, json=params)
        res.encoding = "UTF-8"
        if res.status_code != 200:
            logger2.dcPullLog("调用ai接口失败")
            return info

        arr = json.loads(res.text)['data']
        for item in arr:
            if item['type'] == 'ORG':
                info.append('全国')
            else:
                info.append(item['span'])

        return list(set(info))[0:3] if info else []
    except:
        logger2.dcPullLog("调用ai接口失败")
        return info


# 获取信息类别
def getInformationType(content):
    info = ''
    params = {"model_id": 4, "data": content, "type": "classification"}
    url = get_settings('getInformationType')
    try:
        res = requests.post(url, json=params)
        res.encoding = "UTF-8"
        if res.status_code != 200:
            logger2.dcPullLog("调用ai接口失败")
            return info

        info = json.loads(res.text)['data']

        return info
    except:
        logger2.dcPullLog("调用ai接口失败")
        return info


# 判断关键词过滤
def check_content_have_words(content: str):
    str = ConfigUtil.get_config_content('bianjifagao')
    data = str.split('\n')
    for word in data:
        if word in content:
            return True
    return False


def check_content_have_words_expression(content: str):
    expression = ConfigUtil.get_config_content('bianjifagao')
    words = expression.split('\n')
    for word in words:
        if ' and ' in word:
            and_words = word.split(' and ')
            if all(w in content for w in and_words):
                return True
        elif ' or ' in word:
            or_words = word.split(' or ')
            if any(w in content for w in or_words):
                return True
        else:
            if word in content:
                return True
    return False


handel()
