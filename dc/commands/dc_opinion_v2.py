"""
微信公众号采集-文章分析
2022-10-19
"""

import json
import random
import time
import traceback

import requests
from bs4 import BeautifulSoup
from pymongo import ReturnDocument

from dc.common.utils import Utils
from dc.conf.defines import *
from dc.conf.settings import get_settings
from dc.services.elasticsearch_service import ElasticsearchService
from dc.services.logging_service import LoggingService
from dc.services.mysql_service import MySQLService
from dc.services.kafka_service import KafkaService
from confluent_kafka import Consumer, Producer, KafkaException, Message, KafkaError, TopicPartition

logger = LoggingService('dc_opinion_v2.log', formatter='string')
klogger = LoggingService('dc_opinion_v2_debug.log', formatter='string')
mysql = MySQLService()

ess = ElasticsearchService()
es = ess.get_connect()

kafka = KafkaService()

wechat_index = 'dc_wechat'
yq_index = 'dc_yq'
yuqing_index = 'yuqing'


def error_cb(err):
    logger.error(f"Error: {err}")


def stats_cb(err):
    # logger.info(f"stats: {err}")
    pass


def on_commit(err, tps):  # type: (KafkaError, list(TopicPartition)) -> None
    # logger.info(f"on_commit: {err}")
    pass

def handle():
    """
    公众号-文章分析
    :return:
    """
    logger.info('启动公众号-文章分析服务...')
    settings = get_settings('weixin_gzh')

    session = None
    prefix = None
    # yq_max_time = '2024-01-08 00:00:00'
    yq_max_time = '2024-01-10 15:50:08'

    topics = [
        'dc_wechat',
        'dc_all',
        # 'dc_jiwei',
    ]

    consumer = kafka.get_consumer(extra={
        'group.id': 'consumer_group_opinion_v2',
        'auto.offset.reset': 'earliest',
        'error_cb': error_cb,
        'stats_cb': stats_cb,
        'on_commit': on_commit
    })

    consumer.subscribe(topics)

    num = 0


    while True:

        rand = random.randint(10000000, 99999999)
        prefix = f"[{rand}] "

        try:

            dt = Utils.showTime2()
            logger.info(f'{prefix} ============================================' + dt)

            msg: Message = consumer.poll(5.0)
            num += 1
            logger.info(f'num: {num}')

            if msg is None:
                continue
            if msg.error():
                logger.error("Consumer error: {}".format(msg.error()))
                continue

            # print("Received message: {}".format(msg.value().decode('utf-8')))
            data = {
                'topic': msg.topic(),
                'partition': msg.partition(),
                'offset': msg.offset(),
                'key': msg.key(),
                'value': msg.value().decode('utf-8'),
                'timestamp': msg.timestamp(),
            }
            topic = msg.topic()
            item: dict = json.loads(data['value'])
            esid = item.get(KAFKA_TOPIC_ESID_KDY)
            sourceCategory = item.get('_sourceCategory', '')
            loginSite = item.get('_loginSite', 0)  # 需要登录站点
            logger.info(f"{prefix} consumer: {data}")
            klogger.info(f"{prefix} topic: {data.get('topic')} esid: {esid} sourceCategory: {sourceCategory} loginSite:{loginSite}")

            if loginSite == 1:  # 登录站点不处理
                logger.info(f"{prefix} loginSite continue")
                continue

            yuqing_body = None
            if topic == KAFKA_TOPIC_DC_ALL and sourceCategory == 'dc_yq':   # dc_yq
                yuqing_body = {
                    "_dc_type": 1,
                    "public_time": item['public_time'],
                    # "gather_time": "2023-12-30 16:57:07",
                    # "insert_time": "2023-12-30 16:58:10",
                    "url": item['dc_detail_url'],
                    "type": 1,
                    "title": item['title'],
                    "content": item['text'],
                    # "first_page_url": "",
                    "media_type": 1,
                    "subname": item['dc_site_name'],
                    "site_name": item['dc_site_name'],
                    # "uid": "",
                    "author": "",
                    # "profile_image_url": "",
                    # "profile_url": "",
                    # "attention_cut": 0,
                    # "fans_cut": 0,
                    "reply_cnt": 0,
                    "read_cnt": 0,
                    "forward_cnt": 0,
                    # "abroad_sign": 0,
                    # "reply_user_cnt": 0,
                    # "like_cnt": 0,
                    # "region_type": 0,
                    # "industry_type": 0,
                    "language_type": 1,
                    "website_name": item['dc_site_name'],
                    # "domain_1": "chaoguba.com",
                    # "domain_2": "www.chaoguba.com",
                    # "side_type": 3,
                    # "verified": 0,
                    # "root_level_uid": "",
                    # "root_level_id": "",
                    # "root_level_user": "",
                    "self_type": 1 if item['dc_site_name'] == '集微网' else 0,
                    # "content_len": 1999,
                    # "num_pic": 3,
                    "image_url": [],
                    "words": [],
                    # "id": "96493E6BA46A928D96ECD580E928B316",
                    "abstract": item.get('abstract', None),
                    # "$setOnInsert": {
                    #     "__v": 0
                    # }
                    "esid": esid
                }

            elif topic == KAFKA_TOPIC_DC_WECHAT:    # wechat
                yuqing_body = {
                    "_dc_type": 2,  # wechat
                    "public_time": item['public_time'],
                    # "gather_time": "2023-12-30 16:57:07",
                    # "insert_time": "2023-12-30 16:58:10",
                    "url": item['url'],
                    "type": 1,
                    "title": item['title'],
                    "content": item['content'],
                    # "first_page_url": "",
                    "media_type": 1,
                    "subname": item['gzhNickName'],
                    "site_name": item['gzhNickName'],
                    # "uid": "",
                    "author": "",
                    # "profile_image_url": "",
                    # "profile_url": "",
                    # "attention_cut": 0,
                    # "fans_cut": 0,
                    "reply_cnt": 0,
                    "read_cnt": 0,
                    "forward_cnt": 0,
                    # "abroad_sign": 0,
                    # "reply_user_cnt": 0,
                    # "like_cnt": 0,
                    # "region_type": 0,
                    # "industry_type": 0,
                    "language_type": 1,
                    "website_name": item['gzhNickName'],
                    # "domain_1": "chaoguba.com",
                    # "domain_2": "www.chaoguba.com",
                    # "side_type": 3,
                    # "verified": 0,
                    # "root_level_uid": "",
                    # "root_level_id": "",
                    # "root_level_user": "",
                    "self_type": 0,
                    # "content_len": 1999,
                    # "num_pic": 3,
                    "image_url": [],
                    "words": [],
                    # "id": "96493E6BA46A928D96ECD580E928B316",
                    "abstract": item.get('abstract', None),
                    # "$setOnInsert": {
                    #     "__v": 0
                    # }
                    "esid": esid
                }
            elif topic == KAFKA_TOPIC_DC_JIWEI:     # 集微网
                yuqing_body = {
                    "_dc_type": 3,  # jiwei
                    "public_time": item['public_time'],
                    # "gather_time": "2023-12-30 16:57:07",
                    # "insert_time": "2023-12-30 16:58:10",
                    "url": item['dc_detail_url'],
                    "type": 1,
                    "title": item['title'],
                    "content": item['text'],
                    # "first_page_url": "",
                    "media_type": 1,
                    "subname": item['dc_site_name'],
                    "site_name": item['dc_site_name'],
                    # "uid": "",
                    "author": "",
                    # "profile_image_url": "",
                    # "profile_url": "",
                    # "attention_cut": 0,
                    # "fans_cut": 0,
                    "reply_cnt": 0,
                    "read_cnt": 0,
                    "forward_cnt": 0,
                    # "abroad_sign": 0,
                    # "reply_user_cnt": 0,
                    # "like_cnt": 0,
                    # "region_type": 0,
                    # "industry_type": 0,
                    "language_type": 1,
                    "website_name": item['dc_site_name'],
                    # "domain_1": "chaoguba.com",
                    # "domain_2": "www.chaoguba.com",
                    # "side_type": 3,
                    # "verified": 0,
                    # "root_level_uid": "",
                    # "root_level_id": "",
                    # "root_level_user": "",
                    "self_type": 1,
                    # "content_len": 1999,
                    # "num_pic": 3,
                    "image_url": [],
                    "words": [],
                    # "id": "96493E6BA46A928D96ECD580E928B316",
                    "abstract": item.get('abstract', None),
                    # "$setOnInsert": {
                    #     "__v": 0
                    # }
                    "esid": esid
                }

            if yuqing_body:     # 有需要处理数据

                yq_filter = {
                    "query": {
                        "terms": {
                            "esid": [esid]
                        }
                    },
                    'size': 1
                }

                # print(yuqing_body)
                logger.info(f"{prefix} es yuqing_body: {json.dumps(yuqing_body)}")
                result = ess.upsert(index=yuqing_index, filter=yq_filter, body=yuqing_body)
                logger.info(f"{prefix} es result: {json.dumps(result)}")

        except Exception as ex:
            logger.error(f"{prefix} exception:" + traceback.format_exc())

        finally:
            if session is not None:
                session.close()


handle()
