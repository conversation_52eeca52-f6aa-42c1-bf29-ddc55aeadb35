import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import os
import sys
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.logging_service import LoggingService

log_service = LoggingService('stock_market.log')
redisService = RedisService()
redis_client = redisService.get_client()
mysql1 = MySQLService()

stock_market_list = "stock_market_list"

log_service.dcPullLog('拉取股票行情信息')
chrome_options1 = Options()
chrome_options1.add_argument('--headless')
chrome_options1.add_argument('--no-sandbox')
chrome_options1.add_argument('--disable-gpu')
driver1 = webdriver.Chrome(options=chrome_options1)
driver1.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
    'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
})

while True:
    stock_code = redis_client.rpop(stock_market_list)
    if not stock_code:
        driver1.close()
        time.sleep(300)
        break

    log_service.dcPullLog(f"拉取股票信息：{stock_code}")
    while True:
        try:
            driver1.get(f"https://d.10jqka.com.cn/v2/realhead/hs_{stock_code}/last.js")
            info1 = driver1.find_element("xpath", "/html").text
            if len(info1) > 48:
                log_service.dcPullLog(f"拉取股票信息成功：{stock_code}")
                break
            else:
                log_service.dcPullLog(f"拉取股票信息重试：{stock_code}")
                time.sleep(7)
        except:
            log_service.error(f"打开页面异常：{stock_code}")
            time.sleep(5)
            pass

    if len(info1) > 48:
        log_service.dcPullLog(f"处理股票信息：{stock_code}")
        info1 = info1[48:]
        info1 = info1[:-2]
        info1 = json.loads(info1)

        if not info1['3541450']:
            continue

        if info1['stockStatus'] == '停牌':
            up_sql = f"update DC_Stock_QA set is_pull = 2 where stock_code = {stock_code} and is_pull = 1"
            session.execute(up_sql)
            session.commit()
            session.close()
        session = mysql1.create_session()

        all_price = "{:.2f}".format(float(info1['3541450']) / 100000000) if info1['10'] else 0.00
        pre_price = info1['10'] if info1['10'] else 0.00
        curr_ratio = info1['199112'] if info1['199112'] else 0.00
        up_sql = f"update DC_Stock_QA set capitalization = {all_price}, stock_price = {pre_price}, ratio = {curr_ratio}, is_pull = 2 where is_pull = 1 and stock_code = {stock_code}"
        session.execute(up_sql)
        session.commit()
        session.close()
        log_service.dcPullLog(f"处理股票信息成功：{stock_code}")
