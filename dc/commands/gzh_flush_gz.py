import os
import sys

import xlrd

from dc.services.mysql_service import MySQLService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.gzh_service import GzhService
from dc.services.weixin_service import WeixinService
from dc.models.model import DCGzhInfo, DCGzhWechat, DCGzhWechatRelation

write_mode = 'prod'
mysql = MySQLService(run_model=write_mode)
session = mysql.create_session()

all_gzh: [DCGzhInfo] = session.query(DCGzhInfo).all()
gzh_maps = {}
for gzh in all_gzh:
    gzh_maps[gzh.gzhId] = gzh

already_subs = []
not_have = []

need_insert = [
    'IT数码潮流科技',
    '卓胜微',
    '篆芯半导体',
    '專利限時批',
    '中星微人工智能',
    '中融聚成北京',
    '中科亿海微',
    '中科纳通',
    '中金汇融',
    '中国科学院知识产权信息',
    '中国科学报',
    '中国激光杂志社',
    '中国互联网投资基金',
    '中船投资',
    '置柏投资',
    '智芯公司',
    '智驰华芯',
    '知行科技iMotion',
    '知产财经',
    '正奇控股',
    '正帆科技',
    '长沙韶光半导体有限公司',
    '泽石科技',
    '泽润视角',
    '云英谷科技Viewtrix',
    '越亚半导体',
    '优镓科技',
    '盈峰资本管理有限公司',
    '英诺达EnnoCAD',
    '英科迪 ICD',
    '一创投资',
    '燕东微',
    '巡星投资',
    '兴港半导体',
    '新傲科技',
    '芯至科技',
    '芯鑫租赁',
    '芯朴科技XinpleTek',
    '芯进电子',
    '芯干线科技',
    '芯德科技',
    '显示资讯 Display Times',
    '矽典微',
    '西人马FATRI',
    '西安华泰半导体科技有限公司',
    '武汉新芯集成电路制造有限公司',
    '无锡飞谱电子',
    '闻泰科技',
    '微崇半导体',
    '网优雇佣军',
    '投中网',
    '通信产业网',
    '特仪科技',
    '泰合资本',
    '泰斗',
    '台积电南京',
    '燧原科技Enflame',
    '苏州明皜传感科技有限公司',
    '仕佳光子',
    '昇印光电',
    '昇生微',
    '深圳私募基金业协会',
    '深圳方正微电子有限公司',
    '申能诚毅',
    '上海证券报',
    '上海隐冠半导体技术有限公司',
    '上海易卜半导体有限公司',
    '上海申和投资',
    '上海南土资产',
    '上海理成资产',
    '上海瀚薪科技有限公司',
    '上海果纳半导体',
    '杉金光电',
    '厦门市政投资有限公司',
    '三星电子官方',
    '赛恩领动sinPro',
    '润石智造',
    '瑞为技术Reconova',
    '瑞能半导体',
    '瑞发科半导体',
    '融和租赁',
    '日海智能',
    '群智咨询',
    '清科集团Zero2IPO',
    '清纯半导体',
    '青创伯乐',
    '启迪之星创投 TusStarVC',
    '齐芯资本',
    '沛顿科技',
    '诺辉投资',
    '诺安基金',
    '铌奥光电',
    '南通投资管理有限公司',
    '南京芯视界微电子科技有限公司',
    '南方周末',
    '睦星科技Kolmostar',
    '沐盟集团',
    '默升科技',
    '绿河投资',
    '隆天知识产权',
    '六角形半导体',
    '领挚科技',
    '翎贲资本',
    '联新资本',
    '联想控股微空间',
    '联瑞电子',
    '联创电子科技股份',
    '利扬芯片',
    '澜起科技',
    '蓝鲸汽车芯片',
    '科创南方',
    '科Way',
    '钧犀资本',
    '聚芯微电子',
    '竞泰资本',
    '景旺电子',
    '景略半导体',
    '景嘉微',
    '经济制裁和出口管制',
    '琻捷电子SENASIC',
    '金鼎资本',
    '江苏省投资基金业协会',
    '江苏国经控股集团有限公司',
    '江波龙',
    '嘉实基金',
    '济南基金业协会',
    '吉利控股集团',
    '吉富创投',
    '华芯存储',
    '华为麒麟',
    '华泰创新投资',
    '华势资本',
    '华润微电子功率器件事业群',
    '华民投',
    '华进半导体',
    '华虹宏力微资讯',
    '华大半导体有限公司',
    '湖南高新创业投资集团有限公司',
    '湖滨资本',
    '鸿道投资',
    '横琴金投',
    '恒兆亿',
    '恒玄科技',
    '和芯星通',
    '和光微',
    '合牛资本',
    '禾赛科技',
    '航信资本',
    '杭州朗迅科技股份有限公司',
    '海康威视',
    '海创母基金',
    '国芯科技',
    '国金鼎兴',
    '广发资产管理',
    '光华资本订阅号',
    '光谷产业投资',
    '谷雨嘉禾资本 Spring Ventures',
    '富安达基金',
    '福州产投集团',
    '福建产业基金',
    '动感深迪',
    '德恒无锡律师事务所',
    '淡水泉投资',
    '大唐电信',
    '创新工场',
    '创芯慧联',
    '创世伙伴资本CCV',
    '橙科微电子',
    '博华资本',
    '陛通半导体',
    '北拓资本',
    '北京深维科技有限公司',
    '北京日出安盛资本管理有限公司',
    '北京基金业协会',
    '蚌埠光电',
    '暗涌Waves',
    '安捷利美维上海工厂',
    'UWE优微科技',
    'TechInsights',
    'TCL天津',
    'SYT宇腾科技',
    'Synaptics',
    'StarFive',
    'Soitec',
    'Socionext',
    'Meraki Integrated茂睿芯',
    'Manz亚智科技',
    'KNS库力索法',
    'KLA Corporation',
    'JSIC智能集成电路所',
    'JM Insights 集摩咨询',
    'InfoQ',
    'GfK中国',
    'FaradayFuture',
    'DIGITIMES',
    'BAI Capital',
    'AutoCore',
    'AB 工控室'
]

testNames = [
    # '集邦化合物半导体',
    # '英特尔资讯'
]


def gzh_flush_gz():
    wechats: [DCGzhWechat] = session.query(DCGzhWechat).filter(DCGzhWechat.isUsed == 1).all()
    gzhs: [DCGzhInfo] = session.query(DCGzhInfo).all()
    print(len(gzhs))
    gzh_ids = []
    already_insert = []
    followed = []
    all_follow = []
    for gzh in gzhs:
        gzh_ids.append(gzh.gzhId)

    for wechat in wechats:
        print(wechat.wxId)
        s = WeixinService(wx_id=wechat.wxId)
        p = {
            'wxpid': s.pid,
            'base_url': s.base_url
        }
        subs = s.req_gzh(params=p)
        for sub in subs['result'][wechat.wxId]:
            if len(sub) < 2:
                continue
            # 如果需要打印所有微信号的关注 开启下面的代码
            # all_follow.append(sub)

            # 如果需要导入关注关系 使用下面的代码
            # 不在数据库的列表里
            if sub[0] not in gzh_ids:
                # 如果已经从当前脚本写入数据库  则继续
                if sub[0] in already_insert:
                    continue
                # 如果没有在导入名单中 则继续
                if sub[2] not in need_insert:
                    continue
                # print(sub[0] + "|" + sub[2] + "| need insert")
                session.add(DCGzhInfo(
                    gzhId=sub[0],
                    gzhNo=sub[1],
                    nickName=sub[2],
                    status=1,
                    isSubscribe=1,
                    level=1
                ))
                already_insert.append(sub[0])
                session.add(DCGzhWechatRelation(
                    wxId=wechat.wxId,
                    gzhId=sub[0],
                ))
                print("insert %s,%s", sub[0], sub[2])
        # print(wechat.wxId, wechat.nickName, all_follow)
        # 如果导入关注关系 需要提交数据库   如果是查看微信的已关注全部 请注释掉
        session.commit()
    print(all_follow)


gzh_flush_gz()
session.close()
