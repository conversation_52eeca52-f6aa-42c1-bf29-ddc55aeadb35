# -*-coding:utf-8-*-
import re
import time
import sys
import os
import json
import traceback
import xlrd2
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.services.elasticsearch_service import ElasticsearchService
from dc.models.model import MPCompanyAntitrust, DCSiteList, DCIntellectualProperty


"""
[功能]：数据采集-反垄断 未启用
[作者]：wjh
[日期]：2022-10-19
"""


mysql = MySQLService()
ess = ElasticsearchService()
redisService = RedisService('redis')
client1 = redisService.get_client()

redis_prefix = 'laravel_database_'
redis_company_info_key = f'{redis_prefix}new_company_info'


# 写入主任务队列
def handel():
    session = mysql.create_session()
    rets = session.query(DCSiteList).filter(DCSiteList.sourceCategory == "dc_chanquan").all()
    for ret in rets:
        tmp = ret.to_dict()
        print(tmp['detailUrl'])
        # info = {'siteListId': tmp['id'], 'siteName': tmp['siteName'], 'siteUrl': tmp['siteUrl'],
        #         'sourceName': tmp['sourceName'], 'keyWords': '', 'filterWords': '',
        #         'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
        #         'updateAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
        #         'tags': '', 'remark': ''}
        # print(info)
        # dch = DCIntellectualProperty(**info)
        # session.add(dch)
    #     detail_url = tmp['detailUrl']
    #     up_sql = f"update DC_IntellectualProperty set siteUrl = '{detail_url}' where siteListId = {tmp['id']}"
    #     session.execute(up_sql)
    #     session.commit()
    # session.close()

    exit(22222)

    # mysql1 = MySQLService('mysql_company')
    # session1 = mysql1.create_session()
    # ret = session1.query(MPCompanyAntitrust).filter(MPCompanyAntitrust.isUnconditional == 1, MPCompanyAntitrust.id > 2473).all()
    # for ret in ret:
    #     tmp = ret.__dict__
    #     print(tmp['id'])
    #     info = {'dc_detail_url': tmp['url'], 'dc_site_name': tmp['source'], 'mainTaskId': 2854, 'siteListId': 378,
    #             'taskId': 586698, 'analysis_time': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), 'version': 2,
    #             'title': tmp['title'], 'content': tmp['companyInfo'],
    #             'public_time': deal_format_date(tmp['conclusionDate'])[0]}
    #
    #     es = ess.get_connect()
    #     res = es.index(index="dc_fanlongduanwtj", body=info)
    #     es.close()
    #
    #     up_sql = f"update MP_CompanyAntitrust set esId = '%s', esIndex = 'dc_fanlongduanwtj' where id = {tmp['id']}" % res['_id']
    #     session1.execute(up_sql)
    # session1.commit()
    #
    # exit(1111)
    #
    # session2.close()
    # session1.close()

    # mysql1 = MySQLService('mysql_company')
    # session1 = mysql1.create_session()
    #
    # file_path = r'./12333.xlsx'
    # data = xlrd2.open_workbook(file_path)
    # table = data.sheet_by_name('Sheet4')
    # nrows = table.nrows  # 行数
    # ncols = table.ncols
    # for i in range(0, nrows):
    #     list1 = table.row_values(i)
    #     df = {'title': list1[1],
    #           'source': "反垄断局",
    #           'pubTime': "",
    #           'conclusionDate': list1[3],
    #           'url': '',
    #           'companyInfo': list1[2],
    #           'isUnconditional': 1,
    #           'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
    #           'esId': '',
    #           'year': list1[3][0:4],
    #           'month': list1[3][5:].split('月')[0],
    #           'esIndex': 'dc_fanlongduanwtj'}
    #     dch = MPCompanyAntitrust(**df)
    #     session1.add(dch)
    # session1.commit()
    # session1.close()

    # while client1.llen(redis_company_info_key):
    #     newCompanyName = client1.rpop(redis_company_info_key)
    #     if not newCompanyName:
    #         continue
    #
    #     mysql1 = MySQLService('mysql_company')
    #     session1 = mysql1.create_session()
    #     es = ess.get_connect()
    #     ret = session1.query(MPCompanyAntitrust).filter(MPCompanyAntitrust.isUnconditional == 2).all()
    #     for antitrustInfo in ret:
    #         tmp = antitrustInfo.__dict__
    #         print(tmp['id'])
    #         query = {
    #             "query": {
    #                 "term": {
    #                     "_id": {
    #                         "value": tmp['esId']
    #                     }
    #                 }
    #             }
    #         }
    #         res = es.search(index='dc_fanlongduanftj', body=query)
    #         if not res['hits']['hits']:
    #             continue
    #         newCompanyName = '相关行业协会'
    #         companyNames = getCompanyInfo(newCompanyName, res['hits']['hits'][0]['_source']['text'])
    #         if not companyNames:
    #             continue
    #
    #         if not tmp['companyInfo']:
    #             companyNames = companyNames
    #         else:
    #             companyNames = tmp['companyInfo'] + "、" + companyNames
    #
    #         up_sql = f"update MP_CompanyAntitrust set companyInfo = '%s' where id = {tmp['id']}" % companyNames
    #         session1.execute(up_sql)
    #     session1.commit()
    #     session1.close()

    # chrome_options = Options()
    # chrome_options.add_argument('--headless')
    # chrome_options.add_argument('--no-sandbox')
    # chrome_options.add_argument('--disable-gpu')
    # driver = webdriver.Chrome(options=chrome_options)
    #
    # url11 = 'http://fldj.mofcom.gov.cn/article/zcfb/201710/20171002654398.shtml'
    # driver.get(url11)
    # xpath = driver.find_elements('xpath', "//div[@id='zoom']//table//tr[1]/following-sibling::tr")
    # driver.implicitly_wait(10)
    #
    # mysql1 = MySQLService('mysql_company')
    # session1 = mysql1.create_session()
    #
    # for x in xpath:
    #     td1 = x.find_element('xpath', './td[1]')
    #     if td1.text == '序号':
    #         continue
    #     td2 = x.find_element('xpath', './td[2]')
    #     td3 = x.find_element('xpath', './td[3]')
    #     td4 = x.find_element('xpath', './td[4]')
    #     deal_arr = dealConclusionDate(td4.text, '2014年')
    #     df = {'title': td2.text,
    #           'source': "反垄断局",
    #           'pubTime': "",
    #           'conclusionDate': deal_arr[0],
    #           'url': url11,
    #           'companyInfo': td3.text,
    #           'isUnconditional': 1,
    #           'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
    #           'esId': 'epj6MYQBYWWw2XaaV0TE',
    #           'year': deal_arr[1],
    #           'month': deal_arr[2],
    #           'esIndex': 'dc_fanlongduanwtj'}
    #     # print(df)
    #     dch = MPCompanyAntitrust(**df)
    #     session1.add(dch)
    # session1.commit()
    # exit()


def getCompanyInfo(companyName, body):
    arr = re.search(companyName, body)
    return arr[0] if arr else ''


def deal_format_date(date_str):
    date_str = date_str.strip()
    date_str = date_str.replace('年', '-')
    date_str = date_str.replace('月', '-')
    date_str = date_str.replace('日', '')
    date_str = date_str.replace('/n', '')
    date_str = date_str.replace('/', '-')
    date_str = date_str.replace('\\', '-')
    date_str = date_str.replace('.', '-')
    date_str = date_str.replace('- ', '-')
    date_str = date_str[0:10]

    if not date_str:
        format_date = '1970-01-01'  # time.strftime("%Y-%m-%d", time.localtime())
        type1 = 2
        return [format_date, type1]

    if datetime.strptime(date_str, "%Y-%m-%d"):
        date_str = datetime.strptime(date_str, "%Y-%m-%d")
        format_date = date_str.strftime("%Y-%m-%d")
        type1 = 1
    elif datetime.strptime(date_str, "%m/%d/%Y"):
        date_str = datetime.strptime(date_str, "%m/%d/%Y")
        format_date = date_str.strftime("%Y-%m-%d")
        type1 = 1
    elif datetime.strptime(date_str, "%A, %B %d, %Y"):
        date_str = datetime.strptime(date_str, "%A, %B %d, %Y")
        format_date = date_str.strftime("%Y-%m-%d")
        type1 = 1
    elif datetime.strptime(date_str, "%B %d %Y"):
        date_str = datetime.strptime(date_str, "%B %d %Y")
        format_date = date_str.strftime("%Y-%m-%d")
        type1 = 1
    elif datetime.strptime(date_str, "%A, %b %d, %Y, %I:%M%p"):
        date_str = datetime.strptime(date_str, "%A, %b %d, %Y, %I:%M%p")
        format_date = date_str.strftime("%Y-%m-%d")
        type1 = 1
    elif datetime.strptime(date_str, "%d %b %Y"):
        date_str = datetime.strptime(date_str, "%d %b %Y")
        format_date = date_str.strftime("%Y-%m-%d")
        type1 = 1
    else:
        format_date = '1970-01-01'  # time.strftime("%Y-%m-%d", time.localtime())
        type1 = 2

    return [format_date, type1]


handel()
