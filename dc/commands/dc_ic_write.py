import json
import time
import sys
import os
import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.conf.settings import get_settings
from dc.services.mysql_service import MySQLService
from dc.services.logging_service import LoggingService
from dc.models.model import DCICWrite


def start():
    mysql = MySQLService()
    session = mysql.create_session()

    ymd = time.strftime("%Y-%m-%d", time.localtime())
    start_time = ymd + " 00:00:00"
    end_time = ymd + " 08:00:00"
    ret = session.query(DCICWrite).filter(DCICWrite.createAt >= start_time, DCICWrite.createAt <= end_time).all()

    log_service = LoggingService('dc_ic_write.log')
    if not ret:
        log_service.dcPullLog('未获取到数据')
        session.close()
        exit()

    data = []
    for key, obj in enumerate(ret):
        tmp = {}
        info = obj.to_dict()
        tmp['es_id'] = info['id']
        tmp['value'] = info['title']
        data.append(tmp)

    params = {"model_id": 9, "data": data, "type": "similar", "threshold": "0.8"}
    url = get_settings('filterICList')
    try:
        res = requests.post(url, json=params)
        res.encoding = "UTF-8"
        if res.status_code != 200:
            log_service.dcPullLog('请求去重接口失败')
            session.close()
            exit()

        arr = json.loads(res.text)['data']
        ids = []
        for item in arr:
            ids.append(item['es_id'])

        if not ids:
            log_service.dcPullLog('去重接口返回数据为空')
            session.close()
            exit()

        if len(ids) == 1:
            up_sql = f"update DC_IC_Writing set isRepeat = 2 where id = {ids[0]}"
        else:
            ids = tuple(ids)
            up_sql = f"update DC_IC_Writing set isRepeat = 2 where id in {ids}"
        session.execute(up_sql)
        session.commit()
        session.close()
    except:
        log_service.dcPullLog('请求去重接口异常')
        session.close()
        exit()


start()
