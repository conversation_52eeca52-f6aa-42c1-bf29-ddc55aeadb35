# -*-coding:utf-8-*-
import sys
import os
import threading
import uuid
import traceback
import time
from typing import List

path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
sys.path.append(path)

from dc.models.model import DCSiteTask, DCSiteMainTask, DCSiteList
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.common.dc_web_analyzer_util import AnalyzerUtil
from dc.common.dc_web_analyzer_work import AnalyzerWork

query_list_ids = []


class AnalyzerTask:
    """
    网页分析任务测试脚本
    """
    site_list_id_set = set()  # 如果多个任务site_id相同，则需要阻塞线程(加锁)
    N = 8  # 开启线程并发的数量

    def __init__(self):
        self.redis_service = RedisService()
        self.mysql_service = MySQLService()
        self.session = None

    def do(self):
        web_driver = None
        uid = uuid.uuid4().hex  # 日志唯一id标注
        site_list_id = -1
        try:
            if len(query_list_ids) <= 0:
                print("over test")
                return
            self.session = self.mysql_service.create_session()
            site_list_id = query_list_ids.pop(0)
            main_task = DCSiteMainTask(id=-1, siteListId=site_list_id, keyWord="半导体")
            site_list = self.get_DCSiteList_by_id(site_list_id)
            if not site_list:
                AnalyzerTask.log("not found site_list", uid)
                return
            cnt = 0
            while site_list_id in AnalyzerTask.site_list_id_set:  # 如果当前有相同的site_id正在处理，则阻塞当前线程
                if cnt > 128:  # 如果长时间阻塞，则直接关闭该线程
                    return
                cnt += 1
                AnalyzerUtil.random_sleep(2, 3)
            self.session.commit()  # 刷新数据表
            AnalyzerTask.site_list_id_set.add(site_list_id)  # 加锁
            AnalyzerTask.log("start site_id:%d task" % site_list_id, uid)
            old_site_task = self.get_DCSiteTasks_by_siteListId(site_list_id, main_task.keyWord)  # 获取历史数据
            # 爬取数据
            worker = AnalyzerWork(dc_site_list=site_list,
                                  dc_site_main_task=main_task,
                                  old_dc_site_tasks=old_site_task)
            web_driver = worker.driver
            worker.test()
            # 数据倒置
            site_task_list = worker.crawl_data[::-1]
            if len(site_task_list) == 0 and worker.data_record is False:
                AnalyzerTask.log("(warning) the site_id:%d get empty data" % site_list_id, uid)
            self.add_DCSiteTasks(site_task_list)
            self.session.commit()
            AnalyzerTask.log("the site_id:%d task is successful. the length of new data is %d " % (
                site_list_id, len(site_task_list)), uid)
            self.session.close()
            AnalyzerTask.site_list_id_set.remove(site_list_id)  # 解锁
        except Exception:
            if site_list_id in AnalyzerTask.site_list_id_set:
                AnalyzerTask.site_list_id_set.remove(site_list_id)
            try:
                if web_driver:
                    web_driver.quit()
            except:
                pass
            try:
                if self.session:
                    self.session.close()
            except:
                pass
            AnalyzerTask.log("the task is failed ", uid)
            flag = False
            for cnt in range(2):
                if self.fail_try(site_list_id, uid, cnt):
                    flag = True
                    break
            if not flag:
                tb = traceback.format_exc()
                AnalyzerTask.fail_log("the site_id:%d task is failed after 3 times " % site_list_id + tb, uid)
        finally:
            try:
                if web_driver:
                    web_driver.quit()
            except:
                pass
            try:
                if self.session:
                    self.session.close()
            except:
                pass

    def fail_try(self, site_list_id, uid, try_cnt):
        web_driver = None
        try:
            self.session = self.mysql_service.create_session()
            main_task = DCSiteMainTask(id=-1, siteListId=site_list_id, keyWord="半导体")
            site_list = self.get_DCSiteList_by_id(site_list_id)
            if not site_list:
                AnalyzerTask.log("not found site_list", uid)
                return
            cnt = 0
            while site_list_id in AnalyzerTask.site_list_id_set:  # 如果当前有相同的site_id正在处理，则阻塞当前线程
                if cnt > 128:  # 如果长时间阻塞，则直接关闭该线程
                    return
                cnt += 1
                AnalyzerUtil.random_sleep(2, 3)
            self.session.commit()  # 刷新数据表
            AnalyzerTask.site_list_id_set.add(site_list_id)  # 加锁
            AnalyzerTask.log("start site_id:%d task" % site_list_id, uid)
            old_site_task = self.get_DCSiteTasks_by_siteListId(site_list_id, main_task.keyWord)  # 获取历史数据
            # 爬取数据
            worker = AnalyzerWork(dc_site_list=site_list,
                                  dc_site_main_task=main_task,
                                  old_dc_site_tasks=old_site_task)
            web_driver = worker.driver
            worker.test()
            # 数据倒置
            site_task_list = worker.crawl_data[::-1]
            self.add_DCSiteTasks(site_task_list)
            self.session.commit()
            AnalyzerTask.log("the site_id:%d task is successful. the length of new data is %d " % (
                site_list_id, len(site_task_list)), uid)
            self.session.close()
            AnalyzerTask.site_list_id_set.remove(site_list_id)  # 解锁
            return True
        except Exception:
            if site_list_id in AnalyzerTask.site_list_id_set:
                AnalyzerTask.site_list_id_set.remove(site_list_id)
            try:
                if web_driver:
                    web_driver.quit()
            except:
                pass
            try:
                if self.session:
                    self.session.close()
            except:
                pass
            AnalyzerTask.log(f"the task is failed and try {try_cnt + 1} times ", uid)
            return False
        finally:
            try:
                if web_driver:
                    web_driver.quit()
            except:
                pass
            try:
                if self.session:
                    self.session.close()
            except:
                pass

    @staticmethod
    def log(msg: str, uid: str):
        info = "[" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time())) + " uid:" + uid + "] "
        with open(path + '/logs/dc_web_analyzer_task_out_test.log', 'a') as f:
            f.write(info + msg + "\n")

    @staticmethod
    def fail_log(msg: str, uid: str):
        info = "[" + time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time())) + " uid:" + uid + "] "
        with open(path + '/logs/dc_web_analyzer_task_fail_test.log', 'a') as f:
            f.write(info + msg + "\n")

    def get_DCSiteTasks_by_siteListId(self, siteListId: int, keyword: str = "") -> List[DCSiteTask]:
        """
        根据siteListId获取历史的所有相关子任务(用于去重)
        :param keyword: 关键词
        :param siteListId: list id
        :return:
        """
        if not keyword:
            ret = self.session.query(DCSiteTask).filter(DCSiteTask.siteListId == siteListId).all()
        else:
            ret = self.session.query(DCSiteTask).filter(DCSiteTask.siteListId == siteListId,
                                                        DCSiteTask.keyword == keyword).all()
        return ret

    def add_DCSiteTasks(self, tasks: list[DCSiteTask]) -> None:
        """
        添加一个新的子任务，并推送到redis中
        :param task:
        :return:
        """
        self.session.add_all(tasks)

    def get_DCSiteList_by_id(self, id: int) -> DCSiteList:
        """
        根据id获取sitelist
        :param id: sitelist的id
        :return:
        """
        ret = self.session.query(DCSiteList).filter(DCSiteList.isUsed == 1, DCSiteList.status == 1,
                                                    DCSiteList.id == id).first()
        return ret

    def filter_links(self, site_id: int, new_site_tasks: list[DCSiteTask]) -> list[DCSiteTask]:
        """
        将新爬取到的数据与数据库中的数据比对，根据url，清除已经有的数据
        :param site_id: 当前子任务的site_id
        :param new_site_tasks: 新爬取到的数据
        :return:
        """
        old_site_task = self.get_DCSiteTasks_by_siteListId(site_id)
        old_url_set = set()
        for element in old_site_task:
            old_url_set.add(element.url)
        res = []
        for element in new_site_tasks:
            if element.url not in old_url_set:
                res.append(element)
        return res


class AnalyzerThread(threading.Thread):
    """
    多线程
    """

    def __init__(self, threadName):
        super(AnalyzerThread, self).__init__(name=threadName)

    def run(self):
        task = AnalyzerTask()
        while 1:
            AnalyzerUtil.random_sleep(2, 4)
            task.do()
            AnalyzerUtil.random_sleep(2, 4)


if __name__ == "__main__":
    # ms = MySQLService().create_session()
    # res: list[DCSiteList] = ms.query(DCSiteList.id).filter(DCSiteList.isUsed == 1, DCSiteList.status == 1).all()
    # ms.close()
    res =[227,221,304]
    for e in res:
        query_list_ids.append(e)
    # 开启N个线程
    for i in range(AnalyzerTask.N):
        AnalyzerThread("AnalyzerThread:" + str(i)).start()
