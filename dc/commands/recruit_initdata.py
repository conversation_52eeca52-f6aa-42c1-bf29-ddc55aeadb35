# -*- coding:utf-8 -*-
# @Function  : 数据采集-招聘数据采集
# <AUTHOR> lws
# @Time      : 2022-10-19
# Version    : 1.0

import time
import sys
import os
import json
import string
import zipfile
from selenium import webdriver
from selenium.webdriver import Keys, DesiredCapabilities
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.webdriver import WebDriver
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService
from dc.models.model import MPCompanyInfo, MPCompanyRecruitOriginal, MPCompanyRecruitAlias
from dc.services.logging_service import LoggingService
from dc.commands.recruit_utils import *
from dc.conf.settings import *

mysql = MySQLService('mysql_company')
redisService = RedisService('redis')
redis_key = f'company_recruit_lists'
redis_company_name = f'company_info_recruit_list'
logger = LoggingService('recruit_initdata.log')

# 代理服务器
proxy: dict = get_settings2('recruit', 'proxy', {})
proxyHost, proxyPort, proxyUser, proxyPass = proxy.values()

proxy_url = f'--proxy-server=http://{proxyHost}:{proxyPort}'

login_page_url = 'https://wow.liepin.com/'
main_page_url = 'https://www.liepin.com/'
result_page_url = 'https://www.liepin.com/zhaopin/'


# 写入主任务队列
def handel():
    while True:

        r_client = None
        session = None

        try:
            r_client = redisService.get_client()
            redis_company_info = r_client.rpop(redis_company_name)
            if not redis_company_info:
                time.sleep(5)
                logger.debug('睡 5 秒')
                continue

            # 循环执行
            r_client.lpush(redis_company_name, redis_company_info)

            company_info = json.loads(redis_company_info)
            company = company_info['name']
            companyId = company_info['id']
            companyAlias = company_info['company_alias'] if 'company_alias' in company_info else []
            if not company or not companyId:
                continue

            logger.info(f"companyId: {companyId} company: {company}")
            # print(company)

            params = sys.argv[1:]
            process_id = int(params[0]) if params else 0
            # proxy_auth_plugin_path = create_proxy_auth_extension(
            #     proxy_host=proxyHost,
            #     proxy_port=proxyPort,
            #     proxy_username=proxyUser,
            #     proxy_password=proxyPass)

            chrome_options = Options()
            chrome_options.add_argument('--headless=chrome')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument("--start-maximized")
            # chrome_options.add_extension(proxy_auth_plugin_path)
            chrome_options.add_argument(proxy_url)  # 代理池
            chrome_options.add_argument("--force-device-scale-factor=0.75")     # 缩放
            # 禁止图片加载
            prefs = {
                "profile.managed_default_content_settings.images": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_experimental_option("excludeSwitches", ["--ignore-certificate-errors"])

            ca = DesiredCapabilities.CHROME
            ca["goog:loggingPrefs"] = {"performance": "ALL"}

            driver = webdriver.Chrome(options=chrome_options, desired_capabilities=ca)
            driver.implicitly_wait(10)
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
            })
            logger.info(f"webdriver started")

            company_alias = []
            getFirstPage(driver)
            logger.info(f"getFirstPage")

            try_count = 0
            while True:
                try:
                    try_count += 1
                    if try_count > 100:
                        logger.info(f"超出最大尝试次数")
                        break

                    check_main_page(driver)

                    input_obj = driver.find_element('xpath', '//div[@id="home-search-bar-container"]//input')
                    input_obj.send_keys(company)
                    time.sleep(1)
                    check_login_page(driver)

                    input_obj.send_keys(Keys.ENTER)
                    logger.info(f"输入搜索条件[{try_count}]: {company}")

                    time.sleep(1)
                    driver.switch_to.window(driver.window_handles[-1])
                    time.sleep(2)
                    check_result_page(driver)

                    driver.find_element('xpath', '//div[@class="filter-options-container"]')
                    break
                except:
                    # getFirstPage(driver)
                    pass

            try:
                driver.find_element('xpath', '//div[@class="job-list-box"]')
            except:
                driver.quit()
                continue

            time.sleep(3)

            for i in range(1, 11):
                logs = driver.get_log("performance")
                for log1 in logs:
                    message = log1['message']
                    msg_dict = json.loads(message)
                    try:
                        req_id = msg_dict['message'].get('params', {}).get('requestId', None)
                        if not req_id:
                            continue

                        message_request = msg_dict['message'].get('params', {}).get('request', None)
                        if message_request is None:
                            continue

                        if message_request.get('url', None) is None:
                            continue

                        u = msg_dict['message']['params']['request']['url']
                        if 'https://api-c.liepin.com/api/com.liepin.searchfront4c.pc-search-job' == u:
                            flag = 0
                            content = {}
                            while flag < 5:
                                try:
                                    time.sleep(2)
                                    content = driver.execute_cdp_cmd('Network.getResponseBody',
                                                                     {'requestId': req_id})
                                    break
                                except:
                                    flag += 1

                            if 'body' not in content:
                                continue

                            info = content['body']
                            info1 = json.loads(info)
                            if 'data' not in info1:
                                continue

                            session = mysql.create_session()
                            jobInfos = info1['data']['data']['jobCardList']
                            job_count = len(jobInfos) if jobInfos else 0
                            logger.info(f"job_count: {job_count}")
                            for jobInfo in jobInfos:
                                compArr = jobInfo['comp']
                                if compArr['compName'] not in company_alias:
                                    company_alias.append(compArr['compName'])
                                if compArr['compName'] != company and compArr['compName'] not in companyAlias:
                                    continue

                                jobArr = jobInfo['job']
                                jobRecruitArr = jobInfo['recruiter']
                                df = {'jobId': jobArr['jobId'],
                                      'jobKind': jobArr['jobKind'],
                                      'salary': jobArr['salary'],
                                      'requireWorkYears': jobArr[
                                          'requireWorkYears'] if 'requireWorkYears' in jobArr else '应届',
                                      'dq': jobArr['dq'],
                                      'refreshTime': jobArr['refreshTime'],
                                      'title': jobArr['title'],
                                      'link': jobArr['link'],
                                      'requireEduLevel': jobArr['requireEduLevel'] if 'requireEduLevel' in jobArr else '',
                                      'labels': ','.join(jobArr['labels']),
                                      'recruiterId': jobRecruitArr['recruiterId'],
                                      'recruiterName': jobRecruitArr['recruiterName'],
                                      'recruiterTitle': jobRecruitArr['recruiterTitle'],
                                      'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                                      'companyId': companyId}
                                dch = MPCompanyRecruitOriginal(**df)
                                logger.info(f"job info: {df}")
                                session.add(dch)
                            session.commit()
                            session.close()
                            logger.info(f"session commit")
                    except Exception as ex:
                        logger.error(f"exception {str(ex)}")
                        continue

                try:
                    pageLinks = driver.find_elements('xpath', '/html/body/div/div[3]/section[1]/div[2]/ul/li')
                    if not pageLinks:
                        break

                    for button in pageLinks:
                        if button.text == str(i + 1):
                            while True:
                                try:
                                    logger.info(f"next page: {button.text}")
                                    button.click()
                                    driver.find_element('xpath', '//div[@class="filter-options-container"]')
                                    break
                                except:
                                    pass
                            break
                except Exception as ex:
                    logger.error(f"pageLinks exception {str(ex)}")
                    driver.close()
                    break

                time.sleep(3)

            if company_alias and not companyAlias:
                session = mysql.create_session()
                aliasArr: MPCompanyRecruitAlias = session.query(MPCompanyRecruitAlias).filter(MPCompanyRecruitAlias.companyId == companyId).first()
                if not aliasArr:
                    df = {'companyId': companyId,
                          'companyAlias': ",".join(company_alias),
                          'name': company,
                          'status': 1,
                          'updateAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()),
                          'createAt': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}
                    info = MPCompanyRecruitAlias(**df)
                    session.add(info)
                    session.commit()
                session.close()

            r_client.lpush(redis_key, companyId)
            driver.quit()

        except Exception as ex:
            logger.error(f"exception: {str(ex)}")

        finally:
            if r_client:
                r_client.close()

            if session:
                session.close()


def getFirstPage(driver):
    while True:
        try:
            driver.get("https://www.liepin.com/")
            driver.find_element('xpath', '//div[@id="home-search-bar-container"]//input')
            break
        except:
            pass


def check_main_page(driver: WebDriver):
    """
    判断是否搜索页面
    :param driver:
    :return: bool
    """
    for i in range(1, 5):
        main_page = driver.current_url.__contains__(main_page_url)
        if main_page:
            return True
        else:
            driver.get(main_page_url)
            time.sleep(3)

    raise Exception('check main page fail')


def check_result_page(driver: WebDriver):
    """
    判断是否结果页面
    :param driver:
    :return: bool
    """
    main_page = driver.current_url.__contains__(result_page_url)
    if not main_page:
        raise Exception('check result page fail')

    logger.info(f'check result page: {main_page}')
    return True


def check_login_page(driver: WebDriver):
    """
    判断是否登录页面
    :param driver:
    :return: bool
    """
    main_page = driver.current_url.__contains__(login_page_url)
    if main_page:
        raise Exception('check login page true')

    logger.info(f'check login page: {main_page}')

    return True


handel()
