'''
开发时间：2024/11/15 14:56
项目需求：将es中贸易管制数据（标题、正文、附件、摘要）翻译为中文，并将翻译后的数据存入至ES数据库：
思路：
1、从es中取出is_translated=0的数据
2、附件下载---附件解析---附件翻译
3、标题和正文翻译，调用glm-4-plus提取摘要，并对摘要进行翻译
4、将翻译后的数据 批量更新至es数据中；
'''
import json
import os
import re
import sys
import time
from datetime import datetime
from functools import wraps
from typing import List, Dict, Any

import requests
import schedule
from elasticsearch import helpers
from faker import Faker
from fengchao import FengChao
from tqdm import tqdm

from dc.services.k8s_service import K8sService

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.services.logging_service import LoggingService
from dc.conf.settings import load_base_config
from dc.services.elasticsearch_service import ElasticsearchService
logger = LoggingService('trade_control.log')
ks = K8sService()

conf = load_base_config()

fengchao = FengChao(base_url=conf.get('fengchao').get('base_url'), api_key=conf.get('fengchao').get('api_key'),
                    secret_key=conf.get('fengchao').get('secret_key'))

ess = ElasticsearchService()
faker = Faker(locale='zh_CN')
index_name="dc_tradecontrol"

def retry_decorator(max_retries=3, base_delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries == max_retries:
                        logger.error(f"函数{func.__name__}重试失败：{e}")
                    else:
                        wait_time = base_delay * (2 ** (retries - 1))  # 指数退避
                        logger.warning(f"Retry {retries}/{max_retries} after {wait_time}s")
                        time.sleep(wait_time)
            if func.__name__ == "files_down":
                return None, None
            else:
                return None
        return wrapper
    return decorator

@retry_decorator(max_retries=3, base_delay=1)
def es_query(es):
    """
    函数用途：es调用
    """
    # 配置为中文，查询未翻译的数据
    query_body = {
        "query": {
            "term": {
                "is_translated": 0
            }
        },
        "size": 10  # 指定返回文档的数量
    }
    # 发送搜索请求到指定的索引
    response = es.search(index=index_name, body=query_body)
    es_data = response['hits']['hits']

    if es_data:
        return es_data
    else:
        logger.info("没有找到匹配的数据。")
        return None


@retry_decorator(max_retries=3, base_delay=1)
def files_down(image_url):
    """
    函数用途：附件下载
    image_url:es数据，索引名：dc_tradecontrol
    """
    file_name = image_url.split('/')[-1]
    response = requests.get(image_url)
    file_type = file_name.split('.')[-1]
    if file_type.lower() not in ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'html', 'markdown']:
        logger.info(f"{file_name}:附件格式非 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'html', 'markdown'")
        return None,None
    if response.status_code ==200:
        return file_name,response.content
    else:
        logger.warning(f"无法下载文件{file_name}，状态码：{response.status_code}")
        return None,None

@retry_decorator(max_retries=3, base_delay=1)
def files_analysis(fl_name,fl_content):
    """
    函数用途：附件解析
    fl_name:来源函数files_down(data)返回值：file_name
    fl_content:来源函数files_down(data)返回值：response.content
    """
    url = conf.get('fengchao').get('convert_file')
    querystring = {"oss_file": None, "transmit_type": "content", "out_type": "md"}
    # files = None 如果不使用上传文件
    files = {'file': (fl_name, fl_content, 'multipart/form-data')}
    response = requests.request("POST", url, files=files, params=querystring)
    if response.status_code == 200:
        return response.text
    else:
        logger.warning(f"文档：{fl_name}，解析失败。状态码：{response.status_code}")
        return None


@retry_decorator(max_retries=3, base_delay=1)
def fengchao_models(model_name, query,prompt):
    """
    函数用途：模型调用
    """
    result = fengchao.chat(model_name,
                           query=query,
                           prompt=prompt,
                           mode='invoke',
                           is_sensitive=False,
                           max_tokens=4096,
                           system='',
                           temperature=0.5,
                           top_p=0.5)
    if result.status == 200:
        response = result.data.choices[0].message.content
        return response
    elif result.status == -401301:
        logger.warning(f"命中敏感信息")
        return -1
    else:
        logger.warning(result.__dict__)
        raise ValueError(f"大模型调用异常{result.__dict__}")

def translate_text(model,text):
    """
    函数用途：调用翻译模型并返回结果
    """
    res = fengchao_models(model, text, '多译中')
    return res

def file_process(files_v2: List[Dict[str, Any]]):
    """
    函数用途：处理附件的下载，解析、分割、翻译
    参数：
        files_v2: 包含文件信息的列表。
    返回：
        更新后的文件列表。
    """
    status = 1
    translated_files = []
    for file in files_v2:  # 处理多个附件
        image_url = file.get('image_url', '')
        if image_url:
            file_name, file_content = files_down(image_url)
            if file_name is None or file_content is None:
                translated_files.append(file)
                continue
            fl_analysis = files_analysis(file_name, file_content)
            if fl_analysis:
                file['file_v2_original'] = fl_analysis
                file['file_v2_zh'] = ''
                translated_files.append(file)
            else:
                status = -1
                break
        else:
            translated_files.append(file)
            # TODO 取消附件翻译
            # tran_result = []
            # fl_analysises = fl_analysis.split('\n\n')
            # temp = ""
            # for fl_analysis in fl_analysises:
            #     if len(temp + '\n\n' + fl_analysis) < 4000:
            #         temp += ('\n\n' + fl_analysis)
            #     else:
            #         tran_result.append(translate_text('glm-4-plus', temp))
            #         temp = fl_analysis
            # if temp.strip():
            #     tran_result.append(translate_text('glm-4-plus', temp))
            # file['file_v2_zh'] = '\n\n'.join(tran_result)
    return translated_files, status


@retry_decorator(max_retries=3, base_delay=1)
def update_result(actions, es):
    success, failed = helpers.bulk(es, actions)
    if failed:
        logger.info("以下内容更新失败")
        for fail in failed:
            logger.info(f"失败详情: {fail}")
    else:
        logger.info("批量更新成功")


def main():
    """
    函数用途：es中数据：title、text 、files_v2（附件）翻译为中文，以及批量入es库；
    使用模型：质谱（glm-4-plus)
    """
    exist_es = set()
    es = ess.get_connect()
    while True:
        ks.liveness()  # 探针

        es_data = es_query(es)
        if not es_data:
            logger.info("未从ES中查询到数据")
            break
        # 构建批量更新的动作列表
        actions = []
        for item in tqdm(es_data,desc='总数据进度条'):
            try:
                if item['_id'] in exist_es:
                    continue
                exist_es.add(item['_id'])
                is_translated = 1
                logger.info(f"处理es数据{item['_id']}")
                source = item["_source"]
                updates_data = {}
                # 附件下载、解析和翻译
                files_v2 = source.get('files_v2',[])
                if files_v2:
                    translated_files, status = file_process(files_v2)
                    if status == -1:
                        logger.warning(f"{item['_id']}附件解析异常，稍后重试。")
                        continue
                    updates_data['files_v2'] = translated_files
                else:
                    updates_data['files_v2'] = []

                title = source.get('title','')
                if title.strip():
                    title_zh = translate_text('glm-4-plus',title)
                    if title_zh is None:
                        continue
                    elif title_zh == -1:
                        is_translated = -1
                        updates_data['title_zh'] = ""
                    else:
                        if title_zh.startswith('"'):
                            title_zh = title_zh[1:]
                        if title_zh.endswith('"'):
                            title_zh = title_zh[:-1]
                        if title_zh.startswith('“'):
                            title_zh = title_zh[1:]
                        if title_zh.endswith('”'):
                            title_zh = title_zh[:-1]
                        updates_data['title_zh'] = title_zh
                else:
                    if updates_data['files_v2'] and updates_data['files_v2'][0]['file_v2_original'].strip():
                        title = fengchao_models('glm-4-plus', "\n\n".join(updates_data['files_v2'][0]['file_v2_original'].split('\n\n')[:5]), '短标题生成')
                        if title is None:
                            continue
                        elif title == -1:
                            is_translated = -1
                            updates_data['title_zh'] = ""
                        else:
                            updates_data['title_zh'] = re.compile("[^\u4e00-\u9fa5^a-z^A-Z^0-9]").sub("", title)
                    else:
                        updates_data['title_zh'] = ""
                text  = source.get('text','')
                updates_data['translated_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # 翻译正文、提取并翻译摘要
                if text:
                    text_zh = translate_text('glm-4-plus', text)
                    if text_zh is None:
                        continue
                    elif text_zh == -1:
                        is_translated = -1
                        updates_data['text_zh'] = ""
                    else:
                        updates_data['text_zh'] = text_zh
                    abstract_trans_res = fengchao_models('glm-4-plus', text, '文章摘要')
                    if abstract_trans_res is None:
                        continue
                    elif abstract_trans_res == -1:
                        is_translated = -1
                        updates_data['abstract_zh'] = ""
                    else:
                        updates_data['abstract_zh']=abstract_trans_res
                else:
                    if updates_data['files_v2'] and updates_data['files_v2'][0]['file_v2_original'].strip():
                        abstract_zh = fengchao_models('glm-4-plus', "\n\n".join(updates_data['files_v2'][0]['file_v2_original'].split('\n\n')[:5]), '文章摘要')
                        if abstract_zh is None:
                            continue
                        elif abstract_zh == -1:
                            is_translated = -1
                            updates_data['abstract_zh'] = ""
                        else:
                            updates_data['abstract_zh'] = abstract_zh
                    else:
                        updates_data['abstract_zh'] = ""
                    updates_data['text_zh'] = ""

                # 更新到es库
                if updates_data:
                    updates_data['is_translated']=is_translated
                    doc_id = item["_id"]
                    action = {
                        "_op_type": "update",
                        "_index": index_name,
                        "_id": doc_id,
                        "doc": updates_data
                    }
                    actions.append(action)
            except Exception as e:
                logger.error(f"处理文档{item}时发生错误：{e}")
            # 执行批量更新操作
        if actions:
            update_result(actions, es)

# 每3分钟执行一次
schedule.every(3).minutes.do(main)

while True:
    schedule.run_pending()
    time.sleep(1)