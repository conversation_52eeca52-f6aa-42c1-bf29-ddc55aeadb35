import base64
import json
import random
import time
import openai
import sys
import os
import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from dc.conf.settings import get_settings, RUN_MODE
from dc.services.redis_service import RedisService
from dc.services.mail_service import MailService
from dc.services.qianxun_service import QianxunService
from dc.services.logging_service import LoggingService

# gpt_keys = [
#     # '***************************************************',
#     # '***************************************************',
#     # '***************************************************',
#     # '***************************************************',
#     # '***************************************************',
#     '***************************************************',
#     '***************************************************',
# ]
usable_gpt_key_set = 'usable_gpt_key_set'

logger = LoggingService('chatgpt_check.log')


def check():
    dt = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    config = get_settings('chatgpt')
    redisService = RedisService('redis')
    client1 = redisService.get_client()

    gpt_keys = config['gpt_keys']

    for key in gpt_keys:
        try:
            prompt = "" + "\nHuman: " + "你好"
            openai.api_key = key
            openai.api_base = config['params']['api_base']

            params = {
                'model': 'gpt-3.5-turbo-16k',
                'messages': [{"role": "user", "content": prompt}]
            }

            retries = 3
            flag = False
            while retries > 0:
                retries -= 1

                response = openai.ChatCompletion.create(**params)
                answer = response["choices"][0].get('message', {}).get('content', '').strip()
                if answer:
                    flag = True
                    break

            if flag:
                client1.sadd(usable_gpt_key_set, key)
            else:
                isExist = client1.sismember(usable_gpt_key_set, key)
                if isExist:
                    client1.srem(usable_gpt_key_set, key)
        except:
            pass
            # isExist = client1.sismember(usable_gpt_key_set, key)
            # if isExist:
            #     client1.srem(usable_gpt_key_set, key)
    num = client1.scard(usable_gpt_key_set)
    if num == 0:
        mail = MailService()
        email = {
            'mail_title': "GPT-KEY不可用报警",
            'mail_body': f"注意！注意！\n {'报警时间：' + dt} \n {'报警内容：生成服务GPT-3.5，无可用账号，请尽快修复！'}",
            'mail_to': ["<EMAIL>", "<EMAIL>"],
        }
        # mail.send(email)
        client1.close()
        logger.error('GPT-KEY不可用报警.' + json.dumps(email))
        exit('无可用key')

    client1.close()

    # 验证接口相应
    header = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0",
    }

    data = {

        "content": "你好",
        "templateId": "1",
        "style": "专业简洁",
        "format": "markdown",
        "size": "short",
        "return_type": "stream"
    }

    try:
        tries = 3
        while tries > 0:
            tries -= 1
            flag = False
            daily_creat = get_settings('daily_creat')
            response = requests.post(daily_creat, headers=header, data=data, verify=False, stream=True)
            if response.status_code != 200:
                continue

            for line in response.iter_lines(decode_unicode=True):
                if not line:
                    continue

                lineArr = line.split(':')
                if len(lineArr) <= 1:
                    continue

                tmp_str = base64.b64decode(lineArr[1]).decode('utf-8')
                if tmp_str != '无法生成响应，请重试':
                    flag = True
                    break

            if flag:
                break

            if tries == 1:
                sendWx()
    except:
        # pass
        # 发微信
        sendWx()

    exit()


def sendWx():
    mode = RUN_MODE
    sign = ''
    if mode == "test":
        sign = "测试125"
    if mode == "prod":
        sign = "线上生成"
    dt = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    qx = QianxunService()
    msg = f"注意！注意！（来活喽）\n {'报警产品：AIGC生成日刊'} \n {'报警时间：' + dt} \n {'报警内容：AI无法响应，不能生成日刊，请尽快修复！（' + sign + ')'} [恐惧][恐惧] \n\n"
    title = f"亲爱的同志们，又来活喽"
    body = f"注意！注意！\n {'报警产品：AIGC生成日刊'} \n {'报警时间：' + dt} \n {'报警内容：AI无法响应，不能生成日刊，请尽快修复！（' + sign + ')'}"
    # qx.simple_send({
    #     'type': 87,
    #     'msg': msg,
    #     'mail_title': title,
    #     'mail_body': body,
    #     'wxid': '20254966458@chatroom',
    #     'mail_to': '<EMAIL>;<EMAIL>'
    # })
    logger.error(msg)


check()
