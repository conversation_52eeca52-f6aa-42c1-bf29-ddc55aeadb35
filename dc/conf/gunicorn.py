# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/1/24
# Version    : 1.0

# gunicorn.py

bind = '0.0.0.0:5001'  # 绑定到的地址和端口号
backlog = 2048  # 监听队列的最大连接数

# Worker 进程
workers = 8  # worker 进程的数量，假设有4个CPU核心
worker_class = 'sync'  # 使用的worker类型，默认为'sync'，可选'gevent', 'eventlet', 'tornado', 'gthread'等
worker_connections = 1000  # 最大客户端并发数量，仅在使用异步worker类时有效
timeout = 120  # 工作进程的超时秒数
keepalive = 2  # 在keep-alive的连接中等待下一个请求的秒数
threads = 2  # 每个worker的线程数，仅适用于gthread worker类

# 安全设置
limit_request_line = 40940  # 请求行的最大大小（字节）
limit_request_fields = 200  # HTTP请求头的最大数量
limit_request_field_size = 81900  # HTTP请求头的最大大小（字节）

# 调试和日志
loglevel = 'info'  # 日志级别，可选项包括'debug', 'info', 'warning', 'error', 'critical'
accesslog = '/var/log/gunicorn_access.log'  # 访问日志文件的路径
errorlog = '/var/log/gunicorn_error.log'  # 错误日志文件的路径
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'  # 访问日志的格式

# 进程命名
proc_name = 'dc2_app'  # 进程名称


# 服务器钩子
# 例如定义在启动时执行的函数
def on_starting(server):
    print("Gunicorn is starting")


# 例如定义在退出时执行的函数
def on_exit(server):
    print("Gunicorn is exiting")
