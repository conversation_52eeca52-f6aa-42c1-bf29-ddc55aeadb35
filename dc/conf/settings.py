# -*- coding: utf-8 -*-

"""
  @desc: 系统配置项
  @author: wjh
  @file: settings.py
  @date: 2022/10/19 下午9:59
"""

import os
import yaml


def deep_merge_dicts(base, override):
    for key, value in override.items():
        if isinstance(value, dict) and key in base:
            base[key] = deep_merge_dicts(base.get(key, {}), value)
        else:
            base[key] = value
    return base


def load_config(env) -> dict:
    # 加载基础配置
    _config_dir = os.path.dirname(os.path.abspath(__file__))
    base_filename = os.path.join(_config_dir, 'setting_base.yaml')
    with open(base_filename, 'r', encoding='utf-8') as base_file:
        base_config = yaml.safe_load(base_file)

    # 根据环境加载相应的配置
    env_file = os.path.join(_config_dir, f'setting_{env}.yaml')
    with open(env_file, 'r', encoding='utf-8') as env_file:
        env_config = yaml.safe_load(env_file)

    # 递归合并环境配置到基础配置中
    config = deep_merge_dicts(base_config, env_config)

    return config


def load_base_config(config_file: str = 'setting_base.yaml') -> dict:
    # 加载基础配置
    _config_dir = os.path.dirname(os.path.abspath(__file__))
    base_filename = os.path.join(_config_dir, config_file)
    with open(base_filename, 'r', encoding='utf-8') as base_file:
        config = yaml.safe_load(base_file)

    return config


def get_settings(key: str, default=None, run_model=None) -> dict:
    """
    获取配置信息
    :param key: config key
    :param default: default value if key not exist
    :param run_model: 配置环境，默认 RUN_MODE
    :return: config value
    """
    base_config = load_base_config()
    run_model = run_model if run_model else base_config.get('run_mode')
    config = load_config(run_model)
    config = config.get(key)
    if not config:
        config = default

    return config


def get_settings2(key, key2='', default=None, run_model=None):
    """
    获取配置信息
    :param key: 1级key
    :param key2: 2级key
    :param default: 默认值
    :param run_model: 配置环境，默认 RUN_MODE
    :return:
    """
    base_config = load_base_config()
    run_model = run_model if run_model else base_config.get('run_mode')
    config = load_config(run_model)
    config = config.get(key)
    if key2:
        config = config.get(key2, default)

    return config


def get_settings3(key, key2, key3, default=None, run_model=None):
    """
    获取配置信息
    :param key: 1级key
    :param key2: 2级key
    :param key3: 3级key
    :param default: 默认值
    :param run_model: 配置环境，默认 RUN_MODE
    :return:
    """
    base_config = load_base_config()
    run_model = run_model if run_model else base_config.get('run_mode')
    config = load_config(run_model)
    config = get_dict_value(config, keys=[key, key2, key3], default_value=default)
    return config


def get_settings_by_keys(keys: [], default=None, run_model=None):
    """
    获取配置信息
    :param keys: keys, 逐级key列表
    :param default: 默认值
    :param run_model: 配置环境，默认 RUN_MODE
    :return:
    """
    base_config = load_base_config()
    run_model = run_model if run_model else base_config.get('run_mode')
    config = load_config(run_model)
    config = get_dict_value(config, keys=keys, default_value=default)
    return config


def get_config(keys, default=None):
    """
    获取配置信息 config.yaml
    :param keys: 键列表，表示访问路径
    :param default: 默认值
    :return:
    """
    config = load_base_config('config.yaml')
    return get_dict_value(config, keys, default)


def get_dict_value(data_dict: dict, keys: list, default_value=None):
    """
    递归获取嵌套字典中的值，若键不存在则返回默认值
    :param data_dict: 输入的字典
    :param keys: 键列表，表示访问路径
    :param default_value: 键不存在时返回的默认值
    :return: 指定键的值，若不存在则返回 default_value
    """
    if not isinstance(data_dict, dict):
        return default_value

    # 如果 keys 是单个字符串，则将其转换为列表
    if isinstance(keys, str):
        keys = [keys]

    current_value = data_dict
    for key in keys:
        if isinstance(current_value, dict) and key in current_value:
            current_value = current_value[key]
        else:
            return default_value  # 如果某个键不存在，返回默认值
    return current_value
