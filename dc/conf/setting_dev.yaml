# 研发环境
mysql:
  userName: root
  password: Db1@neiwang
  host: *************
  port: 3306
  database: doc

mysql_company:
  userName: root
  password: Db1@neiwang
  host: *************
  port: 3306
  database: compony

mysql_jiwei:  # 仅供测试 集微网文章
  userName: root
  password: Db1@neiwang
  host: *************
  port: 3306
  database: jiwei

mongo:
  url: ****************************************************
  database: yuqing
  collection: test-1

elasticsearch:
  hosts:
    - http://*************:9200

elasticsearch_elk:
  hosts:
    - *************************************************

kafka:
  bootstrap.servers: *************:9092
  security.protocol: SASL_PLAINTEXT
  sasl.mechanisms: PLAIN
  sasl.username: elk
  sasl.password: elk@q6wcbkj

redis:
  host: *************
  port: 6379
  password: i<PERSON><PERSON>@dev

beanstalk:
  host: *************
  port: 11300
  encoding: utf-8
  use: dc
  watch: dc

webdriver:
  host: *************:4444

weixin_gzh:  # 公众号配置
  host:
    - http://************:23230/api
    - http://************:23230/api
  list_update_days: 1  # 公众号文章检查更新天数，几天检查更新一次 0 每天检查（取消使用）
  list_update_hours: 3  # 公众号文章检查更新小时数，几小时检查更新一次
  list_update_stop: 3  # 公众号文章检查更新几天内文章，天
  list_sleep: 2  # 历史文章列表拉取间隔 秒
  article_sleep: 2  # 公众号文章拉取间隔 秒
  read_sleep: 1  # 阅读数量间隔 秒
  read_update_days: 3  # 阅读数量更新天数 天，如 7天内更新阅读数量
  read_update_interval: 1  # 阅读数量更新间隔天数 天，如 第1天，2天更新一次
  analyse_sleep: 1  # 文章分析间隔 秒
  dc_config:  # 文章内容抓取配置
    content_xpath: //div[@id="js_content" or @id="js_share_notice" or @class="wx_video_play_opr" or @class="rich_media_content"]
  bianjifagao_index: dc_bianjifagao  # 编辑发稿索引名
  # bianjifagao_tags: ["编辑发稿"]  # 编辑发稿标签
  machine_list:   # 采集工具机器列表
    - '192.168.1.'
    - '192.168.2.'
    - '192.168.10.'
  machine_port: 23235 # 采集工具端口

qianxun:  # 千寻微信框架配置
  host: http://*************:7777/DaenWxHook/httpapi/
  channel:
    - wechat
    - mail  # 开启通道 wechat mail
  check_sleep: 1200  # 每个消息类型检测间隔 秒
  time_sleep: 5  # 间隔 秒
  aims: 38890119411@chatroom  # 发送到的对方id
  smtp_host: smtpdm.aliyun.com  # smtp host
  smtp_port: 80  # smtp 端口
  smtp_user: <EMAIL>  # smtp 发送人 email
  smtp_user_name: 小微  # smtp 发送人 名称
  smtp_password: JiWeiWang1234  # smtp 密码
  mail_to: <EMAIL>;<EMAIL>  # 接收人 ,如果多个;分类分隔

api_check:  # api 检测
  check_sleep_time: 60  # 每次检查接口状态时间间隔  秒
  api_sleep_time: 2  # 单个api检测间隔时间 秒
  apis:
    - name: python uwsgi 接口
      url: http://*************/dc/status
      params: {}
      timeout: 3000
      demo: 备注
    - name: soya 接口
      url: http://*************/soya/apps/doc/server/?action=test.status
      params: {}
      timeout: 3000
      demo: 备注

qiniu: # 七牛
  bianjifagao: # 编辑发稿
    document_name: 'tmp3/'

chatgpt:
  gpt_keys:
    - ***************************************************  # api4-1
    # - ***************************************************  # api4-2
  params:
    # api_key: "***************************************************"
    # proxy: 'http://*************:7890'
    api_base: http://**************:3002/v1

jiwei_ai:  # jiwei ai 配置
  host: http://*************:5000
  keywords:  # 拆词配置
    top: 200  # 按照权重取top条

elk:  # elk 配置
  blacklist:
    - dc_gzh_history  # 黑名单
  elasticsearch: test  # es 保存环境
  es_index: elk_log_prod_python,elk_log_prod_gzh,elk_log_prod_ai,elk_log_prod_php  # 默认索引
  log_monitor_alert_api:  # 日志监控api
    host: http://data.ijiwei.com/dc_ali/
    detail_host: http://*************:5001/
    error_service_count: /elk/error_service_count  # 服务类型统计
    error_type_count: /elk/error_type_count  # 错误类型统计
  quiet_period:   # 静默时段
    - start: "00:00:01"
      stop: "07:30:00"
    - start: "22:30:10"
      stop: "23:59:59"
  repeat_time: 1200  # 去重时间 秒
  alert_channel:  # 报警通道
    log:
      # "1": '56138480743@chatroom'  # 微信
      "2": ae1c23b25442d0c702a994ca4ea5de72d3525564690bf1cbdfcc89b31428e95c  # 钉钉
      # "3": '<EMAIL>,<EMAIL>'  # 邮件
    data:
      # "1": '56138480743@chatroom'  # 微信
      "2": 0dbc2690c86b7f22d61073acc13b0fa07f760953efde29f6ce0b0e6db90a14bf  # 钉钉
      # "3": '<EMAIL>,<EMAIL>'  # 邮件
  mail:  # 邮箱配置
    mail_mailer: smtp
    mail_host: smtpdm.aliyun.com
    mail_port: 465
    mail_username: <EMAIL>
    mail_password: JiWeiWang1234
    mail_encryption: ssl
    mail_from_address: <EMAIL>
    mail_from_name: 小微

# ai
getArea: http://*************:6000/predict/DJArea # 日刊地区识别
getIndustry: http://*************:6000/predict/DJIndustry # 日刊行业识别
getInformationType: http://*************:6000/predict/DJInfoType  # 日刊类型分类
filterICList: http://*************:6000/predict/similar # 相似度

hot_unique_param: 1.2
hot_param: 1.2
