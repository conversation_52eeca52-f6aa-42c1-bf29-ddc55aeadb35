import sys
import os

# 项目根目录
ROOT_PATH = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# 公众号
DC_GZH = f'dc_gzh'  # 公众号
DC_GZH_READ = f'dc_gzh_read'  # 公众号阅读数量
DC_GZH_ANALYSE = f'dc_gzh_analyse'  # 公众号文章分析
DC_GZH_PUSH = f'dc_gzh_push'  # 公众号文章推送
DC_GZH_PUSH_LOG = f'dc_gzh_push_log'  # 公众号文章推送历史

# AI URL
JIWEI_AI_SIMILAR = f'/predict/similar/'  # 相似度
JIWEI_AI_KEYWORDS = f'/predict/keywords/'  # 关键词拆分
JIWEI_AI_ERROR_CORRECTION = f'/predict/errorCorrection/'  # 文本纠错
JIWEI_AI_TEXT_QUALITY = f'/predict/textQuality/'  # 文章质量
JIWEI_AI_DJINFOTYPE = f'/predict/DJInfoType/'  # 日刊类型分类
JIWEI_AI_DJINDUSTRY = f'/predict/DJIndustry/'  # 日刊行业识别
JIWEI_AI_DJAREA = f'/predict/DJArea/'  # 日刊地区识别
JIWEI_AI_NEWSKG = f'/predict/NewsKG/'  # jiwei-GPT知识库

# MODEL ID
SIMILAR_MODEL_ID = 9  # 相似度
SEPARATE_WORD_MODEL_ID = 11  # 拆词模型
ERROR_CORRECTION_MODEL_ID = 12  # 文本纠错
TEXT_QUALITY_MODEL_ID = 13  # 文章质量模型
JIWEI_AI_DJINFOTYPE_MODEL_ID = 4  # 日刊类型分类
JIWEI_AI_DJINDUSTRY_MODEL_ID = 6  # 日刊行业识别
JIWEI_AI_DJAREA_MODEL_ID = 7  # 日刊地区识别
JIWEI_AI_NEWSKG_MODEL_ID = 10  # jiwei-GPT知识库

ZHIPU_TOKEN_COUNT = f"https://open.bigmodel.cn/api/v3/tokenizer-api/chatglm_130b_34121693819088333"

# kafka
KAFKA_TOPIC_DC_ALL = 'dc_all'  # kafka dc
KAFKA_TOPIC_DC_YQ = 'dc_yq'  # kafka dc_yq
KAFKA_TOPIC_YUQING = 'yuqing'  # kafka yuqing
KAFKA_TOPIC_DC_WECHAT = 'dc_wechat'  # kafka dc_wechat
KAFKA_TOPIC_DC_JIWEI = 'dc_jiwei'  # kafka dc_jiwei
KAFKA_TOPIC_ESID_KDY = '_esid'  # kafka dc_wechat

# gpt
OPENAI_KEY40_PROD = "***************************************************"
