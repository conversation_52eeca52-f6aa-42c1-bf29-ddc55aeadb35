# 基础配置信息
run_mode: "test"  # 运行模式 dev 研发 test 测试 pre 预上线 prod 生产

# 代理
proxy:
  p01:  # 代理编号
    type: proxy-pool  # 代理类型 ， proxy-pool ip代理池 static-proxy 静态代理  vpn 国外vpn
    region: gn  # 区域 ， gn 国内 gw 国外
    params:  # 参数
      username: t10565224196293
      password: nxdhhkof
      http:
        host: f753.kdltps.com
        port: 15818
      socks:
        host: f753.kdltps.com
        port: 20818

third_app:  # 第三方应用
  fw3sh2nlrtdefy96:  # 泛微
    appid: fw3sh2nlrtdefy96
    app_secret: 3v5wc9hrlaefb0noyz1ujmpdgkqi68xs
  chipsuglpfmnh1do:  # 爱集微
    appid: chipsuglpfmnh1do
    app_secret: 2cojfyk0vx1an4quie3wglr8h97spmt6
  test5bz6415ksntq:  # test 仅供测试
    appid: test5bz6415ksntq
    app_secret: test5m1g6qit9l73puabyhzj8rc0wv2x

mail:   # 邮箱配置
  mail_mailer: smtp
  mail_host: smtpdm.aliyun.com
  mail_port: 465
  mail_username: <EMAIL>
  mail_password: JiWeiWang1234
  mail_encryption: ssl
  mail_from_address: <EMAIL>
  mail_from_name: 小微

recruit:  # 招聘
  proxy:  # 代理配置
    proxy_host: f753.kdltps.com
    proxy_port: 15818
    proxy_user: t10565224196293
    proxy_pass: nxdhhkof

zhipu:  # 智谱
  api_key: d559f1acdedcb93e0bd5c4ef722f24a4.m8S3aK5zAzgZk8Q3
  model_api_url: https://maas.aminer.cn/api/paas/v3/model-api
  token_length: 1000  # 最大token 长度

qiniu: # 七牛
  pic: # 图库
    access_key: '99vFAJy1wVeXiJdSV2BB-VHqOhgKrdRZydXfUv58'
    secret_key: '0_U300euhKNww5ziodpEZmQ2l8rMVRlKnNwqEywx'
    image_url: 'https://s.laoyaoba.com/'
    document_name: 'jwImg/'
    bucket: 'jiwei-images'
  bianjifagao: # 编辑发稿
    access_key: '99vFAJy1wVeXiJdSV2BB-VHqOhgKrdRZydXfUv58'
    secret_key: '0_U300euhKNww5ziodpEZmQ2l8rMVRlKnNwqEywx'
    image_url: 'https://s.laoyaoba.com/'
    document_name: 'tmp3/'
#    document_name: 'bianjifagao/'
    bucket: 'jiwei-images'
    image_filter: # 图片过滤器
      min_width: 100  # 过滤器，最小宽度
      min_height: 100 # 过滤器，最小高度
      min_size: 100   # 过滤器，最小大小
  trade: # 贸易管制
    access_key: '99vFAJy1wVeXiJdSV2BB-VHqOhgKrdRZydXfUv58'
    secret_key: '0_U300euhKNww5ziodpEZmQ2l8rMVRlKnNwqEywx'
    image_url: 'https://privte.laoyaoba.com/'
    document_name: 'infoplat/trade/'
    bucket: 'jiwei-private-video'
  dc: # 数据采集通用目录
    access_key: '99vFAJy1wVeXiJdSV2BB-VHqOhgKrdRZydXfUv58'
    secret_key: '0_U300euhKNww5ziodpEZmQ2l8rMVRlKnNwqEywx'
    image_url: 'https://s.laoyaoba.com/'
    document_name: 'tmp3/dc/'
#    document_name: 'dc/'
    bucket: 'jiwei-images'
fengchao:
  base_url: 'http://192.168.1.233:6000'
  api_key: 'e1bc7a92a32e11ef9c7280615f1e1c07'
  secret_key: '2fc28a69d0e8480188386031a226fa2f'
  convert_file: "http://192.168.1.233:6000/predict/ConvertFile/"
