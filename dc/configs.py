from urllib.parse import quote_plus as urlquote
from dc.conf.settings import get_settings

config = get_settings('mysql')

HOST = config.get('host')   # '*************'
PORT = config.get('port')   # '3306'
DATABASE = config.get('database')   # 'doc_250'
USERNAME = config.get('userName')  # 'root'
PASSWORD = config.get('password')  # 'Db1@neiwang'

DB_URI = "mysql+pymysql://{username}:{password}@{host}:{port}/{db}?charset=utf8mb4".format(username=USERNAME,password=urlquote(PASSWORD), host=HOST,port=PORT, db=DATABASE)

SQLALCHEMY_DATABASE_URI = DB_URI
SQLALCHEMY_TRACK_MODIFICATIONS = False
SQLALCHEMY_ECHO = False
