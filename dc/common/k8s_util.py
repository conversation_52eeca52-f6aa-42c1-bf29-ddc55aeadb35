# -*- coding:utf-8 -*-
# @Function  : k8s_util
# <AUTHOR> wjh
# @Time      : 2025-01-02
# Version    : 1.0

import inspect
import os
import socket


def get_hostname():
    """ 获取服务器的主机名 """
    # 获取服务器的主机名
    hostname = socket.gethostname()
    # print("服务器名称:", hostname)
    return hostname


def get_full_hostname():
    """ 获取完整的域名 """
    fqdn = socket.getfqdn()
    # print("完整域名:", fqdn)
    return fqdn


def get_script_info(script_path):
    """
    获取传入脚本的绝对路径
    调用 helper 中的函数，传入当前脚本的路径
    helper.get_script_info(__file__)
    :param script_path: 传入当前脚本的路径
    :return:
    """
    # 获取传入脚本的绝对路径
    abs_path = os.path.abspath(script_path)
    script_name = os.path.basename(script_path)

    # print(f"脚本路径: {abs_path}")
    # print(f"脚本名称: {script_name}")
    return script_name, abs_path


def get_called_script_info(depth=1):
    """
    获取多层调用中的脚本路径
    :param depth: 指定的层级
    :return:
    """
    # 获取调用栈信息
    caller_frame = inspect.stack()[depth]
    caller_file = caller_frame.filename

    # 获取绝对路径和文件名
    abs_path = os.path.abspath(caller_file)
    script_name = os.path.basename(caller_file)

    # print(f"脚本路径: {abs_path}")
    # print(f"脚本名称: {script_name}")
    return script_name, abs_path


def get_process_name(name=None):
    """ 获取进程名称 """
    process_name = get_process_name_by_env(name)
    if not process_name:
        pid = os.getpid()
        process_name = get_process_name_by_pid(pid)

    return process_name


def get_process_name_by_env(name=None):
    # 获取 Supervisor 中的进程名称
    if not name:
        name = "SUPERVISOR_PROCESS_NAME"
    process_name = os.getenv(name)
    # print(f"当前进程名称: {process_name}")
    return process_name


def get_process_name_by_pid(pid):
    """
    依赖于系统工具 ps
    :param pid:
    :return:
    """
    import subprocess

    try:
        # 使用 ps 命令获取进程名称
        result = subprocess.check_output(['ps', '-p', str(pid), '-o', 'comm='])
        return result.decode().strip()
    except Exception as e:
        print(f"无法获取进程名称: {e}")
        return None

