import time
from datetime import datetime

import parsel

from dc.common.dc_web_analyzer import Analyzer
from dc.common.dc_web_advanced_analyzer import AdvancedAnalyzer
from dc.common.dc_web_analyzer_util import AnalyzerUtil
from dc.common.dc_web_normal_analyzer import NormalAnalyzer
from dc.models.model import <PERSON>Site<PERSON>ist, DCSiteMainTask, DCSiteTask
from dc.common.dc_web_analyzer_pre_processing import pre_processing
from dc.common.webdriver_util import get_remote_webdriver_web, get_remote_webdriver_proxy
from dc.conf.settings import get_settings
import json
from dc.tests.browser import Browser
import random
import unittest
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from webdriver_manager.chrome import ChromeDriverManager
from selenium import webdriver as wd
from selenium.webdriver.chrome.service import Service
from urllib.parse import urlparse
from faker import Faker


def getDriver(is_headless=False):
    option = wd.ChromeOptions()
    fake = Faker()
    arguments = [
        "no-sandbox",
        "--disable-extensions",
        # '--disable-gpu',
        # f'User-Agent="{fake.chrome()}"',
        "window-size=1920x3000",
        "start-maximized",
        'cache-control="max-age=0"'
        "disable-blink-features=AutomationControlled"
    ]
    for argument in arguments:
        option.add_argument(argument)
    if is_headless:
        option.add_argument("--headless")
    option.add_experimental_option('excludeSwitches', ['enable-automation'])
    # option.page_load_strategy = 'eager'
    webdriver = wd.Chrome(service=Service(ChromeDriverManager().install()),
                          options=option)
    webdriver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
        "source": """
        Object.defineProperty(navigator, 'webdriver', {
          get: () => false
        })
      """
    })
    return webdriver


class ParamsError(Exception):

    def __init__(self, msg):
        self.msg = msg

    def __str__(self):
        return self.msg


class AnalyzerWork:
    """
    静态网页分析工作层，根据具体业务进行抓取
    """
    webdriver_url = get_settings('webdriver')['host']
    MAX_NUM = 2048  # 当无限爬取时指定的数量
    type2str = {
        1: "xpath",
        2: "css",
        3: "re",
        4: "jsPath"
    }

    def __init__(self, dc_site_list: DCSiteList, dc_site_main_task: DCSiteMainTask,
                 old_dc_site_tasks: list[DCSiteTask]) -> None:
        """
        初始化作业，处理DCSiteList和DCSiteMainTask
        :param dc_site_list: DCSiteList from mysql
        :param dc_site_main_task: DCSiteMainTask from mysql
        """
        self.dc_site_list = dc_site_list
        self.dc_site_main_task = dc_site_main_task
        self.extra = {}
        if self.dc_site_list.extra:  # 将extra json字符串反序列化成dict对象
            self.extra: dict = json.loads(self.dc_site_list.extra)
        self.relative = self.extra.get("relative", "")
        if not self.relative:  # 如果没有相对路径，则默认为detailUrl
            if "?" in self.dc_site_list.detailUrl:
                self.relative = self.dc_site_list.detailUrl.split("?")[0]
            else:
                self.relative = self.dc_site_list.detailUrl
        self.list_selector = self.dc_site_list.ListSelector
        if not self.list_selector:  # 适配老版本
            self.list_selector = self.dc_site_list.crawlRule
        if self.dc_site_main_task.keyWord and "%s" in self.dc_site_list.detailUrl:  # 将链接中的"%s"替换成keyword
            self.dc_site_list.detailUrl = AnalyzerUtil.url_format(
                self.dc_site_list.detailUrl, r"%s", self.dc_site_main_task.keyWord)
        self.is_single_page = False  # 是否单页爬取
        if self.dc_site_list.dcType == 2 or (self.dc_site_list.listPagiNatePageNum == 0
                                             and self.dc_site_list.listPagiNatePageOffset == 0 and self.dc_site_list.listDcMethod == 1):
            self.is_single_page = True
        self.old_links = set()  # 已有的链接
        for element in old_dc_site_tasks:
            self.old_links.add(element.url)
        # 初始化结果列表、selenium驱动器
        self.res = []
        self.driver = None
        self.interval = 8  # 操作间的间隔
        self.data_record = False  # 记录是否抓到数据

    def list_dc_method4and2(self) -> Analyzer:  # 当listDcMethod == 4 and listDcMethod == 2时执行
        if "browserName" in self.extra:  # 判断使用哪个浏览器
            if "proxy" in self.extra and "proxy_no" in self.extra:
                if self.extra.get("proxy"):
                    self.driver = get_remote_webdriver_proxy(webdriver_url=AnalyzerWork.webdriver_url,
                                                             browser=Browser.Firefox,
                                                             proxy_id=self.extra.get("proxy_no"))
            else:
                self.driver = get_remote_webdriver_web(
                    AnalyzerWork.webdriver_url, Browser.Firefox)  # 初始化驱动
        else:
            if "proxy" in self.extra and "proxy_no" in self.extra:
                if self.extra.get("proxy"):
                    self.driver = get_remote_webdriver_proxy(webdriver_url=AnalyzerWork.webdriver_url,
                                                             browser=Browser.Chrome,
                                                             proxy_id=self.extra.get("proxy_no"),
                                                             options={"experimental_options": {
                                                                 'excludeSwitches': ['enable-automation']}})
            else:
                self.driver = get_remote_webdriver_web(
                    AnalyzerWork.webdriver_url, Browser.Chrome,
                    options={"experimental_options": {'excludeSwitches': ['enable-automation']}})
        # self.driver = getDriver(is_headless=False)  # 本地调试
        self.driver.set_page_load_timeout(20)  # 设置最大页面加载时间
        pagination_type = self.extra.get('nextPage', "")
        if not pagination_type and not self.dc_site_list.listUrl:  # 单页爬取逻辑
            self.is_single_page = True
        # 判断分页器类型
        if not pagination_type:
            if ("scrollNextPage" in self.extra or self.dc_site_list.listPagiNatePageNum == 0
                    or self.dc_site_list.listPagiNatePageOffset == 0):
                line = self.extra.get("scrollNextPage", "")
                if not line:
                    pagination_type = "scroll"
                else:
                    self.dc_site_list.listUrl = line
                    pagination_type = "line"
            else:
                if "%v" in self.dc_site_list.listUrl:
                    pagination_type = "redirect"
                else:
                    pagination_type = "multi"
        # 判断是否需要前置处理
        pre_callback = None
        if "preProcessing" in self.extra:
            def pre_callback(pre_driver):
                info = {
                    'url': self.dc_site_list.listUrl,
                    'keyword': self.dc_site_main_task.keyWord
                }
                scipys = self.extra.get('preProcessing', "")
                pre_processing(pre_driver, scripys=scipys, info=info)
        return AdvancedAnalyzer(web_driver=self.driver,
                                analyzer_url=self.dc_site_list.detailUrl,
                                pagination_selector=self.dc_site_list.listUrl,
                                pagination_type=pagination_type,
                                selector=self.list_selector,
                                rule=AnalyzerWork.type2str[self.dc_site_list.crawlRuleType],
                                pre_processing=pre_callback,
                                interval=self.interval,
                                start_page=int(self.dc_site_list.listPagiNatePageNum) - 1,
                                page_offset=self.dc_site_list.listPagiNatePageOffset,
                                is_single_page=self.is_single_page)

    def list_dc_method1(self) -> Analyzer:  # 当listDcMethod == 1时执行
        proxy_no = ""
        if "proxy" in self.extra and "proxy_no" in self.extra:
            if self.extra.get("proxy"):
                proxy_no = self.extra.get("proxy_no")
        return NormalAnalyzer(analyzer_url=self.dc_site_list.detailUrl,
                              page_split_url=self.dc_site_list.listUrl,
                              selector=self.list_selector,
                              rule=AnalyzerWork.type2str[self.dc_site_list.crawlRuleType],
                              interval=self.interval,
                              start_page=int(self.dc_site_list.listPagiNatePageNum) - 1,
                              page_offset=self.dc_site_list.listPagiNatePageOffset,
                              is_single_page=self.is_single_page,
                              proxy_no=proxy_no)

    def get_analyzer(self) -> Analyzer | None:
        """
        获取一个分析器
        :return: 分析器对象或者为None
        """
        if self.dc_site_list.listDcMethod == 4:
            return self.list_dc_method4and2()
        elif self.dc_site_list.listDcMethod == 1:
            return self.list_dc_method1()
        elif self.dc_site_list.listDcMethod == 2:
            return self.list_dc_method4and2()
        return None

    def work(self):
        """
        执行后开始爬取数据
        :return:
        """
        analyzer = self.get_analyzer()  # 根据site_list的信息获取一个分析器
        if not analyzer:
            with open('../commands/logs/crawl_error-id.log', 'a') as f:
                f.write(str(self.dc_site_list.id) + ",")
        if self.dc_site_list.MaxResult:  # 爬取最新的n条数据
            self.res = self.get_recent_data_by_num(
                analyzer, self.dc_site_list.MaxResult * 10)
        elif self.dc_site_list.MaxDay:  # 爬取最近n天的数据
            self.res = self.get_recent_data_by_day(
                analyzer, self.dc_site_list.MaxDay)
        else:  # 爬取所有数据
            self.res = self.get_recent_data_by_num(
                analyzer, AnalyzerWork.MAX_NUM)
        if self.driver:  # 关闭webdriver连接
            self.driver.quit()
        # if not self.data_record and not self.is_single_page:  # 如果没有抓取到数据，则单页抓取
        #     self.is_single_page = True
        #     analyzer = self.get_analyzer()  # 根据site_list的信息获取一个分析器
        #     if not analyzer:
        #         with open('../commands/logs/crawl_error-id.log', 'a') as f:
        #             f.write(str(self.dc_site_list.id) + ",")
        #     if self.dc_site_list.MaxResult:  # 爬取最新的n条数据
        #         self.res = self.get_recent_data_by_num(
        #             analyzer, self.dc_site_list.MaxResult)
        #     elif self.dc_site_list.MaxDay:  # 爬取最近n天的数据
        #         self.res = self.get_recent_data_by_day_new(
        #             analyzer, self.dc_site_list.MaxDay)
        #     else:  # 爬取所有数据
        #         self.res = self.get_recent_data_by_num(
        #             analyzer, AnalyzerWork.MAX_NUM)
        #     if self.driver:  # 关闭webdriver连接
        #         self.driver.quit()
        # 打印爬取信息日志
        # if len(self.res) == 0:
        #     with open('logs/crawl_error-id.log', 'a') as f:
        #         f.write(str(self.dc_site_list.id) + ",")
        # with open('../commands/logs/crawl_out.log', 'a') as f:
        #     crawl_out = "id=%d successful! len(res)=%d len(set(res))=%d is_max=%s" % (
        #         self.dc_site_list.id, len(self.res), len(set(self.res)), analyzer.is_max)
        #     f.write(crawl_out + "\n")

    @property
    def crawl_data(self) -> list[DCSiteTask]:
        return self.res

    def format_data(self, site_list_htmls: list[str]) -> list[DCSiteTask]:
        """
        将爬取到字符串格式化后输出
        :return: list[DCSiteTask]
        """
        if not site_list_htmls:
            return []
        # 根据选择器从html文本中提取对应的内容
        ret = AnalyzerUtil.data_work(site_list_htmls, AnalyzerWork.type2str[self.dc_site_list.crawlRuleType], {
            "image": self.dc_site_list.CoverImageSelector,
            "title": self.dc_site_list.crawlRuleTtile,
            "url": self.dc_site_list.crawlRule,
        })
        # 如果是旧版，则需要尝试从深层寻找a标签
        if self.dc_site_list.listDcMethod == 1 or self.dc_site_list.listDcMethod == 2:
            if "aPath" in self.extra:
                ret["url"] = \
                    AnalyzerUtil.data_work(site_list_htmls, AnalyzerWork.type2str[self.dc_site_list.crawlRuleType], {
                        "url": self.extra["aPath"],
                    })["url"]
            else:
                ret["url"] = AnalyzerUtil.data_work(site_list_htmls, "css", {
                    "url": 'a',
                })["url"]
        res = []
        root_url = ""
        try:
            parse_url = urlparse(self.dc_site_list.detailUrl)
            root_url = parse_url.scheme + "://" + parse_url.hostname
        except Exception as e:
            pass
        if not root_url:
            root_url = self.dc_site_list.siteUrl
        for i in range(len(site_list_htmls)):
            url = AnalyzerUtil.get_attribute(ret["url"][i], "href")
            url = AnalyzerUtil.fix_url(url, root_url, self.relative)
            title = AnalyzerUtil.get_attribute(ret["title"][i], "title")
            if not title:
                title = AnalyzerUtil.get_inner_text(ret["url"][i])
            title = AnalyzerUtil.prettift_text(title.strip())  # 美化text
            if len(title) > 100:
                title = title[:100] + "..."
            image = AnalyzerUtil.get_attribute(ret["image"][i], "src")
            if not image:
                # image = AnalyzerUtil.get_inner_text(ret["image"][i])
                image = ""
            if image:
                image = AnalyzerUtil.fix_url(image, root_url, self.relative)
            if url == self.dc_site_list.siteUrl or (url + "/") == self.dc_site_list.siteUrl or (
                    self.dc_site_list.siteUrl + "/") == url or url == root_url or (
                    url + "/") == root_url or (root_url + "/") == url:
                continue
            if len(url) >= 499:
                continue
            res.append(DCSiteTask(siteListId=self.dc_site_list.id,
                                  mainTaskId=self.dc_site_main_task.id,
                                  analysisStatus=0,
                                  baseUrl=self.dc_site_list.siteUrl,
                                  coverImg=image,
                                  retryTime=0,
                                  taskStatus=0,
                                  title=title,
                                  url=url,
                                  mongoCollection='',
                                  mongoKey='',
                                  esIndex=self.dc_site_list.sourceCategory,
                                  esId="",
                                  keyword=self.dc_site_main_task.keyWord,
                                  createdAt=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                  ))
        return res

    def test(self):  # 测试
        # self.is_single_page = True
        if not self.dc_site_list.MaxResult:
            self.dc_site_list.MaxResult = 50
        self.work()

    def test_web(self):  # web测试
        # self.is_single_page = True
        self.dc_site_list.MaxResult = 50
        self.work()

    def get_recent_data_by_num(self, analyzer: Analyzer, num: int) -> list[DCSiteTask]:
        """
        获取最近num条数据
        :Args:
            - analyzer - 分析器
            - num - 要获取的最近条数
        """
        res = []
        temp = []
        page_cnt = 10
        temp_cnt = page_cnt
        checked = False
        for element in analyzer.list(num):
            self.data_record = True  # 记录已经爬取到了数据，用于日志报警
            if analyzer.page_size and not checked:
                checked = True
                page_cnt = analyzer.page_size
                temp_cnt = page_cnt
            if not temp_cnt:
                temp = self.format_data(temp)
                flag = False
                for item in temp:
                    if item.url not in self.old_links:
                        self.old_links.add(item.url)
                        flag = True
                        res.append(item)
                        if self.dc_site_list.MaxResult and len(res) >= self.dc_site_list.MaxResult:  #
                            # 如果爬到了指定数量的数据，则直接返回
                            return res
                if not flag:  # 如果这一页没有发生数据变动，则直接终止
                    return res
                temp_cnt = page_cnt
                temp = []
            temp.append(element)
            temp_cnt -= 1
        temp = self.format_data(temp)
        for item in temp:
            if item.url not in self.old_links:
                self.old_links.add(item.url)
                res.append(item)
                if self.dc_site_list.MaxResult and len(res) >= self.dc_site_list.MaxResult:
                    return res
        return res

    def get_recent_data_by_day(self, analyzer: Analyzer, day_num: int) -> list:
        """
        最近num天的数据
        :Args:
            - analyzer - 分析器
            - num - 要获取的最近天数
        """
        day_seconds = day_num * 86400
        res = []
        temp = []
        page_cnt = 10
        temp_cnt = page_cnt
        checked = False
        for element in analyzer.list(AnalyzerWork.MAX_NUM):  # 无限向后遍历
            self.data_record = True
            if analyzer.page_size and not checked:
                checked = True
                page_cnt = analyzer.page_size
                temp_cnt = page_cnt
            if not temp_cnt:
                temp_format = self.format_data(temp)
                temp_time_list = []
                if not self.dc_site_list.TimeSelector:
                    raise ParamsError("当你设置最大日期时，需要配置对应时间选择器")
                if "|" in self.dc_site_list.TimeSelector:
                    select = self.dc_site_list.TimeSelector.split("|")
                    for element2 in select:
                        t = AnalyzerUtil.data_work(
                            temp, AnalyzerWork.type2str[self.dc_site_list.crawlRuleType],
                            {"time": element2.strip()})['time']
                        temp_time_list.append(t)
                else:
                    temp_time_list = [AnalyzerUtil.data_work(
                        temp, AnalyzerWork.type2str[self.dc_site_list.crawlRuleType],
                        {"time": self.dc_site_list.TimeSelector})['time']]
                flag = False
                for i in range(len(temp)):
                    time_list = ["", 2]
                    for temp_time in temp_time_list:
                        selector_text = parsel.Selector(text=temp_time[i])
                        text_list = selector_text.xpath(".//text()").getall()
                        if len(text_list) == 0:
                            continue
                        text = text_list[0]
                        time_list = AnalyzerUtil.deal_format_date(text)  # 处理时间文本，将其转为时间戳
                        if time_list[1] == 1:
                            break
                    if time_list[1] == 2:
                        continue
                    time_str = time_list[0]
                    date_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                    timestamp = date_obj.timestamp()
                    now = time.time()
                    if i < len(temp_format) and temp_format[
                        i].url not in self.old_links and now - timestamp <= day_seconds:
                        self.old_links.add(temp_format[i].url)
                        flag = True
                        res.append(temp_format[i])
                if not flag:  # 如果这一页没有发生数据变动，则直接终止
                    return res
                temp_cnt = page_cnt
                temp = []
            temp.append(element)
            temp_cnt -= 1
        temp_format = self.format_data(temp)
        temp_time_list = []
        if "|" in self.dc_site_list.TimeSelector:
            select = self.dc_site_list.TimeSelector.split("|")
            for element2 in select:
                t = AnalyzerUtil.data_work(
                    temp, AnalyzerWork.type2str[self.dc_site_list.crawlRuleType],
                    {"time": element2.strip()})['time']
                temp_time_list.append(t)
        else:
            temp_time_list = [AnalyzerUtil.data_work(
                temp, AnalyzerWork.type2str[self.dc_site_list.crawlRuleType], {"time": self.dc_site_list.TimeSelector})[
                                  'time']]
        for i in range(len(temp)):
            time_list = ["", 2]
            for temp_time in temp_time_list:
                selector_text = parsel.Selector(text=temp_time[i])
                text_list = selector_text.xpath(".//text()").getall()
                if len(text_list) == 0:
                    continue
                text = text_list[0]
                time_list = AnalyzerUtil.deal_format_date(text)  # 处理时间文本，将其转为时间戳
                if time_list[1] == 1:
                    break
            if time_list[1] == 2:
                continue
            time_str = time_list[0]
            date_obj = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            timestamp = date_obj.timestamp()
            now = time.time()
            if i < len(temp_format) and temp_format[i].url not in self.old_links and now - timestamp <= day_seconds:
                self.old_links.add(temp_format[i].url)
                res.append(temp_format[i])
        return res

    def __del__(self):
        try:
            if self.driver:
                self.driver.quit()
        except Exception:
            pass
