import datetime
import hashlib
import json
import re
import sys
import os
from typing import Di<PERSON>, <PERSON><PERSON>, List
import time
from urllib.parse import urlparse

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__)))))
from selenium.webdriver.remote.webelement import WebElement


class Utils:

    def __init__(self):
        pass

    @staticmethod
    def showDateTime():
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    @staticmethod
    def log_print(logger, msg):
        """
        输出打印日志
        :param logger:
        :param msg:
        :return:
        """
        if logger is not None:
            logger.info(msg)
        else:
            print(msg)

    @staticmethod
    def exec_time(**kwargs):
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        echo = kwargs.get('echo')
        logger = kwargs.get('logger')
        if end_time is None:
            end_time = time.time()
        exec_time = end_time - start_time

        if echo is None:
            echo = True

        if echo:
            start_time2 = datetime.datetime.fromtimestamp(start_time)
            end_time2 = datetime.datetime.fromtimestamp(end_time)
            Utils.log_print(logger, f"start_time: {start_time} end_time: {end_time} exec_time: {exec_time}")
            Utils.log_print(logger, f"start_time2：{start_time2} end_time2: {end_time2} exec_time: {exec_time}")
        return exec_time

    @staticmethod
    def showTime():
        return time.strftime("%H:%M:%S", time.localtime())

    @staticmethod
    def timestamp2str(timestamp):
        """
        把时间戳转化为字符串显示
        :param timestamp:
        :return:
        """
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))

    @staticmethod
    def time2str(param_time=None):
        """
        把时间戳转化为字符串显示
        :param param_time:
        :return:
        """
        if param_time is None:
            param_time = time.time()

        return Utils.timestamp2str(param_time)

    @staticmethod
    def str2timestamp(param_time: str, fmt=None):
        """
        把时间字符串戳转化为时间戳
        :param param_time: 时间字符串，如 2022-10-20 09:44:24
        :param fmt:
        :return:
        """
        # str = '2022-10-20 09:44:24'
        if fmt is None:
            fmt = '%Y-%m-%d %H:%M:%S'

        dt = datetime.datetime.strptime(param_time, fmt)
        return int(dt.timestamp())

    @staticmethod
    def showTime2():
        t = time.time()
        xt = str(int(round(t * 1000)))
        # print(t)
        # print(xt)
        # print(xt[-3:])

        return time.strftime("%H:%M:%S", time.localtime()) + ' ' + xt[-3:]

    @staticmethod
    def millisecond_time() -> int:
        """
        获取毫秒时间戳
        :return: int
        """
        t = time.time()
        return int(round(t * 1000))

    @staticmethod
    def time_ms() -> int:
        """
        获取毫秒时间戳
        :return: int
        """
        t = time.time()
        return int(round(t * 1000))

    @staticmethod
    def time_second() -> int:
        """
        获取秒时间戳
        :return: int
        """
        t = time.time()
        return int(t)

    @staticmethod
    def time_days(days, start=None, only_day=False) -> str:
        """
        计算当前日期几天前日期
        调用示例：
            Utils.time_days(2, time.time())
            Utils.time_days(2, int(time.time()), only_day=True)
            Utils.time_days(2, '2022-10-20')
            Utils.time_days(2, '2022-10-20 09:44:24', only_day=True)
        :param days: 天数
        :param start: 如  2022-10-18 00:00:00  2022-10-18 时间戳
        :return: 计算后日期，如 2022-10-18 00:00:00
        """
        print(f"start_time: {start}")
        fmt = '%Y-%m-%d %H:%M:%S'
        fmt2 = '%Y-%m-%d'

        start_time = None
        if isinstance(start, float) or isinstance(start, int):  # 时间戳, 秒 1666231555.513451 or 1666231555
            # print('---type int--')
            start_time = datetime.datetime.fromtimestamp(start)
        elif isinstance(start, str):  # 时间字符串，2022-10-18 00:00:00
            # print('---type str--')
            start_time = datetime.datetime.strptime(start, fmt2 if len(start) == len('2022-10-18') else fmt)
        else:
            start_time = datetime.datetime.now()

        if only_day:
            start_time = start_time.date()

        # print(f"start_time 2: {start_time}")
        td = datetime.date.today() if start_time is None else start_time
        # print(td)
        timedelta = datetime.timedelta(days=days)
        result = td - timedelta
        result = result.strftime('%Y-%m-%d %H:%M:%S')
        return result

    @staticmethod
    def time_delta(days=0, seconds=0, microseconds=0, milliseconds=0, minutes=0, hours=0, weeks=0) -> str:
        """
        计算当前日期的 time delta
        调用示例：
            Utils.time_delta(2)
        :return: 计算后日期，如 2022-10-18 02:01:00
        """
        start_time = datetime.datetime.now()
        timedelta = datetime.timedelta(days, seconds, microseconds, milliseconds, minutes, hours, weeks)
        result = start_time - timedelta
        result = result.strftime('%Y-%m-%d %H:%M:%S')
        return result

    @staticmethod
    def get_source_path():
        """
        获取源码根目录
        :return:
        """
        path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))  # 包含父目录
        return path

    @staticmethod
    def get_project_path(path=None) -> str:
        """
        获取文件路径
        :param path:
        :return:
        :rtype:str
        """
        root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))  # 包含父目录
        if path is not None:
            new_path = root + '/' + path
        else:
            new_path = root

        # print(new_path)
        return new_path

    @staticmethod
    def save_cookie(driver, file: str):
        cookies = driver.get_cookies()
        jsonCookies = json.dumps(cookies)
        with open(file, 'w') as f:
            f.write(jsonCookies)
        print('save ok')

    @staticmethod
    def load_cookie(driver, file, url):
        driver.delete_all_cookies()
        with open(file, 'r', encoding='utf-8') as f:
            listCookies = json.loads(f.read())
        for cookie in listCookies:
            driver.add_cookie({
                'domain': cookie['domain'],
                'name': cookie['name'],
                'value': cookie['value'],
                'path': '/',
                'expires': None
            })
        driver.get(url)
        print('load ok')

    @staticmethod
    def find_element_by_text(elements, text) -> WebElement:
        # print(elements)
        # menu1 = helper.find_element_by_text(menus,'高级设置')
        element = None
        for btn in elements:
            # print(btn.text)
            if btn.text == text:
                element = btn

        return element

    @staticmethod
    def mkdir(path) -> bool:

        # 引入模块
        import os
        # 去除首位空格
        path = path.strip()

        # 去除尾部 \ 符号
        path = path.rstrip("\\")

        # 判断路径是否存在
        # 存在     True
        # 不存在   False
        isExists = os.path.exists(path)

        # 判断结果
        if not isExists:
            os.makedirs(path)

        return True

    @staticmethod
    def is_html(url):
        """
        判断链接是否html页面
        :param url:
        :return:
        """
        path = urlparse(url).path
        ext = os.path.splitext(path)[1]
        exts = ['.html', '.htm', '.xhtml', '.shtml', '.jhtml', '.asp', '.aspx', '.php']
        if len(ext) == 0:
            return True
        elif ext in exts:
            return True
        else:
            return False

    @staticmethod
    def is_file(url):
        """
        判断链接是否是附件链接
        :param url:
        :return:
        """
        return not Utils.is_html(url)

    @staticmethod
    def get_extra(extra_str: str, key: str, default=None):
        """
        从extra 获取数据
        :param extra_str:
        :param key:
        :param default:
        :return:
        """
        if not extra_str:
            return default

        extra: dict = json.loads(extra_str)
        # {"browserName":"firefox","analysisMethod":1,"nextPage":"multi"}
        browserName = extra.get(key)
        if browserName:
            return browserName
        else:
            return default

    @staticmethod
    def dict_get(data: dict, keys: list, default=None):
        """
        从dict 获取值，支持多级
        :param data:
        :param keys:
        :param default:
        :return:
        """
        if data is None:
            return default

        rd = data
        for key in keys:
            rd = rd.get(key)
            if rd is None:
                break

        return default if rd is None else rd

    @staticmethod
    def filter_first(filter2, data):
        """
        filter list and return first
        :return: str
        """
        result = list(filter(filter2, data))
        return result.pop() if result else None

    @staticmethod
    def get_wechat_type(file: str) -> str:
        """
        通过文件路径获取 wechat 消息发送中的 type 值
        :author wjh
        :date 2022-11-15
        :return: str
        """
        result = Utils.md5(file)
        return result[0:6]

    @staticmethod
    def md5(text) -> str:
        """
        get md5 for text
        :return: str
        """
        md5 = hashlib.md5(text.encode("utf-8")).hexdigest()
        return md5

    @staticmethod
    def json_dumps_pretty(data):
        """
        格式化json 输出
        :param data:
        :return:
        """
        if isinstance(data, str):
            data = json.loads(data)

        return json.dumps(data, sort_keys=True, indent=4, separators=(',', ': '), ensure_ascii=False)
