import json
import re
import sys
import os
from typing import Dict, Tuple, List

import time

from selenium.webdriver.remote.webelement import WebElement

from dc.common.alchemy import DcJsonEncoder
from dc.conf.defines import ROOT_PATH
from dc.services.redis_service import RedisService
from jwtools.io_util import *


class ConfigUtil:
    """
    配置内容工具类
    """

    # 缓存文件配置
    cache_files = {
        'bianjifagao': {'expire': 30, 'file': 'bianjifagao.txt'},  # 编辑发稿
        'xxx': {'expire': 30, 'file': 'xxx.txt'},  # xxxx
    }

    def __init__(self):
        pass

    @staticmethod
    def get_project_path(*paths) -> str:
        """
        获取项目路径
        :param paths:
        :return:
        """
        return os.path.join(ROOT_PATH, *paths)

    @staticmethod
    def get_config_path(*paths) -> str:
        """
        获取项目配置文件路径，不包括 data/config 部分
        :param paths:
        :return:
        """
        return ConfigUtil.get_project_path('data', 'config', *paths)

    @staticmethod
    def get_config_content_from_file(params: dict) -> str:
        """
        从配置文件读取配置内容
        :param params:
        :return:
        """
        key = params.get('key')
        assert key, 'key is empty'

        cfg: dict = ConfigUtil.cache_files.get(key)
        cfg_path = ConfigUtil.get_config_path(cfg.get('file'))
        cfg_expire = cfg.get('expire', 300)
        cfg_content = read_text_from_file(cfg_path)
        # print(cfg_content)
        # print(cfg_expire)
        return cfg_content

    @staticmethod
    def get_config_content(key: str) -> str:
        """
        读取缓存中配置内容，如果不存在自动加载
        :param key: 配置键
        :return:
        """
        cfg: dict = ConfigUtil.cache_files.get(key)
        # cfg_path = ConfigUtil.get_config_path(cfg.get('file'))
        cfg_expire = cfg.get('expire', 300)

        # redis
        rs = RedisService()
        rs.select(2)
        data = rs.get(key, ConfigUtil.get_config_content_from_file, params={'key': key}, ex=cfg_expire)
        rs.close()
        return data
