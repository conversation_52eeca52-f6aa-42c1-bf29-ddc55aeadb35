import datetime
import getopt
import hashlib
import json
import random
import time
import os
import sys
import traceback
from typing import Dict, Tuple, List, Callable

import re
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium import webdriver
from selenium.webdriver.remote.webelement import WebElement

from dc.common.alchemy import Alchemy<PERSON><PERSON>Encoder, DcJsonEncoder
from dc.common.utils import Utils


def validate_opts(opts: List):
    global key
    for key in ['--device_name', '--port']:
        if key not in opts or len(opts.get(key, '')) == 0:
            print("{key} is empty".format(key=key))
            exit(0)


def digitclick(driver, digit):
    btn = driver.find_element_by_id(digit)
    btn.click()
    return True


def print_list(list):
    for item in list:
        print(item)

    return True


def print_debug(driver):
    print('contexts', driver.contexts)
    print('current_activity', driver.current_activity)
    print('current_context', driver.current_context)


def key_in_list(data, key, val):
    for item in data:
        if item[key] and item[key] == val:
            return True

    return False


def driver_move(driver, start=1, end=3, length=4, duration=300):
    """
    滑动屏幕
    :param driver: webdriver
    :param start: 结束位置
    :param end: 开始位置
    :param length: 总长度
    :param duration:
    :return:
    """
    try:
        width = driver.get_window_size()['width']
        height = driver.get_window_size()['height'];
        print('---->driver_move', '(start:', start, 'end:', end, ' length:', length, ' duration:', duration, ') ')
        driver.swipe(width / 2, height * start / length, width / 2, height * end / length, duration);
        print('')

    except Exception as er:
        print('driver_move', er.args)


def driver_move_down(driver, step=1, length=4, interval=0.5):
    """
    向上滑动屏幕（向下滑动内容）
    :param driver: webdriver
    :param length: 总长度
    :param step: 滑动次数
    :param interval: 间隔时间/秒
    :return:
    """
    try:
        width = driver.get_window_size()['width']
        height = driver.get_window_size()['height'];
        print('---->driver_move_down', '(step:', step, ' length:', length, ' interval:', interval, ') ', end='')
        for i in range(0, step, 1):
            time.sleep(interval)
            driver.swipe(width / 2, height * (int(length / 2) + 1) / length, width / 2,
                         height * int(length / 2) / length);
            print(i + 1, ' ', end='', flush=True)

        print('')
    except Exception as er:
        print('driver_move_down', er.args)


def driver_move_up(driver, step=1, length=4, interval=0.5):
    """
    向下滑动屏幕（向上滑动内容）
    :param driver: webdriver
    :param length: 总长度
    :param step: 滑动次数
    :param interval: 间隔时间/秒
    :return:
    """
    try:
        width = driver.get_window_size()['width']
        height = driver.get_window_size()['height']
        print('---->driver_move_up', '(step:', step, ' length:', length, ' interval:', interval, ') ', end='')
        for i in range(0, step, 1):
            time.sleep(interval)
            driver.swipe(width / 2, height * int(length / 2) / length, width / 2,
                         height * (int(length / 2) + 1) / length);
            print(i + 1, ' ', end='', flush=True)

        print('')

    except Exception as er:
        print('driver_move_up', er.args)


def driver_move_vertical(driver, step=1, start=0.8, stop=0.2, interval=0.4, random_range=0):
    """
    竖向滑动屏幕
    :param driver: webdriver
    :param step: 滑动次数
    :param start: 开始单位
    :param stop: 结束单位
    :param interval: 间隔时间/秒
    :return:
    """
    try:
        step = int(step)
        width = driver.get_window_size()['width']
        height = driver.get_window_size()['height'];
        print('---->driver_move_vertical', '(step:', step, ' start:', start, ' stop:', stop, ' interval:', interval,
              ') ', end='')
        for i in range(0, step, 1):
            interval = get_random_scope(interval, random_range, 2)

            swipe_width = int(get_random_percent(width * 0.5, 0.2, 2))
            swipe_width2 = int(get_random_percent(width * 0.5, 0.2, 2))

            swipe_start = int(get_random_percent(height * start, 0.1, 2))
            swipe_stop = int(get_random_percent(height * stop, 0.1, 2))

            # duration = int(interval*1000)
            duration = random.randint(350, 450)
            driver.swipe(swipe_width, swipe_start, swipe_width2, swipe_stop, duration)
            print('')
            print('---->driver_move_vertical2', '(step:', step, ' start:', swipe_start, '(', height * start, ')',
                  ' stop:', swipe_stop, '(', height * stop, ')', ' swipe_width:', swipe_width, '(', width * 0.5, ')',
                  ' swipe_width2:', swipe_width2, '(', width * 0.5, ')', ' duration:', duration, ') ',
                  end='')
            print(i + 1, ' ', end='', flush=True)

        print('')
    except Exception as er:
        print('driver_move_vertical', er.args)


def driver_move_horizontal(driver, step=1, start=0.8, stop=0.2, interval=0.5):
    """
    水平滑动屏幕
    :param driver: webdriver
    :param step: 滑动次数
    :param start: 开始单位
    :param stop: 结束单位
    :param interval: 间隔时间/秒
    :return:
    """
    try:
        step = int(step)
        width = driver.get_window_size()['width']
        height = driver.get_window_size()['height'];
        print('---->driver_move_horizontal', '(step:', step, ' start:', start, ' stop:', stop, ' interval:', interval,
              ') ', end='')
        for i in range(0, step, 1):
            time.sleep(interval)
            driver.swipe(width * start, height / 3, width * stop, height / 3);
            print(i + 1, ' ', end='', flush=True)

        print('')
    except Exception as er:
        print('driver_move_horizontal', er.args)


def get_app_path(app) -> str:
    """
    获取项目的APP安装路径
    :param app:
    :return:
    """
    # 获取项目的根目录路径
    p = os.path.abspath(os.path.join(os.path.dirname(os.path.realpath(__file__)), ".."))
    # 获取app路径
    appPath = lambda x: os.path.join(p, "data/apps", x)
    # print(appPath(app))
    return appPath(app)


def get_text_md5(text) -> str:
    """
    计算字符串md5
    :param text:
    :return:
    """
    # print('md5处理：%s' % text)
    md5 = hashlib.md5(text.encode("utf-8")).hexdigest()
    return md5


def get_opt(args=sys.argv[1:], shortopts="hvli:o:",
            longopts=["port=", "device_name=", "platform_version=", "isopen", "login", "check_time"]):
    opts, args = getopt.getopt(args, shortopts, longopts)

    print(opts)
    data = {}
    for op, value in opts:
        data[op] = value

    return data


def debug_links(links):
    for i in range(0, len(links), 1):
        article = links[i]
        # print(article.text)
        print("{i}.{text}  ".format(i=i, text=article.text), end='')
        print('')


def print_sleep(sleep, interval=1, *args) -> None:
    """
    打印sleep信息
    :param sleep:等待次数
    :param interval:等待间隔
    :return:
    """
    if len(args) > 0:
        for c in args:
            print(c, end='')
        print('', end=' ')

    print('sleep {sleep} (interval {interval}):'.format(interval=interval, sleep=sleep), end='')
    for i in range(0, sleep, 1):
        time.sleep(interval)
        print(' ', i + 1, end='', flush=True)

    print('')


def print_wait(a, message='') -> None:
    """
    等待指定时间，并打印提示信息
    :param a: 等待时间/秒
    :param message: 提示信息
    :return: None
    """
    wait_time = a
    print(message, wait_time)
    print_sleep(wait_time, 1)


def random_wait(a, b, interval=1, *args) -> None:
    """
    随机等待一定时间
    :param a: 最小值
    :param b: 最大值
    :param message: 提示信息
    :param interval: 时间间隔
    :return: None
    """
    if len(args) > 0:
        for c in args:
            print(c, end='')
        print('', end=' ')

    wait_time = random.randint(a, b)
    print('random_wait:', wait_time)
    print_sleep(wait_time, interval)


def random_check(a, b, nums=[], message='') -> bool:
    """
    随机等待检测
    :param a: 最小值
    :param b: 最大值
    :param nums: 检测数组
    :param message: 提示信息
    :return: Bool
    """
    wait_time = random.randint(a, b)
    print(message, 'a:', a, 'b:', b, 'random_check', wait_time, 'nums:', nums, end=' ')
    if wait_time in nums:
        print('--> in')
        return True
    else:
        print('not in');
        return False


def print_element(element, chars='-', num=20) -> None:
    print(chars * num)
    if element is None:
        print('元素无效')
        return False

    id = element.get_attribute("resourceId")
    className = element.get_attribute("className")
    text = element.get_attribute("text")
    print('id:', id, '\nclassName:', className, '\ntext:', text)
    print(chars * num)


def get_project_path(path=None) -> str:
    """
    获取文件路径
    :param path:
    :return:
    :rtype:str
    """
    root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))  # 包含父目录
    if path is not None:
        new_path = root + '/' + path
    else:
        new_path = root

    # print(new_path)
    return new_path


def get_time(t) -> int:
    """
    获取时间的秒数
    :param t:
    :return:
    :rtype: int
    """
    # xx = '02:29'
    x1, x2 = t.split(':')
    z = 60 * int(x1) + int(x2)
    # print(t, z)
    return z


def center_text(text: str, width: int, fillchar='-') -> str:
    """
    居中打印文本
    :param text: 文本
    :param width: 宽度
    :param fillchar: 填充字符
    :return:
    """
    text = ' ' + text.strip() + ' '
    return text.center(width, fillchar)


def save_screenshot(driver, project, pre='', extend_name='') -> None:
    """
    保存截图
    :param driver:
    :param project:
    :param pre:
    :param extend_name:
    :return:
    """
    try:
        date = time.strftime('%Y-%m-%d')
        path = get_project_path('data/screenshots/{project}/{date}'.format(project=project, date=date))
        if os.path.exists(path) is False:
            os.makedirs(path)

        file = path + '/{r}{l}{e}.png'.format(r=time.strftime("%Y%m%d%H%M%S", time.localtime()),
                                              l='-' if len(extend_name) > 0 else '', e=extend_name)
        print(pre, '截图：', file)
        result = driver.save_screenshot(file)
        if result is False:
            print('保存失败')
    except Exception as eri:
        print('save_screenshot error')
        print_traceback_exception(eri)


def print_traceback_exception(ex: Exception) -> None:
    """
    打印异常信息
    :param ex:
    :return: None
    """
    # addproduct.screenshot()
    # print('str(Exception):\t', str(Exception))
    print('str(e):\t\t', str(ex))
    # print('repr(e):\t', repr(ex))
    print('e.message:\t', ex.args)
    # print('traceback.print_exc():', traceback.print_exc())
    print('traceback.format_exc():\n%s' % traceback.format_exc())


def save_debug_info(driver: WebDriver, project: str, file_name: str, page_source=False, pre='') -> str:
    """
    保存截图
    :param driver: WebDriver
    :param project: str
    :param pre:
    :param page_source:
    :return:
    """
    try:
        date = time.strftime('%Y-%m-%d')
        path = get_project_path('data/screenshots/{project}/{date}'.format(project=project, date=date))
        if os.path.exists(path) is False:
            os.makedirs(path)

        file = path + '/{f}-{r}.png'.format(f=file_name, r=time.strftime("%Y%m%d%H%M%S", time.localtime()))
        file2 = path + '/{f}-{r}.html'.format(f=file_name, r=time.strftime("%Y%m%d%H%M%S", time.localtime()))
        print(pre, '截图：', file)
        result = driver.save_screenshot(file)
        if result is False:
            print('保存失败')

        if page_source is True:
            mylog = open(file2, mode='a', encoding='utf-8')
            print(driver.page_source, file=mylog)
            mylog.close()

        return file

    except Exception as eri:
        print('save_screenshot error')
        print_traceback_exception(eri)

        return None


def print_current_time():
    dt = datetime.datetime.now().strftime('%H:%M:%S')
    print(dt)


def print_current_time2():
    dt = datetime.datetime.now().strftime('%H:%M:%S.%f')
    print(dt)


def get_item(v, times):
    """
    从列表中检索
    :param v:
    :param times:
    :return:
    """
    result = False
    for item in times:
        if item['start'] <= v < item['stop']:
            if len(item['phone']) > 0:
                result = item
                break

    return result


def get_minutes(v=None):
    """
    从时钟表达转换为分钟
    :param v:
    :return:
    """

    if v is None:
        nt = int(time.strftime('%H%M'))
        # print(nt)
        ht = int(time.strftime('%H'))
        # print(ht)
        mt = int(time.strftime('%M'))
        # print(mt)

        ss = ht * 60 + mt
        # print(ss)
        return ss
    else:
        str = '%s' % v
        fz = (int)(str[-2:])
        sz = (int)(str[:-2])
        return sz * 60 + fz


def get_time_from_minutes(v):
    """
    从分钟表达转换为时钟表达
    :param v:
    :return:
    """

    s = v // 60
    y = v % 60

    return (int)(str(s) + str("{:0>2}").format(y))


def get_times_list(time_step=60, phones=None):
    first_start_time = '000'  # 时钟

    print('first_start_time:', first_start_time)

    last_stop_time = None
    # time_step = 60  # 数字，间隔分钟数
    times = []
    start_time = get_minutes(first_start_time)
    max_time = 2359
    # print(start_time)
    for i in range(1, 1000, 1):
        # item1

        for phone_key, phone_info in phones.items():
            # print(phone_key, phone_info['phone'], phone_info['password'])
            start_time_item = get_time_from_minutes(start_time)
            stop_time_item = get_time_from_minutes(start_time + time_step)
            if int(start_time_item) > max_time:
                break
            last_stop_time = stop_time_item

            times.append({'start': start_time_item, 'stop': stop_time_item, 'phone': phone_info['phone'],
                          'password': phone_info['password']})
            start_time += time_step

    print('stop_time_item:', last_stop_time)

    return times


def get_times_list_for_test(time_step=60, phones=None, works: Dict = None):
    first_start_time = '000'  # 时钟

    print('first_start_time:', first_start_time)

    last_stop_time = None
    # time_step = 60  # 数字，间隔分钟数
    times = []
    start_time = get_minutes(first_start_time)
    max_time = 2359
    # print(start_time)
    for i in range(1, 1000, 1):
        # item1

        for phone_key, phone_info in phones.items():
            # print(phone_key, phone_info['phone'], phone_info['password'])

            for task_key, task in works.items():
                start_time_item = get_time_from_minutes(start_time)
                stop_time_item = get_time_from_minutes(start_time + time_step)
                if int(start_time_item) > max_time:
                    break
                last_stop_time = stop_time_item

                times.append({'start': start_time_item, 'stop': stop_time_item, 'task': task['name'],
                              'phone': phone_info['phone']})
                start_time += time_step

    print('stop_time_item:', last_stop_time)

    return times


def get_time_hm() -> int:
    nt = int(time.strftime('%H%M'))
    return nt


def get_random_scope(num, scope=0.1, round_num=1) -> float:
    """
    按照数值浮动
    :param num: 数值
    :param scope: 浮动范围
    :param round_num: 保留小数点位置
    :return:
    """
    ac1 = num - scope
    ac2 = num + scope

    # 生成随机数，浮点类型
    a = random.uniform(ac1, ac2)
    # 控制随机数的精度round(数值，精度)
    result = round(a, round_num)
    return result


def get_random_percent(num, percent=0.5, round_num=1) -> float:
    """
    按照百分比浮动
    :param num: 数值
    :param percent: 浮动范围，用小数表示
    :param round_num: 保留小数点位置
    :return:
    """
    ac1 = num * (1 - percent)
    ac2 = num * (1 + percent)

    # 生成随机数，浮点类型
    a = random.uniform(ac1, ac2)
    # 控制随机数的精度round(数值，精度)
    result = round(a, round_num)
    return result


def get_desired_caps2(device_info) -> Dict:
    '''
    desired_caps = {
        'platformName': device_info['platform_name'],
        'platformVersion': device_info['platform_version'],
        'deviceName': device_info['device_name'],
        'udid': device_info['udid'],
        # 'app': app,
        'appPackage': 'com.jifen.qukan',
        'appActivity': 'com.jifen.qkbase.main.MainActivity',
        'automationName': device_info['automation_name'],
        'newCommandTimeout': "500",  # 超时时间
        # 'unicodeKeyboard': True,
        # 'resetKeyboard': True,
        # 'fullReset':False,
        # 'fastReset':False,
        'chromeOptions': {
            # 'androidProcess': 'com.ttyouqu.app'
            # 'args': ['--no-sandbox']
        },
        'noReset': True,
    }
    '''

    desired_caps = device_info

    # 内容判断

    return desired_caps


def check_current_app2(driver, package, activity=None) -> None:
    """
    检测当前APP
    :param driver:
    :return:
    """
    print('current_package:', driver.current_package, 'current_activity:', driver.current_activity, 'package:', package,
          'activity:', activity)
    if driver.current_package != package:
        print('current_activity:', driver.current_activity)
        raise Exception('APP 不在当前界面', package)

    if activity is not None and driver.current_activity != activity:
        print(' current_package:', driver.current_package)
        raise Exception('APP 不在当前界面', activity)

    print('页面检测[通过]')


def check_current_time(driver, stop_run_time, pre='') -> None:
    """
    检测当前运行时间
    :param driver:
    :return:
    """
    nt = int(time.strftime('%H%M'))
    print(pre, 'current time:', nt, ' -> ', stop_run_time)
    if nt >= stop_run_time:
        print(pre, 'stop run:', nt, ' stop_run_time:', stop_run_time)
        driver.quit()
        sys.exit(0)


def check_current_time_plus(driver, start_run_time, stop_run_time, pre='', check_time=True) -> bool:
    """
    检测当前运行时间
    :param driver:
    :return:
    """
    if check_time is False:
        return True

    nt = int(time.strftime('%H%M'))
    print(pre, 'current time:', nt, ' -> ', start_run_time, '-', stop_run_time)

    if nt >= start_run_time and nt <= stop_run_time:
        return True
    else:
        return False


def check_current_time_call(pre, nt, check_time, tasks):
    """
    检测当前运行时间
    :param driver:
    :return:
    """
    if check_time is False:
        # return True
        pass

    # nt = get_time_hm()
    print('pre:', pre, ' current time:', nt, ' check_time:', check_time)

    # tasks = devices[device_name]['tasks']
    arr = [elem for elem in tasks if int(elem['start']) <= nt < int(elem['stop']) and len(elem['phone']) > 0]
    result = arr.pop() if len(arr) > 0 else False
    return result


def get_times(run_time=None, pre='') -> int:
    """
    检测当前运行时间
    :param driver:
    :return:
    """
    nt = int(time.strftime('%H%M'))
    print(pre, 'current time:', nt, ' -> ', run_time)

    return nt


def time_to_timestamp(source_time: str) -> float:
    """
    从时间获取时间戳
    :param source_time:
    :return:
    """
    z1 = source_time.split('.')
    # print(z1)
    a1 = z1[0]
    a2 = float('0.' + z1[1])
    # 先转换为时间数组
    timeArray = time.strptime(a1, "%Y-%m-%d %H:%M:%S")
    # 转换为时间戳
    timeStamp = int(time.mktime(timeArray))
    # print(a2)
    dt = timeStamp + a2
    return dt


def timestamp_to_time(timestamp: float) -> str:
    """
    毫秒时间戳转时间显示 显示3位毫秒
    :param timestamp: 毫秒时间戳 如： 1582898150.001
    :return:
    """
    dz = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(timestamp))) + '.' + str(timestamp).split('.')[1][0:3]
    return dz


def call_func_by_time(start_time: str, sleep_time: float, once: bool, func, **params):
    start_timestamp = time_to_timestamp(start_time)
    print('start_time:', start_time, '(', start_timestamp, ')')
    print('current_time:', Utils.showTime2())

    csh = 0
    while True:
        csh += 1

        nt = time.time()
        dz = timestamp_to_time(nt)
        print("csh:", csh, "nt:", nt, '(', dz, ') ', 'start_time:', start_timestamp, '(', start_time, ')')
        if nt > start_timestamp:
            print("nt:", nt, '(', dz, ') ', ' ----------------------------> break ')
            func(**params)
            if once:
                break

        time.sleep(sleep_time)

    print('stop_time:', nt, '(', dz, ')')


def call_func(sleep_time: float, once: bool, func, **params):
    print('[call_func] current_time:', Utils.showTime2())

    csh = 0
    while True:
        csh += 1

        nt = time.time()
        dz = timestamp_to_time(nt)
        print("csh:", csh, "nt:", nt, '(', dz, ') ')
        func(**params)
        if once:
            break

        time.sleep(sleep_time)

    print('[call_func] stop_time:', nt, '(', dz, ')')


def get_total_seconds(text: str, pattern: str = '小时|分|秒') -> int:
    # zx = re.split('小时|分|秒', '1小时2分41秒')
    zx = re.split(pattern, text)
    # print(zx)
    zx.pop()
    # print(zx)
    total_second = 0
    if len(zx) == 1:
        total_second = int(zx[0])
    elif len(zx) == 2:
        total_second = int(zx[0]) * 60 + int(zx[1])
    elif len(zx) == 3:
        total_second = int(zx[0]) * 60 * 60 + int(zx[1]) * 60 + int(zx[2])
    else:
        total_second = 1000000000

    return total_second


def time2() -> float:
    """
    获取秒级时间戳，并以小数形式包含毫秒3位数
    time.time()  1582962057.986398
    此处为  1582962057.986
    :return: float
    """
    times = str(time.time()).split('.')
    return str(times[0]) + '.' + times[1][0, 3]


def time_ms() -> int:
    """
    获取毫秒级时间戳
    :return: int
    """
    t = time.time()
    return int(round(t * 1000))


def md5(text) -> str:
    """
    get md5 for text
    :return: str
    """
    return get_text_md5(text)


def list_to_dict_by_key(list: list, key, value='') -> dict:
    """
    array_column
    """
    d = {}
    for v in list:
        print("--" * 30, v, v.to_dict())
        if value != '':
            d[v.to_dict()[key]] = v.value
        else:
            d[v.to_dict()[key]] = v

    return d


def dict_filter_sort(data: dict, filter_func: Callable[[], bool] = None, sort_func: Callable[[], None] = None, top=0, reverse=True) -> dict:
    """
    对 dict 进行过滤并排序，最后返回 top 条记录
    :author wjh
    :date 2023-10-7
    :example  调用示例：
        result = dict_filter_sort(
            data=data,
            filter_func=lambda x: x[0].startswith('k') and x[1].get('weight') >= 0,
            sort_func=lambda item: item[1].get('weight'),
            top=2
        )
    :param data: 数据
    :param filter_func: 可选，过滤函数，None 为未设置
    :param sort_func: 可选，排序函数， None 为未设置
    :param top: top 条数
    :param reverse: 是否倒序
    :return: 处理后数据
    """
    # 使用 filter 筛选
    filtered_data = filter(filter_func, data.items()) if filter_func else data.items()
    # 使用 sorted 函数进行倒序排序
    sorted_filtered_data = sorted(filtered_data, key=sort_func, reverse=reverse) if sort_func else sorted(filtered_data, reverse=reverse)
    # 生成最终结果的字典
    result = dict(sorted_filtered_data[:top] if top > 0 else sorted_filtered_data)
    return result


def convert_to_timestamp(time_str, time_format="%Y-%m-%d %H:%M:%S"):
    """
    将给定的时间字符串转换为时间戳。

    :param time_str: 时间字符串，如 "2023-11-02 17:00:25"
    :param time_format: 时间格式，默认是 "%Y-%m-%d %H:%M:%S"
    :return: 转换后的时间戳（秒级）
    """
    try:
        # 使用 datetime 解析时间字符串
        exec_time_date = time.strptime(time_str, time_format)
        return int(time.mktime(exec_time_date))

    except ValueError as e:
        # 如果时间格式不正确，抛出异常
        print(f"时间格式错误: {e}")
        return None