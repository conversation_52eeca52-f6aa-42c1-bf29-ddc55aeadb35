# -*- coding:utf-8 -*-
# @Function  : assert 实现类
# <AUTHOR> wjh
# @Time      : 2024/1/19
# Version    : 1.0


def assert_true(expression, msg):
    if not expression:
        raise AssertionError(msg)


def assert_error(expression, msg, error_type: Exception = None):
    if error_type is None:
        error_type = Exception

    if not expression:
        raise error_type(msg)


def assert_test(expression, msg):
    assert expression, msg
