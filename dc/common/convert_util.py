import json
import re
import sys
import os
from typing import Dict, <PERSON><PERSON>, List

import time

from selenium.webdriver.remote.webelement import WebElement

from dc.common.alchemy import DcJsonEncoder


class ConvertUtil:
    """
    数据转换帮助类
    """

    def __init__(self):
        pass

    @staticmethod
    def list2json(results) -> str:
        """
        list with dict to json
        :return: str
        """
        data = [dict(zip(result.keys(), result)) for result in results]
        return json.dumps(data, cls=DcJsonEncoder)

    @staticmethod
    def dict2json(result) -> str:
        """
        dict to json
        :param result:
        :return: str
        """
        data = dict(zip(result.keys(), result))
        return json.dumps(data, cls=DcJsonEncoder)

    @staticmethod
    def to_json(data) -> str:
        """
        dict to json
        :param result:
        :return: str
        """
        return json.dumps(data, cls=DcJsonEncoder)

    @staticmethod
    def row2json(result) -> str:
        """
        dict to json
        :param result:
        :return: str
        """
        data = dict(zip(result.keys(), result))
        data = ConvertUtil.to_json(data)
        return data

    @staticmethod
    def row2dict(result) -> dict:
        """
        dict to json
        :param result:
        :return: str
        """
        data = dict(zip(result.keys(), result))
        return data

    @staticmethod
    def rows2dict_list(results) -> list[dict]:
        """
        rows to dict list
        :return: list[dict]
        """
        data = [dict(zip(result.keys(), result)) for result in results]
        return data

    @staticmethod
    def rows2json(results) -> str:
        """
        list with dict to json
        :return: str
        """
        data = ConvertUtil.rows2dict_list(results)
        data = ConvertUtil.to_json(data)
        return data