# -*- coding:utf-8 -*-
# @Function  : 数据采集帮助类
# <AUTHOR> wjh
# @Time      : 2024/1/19
# Version    : 1.0

import requests
from bs4 import BeautifulSoup
from lxml import html
from lxml.html import HtmlElement
from lxml import etree
from selenium.webdriver.common.by import By

from dc.tests.browser import Browser
from dc.services.check_analysis import CheckAnalysis


class DcHelper:

    def __init__(self) -> None:
        super().__init__()

    def __new__(cls):
        return super().__new__(cls)

    @staticmethod
    def get_html(url) -> str:
        """
        获取指定 URL 的 HTML 内容
        :author wjh
        :date 2024-1-26
        :param url:
        :return:
        """
        r = requests.get(url)
        # r.encoding = 'utf-8'
        # print(r.encoding)
        return r.text

    @staticmethod
    def get_html_by_requests(url, params: dict = {}):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        }
        assert url, 'url is empty'
        r = requests.get(url, headers=headers, verify=False, timeout=(3.05, 27))
        # r.encoding = "utf-8"
        status_code = r.status_code

        # BeautifulSoup
        soup = BeautifulSoup(r.content, 'lxml')
        charset = soup.original_encoding
        r_text = str(soup)

        return {'status_code': status_code, 'text': r_text, 'charset': charset}

    @staticmethod
    def get_html_by_webdriver(url, params: dict = {}):
        analysisMethod = params.get('analysisMethod', 0)    # firefox 1 chrome 0
        page_wait = params.get('pageWait', False)

        from dc.common.webdriver_helper import WebDriverHelper

        try:
            driver = WebDriverHelper.get_webdriver(Browser.Chrome if analysisMethod == 0 else Browser.Firefox)
            driver.implicitly_wait(15)
            driver.get(url)
            if page_wait:
                print(f"page wait -->> {page_wait}")
                driver.find_element(By.XPATH, page_wait)

            result = driver.page_source
            charset = CheckAnalysis.get_char_set(driver)
            driver.quit()

            return {'status_code': 200, 'text': result, 'charset': charset}
        except:
            return {'status_code': 201, 'text': '', 'charset': None}


    @staticmethod
    def fetch_html(url) -> bytes:
        """
        获取指定 URL 的 HTML 内容
        :author wjh
        :date 2024-1-26
        :param url:
        :return:
        """
        response = requests.get(url)
        return response.content

    @staticmethod
    def extract_text_with_newlines(content_element: HtmlElement):
        """
        使用 iter() 方法提取元素中的文本内容，将特定标签替换为换行符
        :author wjh
        :date 2024-1-26
        :param content_element:
        :return:
        """
        text_parts = []
        text = ''
        for element in content_element.iter():  # type: HtmlElement

            if isinstance(element, etree._Comment):  # 检查元素是否是注释
                continue
            if element.tag in ['style', 'script']:
                continue
            if element.tag in ['br', 'p', 'section']:
                text_parts.append('\n')
                text += '\n' if not text.endswith('\n') else ''
            if element.text:
                text_parts.append(element.text)
                text += element.text.strip()
            if element.tail:
                text_parts.append(element.tail)
                text += element.tail.strip()

        # return ''.join(text_parts).strip()
        return text.strip()

    @staticmethod
    def get_content(html_content, xpath, by: By = By.XPATH) -> str:
        """
        获取一个元素的文本内容，包括所有子元素
        :author wjh
        :date 2024-1-26
        :param html_content: html 内容
        :param xpath: xpath 路径
        :param by: 选择器类型
        :return: 文本内容
        """
        tree: HtmlElement = html.fromstring(html_content)
        elements: list[HtmlElement] = []
        if by == By.XPATH:
            elements: list[HtmlElement] = tree.xpath(xpath)
        elif by == By.CSS_SELECTOR:
            elements: list[HtmlElement] = tree.cssselect(xpath)
        elif by == By.TAG_NAME:
            elements: list[HtmlElement] = tree.find(xpath)
        elif by == By.CLASS_NAME:
            elements: list[HtmlElement] = tree.find_class(xpath)
        else:
            raise "not support method"

        text_content = ''.join(DcHelper.extract_text_with_newlines(element) for element in elements)
        return text_content
