# -*- coding:utf-8 -*-
# @Function  : 文件操作工具类
# <AUTHOR>
# @Time      : 2024-11-05
# Version    : 1.0

import errno
import os


def url_path_join(*args):
    """
    类似于 os.path.join 的 URL 路径拼接函数。
    确保各个路径片段正确拼接，不覆盖前面的路径。
    """
    # 将所有片段去掉多余的斜杠
    stripped_args = [str(arg).strip('/') for arg in args]

    # 用 '/' 分隔符连接各个路径片段
    return '/'.join(stripped_args)


def path_join(path1: str, path2: str):
    p1 = path1.replace('/', os.sep).replace('\\', os.sep).rstrip(os.sep)
    p2 = path2.replace('/', os.sep).replace('\\', os.sep).lstrip(os.sep)
    return os.path.join(p1, p2)


def ensure_dir(path: str) -> None:
    """os.path.makedirs without EEXIST."""
    try:
        os.makedirs(path)
    except OSError as e:
        # Windows can raise spurious ENOTEMPTY errors. See #6426.
        if e.errno != errno.EEXIST and e.errno != errno.ENOTEMPTY:
            raise
