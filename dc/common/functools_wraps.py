# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/1/24
# Version    : 1.0

import functools
from functools import wraps
import random
import time
import warnings
from contextlib import contextmanager

from elasticsearch.client import Elasticsearch
from flask import request
from pymongo import MongoClient
from redis import Redis
from sqlalchemy.orm import Session

from dc.common.sign_helper import SignHelper
from dc.conf.settings import get_settings


def check_sign(func):
    @functools.wraps(func)  # 加上该装饰器的原因：以 index() 为例，经 auth 装饰过的 index函数 是 inner，然后 inner函数和"/index"这个路径绑定， 假如有好多视图函数都加上了这个装饰器，那么 inner 函数就会和好多路径绑定， Flask就不知道你绑定的是哪一个
    def inner(*args, **kwargs):
        from dc.services.sign_service import SignService
        data = request.values
        appid = data.get('appid')
        third_app = get_settings('third_app')
        config = third_app.get(appid, None)
        assert config is not None, 'config is empty'

        # // 验签
        sign = SignService(config)
        try:
            sign_res = sign.valid_sign(data)
        except Exception as ex:
            return req_check_sign_failed(config)

        return func(*args, **kwargs)

    return inner


@staticmethod
def req_check_sign_failed(config, msg='failed', ret=0):
    return {
        "app_id": config['appid'],
        "trx_id": SignHelper.get_nonce_str(22),
        "service": request.endpoint,
        "return_code": "FAIL",
        "return_message": "验签失败",
        "result_code": "401",
        "result_message": "验签失败"
    }

    return {"status": -1, "data": data, 'msg': msg, 'ret': ret}


def deprecated(message: str = None, replacement: str = None):
    """
    标识方法不推荐
    :author wjh
    :date 2024-10-10
    :param message: 提示信息
    :param replacement: 替代方法
    :return:
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            warn_message = message or f"{func.__name__} is deprecated and may be removed in a future version."
            warnings.warn(warn_message, category=DeprecationWarning, stacklevel=2)
            return func(*args, **kwargs)

        return wrapper

    return decorator


@contextmanager
def get_session(name: str = 'mysql', run_model=None):
    """接收 mysql连接信息并创建 session"""
    from dc.services.mysql_service import MySQLService
    mysql = MySQLService(name=name, run_model=run_model)
    session: Session = None
    try:
        session = mysql.create_session()
        yield session
        session.commit()
    except Exception as ex:
        session.rollback()
        print(f"Error in mysql session for {name}: {ex}")
        raise
    finally:
        if session is not None:
            session.close()


@contextmanager
def get_redis_connection(name: str = 'redis', run_model=None):
    """接收 redis 连接信息并创建 connection"""
    from dc.services.redis_service import RedisService
    redis = RedisService(name=name, run_model=run_model)
    redis_client: Redis = None

    try:
        redis_client = redis.get_client()
        yield redis_client
    except Exception as ex:
        print(f"Error in redis connection: {ex}")
        raise
    finally:
        if redis_client is not None:
            redis_client.close()


@contextmanager
def get_elasticsearch_connection(name: str = 'elasticsearch', run_model=None):
    """接收 redis 连接信息并创建 connection"""
    from dc.services.elasticsearch_service import ElasticsearchService
    ess = ElasticsearchService(name=name, run_model=run_model)
    es: Elasticsearch = None

    try:
        es = ess.get_connect()
        # 检查连接是否成功
        if not es.ping():
            raise ValueError("无法连接到Elasticsearch")

        yield es
    except Exception as ex:
        print(f"Error in Elasticsearch connection: {ex}")
        raise
    finally:
        if es is not None:
            es.close()


@contextmanager
def get_mongo_client(name: str = 'mongo', run_model=None):
    """接收 mongo 连接信息并创建 connection"""
    from dc.services.mongo_service import MongoService
    ms = MongoService(name=name, run_model=run_model)
    mc: MongoClient = None

    try:
        mc = ms.get_client()
        # 检查连接是否成功
        yield mc
    except Exception as ex:
        print(f"Error in Elasticsearch connection: {ex}")
        raise
    finally:
        if mc is not None:
            mc.close()


@contextmanager
def file_manager(file, mode='+w', buffering=-1, encoding=None, errors=None, newline=None, closefd=True):
    file = open(file, mode=mode, buffering=buffering, encoding=encoding, errors=errors, newline=newline, closefd=closefd)
    try:
        yield file  # 将控制权交给 `with` 块，让调用者可以操作 file
    finally:
        file.close()  # `with` 结束后自动关闭文件


@contextmanager
def try_catch(logger, raise_exception=False):
    """
    用于捕获异常，并根据 `raise_exception` 参数决定是否抛出异常
    :author wjh
    :param logger:
    :param raise_exception:
    :return:
    """
    try:
        yield  # 进入 `with` 块
    except Exception as ex:
        logger.error(f"发生异常: {ex}")
        # 根据 `raise_exception` 参数决定是否抛出异常
        if raise_exception:
            raise ex


def rate_limit(interval: int):
    """
    装饰器：限制函数执行的最小间隔(秒)
    :param interval: 间隔(秒)
    :return:
    """
    def decorator(func):
        last_execution_time = 0  # 记录上次执行时间

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal last_execution_time
            current_time = time.time()
            # 判断是否满足间隔条件
            if current_time - last_execution_time >= interval:
                last_execution_time = current_time  # 更新上次执行时间
                print(f"Function execution.")
                return func(*args, **kwargs)  # 执行原函数
            else:
                print(f"Function skipped, needs to wait {interval - (current_time - last_execution_time):.2f} more seconds.")
                return None  # 如果没有达到间隔条件，就跳过执行

        return wrapper

    return decorator


def rate_limit_instance(interval: int):
    """
    装饰器：限制函数执行的最小间隔(秒), 绑定到实例
    :param interval: 间隔(秒)
    :return:
    """
    def decorator(func):
        last_execution_time = 0  # 记录上次执行时间

        @wraps(func)
        def wrapper(self, *args, **kwargs):
            nonlocal last_execution_time
            current_time = time.time()
            # 判断是否满足间隔条件
            if current_time - last_execution_time >= interval:
                last_execution_time = current_time  # 更新上次执行时间
                print(f"Function execution.")
                return func(self, *args, **kwargs)  # 执行原函数
            else:
                print(f"Function skipped, needs to wait {interval - (current_time - last_execution_time):.2f} more seconds.")
                return None  # 如果没有达到间隔条件，就跳过执行

        return wrapper
    return decorator
