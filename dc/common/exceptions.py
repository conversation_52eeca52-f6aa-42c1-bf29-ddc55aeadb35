import json
import sys
import traceback

from dc.services.logging_service import LoggingService

# 设置日志记录
logger = LoggingService(logfile="exception.log")


def handle_exception(exc_type, exc_value, exc_traceback):
    # 这里可以定义全局异常处理的逻辑
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # 格式化堆栈信息
    stack_info_list = traceback.format_exception(exc_type, exc_value, exc_traceback)
    print(type(exc_traceback))

    # 将换行符转换为 \n 字符串
    stack_info_cleaned = [line.replace('\n', '\\n') for line in stack_info_list]
    stack_info = "".join(stack_info_cleaned)
    stack_info = json.dumps(stack_info)

    # 输出到日志文件
    # logger.error("Uncaught exception:\n%s", stack_info)
    logger.error("Uncaught exception:" + stack_info)

    # 输出到控制台
    print(f"Error: {exc_value}")
    print(f"Stack Trace:\n{stack_info}")


# 设置全局异常处理
sys.excepthook = handle_exception
