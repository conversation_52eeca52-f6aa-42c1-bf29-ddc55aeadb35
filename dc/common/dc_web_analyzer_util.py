import re
import random
import time
import requests as req
from bs4 import BeautifulSoup as BS
import parsel
from datetime import datetime
import chardet
from urllib.parse import urljoin
from dc.services.dc_proxy_pool_service import DcProxyPoolService
from faker import Faker


class AnalyzerUtil:
    """
    网页分析工具类
    """

    @staticmethod
    def get_html(url: str, proxy_no: str = "") -> str:
        try:
            fake = Faker()
            headers = {
                "User-Agent": fake.chrome()
            }
            if proxy_no:
                proxy = DcProxyPoolService()
                proxies = proxy.get_requests_proxy(proxy_no)
                r = req.get(url, headers=headers, proxies=proxies)
                r.raise_for_status()
                r.encoding = chardet.detect(r.content)['encoding']
                return r.text
            else:
                r = req.get(url, headers=headers)
                r.raise_for_status()
                r.encoding = chardet.detect(r.content)['encoding']
                return r.text
        except Exception:
            return ""

    @staticmethod
    def get_website_latency(url):
        try:
            start_time = time.time()  # 请求发送前的时间
            response = req.get(url)
            end_time = time.time()  # 请求返回后的时间

            # 计算网络延迟
            latency = end_time - start_time
            # print(f"网络延迟：{latency * 1000} 毫秒")

            return latency, response.status_code
        except req.exceptions.RequestException as e:
            return 0.8, 500

    # 选择html元素
    selector_rules = {"xpath", "css", "regExp", "jsPath"}

    @staticmethod
    def html_selector(html: str, rule: str, selector: str) -> list:
        """
        根据规则解析html文本内容
        :param html: html文本
        :param rule: 解析规则（"xpath", "css", "regExp", "jsPath"）
        :param selector: 选择器
        :return:
        """
        assert rule in AnalyzerUtil.selector_rules
        html = AnalyzerUtil.html_replace_str(html)  # 清洗html文本
        res = []
        if rule == "xpath":
            # select = etree.HTML(html, etree.HTMLParser())
            # res_xpath = select.xpath(selector)
            # for e in res_xpath:
            #     res.append(etree.tostring(e, pretty_print=True, encoding='unicode'))
            p_selector = parsel.Selector(text=html)
            if "|" in selector:
                selector_list = selector.split("|")
                for e in selector_list:
                    temp = p_selector.xpath(e).getall()
                    for e2 in temp:
                        res.append(e2)
            else:
                res = p_selector.xpath(selector).getall()
        elif rule == "css":
            # select = BS(html, features="lxml")
            # res_bs = select.select(selector)
            # for e in res_bs:
            #     res.append(e.prettify())
            p_selector = parsel.Selector(text=html)
            if "|" in selector:
                selector_list = selector.split("|")
                for e in selector_list:
                    temp = p_selector.css(e).getall()
                    for e2 in temp:
                        res.append(e2)
            else:
                res = p_selector.css(selector).getall()
        elif rule == "regExp":
            # pattern = re.compile(selector)
            # res = pattern.findall(html)
            p_selector = parsel.Selector(text=html)
            if "|" in selector:
                selector_list = selector.split("|")
                for e in selector_list:
                    temp = p_selector.re(e).getall()
                    for e2 in temp:
                        res.append(e2)
            else:
                res = p_selector.re(selector).getall()
        elif rule == "jsPath":
            # Node.js脚本，用于解析HTML并获取元素
            if "|" in selector:
                selector_list = selector.split("|")
                for e in selector_list:
                    selector_res = e
                    if "\"" in e:
                        p_selector = parsel.Selector(text=e)
                        selector_res = p_selector.re('("(.*)")')
                        if len(selector_res) == 0:
                            selector_res = e
                        else:
                            selector_res = selector_res[0]
                        selector_res = selector_res[1:-1]
                    ss = selector_res.split(">")
                    ss_1 = ">".join(ss[:len(ss) - 1])
                    ss_2 = ss_1 + ss[-1]
                    p_selector = parsel.Selector(text=html)
                    temp = p_selector.css(ss_2).getall()
                    for e2 in temp:
                        res.append(e2)
            else:
                selector_res = selector
                if "\"" in selector:
                    p_selector = parsel.Selector(text=selector)
                    selector_res = p_selector.re('("(.*)")')
                    if len(selector_res) == 0:
                        selector_res = selector
                    else:
                        selector_res = selector_res[0]
                    selector_res = selector_res[1:-1]
                ss = selector_res.split(">")
                ss_1 = ">".join(ss[:len(ss) - 1])
                ss_2 = ss_1 + ss[-1]
                p_selector = parsel.Selector(text=html)
                res = p_selector.css(ss_2).getall()
            # html = BS(html, features="lxml").prettify()
            # node_script = """
            #     const jsdom = require('jsdom');
            #     const { JSDOM } = jsdom;
            #     var htmlContent = `%s`
            #     const dom = new JSDOM(htmlContent);
            #     function getElement() {
            #         let nodelist = dom.window.%s
            #         if (nodelist.length === undefined){
            #             return [nodelist.outerHTML]
            #         }
            #         let res = Array()
            #         for(let i = 0; i < nodelist.length ; i++){
            #             res.push(nodelist[i].outerHTML)
            #         }
            #         return res
            #     }
            # """%(html, selector)
            # print(node_script)
            # context = execjs.get()
            # ctx = context.compile(node_script)
            # res = ctx.call("getElement")
        return res

    @staticmethod
    def url_format(url: str, old_text, new_text: str) -> str:
        """
        替换url中的字符，主要是为了替换%v和%s
        :param url: url字符串
        :param old_text: 旧文本
        :param new_text: 新文本
        :return:
        """
        return url.replace(old_text, new_text)

    @staticmethod
    def random_sleep(start, end):
        """
        随机延时
        :param start: 范围左界
        :param end: 范围右界
        :return:
        """
        # 产生一个start到end之间的随机小数
        random_delay = random.uniform(start, end)
        # 延时,拟人行为
        time.sleep(random_delay)

    judge_nextPage_button_texts = {"下一页", "下页"}

    @staticmethod
    def judge_next_page_button(element_text: str) -> bool:
        for e in AnalyzerUtil.judge_nextPage_button_texts:
            if e in element_text:
                return True
        return False

    @staticmethod
    def data_work(data_list: list, rule: str, data_list_rules_mp: dict) -> dict:
        if not data_list:
            res = {}
            for key in data_list_rules_mp.keys():
                res[key] = []
            return res
        if rule == 'xpath':
            root = "//" + \
                   re.findall(r"<([A-Za-z0-9]{1,})(>|\s)",
                              data_list[0])[0][0] + "/"
            for k in data_list_rules_mp.keys():
                if data_list_rules_mp[k] != "" and data_list_rules_mp[k] and isinstance(data_list_rules_mp[k],
                                                                                        str) and not data_list_rules_mp[
                    k].startswith("//"):
                    if "|" in data_list_rules_mp[k]:
                        p_l = data_list_rules_mp[k].split("|")
                        for i in range(len(p_l)):
                            p_l[i] = root + p_l[i]
                        data_list_rules_mp[k] = "|".join(p_l)
                    else:
                        data_list_rules_mp[k] = root + data_list_rules_mp[k]
            for k in data_list_rules_mp.keys():
                root = "//" + \
                       re.findall(r"<([A-Za-z0-9]{1,})(>|\s)",
                                  data_list[0])[0][0]
                if not data_list_rules_mp[k]:
                    data_list_rules_mp[k] = root
        res = {}
        for k in data_list_rules_mp.keys():
            if data_list_rules_mp[k] is None:
                data_list_rules_mp[k] = ""
        for key, pattern in data_list_rules_mp.items():
            res[key] = []
            if "|" in pattern:
                pattern_list = pattern.split("|")
                for _ in range(len(data_list)):
                    res[key].append("")
                for p in pattern_list:
                    if p != "":
                        for i in range(len(data_list)):
                            select = AnalyzerUtil.html_selector(data_list[i], rule, p)
                            if len(select) > 0 and res[key][i] == "":
                                res[key][i] = select[0]
            else:
                if pattern != "":
                    for e in data_list:
                        select = AnalyzerUtil.html_selector(e, rule, pattern)
                        if len(select) > 0:
                            res[key].append(select[0])
                        else:
                            res[key].append("")
                else:
                    for _ in range(len(data_list)):
                        res[key].append("")
        return res

    filter_set = {"\n", "\r", "\t", "<br>"}

    @staticmethod
    def html_filter(html: str) -> str:
        # 去除html中无用信息
        result = ""
        i = 0
        while i < len(html):
            flag = True
            for e in AnalyzerUtil.filter_set:
                if html[i:i + len(e)] == e:
                    i += len(e)
                    flag = False
            if flag:
                result += html[i]
                i += 1
        return result

    @staticmethod
    def prettift_text(text: str) -> str:
        result_list = []
        i = 0
        while i < len(text):
            flag = True
            for e in {"\n", "\r", "\t"}:
                if text[i:i + len(e)] == e:
                    i += len(e)
                    flag = False
            flag2 = True
            while i < len(text) and text[i] == " ":
                flag2 = False
                i += 1
            if not flag2:
                i -= 1
            if flag:
                result_list.append(text[i])
                i += 1
        result = "".join(result_list)
        return result

    @staticmethod
    def html_replace_str(html: str) -> str:
        # 去除html中无用信息
        temp = ""
        try:
            temp = html.encode("utf-8").decode("utf-8")
        except:
            try:
                temp = html.encode("gbk", 'ignore').decode("gbk", 'ignore')
            except:
                pass
        if temp:
            html = temp
        html = html.replace("\n", '')
        html = html.replace("\r", '')
        html = html.replace("\t", '')
        html = html.replace("<br>", '')
        return html

    @staticmethod
    def deal_format_date(date_str: str):
        # 日期处理
        if date_str == "刚刚":
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), 1]
        if re.findall("秒前", date_str):
            num = re.search("\\d{1,2}", date_str).group()
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - float(int(num) * 1))), 1]
        if re.findall("分钟前", date_str):
            num = re.search("\\d{1,2}", date_str).group()
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - float(int(num) * 60))), 1]
        if re.findall("小时前", date_str):
            num = re.search("\\d{1,2}", date_str).group()
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - float(int(num) * 3600))), 1]
        if re.findall("昨天", date_str):
            day = time.strftime("%Y-%m-%d %H:%M:%S",
                                time.localtime(time.time() - 86400))
            date_time = day
            return [date_time, 1]
        if re.findall("前天", date_str):
            day = time.strftime("%Y-%m-%d %H:%M:%S",
                                time.localtime(time.time() - 2 * 86400))
            date_time = day
            return [date_time, 1]
        if re.findall("天前", date_str):
            num = re.search("\\d{1,2}", date_str).group()
            return [time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - float(int(num) * 86400))), 1]

        date_str = date_str.strip()
        # date_str = date_str.replace('- ', '-')
        date_str = date_str.replace(', ', '-')
        date_str = date_str.replace('年', '-')
        date_str = date_str.replace('月', '-')
        date_str = date_str.replace(' 日', '')
        date_str = date_str.replace('日', '')
        date_str = date_str.replace('/n', '')
        date_str = date_str.replace('/', '-')
        date_str = date_str.replace('\\', '-')
        date_str = date_str.replace('.', '-')
        date_str = date_str.replace('时', ':')
        date_str = date_str.replace('分', ':')
        date_str = date_str.replace('秒', '')
        date_str = date_str.replace('\n', '')
        date_str = date_str.replace('- ', '-')
        date_str = date_str.replace(' -', '-')

        if not date_str:
            format_date = '1970-01-01 00:00:00'
            return [format_date, 2]
        if date_str.count("-") == 1:
            if "02-29" in date_str:
                date_str = date_str.replace(
                    "02-29", str(datetime.now().year) + "-02-29")
            elif "2-29" in date_str:
                date_str = date_str.replace(
                    "2-29", str(datetime.now().year) + "-02-29")
        try:
            date_str = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%Y-%m-%d %H:%M")
            format_date = date_str.strftime("%Y-%m-%d %H:%M") + ":00"
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%m-%d")
            now = datetime.now()
            year = now.year
            format_date = year + "-" + date_str.strftime("%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%m-%d-%Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%Y-%m-%d")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%B %d,%Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%A, %B %d, %Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%A, %b %d, %Y %I:%M%p")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%Y%m-%d%H:%M:%S")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%Y%m-%d")
            format_date = date_str.strftime("%Y-%m-%d")
            h = random.randint(10, 23)
            m = random.randint(10, 59)
            s = random.randint(10, 59)
            format_date = format_date + f" {h}:{m}:{s}"
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%A, %d %B %Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%B- %d-%Y %H:%M")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%H:%M %Y-%m-%d")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%d %b-%Y %H:%M")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%B %d,%Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%d-%b-%Y")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%a %d %b %Y // %H:%M UTC")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            return [format_date, 1]
        except:
            pass

        try:
            date_str = datetime.strptime(date_str, "%m-%d %H:%M")
            format_date = date_str.strftime("%Y-%m-%d %H:%M:%S")
            current_year = datetime.now().year
            format_date = (str(current_year) + format_date[4:])
            return [format_date, 1]
        except:
            pass

        return ['1970-01-01 00:00:00', 2]

    @staticmethod
    def selector_xpath_text(html, craw_rule):
        selector = parsel.Selector(text=html)
        xpath_obj = selector.xpath(f"{craw_rule}")
        craw_arr = re.findall(f"text\(\)", f"{craw_rule}")
        if craw_arr:
            str_arr = xpath_obj.getall()
        else:
            str_arr = xpath_obj.xpath(".//text()").getall()

        deal_arr = [tmp_str for tmp_str in str_arr if tmp_str.strip()]

        flag = "/n"
        str1 = flag.join(deal_arr)

        return str1

    @staticmethod
    def selector_css_text(html, craw_rule):
        selector = parsel.Selector(html)
        css_obj = selector.css(f"{craw_rule}")
        craw_arr = re.findall(f"text\(\)", f"{craw_rule}")
        if craw_arr:
            str_arr = css_obj.getall()
        else:
            str_arr = css_obj.xpath(".//text()").getall()

        deal_arr = [tmp_str for tmp_str in str_arr if tmp_str.strip()]

        flag = "/n"
        str1 = flag.join(deal_arr)

        return str1

    @staticmethod
    def get_inner_text(html: str):
        # 获取节点中的文字信息
        selector = parsel.Selector(html)
        l = selector.xpath(".//text()").getall()
        return "".join(l)

    @staticmethod
    def get_inner_text2(html: str):
        # 获取节点中的文字信息，以空格分隔
        selector = parsel.Selector(html)
        l = selector.xpath(".//text()").getall()
        return " ".join(l)

    @staticmethod
    def get_attribute(html: str, attr: str):
        # 获取节点中的属性内容
        if not html:
            return ""
        soup = BS(html, 'html.parser').select_one("*")
        res = soup.get(attr)
        if isinstance(res, list):
            return res[0]
        return res

    @staticmethod
    def fix_relative_url(baseurl: str, relative: str) -> str:
        # 处理相对路径
        baseurl = baseurl.strip()
        relative = relative.strip()
        if not baseurl.endswith('/'):
            baseurl += '/'
        # if relative.startswith('/'):
        #     relative = relative[1:]
        # elif relative.startswith('../'):
        #     while relative.startswith('../'):
        #         relative = relative[3:]
        # elif relative.startswith('./'):
        #     while relative.startswith('./'):
        #         relative = relative[2:]
        return urljoin(baseurl, relative)

    @staticmethod
    def fix_url(url: str, siteUrl: str, relative) -> str:
        if not url:
            return siteUrl
        # 处理路径
        if url.startswith("//"):  # 如果链接是"//"开头，说明是完整路径
            url = url[2:]
            if not url.startswith("http"):  # 如果不是http开头，则需要补全http头
                if siteUrl.startswith("https"):
                    url = "https://" + url
                elif siteUrl.startswith("http"):
                    url = "http://" + url
                else:
                    url = "https://" + url
        elif url.startswith("/"):  # /开头，说明是以siteUrl开头
            url = AnalyzerUtil.fix_relative_url(siteUrl, url)
        elif url.startswith("http"):  # http开头,就不需要处理
            url = url
        else:  # 相对路径
            url = AnalyzerUtil.fix_relative_url(relative, url)
        return url
