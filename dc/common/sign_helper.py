# -*- coding:utf-8 -*-
# @Function  : 
# <AUTHOR> wjh
# @Time      : 2024/1/24
# Version    : 1.0

import hashlib
import random
import time


class SignHelper:
    @staticmethod
    def get_sign(data: dict, appkey):
        data = data.copy()
        data.pop('sign', None)
        data.pop('_url', None)

        sorted_data = sorted(data.items())
        str_data = '&'.join(f"{k}={v}" for k, v in sorted_data)
        sign_temp = f'{str_data}&key={appkey}'
        return hashlib.md5(sign_temp.encode()).hexdigest().upper()

    @staticmethod
    def get_nonce_str(length=32):
        chars = "abcdefghijklmnopqrstuvwxyz0123456789"
        return ''.join(random.choice(chars) for _ in range(length))

    @staticmethod
    def get_millisecond():
        return int(time.time() * 1000)
