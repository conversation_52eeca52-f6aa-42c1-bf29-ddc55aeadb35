# -*- coding:utf-8 -*-
# @Function  : 七牛云辅助类
# <AUTHOR> wjh
# @Time      : 2024/9/7
# Version    : 1.0

import hashlib
import json
import logging
import mimetypes
import os.path
from urllib.parse import urljoin

import requests
import urllib3
from bs4 import BeautifulSoup
from qiniu.http import ResponseInfo
from retry import retry
from PIL import Image
import cairosvg
from io import BytesIO
from dc.common.func import get_app_path
from dc.conf.settings import get_settings, get_settings2

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 超时设置，5秒以上连接超时，10-20秒响应超时：适合外部服务或复杂请求
requests_headers_timeout = (5, 20)

# 定义常见的 MIME 类型到扩展名的映射
mime_extension_map = {
    'image/jpeg': '.jpg',  # JPEG 图像文件
    'image/png': '.png',  # PNG 图像文件
    'image/gif': '.gif',  # GIF 图像文件
    'image/webp': '.webp',  # WebP 图像文件
    'image/bmp': '.bmp',  # BMP 位图文件
    'image/tiff': '.tiff',  # TIFF 图像文件
    'image/avif': '.avif',  # avif 图像文件
    'image/heic': '.heic',  # heic 图像文件
    'image/heif': '.heif',  # heif 图像文件
    'image/jp2': '.jp2',  # jp2 图像文件
    'image/svg+xml': '.svg',  # svg 图像文件
    'image/jxl': '.jxl',  # jxl 图像文件
    'text/plain': '.txt',  # 文本文件
    'text/html': '.html',  # HTML 文件
    'text/css': '.css',  # CSS 样式表文件
    'application/json': '.json',  # JSON 文件
    'application/pdf': '.pdf',  # PDF 文件
    'application/zip': '.zip',  # ZIP 压缩文件
    'application/x-rar-compressed': '.rar',  # RAR 压缩文件
    'application/x-tar': '.tar',  # TAR 压缩文件
    'application/gzip': '.gz',  # GZIP 压缩文件
    'audio/mpeg': '.mp3',  # MP3 音频文件
    'audio/wav': '.wav',  # WAV 音频文件
    'audio/flac': '.flac',  # FLAC 无损音频文件
    'audio/aac': '.aac',  # AAC 音频文件
    'video/mp4': '.mp4',  # MP4 视频文件
    'video/x-matroska': '.mkv',  # Matroska 视频文件
    'video/quicktime': '.mov',  # QuickTime 视频文件
    'video/x-msvideo': '.avi',  # AVI 视频文件
    'application/msword': '.doc',  # Microsoft Word 文件
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',  # Microsoft Word 新格式文件
    'application/vnd.ms-excel': '.xls',  # Microsoft Excel 文件
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',  # Microsoft Excel 新格式文件
    'application/vnd.ms-powerpoint': '.ppt',  # Microsoft PowerPoint 文件
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',  # Microsoft PowerPoint 新格式文件
    'application/x-python-code': '.py',  # Python 源代码文件
    'text/javascript': '.js',  # JavaScript 文件
    'text/x-python': '.py',  # Python 文件（text类型）
    'text/xml': '.xml',  # XML 文本文件
    'application/xml': '.xml',  # XML 文件（application类型）
    'application/sql': '.sql',  # SQL 数据库文件
    'application/vnd.sqlite3': '.sqlite',  # SQLite 数据库文件
    'application/octet-stream': '.bin'  # 二进制文件
}

# 图片扩展名
image_valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}

# html 扩展名
html_valid_extensions = {'.html', '.htm', '.shtml', '.xhtml'}

attachment_valid_extensions = {'.docx', '.xlsx', '.xls', '.wps', '.zip', '.doc', '.pdf', '.txt', '.ofd', '.rar', '.ppt', '.pptx', '.tif'}


def is_valid_image_extension(extension: str):
    """
    判断扩展名是否有效（是否是常见图片格式）
    :param str extension: 文件扩展名
    :return: True 表示有效，False 表示无效
    """
    if not extension.startswith('.'):
        extension = '.' + extension

    valid_extensions = get_image_extension()
    return extension.lower() in valid_extensions


def is_valid_html_extension(extension: str):
    """
    判断扩展名是否有效 html 扩展名
    :param str extension: 文件扩展名
    :return: True 表示有效，False 表示无效
    """
    if not extension.startswith('.'):
        extension = '.' + extension

    # valid_extensions = list(html_valid_extensions)
    attachment_extensions = list(attachment_valid_extensions)
    return extension.lower() not in attachment_extensions


def is_valid_html_extension_by_url(url: str):
    """
    判断扩展名是否有效 html 扩展名
    :param str url: 文件扩展名
    :return: True 表示有效，False 表示无效
    """
    ext = get_extension_from_url(url, '.html')
    return is_valid_html_extension(ext)


def is_valid_attachment_extension(extension: str):
    """
    判断扩展名是否有效 附件 扩展名
    :param str extension: 文件扩展名
    :return: True 表示有效，False 表示无效
    """
    return not is_valid_html_extension(extension)


def get_image_extension() -> list:
    """
    获取扩展名
    :return: True 表示有效，False 表示无效
    """
    return list(image_valid_extensions)


def get_test_image(name):
    fullname = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'docs/images/', name)
    return fullname


def get_project_path(filepath):
    fullname = os.path.join(os.path.dirname(os.path.dirname(__file__)), filepath)
    return fullname


def get_key(key, path: str):
    return url_path_join(path, key)


def url_path_join(*args):
    """
    类似于 os.path.join 的 URL 路径拼接函数。
    确保各个路径片段正确拼接，不覆盖前面的路径。
    """
    # 将所有片段去掉多余的斜杠
    stripped_args = [str(arg).strip('/') for arg in args]

    # 用 '/' 分隔符连接各个路径片段
    return '/'.join(stripped_args)


def fetch_and_get_image_info(bucket_domain: str, key: str, logger=None):
    """
    从第三方 URL 抓取图片并返回图片的元数据信息。
    :param bucket_domain: 域名
    :param key: 在七牛云存储空间保存的文件名（key）
    :return: 图片的元数据信息 (width, height, format) 信息
    """
    image_info_url = urljoin(bucket_domain, f'{key}?imageInfo')
    # response = fetch_url_json(image_info_url)
    response = retry_with_backoff(image_info_url, logger)
    if response:
        return response.json()
    else:
        logger.error(f"fetch_and_get_image_info exception")
        return None


def get_image_info(info: ResponseInfo) -> dict:
    """
    转化图片信息格式
    :param info: 图片信息
    :return:
    """
    try:
        assert info.status_code == 200
        data = json.loads(info.text_body)
        data['req_id'] = info.req_id
        data['x_log'] = info.x_log
        # print(data)
        return data
    except Exception as ex:
        print(f"ex: {str(ex)}")
        return None


def calculate_md5(input_string) -> str:
    """
    计算字符串 md5
    :param str input_string:
    :return:
    """
    # Create an md5 hash object
    md5_hash = hashlib.md5()

    # Update the hash object with the string (it needs to be encoded to bytes)
    md5_hash.update(input_string.encode('utf-8'))

    # Return the hexadecimal digest of the hash
    return md5_hash.hexdigest()


def get_extension_from_url(url, default_extension='.jpg'):
    """
    根据图片 URL 请求头的 Content-Type 推断文件扩展名。
    :param url: URL 地址
    :param default_extension: 无法推断时的默认扩展名，默认为 '.jpg'
    :return: 文件扩展名 (例如 '.jpg', '.png')，如果无法推断则返回默认扩展名
    """
    try:

        header = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        }

        # 发起 HEAD 请求，获取图片的 Content-Type
        response = requests.head(url, allow_redirects=True, verify=False, headers=header, timeout=requests_headers_timeout)
        if response.status_code != 200:  # 服务器返回不是 200，则表示不支持 HEAD 请求, 405 Method Not Allowed
            response = requests.get(url, allow_redirects=True, stream=True, verify=False, headers=header, timeout=requests_headers_timeout)

        content_type = response.headers.get('Content-Type').split(';')[0].strip()
        if content_type == 'text/plain':    # text/plain 特殊处理
            response = requests.get(url, allow_redirects=True, verify=False, headers=header, timeout=requests_headers_timeout)
            html = BeautifulSoup(response.text, "html.parser").find()
            if html:
                content_type = 'text/html'

        # 为 mimetypes 添加映射
        for mime_type, ext in mime_extension_map.items():
            mimetypes.add_type(mime_type, ext)

        # 根据 Content-Type 推断扩展名
        extension = mimetypes.guess_extension(content_type)
        if extension is None:
            return default_extension

        return extension
    except Exception as ex:
        # 请求失败时，返回默认扩展名
        print(f"Error fetching extension: {str(ex)}")
        return default_extension


def ensure_tmp_directory():
    """确认临时目录存在"""
    tmp_dir = get_project_path('tmp')
    if not os.path.exists(tmp_dir):
        os.makedirs(tmp_dir)
    return tmp_dir


def retry_with_backoff(url, logger=None):
    @retry(tries=3, delay=2, backoff=2, exceptions=(requests.exceptions.RequestException,))
    def make_request(url):
        response = requests.get(url)
        response.raise_for_status()  # 如果请求失败会抛出异常
        return response

    try:
        response = make_request(url)
        return response
    except Exception as e:
        if logger:
            logger.error(f"Failed to make request after retries: {e}")
        # print(f"Failed to make request after retries: {e}")
        return None


def download_image(url, local_filename):
    """
    下载图片
    :param url:
    :param local_filename:
    :return:
    """
    tmp_dir = ensure_tmp_directory()  # 确保 tmp 目录存在
    local_filepath = os.path.join(tmp_dir, local_filename)

    response = retry_with_backoff(url)
    if response:
        with open(local_filepath, 'wb') as f:
            f.write(response.content)
        return local_filepath
    else:
        print(f"Failed to download image: {response.status_code}")
        return None


def get_local_image_info(filepath):
    """
    获取本地图片信息
    :param filepath:
    :return:
    """
    try:
        # 先判断文件扩展名是否为 SVG
        if filepath.lower().endswith('.svg'):
            return handle_svg(filepath)

        # 先尝试用 Pillow 打开文件（如果不是 SVG）
        with Image.open(filepath) as img:
            return {
                "size": os.path.getsize(filepath),
                "format": img.format,
                "width": img.width,
                "height": img.height,
                "colorModel": img.mode
            }
    except (OSError, IOError) as e:
        # 捕捉具体的错误信息，避免盲目假设是 SVG
        raise ValueError(f"无法打开文件 {filepath}: {e}")


def handle_svg(filepath):
    """
    svg 图片处理
    :param filepath:
    :return:
    """
    # 使用 cairosvg 将 SVG 转换为 PNG 并在内存中处理
    png_image_data = cairosvg.svg2png(url=filepath)
    img = Image.open(BytesIO(png_image_data))
    return {
        # "size": len(png_image_data),
        "size": os.path.getsize(filepath),
        "format": "svg",
        "width": img.width,
        "height": img.height,
        "colorModel": ""  # SVG 没有 color model 概念
    }


def process_image(url):
    """
    下载网络图片到本地并获取本地图片信息
    :param url:
    :return:
    """
    local_filename = url.split('/')[-1]
    local_filepath = download_image(url, local_filename)

    if local_filepath:
        info = get_local_image_info(local_filepath)

        # 删除文件
        os.remove(local_filepath)

        return info
    return None
