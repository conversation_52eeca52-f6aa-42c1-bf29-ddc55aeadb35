# -*- coding:utf-8 -*-
# @Function  : 数据采集工具类
# <AUTHOR>
# @Time      : 2024/9/7
# Version    : 1.0


def check_trade_control(sourceCategory: str) -> bool:
    """
    判断是否贸易管制
    :param sourceCategory:  数据源分类
    :return:
    """
    if not sourceCategory:
        return False

    return sourceCategory == 'dc_tradecontrol'


def check_attachment(sourceCategory: str) -> bool:
    """
    判断是否开启附件采集
    :param sourceCategory:  数据源分类
    :return:
    """
    if not sourceCategory:
        return False

    return sourceCategory in ['dc_tradecontrol', 'dc_bianjifagao']


def get_url_type(is_html) -> int:
    """ get url type """
    return 1 if is_html else 2


def get_url_type_by_file(is_file) -> int:
    """ get url type by file """
    return 2 if is_file else 1
