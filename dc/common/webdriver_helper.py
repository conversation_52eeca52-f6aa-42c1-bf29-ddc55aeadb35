import re
from typing import Dict, List

from selenium import webdriver
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement

from dc.common.webdriver_util import get_remote_webdriver, get_webdriver
from dc.tests.browser import Browser


class WebDriverHelper:

    def __init__(self, driver: WebDriver):
        if driver is None:
            raise ValueError('driver is empty')

        self.driver = driver
        self.data = []

    def find_elements(self, by=By.ID, value=None, timeout=3, time_to_wait=8) -> List[WebElement]:
        """
        find_elements
        :param by: By 查找方式
        :param value: str 默认值
        :param timeout: int 超时时间
        :param time_to_wait: int 默认等待时间
        :return: list of WebElement
        """
        try:
            self.driver.implicitly_wait(timeout)
            print('elements find (by:{by} value:{value})'.format(by=by, value=value))
            elements = self.driver.find_elements(by, value)
            self.driver.implicitly_wait(time_to_wait)
            return elements
        except TimeoutException as ex:
            print(ex.args)
            return False

    def find_element(self, by=By.ID, value=None, timeout=3, time_to_wait=8) -> WebElement:
        """
        find_elements
        :param by: By 查找方式
        :param value: str 默认值
        :param timeout: int 超时时间
        :param time_to_wait: int 默认等待时间
        :return: WebElement
        """
        try:
            self.driver.implicitly_wait(timeout)
            print('element find (by:{by} value:{value})'.format(by=by, value=value))
            element = self.driver.find_element(by, value)
            self.driver.implicitly_wait(time_to_wait)
            return element
        except Exception as ex:
            print(ex.args)
            return None

    def find_element_from_elements(self, elements: List[WebElement], by=By.ID, text='', isre=False) -> WebElement:
        """
        从元素数组中查看元素
        :param elements:
        :param by:
        :param text:
        :param isre: 是否正则表达式
        :return: WebElement - the element if it was found
        :rtype: WebElement
        :Usage:
            elements = helper.find_elements(elements,'id','新闻')
        """
        for adv in elements:
            if by == By.ID:
                val = adv.get_attribute("resourceId")
                if val == text:
                    return adv

            elif by == By.NAME:
                val = adv.get_attribute("text")
                if isre is False:
                    if val == text:
                        return adv
                else:
                    pattern = re.compile(text, re.I)  # re.I 表示忽略大小写  r'(\d+)图
                    m = pattern.match(val)
                    if m is not None:
                        return adv

            elif by == By.CLASS_NAME:
                val = adv.get_attribute("className")
                if val == text:
                    return adv
            else:
                return None

        return None

    def find_element_by_text(self, elements, text, isre=False) -> WebElement:
        """
        通过文本查找元素
        :param elements:
        :param text:
        :param isre: 是否正则表达式
        :return:
           - WebElement - the element if it was found
        :rtype: WebElement
        :Usage:
            elements = helper.find_element_by_text(elements,,'新闻')
        """
        element = self.find_element_from_elements(elements, By.NAME, text, isre)
        if element is not None:
            return element
        else:
            return None

    def attribute(self, element: WebElement, attribute: str) -> str:
        """
        获取元素文本
        :param element: WebElement
        :return: element text
        :rtype:str
        """
        if element is None:
            return ''

        try:
            value = element.get_attribute(attribute)
            return value

        except Exception as er:
            # print('attribute',er.args)
            return ''

    def text(self, element) -> str:
        """
        获取元素文本
        :param element:
        :return: element text
        :rtype:str
        """
        return element.text if element is not None else ''

    def get_attributes(self, element: WebElement, pre='') -> Dict:
        """
        获取元素的所有属性
        :param element: WebElement
        :param pre:
        :return: dict
        """
        data = {}
        name = self.attribute(element, "name")
        resourceId = self.attribute(element, "resourceId")
        className = self.attribute(element, "className")
        checkable = self.attribute(element, "checkable")
        clickable = self.attribute(element, "clickable")
        # index = self.attribute(element, "index")
        # instance = self.attribute(element, "instance")

        # data['index'] = index
        data['resourceId'] = resourceId
        data['className'] = className
        # data['instance'] = instance
        data['text'] = ''

        if className == 'android.widget.LinearLayout':
            pass
        elif className in ['android.widget.TextView', 'android.widget.Button', 'android.view.View']:
            text = self.attribute(element, "text")
            # content_desc = self.attribute(element, "content-desc")
            data['text'] = text
            # data['content-desc'] = content_desc
            pass
        else:
            pass

        return data

    def get_elements_attributes(self, links) -> List:
        """
        获取元素数组的所有属性
        :param links: list of WebElement
        :return: list
        """
        data = []
        for element in links:
            x = self.get_attributes(element)
            data.append(x)

        return data

    def switch_to_alert(self):
        """
        获取元素数组的所有属性
        :param links: list of WebElement
        :return: list
        """
        alert = None
        try:
            alert = self.driver.switch_to.alert
        except Exception as er:
            # print(er)
            pass
        finally:
            return alert

    @staticmethod
    def get_remote_webdriver(webdriver_url: str, browser: Browser):
        """
        get remote webdriver
        :param webdriver_url: str url for remote
        :param browser: Browser browser type
        :return:
        """
        return get_remote_webdriver(webdriver_url=webdriver_url, browser=browser)

    @staticmethod
    def get_webdriver(browser: Browser):
        if browser == Browser.Firefox:
            opt = webdriver.FirefoxOptions()
            opt.add_argument("no-sandbox")
            opt.add_argument("--disable-extensions")
            opt.add_argument("--headless")
            opt.add_argument("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0")
            return webdriver.Chrome(executable_path="/usr/local/bin/geckodriver", options=opt)
        elif browser == Browser.Edge:
            opt = webdriver.EdgeOptions()
            opt.add_argument("no-sandbox")
            opt.add_argument("--disable-extensions")
            opt.add_argument("--headless")
            opt.add_argument("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0")
            return webdriver.Chrome(executable_path="/usr/local/bin/msedgedriver", options=opt)
        else:
            opt = webdriver.ChromeOptions()
            opt.add_argument("no-sandbox")
            opt.add_argument("--disable-extensions")
            opt.add_argument("--headless")
            opt.add_argument("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0")
            return webdriver.Chrome(executable_path="/usr/local/bin/chromedriver", options=opt)
