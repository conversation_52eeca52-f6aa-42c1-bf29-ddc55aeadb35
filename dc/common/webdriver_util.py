from typing import List, Any

from selenium import webdriver
from selenium.webdriver import DesiredCapabilities
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement

from dc.conf.webdriver_config import *
from dc.tests.browser import <PERSON>rowser
from dc.services.dc_proxy_pool_service import DcProxyPoolService
from faker import Faker

page_load_timeout = 120  # 页面加载超时
script_timeout = 30  # 脚本执行超时
implicitly_wait = 10  # 隐式等待


def list_merge(list1: list, list2: list):
    """merge list"""
    list1.extend(list2)
    return list(dict.fromkeys(list1))


def get_remote_webdriver(webdriver_url: str, browser: Browser, options: dict = {}) -> WebDriver:
    """
    get remote webdriver
    :author wjh
    :date 2024-2-28
    :param webdriver_url: str url for remote
    :param browser: browser type
    :param options: options , include arguments, experimental_options, extensions
    :return:
    """
    arguments: list[str] = options.get('arguments', [])
    experimental_options: dict = options.get('experimental_options', {})
    extensions: list[str] = options.get('extensions', [])
    preferences: dict = options.get('preferences', {})
    if browser == Browser.Firefox:
        init_arguments = [
            # "no-sandbox",
            # "--disable-extensions",
            '--disable-gpu',
            "--headless",
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        ]

        opt = webdriver.FirefoxOptions()
        arguments = list_merge(init_arguments, arguments)
        for argument in arguments:
            opt.add_argument(argument)

        for name, value in preferences.items():
            opt.set_preference(name, value)

        driver = webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.FIREFOX,
                                  options=opt)
    else:

        init_arguments = [
            "no-sandbox",
            "--disable-extensions",
            '--disable-gpu',
            "--headless",
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        ]

        opt = webdriver.ChromeOptions()
        arguments = list_merge(init_arguments, arguments)
        for argument in arguments:
            opt.add_argument(argument)

        for k, v in experimental_options.items():
            opt.add_experimental_option(k, v)

        for extension in extensions:
            opt.add_extension(extension)

        driver = webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.CHROME, options=opt)

    driver.set_page_load_timeout(page_load_timeout)  # 页面加载超时
    driver.set_script_timeout(script_timeout)  # 脚本执行超时
    driver.implicitly_wait(implicitly_wait)  # 隐式等待
    return driver


def get_remote_webdriver_web(webdriver_url: str, browser: Browser, options: dict = {}) -> WebDriver:
    """
    get remote webdriver
    :author wwy
    :date 2024-6-7
    :param webdriver_url: str url for remote
    :param browser: browser type
    :param options: options , include arguments, experimental_options, extensions
    :return:
    """
    arguments: list[str] = options.get('arguments', [])
    experimental_options: dict = options.get('experimental_options', {})
    extensions: list[str] = options.get('extensions', [])
    preferences: dict = options.get('preferences', {})
    fake = Faker()
    if browser == Browser.Firefox:

        init_arguments = [
            # "no-sandbox",
            # "--disable-extensions",
            "--headless",
            f"User-Agent: {fake.firefox()}"
        ]

        opt = webdriver.FirefoxOptions()
        arguments = list_merge(init_arguments, arguments)
        for argument in arguments:
            opt.add_argument(argument)

        for name, value in preferences.items():
            opt.set_preference(name, value)

        driver = webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.FIREFOX, options=opt)
    else:

        init_arguments = [
            "no-sandbox",
            "--disable-extensions",
            # '--disable-gpu',
            "--headless",
            f"User-Agent: {fake.chrome()}"
        ]

        opt = webdriver.ChromeOptions()
        arguments = list_merge(init_arguments, arguments)
        for argument in arguments:
            opt.add_argument(argument)

        for k, v in experimental_options.items():
            opt.add_experimental_option(k, v)

        for extension in extensions:
            opt.add_extension(extension)

        driver = webdriver.Remote(command_executor=webdriver_url, desired_capabilities=DesiredCapabilities.CHROME, options=opt)

    driver.set_page_load_timeout(page_load_timeout)  # 页面加载超时
    driver.set_script_timeout(script_timeout)  # 脚本执行超时
    driver.implicitly_wait(implicitly_wait)  # 隐式等待
    return driver


def get_remote_webdriver_proxy(webdriver_url: str, browser: Browser,
                               proxy_id: str, is_use_socks: bool = False, options: dict = {}) -> WebDriver:
    """
    get remote webdriver proxy
    :author wwy
    :date 2024-4-9
    :param webdriver_url: str url for remote
    :param browser: browser type
    :param proxy_id: 使用的id编号
    :param is_use_socks: 是否使用socks，仅支持谷歌浏览器
    :param options: options , include arguments, experimental_options, extensions
    :return:
    """
    proxy_service = DcProxyPoolService()
    if browser == Browser.Firefox:
        setting = proxy_service.get_selenium_firefox_proxy(proxy_id=proxy_id)
        if 'preferences' not in options:
            options['preferences'] = setting
        else:
            options['preferences'].update(setting)
    else:
        setting = proxy_service.get_selenium_chrome_proxy(proxy_id=proxy_id,
                                                          is_use_socks=is_use_socks)
        if 'arguments' not in options:
            options['arguments'] = [setting]
        else:
            options['arguments'].append(setting)
    return get_remote_webdriver_web(webdriver_url, browser, options)


def get_webdriver(browser: Browser, options: dict = {}) -> WebDriver:
    """
    get webdriver from local
    :param browser: browser type
    :param options: options , include arguments, experimental_options, extensions
    :return: WebDriver
    """
    arguments: list[str] = options.get('arguments', [])
    experimental_options: dict = options.get('experimental_options', {})
    extensions: list[str] = options.get('extensions', [])
    preferences: dict = options.get('preferences', {})
    if browser == Browser.Firefox:
        init_arguments = [
            "no-sandbox",
            "--disable-extensions",
            '--disable-gpu',
            "--headless",
            # "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        ]

        opt = webdriver.FirefoxOptions()
        arguments = list_merge(init_arguments, arguments)
        for argument in arguments:
            opt.add_argument(argument)

        for k, v in preferences.items():
            opt.set_preference(k, v)

        opt.binary_location = firefox_binary_location
        driver = webdriver.Firefox(executable_path=firefox_executable_path, options=opt)
    else:
        init_arguments = [
            "no-sandbox",
            "--disable-extensions",
            '--disable-gpu',
            "--headless=chrome",
            # "--auto-open-devtools-for-tabs"
            # "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:99.0) Gecko/20100101 Firefox/99.0"
        ]

        caps = DesiredCapabilities.CHROME
        caps['goog:loggingPrefs'] = {'performance': 'ALL'}

        opt = webdriver.ChromeOptions()
        arguments = list_merge(init_arguments, arguments)
        for argument in arguments:
            opt.add_argument(argument)

        for k, v in experimental_options.items():
            opt.add_experimental_option(k, v)

        for extension in extensions:
            opt.add_extension(extension)

        opt.binary_location = chrome_binary_location
        driver = webdriver.Chrome(executable_path=chrome_executable_path, options=opt, desired_capabilities=caps)

    driver.set_page_load_timeout(page_load_timeout)  # 页面加载超时
    driver.set_script_timeout(script_timeout)  # 脚本执行超时
    driver.implicitly_wait(implicitly_wait)  # 隐式等待
    return driver


def format_content(content):
    """
    格式化文本内容，主要处理多空行问题
    :author wjh
    :date 2024-2-28
    :param content:
    :return:
    """
    lines = content.split('\n')
    lines = list(filter(lambda x: len(x.strip()) > 0, lines))
    content = "\n".join(lines)
    return content


def find_element(driver: WebDriver, by=By.ID, value=None, timeout=3) -> WebElement:
    """
    find_element by webdriver
    :author wjh
    :date 2024-2-28
    :param driver: web driver
    :param by: By 查找方式
    :param value: str 默认值
    :param timeout: int 超时时间
    :return: WebElement , return None if error
    """
    try:
        implicit_wait = driver.timeouts.implicit_wait
        driver.implicitly_wait(timeout)
        print('element find (by:{by} value:{value})'.format(by=by, value=value))
        element = driver.find_element(by, value)
        driver.implicitly_wait(implicit_wait)
        return element
    except Exception as ex:
        print(ex.args)
        return None


def find_elements(driver: WebDriver, by=By.ID, value=None, timeout=3) -> list[WebElement]:
    """
    find_elements by webdriver
    :author wjh
    :date 2024-2-28
    :param driver: web driver
    :param by: By 查找方式
    :param value: str 默认值
    :param timeout: int 超时时间
    :return: list[WebElement] , return None if error
    """
    try:
        implicit_wait = driver.timeouts.implicit_wait
        driver.implicitly_wait(timeout)
        print('element find (by:{by} value:{value})'.format(by=by, value=value))
        elements = driver.find_elements(by, value)
        driver.implicitly_wait(implicit_wait)
        return elements
    except Exception as ex:
        print(ex.args)
        return None
