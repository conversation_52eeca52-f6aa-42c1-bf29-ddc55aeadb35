import base64
import urllib
import urllib.parse
from datetime import timedelta

import greenstalk
from dc.services.elasticsearch_service import ElasticsearchService

from dc.conf.settings import get_settings2

from dc.services.logging_service import LoggingService
from flask import Blueprint
from flask import Response
from jwtools.func import *
from prometheus_client import Gauge, generate_latest
from prometheus_client.core import CollectorRegistry
from sqlalchemy import func, text

from dc.common.asserts import *
from dc.common.functools_wraps import *
from dc.models.model import DCGzhArticle
from dc.services.dashboard_service import DashboardService
from dc.services.mysql_service import MySQLService
from dc.services.redis_service import RedisService

dashboard = Blueprint('app_dashboard', __name__)

logger = LoggingService(logfile='dashboard.log')
ds = DashboardService(config={'logger': logger})


@dashboard.route("/metrics_test")
def metrics_test():
    # 设置metrics
    # Prometheus提供4种类型Metrics：Counter, Gauge, Summary和Histogram
    # Counter 累加器，只有inc方法,定义方法指标名，描述，默认增长值是1
    # Gauge 可任意设置，比如cpu、内存、磁盘等指标，定义方法，指标名，描述，标签
    # Histogram 分桶统计，对每个桶的数据进行统计
    # Summary 分位统计，对每个值进行统计

    registry = CollectorRegistry(auto_describe=False)
    product_cvm = Gauge('product_cvm', 'product_usage_cvm', ['product'], registry=registry)
    product_cbs = Gauge('product_cbs', 'product_usage_cbs', ['product'], registry=registry)
    product_clb = Gauge('product_clb', 'product_usage_clb', ['product'], registry=registry)

    # 获取源数据，数据源可以是任意接口、数据库、文件等
    data = {
        'cvm': 1,
        'cbs': 2,
        'clb': 3
    }

    for key, value in data.items():
        if key == 'cvm':
            product_cvm.labels(product=key).set(value)
        elif key == 'cbs':
            product_cbs.labels(product=key).set(value)
        elif key == 'clb':
            product_clb.labels(product=key).set(value)
    return Response(generate_latest(registry), mimetype="text/plain")


@dashboard.route("/task/metrics")
def task_metrics():
    """数据采集任务积压情况统计"""
    # 设置metrics
    # Prometheus提供4种类型Metrics：Counter, Gauge, Summary和Histogram
    # Counter 累加器，只有inc方法,定义方法指标名，描述，默认增长值是1
    # Gauge 可任意设置，比如cpu、内存、磁盘等指标，定义方法，指标名，描述，标签
    # Histogram 分桶统计，对每个桶的数据进行统计
    # Summary 分位统计，对每个值进行统计

    redis_prefix = 'laravel_database_'
    keys = [f"{redis_prefix}dc-main-task", f"{redis_prefix}dc-site-task", f"{redis_prefix}dc-site-task-analyse",
            f"{redis_prefix}dc-site-list-hash"]

    redis = RedisService()
    client = redis.get_client()
    client.select(0)

    registry = CollectorRegistry(auto_describe=False)
    for key in keys:
        key_type = client.type(key)
        length = redis.get_length(key)
        key = key.replace('-', '_').removeprefix(redis_prefix)
        print_vf(
            f"{key}, {key_type}, {length}"
        )
        product_cvm = Gauge(f'dc_task_{key}', f'dc_task_usage_{key}', ['task'], registry=registry)
        product_cvm.labels(task=key).set(length)

    return Response(generate_latest(registry), mimetype="text/plain")


@dashboard.route("/gzh/metrics")
@deprecated(message="gzh_metrics is deprecated and may be removed in a future version.")
def gzh_metrics():
    """公众号文章总量统计"""

    mysql = MySQLService()
    session = mysql.create_session()
    data = session.execute('''
    SELECT COUNT(*) as total
FROM DC_GzhArticle
WHERE `status`=1
    ''').all()

    registry = CollectorRegistry(auto_describe=False)
    g = Gauge(f'dc_gzh_total', f'dc_gzh_total_usage', ['task'], registry=registry)
    for item in data:
        key = 'total'
        length = item['total']
        print_vf(
            f"{key}, {length}"
        )
        g.labels(task=key).set(length)

    session.close()

    return Response(generate_latest(registry), mimetype="text/plain")


@dashboard.route("/mysql_count/metrics")
def mysql_count_metrics():
    """
    mysql 按照表名统计数量
    访问：/dashboard/mysql_count/metrics?table_name=DC_GzhArticle
    可用参数:
        table_name 表名
        where 搜索条件
    :return:
    """

    table_name = request.args.get('table_name', 'DC_GzhArticle')
    where = urllib.parse.unquote(request.args.get('where', 'status=1'))

    # 创建 registry 用于每次请求时生成新的 metrics 数据
    registry = CollectorRegistry(auto_describe=False)

    # 全局注册 Gauge 指标，带有日期标签
    article_count_gauge = Gauge('mysql_count', 'Number of records created', ['table'], registry=registry)

    mysql = MySQLService()
    session = mysql.create_session()

    try:
        # 构造 SQL 查询语句，按传入的表名和字段分组
        sql = f"""
                SELECT COUNT(*) as count
                FROM {table_name}
                WHERE {where}
            """

        # 执行 SQL 语句
        results = session.execute(text(sql)).scalar()

        # 设置 Prometheus 指标，并为其添加表名、分组字段和日期标签
        gauge = article_count_gauge.labels(table=table_name)
        gauge.set(results)

        # 将 Gauge 指标注册到新的 registry 中
        # registry.register(article_count_gauge)

    except Exception as e:
        print(f"Error: {e}")
        return str(e)
    finally:
        session.close()

    return Response(generate_latest(registry), mimetype="text/plain")


@dashboard.route("/mysql_count_by_period/metrics")
def mysql_count_by_period_metrics():
    """
    mysql 按照表名、时段统计数量
    访问：/dashboard/mysql_count_by_period/metrics?table_name=DC_GzhArticle&time_field=createAt&where=status=1&interval_minutes=5
    可用参数:
        table_name 表名
        time_field 时间字段
        where 搜索条件
        interval_minutes 分钟数
    :return:
    """

    table_name = request.args.get('table_name', 'DC_GzhArticle')
    time_field = urllib.parse.unquote(request.args.get('time_field', 'createAt'))
    where = urllib.parse.unquote(request.args.get('where', 'status=1'))
    interval_minutes = int(request.args.get('interval_minutes', 1))

    # 创建 registry 用于每次请求时生成新的 metrics 数据
    registry = CollectorRegistry(auto_describe=False)

    # 全局注册 Gauge 指标，带有日期标签
    article_count_gauge = Gauge('mysql_count_by_period_count', 'Number of records created', ['table', 'interval_minutes'], registry=registry)

    mysql = MySQLService()
    session = mysql.create_session()

    try:
        # 构造 SQL 查询语句，按传入的表名和字段分组
        sql = ds.generate_sql_for_previous_period(table_name, time_field, where, interval_minutes)

        # 执行 SQL 语句
        results = session.execute(text(sql)).scalar()

        # 设置 Prometheus 指标，并为其添加表名、分组字段和日期标签
        article_count_gauge.labels(table=table_name, interval_minutes=interval_minutes).set(results)

        # 将 Gauge 指标注册到新的 registry 中
        # registry.register(article_count_gauge)

    except Exception as e:
        print(f"Error: {e}")
        return str(e)
    finally:
        session.close()

    return Response(generate_latest(registry), mimetype="text/plain")


@dashboard.route("/mysql_count_by_date/metrics")
def mysql_count_by_date_metrics():
    """
    mysql 按照表名、分组字段、日期统计数量
    访问：/dashboard/mysql_count_by_date/metrics?table_name=DC_GzhArticle&time_field=createAt&where=status=1&days=7
    可用参数:
        table_name 表名
        time_field 时间字段
        where 搜索条件
        days 天数
    :return:
    """

    table_name = request.args.get('table_name', 'DC_GzhArticle')
    time_field = request.args.get('time_field', 'createAt')
    where = urllib.parse.unquote(request.args.get('where', 'status=1'))
    days = int(request.args.get('days', 7))

    # 创建 registry 用于每次请求时生成新的 metrics 数据
    registry = CollectorRegistry(auto_describe=False)

    # 全局注册 Gauge 指标，带有日期标签
    article_count_gauge = Gauge('mysql_count_by_date_count', 'Number of records created', ['date', 'table', 'group_field'], registry=registry)

    mysql = MySQLService()
    session = mysql.create_session()

    try:
        # 计算过去 7 天的日期
        seven_days_ago = (datetime.datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        # 构造 SQL 查询语句，按传入的表名和字段分组
        sql = f"""
                SELECT DATE({time_field}) as date, COUNT(*) as count
                FROM {table_name}
                WHERE {where} and {time_field} >= :seven_days_ago
                GROUP BY DATE({time_field})
            """

        # 执行 SQL 语句
        results = session.execute(text(sql), {'seven_days_ago': seven_days_ago}).fetchall()

        # 初始化每一天的计数为 0
        today = datetime.datetime.now().date()

        # 将查询结果映射到 Prometheus 标签
        for result in results:
            result_date = result['date'].strftime('%Y-%m-%d')  # 转换为字符串格式的日期标签
            count = result['count']

            # 设置 Prometheus 指标，并为其添加表名、分组字段和日期标签
            gauge = article_count_gauge.labels(date=result_date, table=table_name, group_field=time_field)
            gauge.set(count)

        # 将 Gauge 指标注册到新的 registry 中
        # registry.register(article_count_gauge)

    except Exception as e:
        print(f"Error: {e}")
        return str(e)
    finally:
        session.close()

    return Response(generate_latest(registry), mimetype="text/plain")


@dashboard.route("/redis/metrics")
def redis_metrics():
    """
    redis 按照db 和 key 统计数量
    访问：/dashboard/redis/metrics?key=dc_article_weight&db=0
    可用参数:
        key key
        db 数据库
        remove_prefix 去掉前缀
        check_key 检查key是否存在
    :return:
    """

    redis = RedisService()
    client = None

    try:
        key = request.args.get("key", None)     # key
        db = request.args.get("db", 0)          # 数据库
        remove_prefix = request.args.get("remove_prefix", "")   # 去掉前缀
        check_key = request.args.get("check_key", False)        # 检查key是否存在
        assert_true(key, "key is empty")

        # redis
        client = redis.get_client()
        client.select(db)
        exists = client.exists(key)
        if check_key:
            assert_true(exists, "key not exists")

        if not exists:
            length = -1
            key_type = "none"
        else:
            key_type = client.type(key)
            length = redis.get_length(key)

        # registry
        registry = CollectorRegistry(auto_describe=False)
        key = key.replace('-', '_').removeprefix(remove_prefix)
        print_vf(
            f"{key}, {key_type}, {length}"
        )
        product_cvm = Gauge(f'redis_metrics_{key}', f'redis_metrics_usage_{key}', ['db', 'key'], registry=registry)
        product_cvm.labels(db=db, key=key).set(length)

        return Response(generate_latest(registry), mimetype="text/plain")

    except Exception as e:
        return str(e)
    finally:
        if client:
            client.close()


@dashboard.route("/elasticsearch_count/metrics")
def elasticsearch_count_metrics():
    """
    统计 Elasticsearch 文档数量
    访问：/elasticsearch/metrics?connection=connection&query=query
    可用参数:
        index 索引， 如 dc_hotarticle
        query 已编码的查询语句， 如： { "query": { "match_all": {} }}   可以使用 {{ startTime }}   {{ stopTime }}
        interval_minutes 分钟数
    :return:
    """
    logger.info(f"elasticsearch_count_metrics request: {request.args}")

    registry = CollectorRegistry(auto_describe=False)
    client = None

    try:
        index = request.args.get("index", "dc_hotarticle")  # 索引
        encoded_query = request.args.get("query", '{ "query": { "match_all": {} }}')  # 已编码的查询语句
        interval_minutes = int(request.args.get('interval_minutes', 1440))  # 1天

        assert encoded_query, "query is empty"

        logger.info(f"elasticsearch_count_metrics index: {index}")
        logger.info(f"elasticsearch_count_metrics encoded_query: {encoded_query}")

        # 解码连接字符串和查询语句
        query_template = encoded_query

        # 替换查询语句中的占位符
        # query = query_template
        query = ds.generate_simple_query_for_previous_period(query_template=query_template, interval_minutes=interval_minutes)

        # Elasticsearch 客户端
        ess = ElasticsearchService()
        client = ess.get_connect()

        # 执行查询语句获取文档数量
        response = client.count(index=index, body=query)
        doc_count = response['count']
        client.close()

        # 注册指标
        doc_count_gauge = Gauge('elasticsearch_doc_count', 'Document count based on custom query', ['index'], registry=registry)
        doc_count_gauge.labels(index=index).set(doc_count)

        return Response(generate_latest(registry), mimetype="text/plain")

    except Exception as ex:
        logger.error(f"elasticsearch_count_metrics Exception: {str(ex)}")
        return str(ex)
    finally:
        if client:
            client.transport.close()


@dashboard.route("/elasticsearch_count_by_period/metrics")
def elasticsearch_count_by_period_metrics():
    """
    统计 Elasticsearch 按照时间段 文档数量
    访问：/elasticsearch_count_by_period/metrics?connection=connection&index=index&query=query&time_field=createAt&interval_minutes=10
    可用参数:
        index 索引， 如 dc_hotarticle
        query 已编码的查询语句， 如 { "query": { "range": { "createAt": { "gte": "{{ startTime }}", "lte": "{{ stopTime }}" } } } }
        time_field 时间字段
        interval_minutes 分钟数
    :return:
    """
    logger.info(f"elasticsearch_count_by_period_metrics request: {request.args}")

    registry = CollectorRegistry(auto_describe=False)
    client = None

    try:
        index = request.args.get("index", "dc_hotarticle")  # 索引
        encoded_query = request.args.get("query", '{ "query": { "range": { "createAt": {"gte": "{{ startTime }}",  "lte": "{{ stopTime }}" } } } }')
        interval_minutes = int(request.args.get('interval_minutes', 1))

        assert encoded_query, "query is empty"
        assert interval_minutes, "interval_minutes is empty"

        logger.info(f"elasticsearch_count_by_period_metrics encoded_query: {encoded_query}")
        logger.info(f"elasticsearch_count_by_period_metrics interval_minutes: {interval_minutes}")

        # 解码连接字符串和查询语句
        query_template = encoded_query

        # 替换查询语句中的占位符
        # query = query_template.replace("{{ timeField }}", time_field).replace("{{ startTime }}", start_time).replace("{{ stopTime }}", stop_time)

        # 构造 SQL 查询语句，按传入的表名和字段分组
        query = ds.generate_query_for_previous_period(query_template=query_template, interval_minutes=interval_minutes)

        # Elasticsearch 客户端
        ess = ElasticsearchService()
        client = ess.get_connect()

        # 执行查询语句获取文档数量
        response = client.count(index=index, body=query)
        doc_count = response['count']
        client.close()

        # 注册指标
        doc_count_gauge = Gauge('elasticsearch_count_by_period_count', 'Document count based on custom query', ['index', 'interval_minutes'], registry=registry)
        doc_count_gauge.labels(index=index, interval_minutes=interval_minutes).set(doc_count)

        return Response(generate_latest(registry), mimetype="text/plain")

    except Exception as ex:
        logger.error(f"elasticsearch_count_by_period_metrics Exception: {str(ex)}")
        return str(ex)
    finally:
        if client:
            client.transport.close()


@dashboard.route("/elasticsearch_group_count_by_period/metrics")
def elasticsearch_group_count_by_period_metrics():
    """
    统计 Elasticsearch 按照时间段 文档数量
    访问：/elasticsearch_count_by_period/metrics?index=index&query=query&time_field=createAt&interval_minutes=10
    可用参数:
        index 索引， 如 dc_hotarticle
        query 已编码的查询语句， 如 {"size": 0, "query": {"bool": {"must": [{"range": {"createdAt": {"gte": "{{ startTime }}", "lte": "{{ stopTime }}"}}}, {"term": {"is_repeat": {"value": "1"}}}]}}, "aggs": {"type_count": {"terms": {"field": "type", "size": 10}}}}
        time_field 时间字段
        interval_minutes 分钟数
    :return:
    """
    logger.info(f"elasticsearch_count_by_period_metrics request: {request.args}")

    registry = CollectorRegistry(auto_describe=False)
    client = None

    try:
        index = request.args.get("index", "dc_hotarticle")  # 索引
        encoded_query = request.args.get("query", '{ "query": { "range": { "createAt": {"gte": "{{ startTime }}",  "lte": "{{ stopTime }}" } } } }')
        interval_minutes = int(request.args.get('interval_minutes', 1))

        assert encoded_query, "query is empty"
        assert interval_minutes, "interval_minutes is empty"

        logger.info(f"elasticsearch_count_by_period_metrics encoded_query: {encoded_query}")
        logger.info(f"elasticsearch_count_by_period_metrics interval_minutes: {interval_minutes}")

        # 解码连接字符串和查询语句
        query_template = encoded_query

        # 替换查询语句中的占位符
        # query = query_template.replace("{{ timeField }}", time_field).replace("{{ startTime }}", start_time).replace("{{ stopTime }}", stop_time)

        # 构造 SQL 查询语句，按传入的表名和字段分组
        query = ds.generate_query_for_previous_period(query_template=query_template, interval_minutes=interval_minutes)

        # Elasticsearch 客户端
        ess = ElasticsearchService()
        client = ess.get_connect()

        # 执行查询语句获取文档数量
        response = client.search(index=index, body=query)
        buckets = response['aggregations']['type_count']['buckets']
        doc_count_gauge = Gauge('elasticsearch_group_count_by_period_count', 'Document count based on custom query', ['index', 'type', 'interval_minutes'], registry=registry)
        for bucket in buckets:
            # 注册指标
            doc_count_gauge.labels(index=index, interval_minutes=interval_minutes, type=bucket['key']).set(bucket['doc_count'])

        client.close()
        return Response(generate_latest(registry), mimetype="text/plain")

    except Exception as ex:
        logger.error(f"elasticsearch_count_by_period_metrics Exception: {str(ex)}")
        return str(ex)
    finally:
        if client:
            client.transport.close()


@dashboard.route("/beanstalk_state/metrics")
def beanstalk_state_metrics():
    """
    Beanstalk 服务器状态指标
    访问：/dashboard/beanstalk_state/metrics?host=127.0.0.1&port=11300
    可用参数:
        host 服务器地址
        port 服务器端口
    :return:
    """

    host = request.args.get('host', '127.0.0.1')
    port = int(request.args.get('port', 11300))

    def update_metrics(servers):
        """更新每个服务器的 Prometheus 指标"""
        for server in servers:
            server_ip = server['host']
            server_port = str(server.get('port', 11300))
            tube_stats = ds.get_beanstalk_stats(server_ip, int(server_port))

            for tube, stats in tube_stats.items():
                total_jobs_gauge.labels(server=server_ip, port=server_port, tube=tube).set(stats['total-jobs'])
                ready_jobs_gauge.labels(server=server_ip, port=server_port, tube=tube).set(stats['current-jobs-ready'])
                reserved_jobs_gauge.labels(server=server_ip, port=server_port, tube=tube).set(stats['current-jobs-reserved'])
                delayed_jobs_gauge.labels(server=server_ip, port=server_port, tube=tube).set(stats['current-jobs-delayed'])
                buried_jobs_gauge.labels(server=server_ip, port=server_port, tube=tube).set(stats['current-jobs-buried'])

    # 创建 registry 用于每次请求时生成新的 metrics 数据
    registry = CollectorRegistry(auto_describe=False)

    # 定义 Prometheus 指标，增加 server 标签
    total_jobs_gauge = Gauge('beanstalk_total_jobs', 'Total jobs in Beanstalk', ['server', 'port', 'tube'], registry=registry)
    ready_jobs_gauge = Gauge('beanstalk_ready_jobs', 'Ready jobs in Beanstalk', ['server', 'port', 'tube'], registry=registry)
    reserved_jobs_gauge = Gauge('beanstalk_reserved_jobs', 'Reserved jobs in Beanstalk', ['server', 'port', 'tube'], registry=registry)
    delayed_jobs_gauge = Gauge('beanstalk_delayed_jobs', 'Delayed jobs in Beanstalk', ['server', 'port', 'tube'], registry=registry)
    buried_jobs_gauge = Gauge('beanstalk_buried_jobs', 'Buried jobs in Beanstalk', ['server', 'port', 'tube'], registry=registry)

    try:
        servers = [
            {'host': host, 'port': port},
            # {'host': '*************', 'port': 11300}
        ]

        update_metrics(servers)
    except Exception as e:
        print(f"Error: {e}")
        return str(e)
    finally:
        pass

    return Response(generate_latest(registry), mimetype="text/plain")


@dashboard.route("/gzh/date/metrics")
@deprecated(message="gzh_date_metrics is deprecated and may be removed in a future version.")
def gzh_date_metrics():
    """公众号文章按照日期统计最近一个月文章数量"""

    mysql = MySQLService()
    session = mysql.create_session()
    data = session.execute('''
    SELECT DATE(createAt) as date, COUNT(*) as total
FROM DC_GzhArticle
WHERE `status`=1 and createAt >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
GROUP BY DATE(createAt)
ORDER BY date;
    ''').all()

    registry = CollectorRegistry(auto_describe=False)
    g = Gauge(f'dc_gzh_date_total', f'dc_gzh_date_total_usage', ['day'], registry=registry)
    for item in data:
        key = str(item['date'])
        length = item['total']
        g.labels(day=key).set(length)

    session.close()

    return Response(generate_latest(registry), mimetype="text/plain")


@dashboard.route("/mysql_date/metrics")
@deprecated(message="mysql_date_metrics is deprecated and may be removed in a future version.")
def mysql_date_metrics():
    """公众号文章总量统计"""

    mysql = MySQLService()
    session = mysql.create_session()
    registry = CollectorRegistry(auto_describe=False)

    # 全局注册 Gauge 指标，带有日期标签
    article_count_gauge = Gauge('dc_gzharticle_created_count', 'Number of articles created', ['date'], registry=registry)

    try:
        # 计算过去 7 天内按天分组的新增记录数量
        seven_days_ago = datetime.datetime.now().date() - timedelta(days=7)

        # 使用 SQLAlchemy 按天分组查询记录数量
        results = session.query(
            func.date(DCGzhArticle.createAt),
            func.count(DCGzhArticle.id)
        ).filter(
            DCGzhArticle.createAt >= seven_days_ago
        ).group_by(
            func.date(DCGzhArticle.createAt)
        ).all()

        # 初始化每一天的计数为 0
        today = datetime.datetime.now().date()

        # 将查询结果映射到 Prometheus 标签
        for result in results:
            result_date = result[0].strftime('%Y-%m-%d')  # 转换为字符串格式的日期标签
            count = result[1]

            # 设置 Prometheus 指标，并为其添加日期标签
            gauge = article_count_gauge.labels(date=result_date)
            gauge.set(count)

    except Exception as e:
        print(f"Error: {e}")
    finally:
        session.close()

    return Response(generate_latest(registry), mimetype="text/plain")
