import flask
import openai
from flask import Blueprint, make_response, request
from flask import Response, Flask
from atexit import register
import mimetypes
from prometheus_client.core import CollectorRegistry
from prometheus_client import Gauge, Counter, Info, Enum, generate_latest, start_http_server
from dc.services.redis_service import RedisService
from jwtools.func import *
from dc.models.model import DCGzhInfo, DCGzhArticle
from dc.services.mysql_service import MySQLService

wechat = Blueprint('app_wechat', __name__)

