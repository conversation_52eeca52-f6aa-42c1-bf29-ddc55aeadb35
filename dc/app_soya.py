import flask
import openai
from flask import Blueprint, make_response, request
from dc.common.functools_wraps import *

app_soya = Blueprint('app_soya', __name__)


@app_soya.route("/")
@check_sign
def main_page():
    params = request.values
    action = params.get('action')
    data = []
    # if action == 'yq.esData':
    data = es_data()

    return {"code": 200, "data": data}


def es_data():
    from dc.services.elasticsearch_service import ElasticsearchService
    ess = ElasticsearchService(run_model='prod')
    es = ess.get_connect()
    body = {
        "sort": {
            "createAt": {
                "order": "desc"
            }
        },
        "from": 0,
        "size": "50",
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "isDeal": "0"
                        }
                    },
                    {
                        "range": {
                            "public_time": {
                                "gte": "2024-01-21 12:10:28"
                            }
                        }
                    }
                ]
            }
        }
    }
    result = es.search(index='dc_bianjifagao', body=body)
    data = [item['_source'] for item in result['hits']['hits']]
    return data
