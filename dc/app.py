import functools
import json
import os
import random
import sys
import time
import traceback

import xlrd
import flask
from flask import request, jsonify, Response
from flask import Flask
from flask import render_template
from flask_sqlalchemy import BaseQuery
from sqlalchemy import text
from sqlalchemy.orm import Query
from faker import Faker

from dc.services.gzh_service_v2 import GzhServiceV2

from dc.common.dc_web_analyzer_work import AnalyzerWork
from dc.services.ai_daily_service import AIDailyService
from dc.services.chatgpt_service import ChatGPTService
from dc.common.utils import Utils
from dc.conf.settings import get_settings, get_settings2
from dc.services.qianxun_service import QianxunService
from dc.services.logging_service import LoggingService
import dc.configs
from dc.common.alchemy import query2dict
from dc.exts import db
from dc.models.model_test import User
from dc.models.model import SOYA_User, AIDailyTemplate, DCSiteMainTask
from dc.services.check_analysis import CheckAnalysis

# from dc.services.job_analysis import JobAnalysis
from dc.services.mysql_service import MySQLService
from dc.services.task_again_service import TaskAgainService
from dc.services.gzh_service import GzhService
from dc.app_test import app_test
from dc.app_soya import app_soya
from dc.app_dashboard import dashboard
from dc.app_wechat import wechat
from dc.app_elk import app_elk
from dc.app_image import app_image

# from dc.services.chatgpt_service import ChatGPTService
from dc.services.aigc_service import AIGCService
from dc.services.prompt_generator_service import PromptGeneratorService
from dc.services.zhipu_service import ZhipuService
from dc.services.dc_web_analyzer_service import AnalyzerService

app = Flask(__name__)
app.register_blueprint(app_test, url_prefix='/test')  # 测试接口
app.register_blueprint(app_soya, url_prefix='/soya_server/')  # soya 接口
# app.register_blueprint(app_soya, url_prefix='/mp_s/')            # 数据中台接口
app.register_blueprint(dashboard, url_prefix='/dashboard')  # 数据看板
app.register_blueprint(wechat, url_prefix='/wechat')  # 微信接口
app.register_blueprint(app_elk, url_prefix='/elk')  # elk接口
app.register_blueprint(app_image, url_prefix='/image')  # elk接口
app.config.from_object(dc.configs)
db.init_app(app)

faker = Faker(locale='zh_CN')  # 配置为中文


def create_app():
    db.init_app(app)
    return app


@app.route('/')
def hello_world():
    # db.create_all()
    return 'Hello World!'


@app.route('/status')
def check_status():
    """
    服务状态检测接口
    :author wjh
    :date 2022-11-15
    :return:
    """
    return {
        'code': 200,
        'time': Utils.showDateTime()
    }


def auth(func):
    @functools.wraps(
        func)  # 加上该装饰器的原因：以 index() 为例，经 auth 装饰过的 index函数 是 inner，然后 inner函数和"/index"这个路径绑定， 假如有好多视图函数都加上了这个装饰器，那么 inner 函数就会和好多路径绑定， Flask就不知道你绑定的是哪一个
    def inner(*args, **kwargs):
        m = MySQLService()
        s = m.create_session()
        c = request.values.get('token', '')
        if c != '':
            u = s.query(SOYA_User).where(SOYA_User.curSessionId == c, SOYA_User.status == 1).first()
            s.close()
            if u is None:
                return req_failed({'err': 'user invalid'}, ret=704)
            return func(*args, **kwargs)

        c = request.cookies.get("compony")

        if c is None:
            s.close()
            return req_failed({'err': 'user invalid'}, ret=704)
        u = s.query(SOYA_User).where(SOYA_User.curSessionId == c, SOYA_User.status == 1).first()
        s.close()
        if u is None:
            return req_failed({'err': 'user invalid'}, ret=704)
        return func(*args, **kwargs)

    return inner


mysql_service = MySQLService()
session = mysql_service.create_session()


@app.route('/check_analysis', methods=['GET', 'POST'])
def check_analysis():
    """验证提取规则"""
    params = {}
    for dic in request.values.dicts:
        for key, value in dic.items():
            params[key] = value

    return CheckAnalysis.check_analysis(params)


# @app.route('/job_analysis', methods=['GET', 'POST'])
# def job_analysis():
#     """职场抓取"""
#     params = {}
#     for dic in request.values.dicts:
#         for key, value in dic.items():
#             params[key] = value
#
#     return JobAnalysis.job_analysis(params)


@app.route('/init_site_list', methods=['GET', 'POST'])
def init_site_list():
    """重新执行抓取数据源"""
    params = {}
    for dic in request.values.dicts:
        for key, value in dic.items():
            params[key] = value

    return TaskAgainService.main_site_list(params)


@app.route('/process_site_list', methods=['GET', 'POST'])
def process_site_list():
    """执行抓取数据源"""
    params = {}
    for dic in request.values.dicts:
        for key, value in dic.items():
            params[key] = value

    return TaskAgainService.process_site_list(params)


@app.route('/init_main_task', methods=['GET', 'POST'])
def init_main_task():
    """重新执行抓取主任务"""
    params = {}
    for dic in request.values.dicts:
        for key, value in dic.items():
            params[key] = value

    return TaskAgainService.main_task(params)


@app.route('/process_main_task', methods=['GET', 'POST'])
def process_main_task():
    """执行抓取主任务"""
    params = {}
    for dic in request.values.dicts:
        for key, value in dic.items():
            params[key] = value

    return TaskAgainService.process_main_task(params)


@app.route('/init_task', methods=['GET', 'POST'])
def init_task():
    """重新执行抓取子任务"""
    params = {}
    for dic in request.values.dicts:
        for key, value in dic.items():
            params[key] = value

    return TaskAgainService.task(params)


@app.route('/wechat/get_article_list', methods=['GET', 'POST'])
def get_article_list():
    """
    文章列表
    :param params:
    :return:
    """


@app.route('/wechat/update_tags_by_gzh', methods=['GET', 'POST'])
def update_tags_by_gzh():
    """
    更新公众号的标签
    :param params:
    :return:
    """


@app.route('/wechat/follow_gzh_with_wechat', methods=['GET', 'POST'])
@auth
def follow_gzh_with_wechat():
    """用某个微信号关注公众号"""
    wxId = request.values.get('wxId', '')
    gzhId = request.values.get('gzhId', '')
    tag = request.values.get('tag', '')
    gzh_data = request.values.get('gzhData', '')
    if wxId == '' or gzhId == '':
        return "微信id 或公众号id 或 公众号信息 不能为空"
    r = GzhService.follow_gzh_with_wechat(wx_id=wxId, gzh_id=gzhId, tag=tag, gzh_data=gzh_data)
    if not r:
        return req_failed([])
    return req_success(r)


@app.route('/wechat/unfollow_gzh', methods=['GET', 'POST'])
@auth
def unfollow_gzh():
    """用某个微信号关注公众号"""
    wxId = request.values.get('wxId', '')
    gzhId = request.values.get('gzhId', '')
    if wxId == '' or gzhId == '':
        return "微信id 或公众号id 不能为空"
    r = GzhService.unfollow_gzh(wx_id=wxId, gzh_id=gzhId)
    return req_success(r)


@app.route('/wechat/req_gzh_search', methods=['GET', 'POST'])
@auth
def req_gzh_search():
    """用某个微信号关注公众号"""
    wxId = request.values.get('wxId', '')
    keyword = request.values.get('keyword', '')
    if keyword == '':
        return "搜索内容 不能为空"
    r = GzhService.req_gzh_search(wx_id=wxId, keyword=keyword)
    if r != None:
        if r['result']['result']['nomoreText'] == "没有更多的搜索结果":
            return req_failed({}, "没有更多的搜索结果")
        data = r['result']['result']['data'][0]['items']
        # return data
        res = []
        for v in data:
            res.append(v['jumpInfo'])
        return req_success(res)
    else:
        return req_success({})


@app.route('/wechat/req_gzh_info', methods=['GET', 'POST'])
@auth
def req_gzh_info():
    """用某个微信号关注公众号"""
    wxId = request.values.get('wxId', '')
    gzhId = request.values.get('gzhId', '')
    if wxId == '' or gzhId == '':
        return "微信id 或公众号id 不能为空"
    r = GzhService.req_gzh_info(wx_id=wxId, gzh_id=gzhId)
    return req_success(r)


@app.route('/wechat/req_wechat_info', methods=['GET', 'POST'])
@auth
def req_wechat_info():
    """用某个微信号列表信息"""
    wxId = request.values.get('wxId', '')
    if wxId == '':
        return "微信id 或公众号id 不能为空"
    r = GzhService.get_req_login(wx_id=wxId)
    return req_success(r)


@app.route('/wechat/start_wechat', methods=['GET', 'POST'])
@auth
def start_wechat():
    """
    开启一个微信进程
    返回一个pid
    """
    mIp = request.values.get('machineIp', '')
    if mIp == '':
        return "mIp  不能为空"
    r = GzhService.start_wechat(machine_ip=mIp)
    return req_success(r)


@app.route('/wechat/get_qr_code', methods=['GET', 'POST'])
@auth
def get_qr_code():
    """
    获取登录二维码
    """
    mIp = request.values.get('machineIp', '')
    if mIp == '':
        return "mIp 不能为空"
    r = GzhService.get_qr_code(machine_ip=mIp)
    return req_success(r)


@staticmethod
@app.route('/wechat/test_req', methods=['GET', 'POST'])
def req_success(data):
    return {"status": 0, "data": data}


@staticmethod
def req_failed(data, msg='failed', ret=0):
    return {"status": -1, "data": data, 'msg': msg, 'ret': ret}


@staticmethod
def req_response(**kwargs):
    data = kwargs.get('data', None)
    result = {
        'ret': kwargs.get('ret', 0),
        'msg': kwargs.get('msg', '操作成功')
    }
    if data:
        result['data'] = data

    return result


@app.route('/wechat/init_gzh_pull', methods=['GET', 'POST'])
def init_gzh_pull():
    """重新拉取公众号文章"""
    id = request.values.get('id', '')
    gzh_service = GzhService()
    logger = LoggingService(logfile='init_gzh_pull.log')
    result = gzh_service.init_gzh_pull(id)
    print(result)
    logger.info(f"id:{id}")
    ret = 0 if result else 201
    msg = '操作成功' if result else '操作失败'
    return req_response(ret=ret, msg=msg)


@app.route('/wechat/push', methods=['GET', 'POST'])
def wechat_push():

    # 取消此功能 ，使用 dcgzh_push 服务处理
    return {'data_from': '', 'count': 1001}

    logger = LoggingService(logfile='dc_gzh_push.log')

    """用某个微信号列表信息"""
    s = request.data
    if not s:
        return req_failed([], msg='数据为空')

    logger.info(f"request content: {s}")
    j = json.loads(s)
    logger.info(f"request data: {j}")
    r = GzhServiceV2.wechat_push(j)
    logger.info(f"response: {r}")
    return req_success(r)


@app.route('/wechat/check_wechat_by_config', methods=['GET', 'POST'])
# @auth
def check_wechat_by_config():
    print("start check check_wechat_by_config ")
    res = GzhService.check_wechat_by_config()
    return req_success(res)


@app.route('/wechat/get_machine_list', methods=['GET', 'POST'])
def get_machine_list():
    res = GzhService.get_machine_list()
    return req_success(res)


@app.route('/qianxun/send_text', methods=['GET', 'POST'])
def qianxun_send_text():
    """
    微信发送文本消息 [千寻框架]
    :return:
    """
    try:
        qx = QianxunService()

        # test_wxid = '25322860479@chatroom'
        test_msg = f"[@,wxid=all,nick=,isAuto=true]\n测试召唤全体\n测试\n类型：\n时间：[emoji=D83D][emoji=DE01]"

        type = request.values.get('type', 1000)
        wxid = request.values.get('wxid', None)
        msg = request.values.get('msg', None)

        if wxid is None:
            qx_settings = get_settings('qianxun')
            wxid = qx_settings.get('aims')

        if msg is None:
            raise Exception('msg is empty')

        data2 = {
            "wxid": wxid,
            "msg": msg
        }
        result = qx.send_text(data2)

        return req_success(result)

    except Exception as ex:
        return req_failed(ex.args)


@app.route('/qianxun/send_text2', methods=['GET', 'POST'])
def qianxun_send_text2():
    """
    微信发送文本消息(redis队列) [千寻框架]
    :return:
    """
    try:
        qx = QianxunService()

        dt = Utils.showDateTime()
        test_wxid = '25322860479@chatroom'
        test_msg = f"[@,wxid=all,nick=,isAuto=true]\n测试召唤全体\n测试\n类型：\n时间：[emoji=D83D][emoji=DE01]"

        channel = request.values.get('channel', 'all')  # 通道 all 所有，wechat 微信 mail 邮件
        wxid = request.values.get('wxid', None)
        type = request.values.get('type', 1000)
        msg = request.values.get('msg', None)
        # msg = f"[@,wxid=all,nick=,isAuto=true]\n {msg} \n[emoji=D83D][emoji=DE01]"
        msg = f" {msg} {dt}\n[emoji=D83D][emoji=DE01]"

        # mail
        mail_title = request.values.get('mail_title', None)
        mail_body = request.values.get('mail_body', None)
        mail_to = request.values.get('mail_to', None)
        mail_cc = request.values.get('mail_cc', None)

        if wxid is None:
            qx_settings = get_settings('qianxun')
            wxid = qx_settings.get('aims')

        if type is None:
            raise Exception('type is empty')

        if msg is None:
            raise Exception('msg is empty')

        send_data = {
            "wxid": wxid,
            "msg": msg
        }

        data = {
            "channel": channel,
            "type": type,
            "message": 'xx操作',
            "data": send_data,
            "mail_title": mail_title,
            "mail_body": mail_body,
            "mail_to": mail_to,
            "mail_cc": mail_cc,
        }
        # qx.push_send_text(data)
        result = qx.push_send_text(data)
        return req_success(result)

    except Exception as ex:
        return req_failed(ex.args)


@app.route('/qianxun/wechat_list', methods=['GET', 'POST'])
def qianxun_wechat_list():
    """
    获取微信列表 [千寻框架]
    :return:
    """
    try:
        qx = QianxunService()
        result = qx.get_wechat_list()
        result = {"status": 0, "data": result}
        result = json.dumps(result, ensure_ascii=False)
        return result

    except Exception as ex:
        return req_failed(ex.args)


@app.route('/qianxun/qunliao_list', methods=['GET', 'POST'])
def qianxun_qunliao_list():
    """
    微信群聊列表 [千寻框架]
    :return:
    """
    try:
        params = {'wxid': request.values.get('wxid')}

        qx = QianxunService()
        result = qx.get_qunliao_list(params)
        result = {"status": 0, "data": result}
        result = json.dumps(result, ensure_ascii=False)
        return result

    except Exception as ex:
        return req_failed(ex.args)


@app.route('/qianxun/friend_list', methods=['GET', 'POST'])
def qianxun_friend_list():
    """
    微信群聊列表 [千寻框架]
    :return:
    """
    try:
        params = {'wxid': request.values.get('wxid')}

        qx = QianxunService()
        result = qx.get_friend_list(params)
        result = {"status": 0, "data": result}
        result = json.dumps(result, ensure_ascii=False)
        return result

    except Exception as ex:
        return req_failed(ex.args)


@app.route('/qianxun/push', methods=['GET', 'POST'])
def qianxun_push():
    """
    微信发送文本消息 [千寻框架]
    :return:
    """
    logger = LoggingService(logfile='push_qianxun.log')
    logger.info(request.headers)

    """用某个微信号列表信息"""
    s = request.data
    logger.info("push_qianxun get request data : " + f'{s}')
    return req_success({})


@app.route('/wechat/upload_follow', methods=['POST'])
def upload_wechat_follow_list():
    """
    微信导入关注
    :return:
    """
    f = request.files['file']
    name = "../" + str(random.randint(1000, 9999)) + str(time.time_ns()) + ".xls"
    f.save(name)
    print(f, type(f))
    xls = xlrd.open_workbook(filename=name)
    sh = xls.sheets()[0]
    gzh_service = GzhService()
    for row in range(1, sh.nrows):
        row_n = row + 1
        num = sh.cell(row, 0).value  # 第一列的数据
        cate = sh.cell(row, 1).value  # 第二列的数据
        ly = sh.cell(row, 2).value  # 第三列的数据
        name = sh.cell(row, 3).value  # 第三列的数据
        wxid = sh.cell(row, 4).value  # 第三列的数据
        print(num, cate, ly, name, wxid)
        gzh_service.search_and_follow(name, ly)
    return req_success({})


#
# @app.route('/get_chatgpt', methods=['GET', 'POST'])
# def get_chatgpt():
#     """重新执行抓取子任务"""
#     params = {}
#     for dic in request.values.dicts:
#         for key, value in dic.items():
#             params[key] = value
#
#     gpt = ChatGPTService()
#     return gpt.stock_chat(params)

#
# t = threading.Thread(target=check_wechat)
# t.start()
# check_wechat()


@app.route('/aigc/create', methods=['GET', 'POST'])
def aigc_create():
    """
    aigc 数据重新生成
    :author wjh
    :date 2019-12-20
    :return:
    """
    try:
        values: dict = request.values
        prompt = values.get('prompt', None)
        model = values.get('model', None)

        aigc = AIGCService()
        text = aigc.chat(prompt)

        result = {
            'status': 0,
            'data': text
        }
        return result
    except Exception as ex:
        return req_failed(ex.args)


@app.route('/daily/create', methods=['GET', 'POST'])
def daily_create():
    """
    aigc 生成日刊
    :author wuzhou
    :date 2019-12-20
    :return:
    """
    try:
        form = request.form
        content = form.get('content', '')
        templateId = form.get('templateId', 0)
        style = form.get('style', '')
        format = form.get('format', '')
        size = form.get('size', '')
        return_type = form.get('return_type', 'normal')
        sizes = {"long": '1000', 'middle': '800', 'short': '500'}

        if not content or not templateId or not style or not format or size not in sizes:
            return req_failed(None, msg='缺少参数')
        mysql = MySQLService()
        session = mysql.create_session()
        template: AIDailyTemplate = session.query(AIDailyTemplate).filter(AIDailyTemplate.id == templateId).first()
        if not template:
            return req_failed(None, msg='模板id错误')
        prompt = PromptGeneratorService().daily(content, style, format, sizes[size], user_setting=template.userSetting,
                                                question_description=template.questionDescription,
                                                target_need=template.targetNeed,
                                                more_description=template.moreDescription)

        gptService = ChatGPTService()
        if return_type == 'stream':
            return flask.Response(gptService.chat16k_stream(prompt), mimetype='text/event-stream')
        else:
            ans = gptService.chat16k(prompt)
            return {'status': 0, 'data': ans}

    except Exception as ex:
        return req_failed(ex.args)


@app.route('/chatgpt_aigc', methods=['GET', 'POST'])
def chatgpt_aigc():
    """chatgpt aigc"""
    s = request.data
    logger = LoggingService(logfile='aigc.log')
    j = json.loads(s)
    logger.info("chatgpt_aigc get request data : " + f'{j}')
    aigcService = AIGCService()
    max_token = 3700
    request_token = aigcService.token_len(content=j['content'] + j['text'] + j['question'])
    response_token = max_token - request_token
    if response_token > 1800:
        j['content'] = aigcService.cut_content_by_token_len(j['content'], 1800)
        response_token = 1800
    j['text'] = aigcService.cut_content_by_token_len(j['text'], response_token)
    rewrite_content = generate_rewrite_request_prompt(rewrite_content=j['content'], reference=j['text'],
                                                      need=j['question'], t=j['type'])
    gpt = ChatGPTService()
    q = aigcService.cut_content_by_token_len(rewrite_content, 1800)
    result = gpt.chat(q, max_tokens=1800)
    arr = result.split("：")
    # print(arr)
    if len(arr) > 1:
        result = " ".join(arr[1:])
    else:
        result = " ".join(arr[0:])
    return req_success(result)


def generate_rewrite_request_prompt(rewrite_content='', reference='', need='', t=''):
    """
    重写
    """
    print(t, need)
    if t == '2':
        print('type 2')
        string = "假如你是个资深编辑，我现在要以:" + need + " 为标题,写一篇文章 , 给我生成文章的提纲，以列表形式返回"
        return string
    else:
        print('type' + f'{t}')
        string = "你是一名资深编辑 根据要求:" + need
        if rewrite_content != '':
            string = string + " ,参考内容:'" + rewrite_content + "'"
        else:
            if reference != '':
                string = string + " ,参考内容:'" + reference + "'"
        return string


@app.route('/ai_daily/download_file', methods=['GET', 'POST'])
def download_file():
    # 拿到文件路径
    file_path = request.values.get('file_path', '')
    filename = request.values.get('file_name', '')
    if file_path == '':
        return req_failed(None, msg='缺少参数')

    ai_daily_service = AIDailyService()
    filename = ai_daily_service.get_file_name(file_path, filename)
    file_path = os.path.dirname(os.path.realpath(__file__)) + '/../data/ai_daily/' + file_path
    print(file_path, filename)

    try:
        with open(file_path, 'rb') as f:
            stream = f.read()
    except Exception as ex:
        return req_failed("下载失败")

    response = Response(stream, content_type='application/octet-stream')
    response.headers['Content-disposition'] = f'attachment; filename={filename}'.encode('utf-8')
    return response


@app.route('/generate_ppt_and_pdf', methods=['GET', 'POST'])
def generate_ppt_and_pdf():
    # text = request.values('text')
    # content_id = request.values('content_id')
    values = request.form
    print(values)
    context = values.get('text', '')
    content_id = values.get('content_id', '')
    title = values.get('title', '')
    dc_detail_url = values.get('dc_detail_url', '')
    print(context, content_id, title, dc_detail_url)
    if context == '' or content_id == '':
        return req_failed(None, msg='缺少参数')
    res = {}
    ai_daily_service = AIDailyService()
    res['ppt'], res['pdf'] = ai_daily_service.generate_ppt_and_pdf(content_id, context, dc_detail_url)
    if res['ppt'] != '':
        if title == '':
            title = content_id
        res['file_name'] = ai_daily_service.get_file_name(res['ppt'], title)
        return req_success(res)
    else:
        return req_failed(None, msg='生成失败,请重新检查文章的格式')


@app.route('/jiwei_gpt', methods=['GET', 'POST'])
def jiwei_gpt():
    """
    jiwei gpt
    :date 2023-10-09
    :return:
    """
    try:
        form = request.form
        data = form.get('data', {})
        res: list = json.loads(data)
        # {"role": "user", "content": "你好"},
        # {"role": "assistant", "content": "我是jiwei-GPT，您的AI数字咨询专家，很高兴为你服务。"},
        res.insert(0, {"type": 2,
                       "content": "我是爱集微开发的AI数字咨询专家,名字叫做JiweiGPT。能帮你解答关于ICT产业相关的问题，并能够对您的其他问题和要求提供适当的答复和支持。"})
        res.insert(0, {"type": 1, "content": "你好"})

        # token filter
        token_length = get_settings2('zhipu', 'token_length', 1000)
        logger = LoggingService('jiwei_gpt.log')
        logger.info('filter_list before:' + json.dumps(res, ensure_ascii=False))
        res2 = ZhipuService.token_count(res)
        logger.info('filter_list debug:' + json.dumps(res2, ensure_ascii=False))
        res = ZhipuService.token_filter(res, token_length)
        logger.info('filter_list:' + json.dumps(res, ensure_ascii=False))

        roles = {1: "user", 2: "assistant"}
        infos = []
        for _, info in enumerate(res):
            tmp = {'role': roles[info['type']], 'content': info['content']}
            infos.append(tmp)
        # infos = [{'role': 'user', 'content': '写一篇500字道歉信'}]

        glmService = ZhipuService()
        return flask.Response(glmService.sse_invoke(infos), mimetype='text/event-stream')

    except Exception as ex:
        return req_failed(ex.args)


@app.route('/testAnalysis', methods=['GET'])
def test_analysis_list():
    driver = None
    try:
        site_id = request.values.get("siteId", type=int)
        site_list = AnalyzerService.get_DCSiteList_by_id(site_id)
        if not site_list:
            return req_success({"error": "no existing"})
        site_key_words = AnalyzerService.get_DCSiteKeyWords_lately()
        site_main_task = DCSiteMainTask(id=1, keyWord=site_key_words.keyword)
        worker = AnalyzerWork(dc_site_list=site_list,
                              dc_site_main_task=site_main_task,
                              old_dc_site_tasks=[])
        driver = worker.driver
        worker.test_web()
        crawl_data = worker.crawl_data
        length = len(crawl_data)
        crawl_data_dir = []
        for element in crawl_data:
            temp = element.__dict__
            temp.pop('_sa_instance_state')
            crawl_data_dir.append(temp)
        return req_success(
            {"count": length, "keyword": site_main_task.keyWord, "list": crawl_data_dir, "detailUrl": site_list.detailUrl})
    except Exception as ex:
        try:
            if driver:
                driver.quit()
        except:
            pass
        tb = traceback.format_exc()
        # 将堆栈跟踪信息写入到文件
        # time_now = time.strftime("%Y-%m-%d%H-%M-%S", time.localtime(time.time()))
        # with open("logs/" + time_now + '-error.log', 'w') as f:
        #     f.write(tb)
        return req_failed(tb)


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
