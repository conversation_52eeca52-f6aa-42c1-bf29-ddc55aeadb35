import random

import flask_sqlalchemy
from flask import request, jsonify
from flask import Flask
from flask import render_template
from flask_sqlalchemy import BaseQuery

import dc.configs
from dc.common.alchemy import query2dict
from dc.exts import db
from dc.models.model_test import User2 as User
from faker import Faker

app = Flask(__name__)
app.config.from_object(dc.configs)
db.init_app(app)

faker = Faker(locale='zh_CN')  # 配置为中文


def create_app():
    db.init_app(app)
    return app

@app.route('/')
def hello_world():
    #db.create_all()
    return 'Hello World!'


@app.route('/index')
def index_page():
    return render_template("index.html")


@app.route('/login', methods=['GET', 'POST'])
def login_page():
    for dic in request.values.dicts:
        for key, value in dic.items():
            print(f"{key} {value}")

    return "ok"


@app.route('/json', methods=['GET', 'POST'])
def json_page():
    result = dict()
    result['name'] = 'wjh'
    result['age'] = 8
    result['r'] = random.randint(100,999)
    return result


@app.route('/json2', methods=['GET', 'POST'])
def json2_page():
    app.logger.debug('A value for debugging--->')
    return jsonify(
        username="zhangsan",
        email="<EMAIL>",
        id=333,
    )


@app.route('/db/add', methods=['GET', 'POST'])
def db_add():
    """增加数据"""
    user1 = User(name='zs22', age=20)
    db.session.add(user1)
    db.session.flush()
    print(user1.to_json())
    db.session.commit()
    exit(0)


@app.route('/db/init', methods=['GET', 'POST'])
def db_init():
    """增加数据"""

    for i in range(1, 20):
        user1 = User(name=faker.name(), age=faker.random.randint(10, 30))
        db.session.add(user1)

    db.session.commit()
    return "index"


@app.route('/db/first', methods=['GET', 'POST'])
def db_first():
    """first"""
    query: BaseQuery = User.query
    user1: User = query.filter_by(age="28").first()
    print(user1)
    return "index" + user1.name

@app.route('/db/get', methods=['GET', 'POST'])
def db_get():
    """first"""
    query: BaseQuery = User.query
    user1: User = query.get(25)
    print(user1)
    return "index" + user1.name


@app.route('/db/list', methods=['GET', 'POST'])
def db_list():
    """first"""
    query: BaseQuery = User.query
    users = query.filter_by(age=20).all()
    print(users)
    zzz = query2dict(users)
    return zzz


@app.route('/db/query', methods=['GET', 'POST'])
def db_query():
    """first"""
    query: BaseQuery = User.query
    users = query.filter_by(age="30").all()
    zzz = query2dict(users)
    return zzz


@app.route('/db/query2', methods=['GET', 'POST'])
def db_query2():
    """first"""
    query: BaseQuery = User.query
    users = User.query.filter(User.age == 20).order_by(User.id).limit(5).all()
    zzz = query2dict(users)
    return zzz


@app.route('/db/update', methods=['GET', 'POST'])
def db_update():
    """增加数据"""
    query: BaseQuery = User.query
    user1: User = query.first()
    user1.name = 'xxxxxx--22'
    db.session.add(user1)
    db.session.commit()
    return "index"


@app.route('/db/delete', methods=['GET', 'POST'])
def db_delete():
    """增加数据"""
    query: BaseQuery = User.query
    user1: User = query.first()
    db.session.delete(user1)
    db.session.commit()
    return "delete"


@app.route('/db/delete2', methods=['GET', 'POST'])
def db_delete2():
    """增加数据"""
    user1: User = User(id=21)
    db.session.delete(user1)
    db.session.commit()
    return "delete2"


@app.route('/db/clear', methods=['GET', 'POST'])
def db_clear():
    """增加数据"""
    query: BaseQuery = User.query
    query.filter().delete()
    db.session.commit()
    return "delete"


@app.route('/dc')
def dc():
    """
    数据采集测试
    :return:
    """
    name = request.values.get("name")
    if not name:
        name = '张三'

    for dic in request.values.dicts:
        for key, value in dic.items():
            print(f"{key} {value}")

    r = random.randint(100, 999)
    return f"hello {name} from [{r}]"


if __name__ == '__main__':
    app.run(debug=True)
