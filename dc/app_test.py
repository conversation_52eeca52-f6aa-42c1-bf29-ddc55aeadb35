import flask
import openai
from flask import Blueprint, make_response, request

app_test = Blueprint('app_test', __name__)


@app_test.route("/")
def accountList1():
    return "我是A0哈哈"


@app_test.route("json")
def accountList2():
    return {"id": 1, "message": "我是A1哈哈"}


@app_test.route('/stream_result')
def streamed_response():
    """
    ChatGPT SSE 流式处理协议测试
    :author wjh
    :date 2023-6-20
    :return:
    """
    openai.api_key = "***************************************************"
    openai.api_base = "http://18.189.127.237:3002/v1"

    def stream():
        completion = openai.ChatCompletion.create(
            model='gpt-3.5-turbo',
            messages=[{"role": "user", "content": "what is python"}],
            stream=True)
        for line in completion:
            chunk = line['choices'][0].get('delta', {}).get('content', '')
            if chunk:
                yield 'data: %s\n\n' % chunk

    return flask.Response(stream(), mimetype='text/event-stream')



@app_test.route("yq")
def yq():

    from dc.services.elasticsearch_service import ElasticsearchService
    ess = ElasticsearchService(run_model='prod')
    es = ess.get_connect()
    body = {
        "sort": {
            "createAt": {
                "order": "desc"
            }
        },
        "from": 0,
        "size": "50",
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "isDeal": "0"
                        }
                    },
                    {
                        "range": {
                            "public_time": {
                                "gte": "2024-01-21 12:10:28"
                            }
                        }
                    }
                ]
            }
        }
    }
    result = es.search(index='dc_bianjifagao',body=body)
    data = [item['_source'] for item in result['hits']['hits']]

    return {"code": 200, "data": data}