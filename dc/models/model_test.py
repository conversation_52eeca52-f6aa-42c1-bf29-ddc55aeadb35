import json
from sqlalchemy import create_engine, <PERSON>umn, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete
from sqlalchemy.orm import declarative_base, relationship, backref, Session, DeclarativeMeta
from sqlalchemy.types import CHAR
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import scoped_session
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum
from urllib.parse import quote_plus as urlquote
from dc.exts import db
# 声明ORM的一个基类并建立映射关系
from dc.common.alchemy import AlchemyJsonEncoder, ModelMixin

Base = declarative_base()


class User(Base, ModelMixin):
    """
    仅供测试使用
    """
    __tablename__ = 'test_user'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column('name', db.String(20), unique=True)
    age = db.Column(db.Integer, index=True)
    created = db.Column(db.TIMESTAMP(True))
    # addresses = db.relationship('Address', backref='test_user', lazy=True)


class User2(db.Model, ModelMixin):
    """
    仅供测试使用
    """
    __tablename__ = 'test_user'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column('name', db.String(20), unique=True)
    age = db.Column(db.Integer, index=True)
    created = db.Column(db.TIMESTAMP(True))
    # addresses = db.relationship('Address', backref='test_user', lazy=True)


class Address(db.Model, ModelMixin):
    """
    仅供测试使用
    """
    __tablename__ = 'test_address'
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), nullable=False)
    person_id = db.Column(db.Integer, db.ForeignKey('test_user.id'), nullable=False)


class ProxyTest(db.Model, ModelMixin):
    """
    仅供测试使用
    """
    __tablename__ = 'proxy_test'
    id = db.Column(db.Integer, primary_key=True)
    no = db.Column(db.Integer, name="no", nullable=False)
    title = db.Column(db.String(200), name="title", nullable=False)
    start_time = Column(DateTime, name='start_time', comment="创建时间")
    stop_time = Column(DateTime, name='stop_time', comment="更新时间")
    exec_time = Column(Integer, name='exec_time')
    demo = db.Column(db.String(200), name="demo", nullable=False)
