from sqlalchemy import Column, Integer, String, DateTime
from datetime import datetime

from sqlalchemy import Column, Integer, String, DateTime, SmallInteger, Text, Date, TIMESTAMP, BigInteger, Boolean
from sqlalchemy.dialects.mssql import TINYINT

from dc.common.alchemy import ModelMixin
from dc.models.model_base import Base



class TradeControlCompany(Base, ModelMixin):
    """贸易管制公司控制表"""
    __tablename__ = 'MP_TradeControlCompany'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    companyId = Column(Integer, name='companyId', nullable=True, comment="公司Id")
    customer_id = Column(Integer, name='customer_id', nullable=True, comment="用户Id")
    companyName = Column(String(500), name='companyName', nullable=True, comment="公司名称")
    companyStatus = Column(Integer, name='companyStatus', nullable=True, comment="企业关键词状态, 0关闭 1开启")
    createdAt = Column(TIMESTAMP, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(TIMESTAMP, default=datetime.now(), onupdate=datetime.now(), name='updatedAt', comment="更新时间")


class TradeControlDailyReport(Base, ModelMixin):
    """贸易管制日结算表"""
    __tablename__ = 'MP_TradeControlDailyReport'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    companyId = Column(Integer, name='companyId', nullable=True, comment="公司id")
    customer_id = Column(Integer, name='customer_id', nullable=True, comment="用户id")
    totalCount = Column(SmallInteger, default=0, name='totalCount', nullable=True, comment="总数")
    newsCount = Column(SmallInteger, name='newsCount', nullable=True, comment="新闻数量")
    reportCount = Column(SmallInteger, name='reportCount', nullable=True, comment="报告数量")
    videoCount = Column(SmallInteger, name='videoCount', nullable=True, comment="视频数量")
    keywords = Column(String(500), name='keywords', nullable=True, comment="监控到的关键词")
    reportDate = Column(Date, name='reportDate', nullable=True, comment="报告日期")
    createdAt = Column(TIMESTAMP, default=datetime.now(), name='createdAt', comment="创建时间")


class TradeControlKeywordsRelation(Base, ModelMixin):
    """贸易管制关键词对应关系管理"""
    __tablename__ = 'MP_TradeControlKeywordsRelation'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    keywordShow = Column(String(100), name='keywordShow', nullable=True, comment="展示关键词")
    keyword = Column(String(100), name='keyword', nullable=True, comment="关键词")
    childWord = Column(String(100), name='childWord', nullable=True, comment="关联的翻译后的关键词")
    createdAt = Column(TIMESTAMP, default=datetime.now(), name='createdAt', comment="创建时间")


class TradeControlMonitorReport(Base, ModelMixin):
    """贸易管制监控报告详细信息"""
    __tablename__ = 'MP_TradeControlMonitorReport'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    companyId = Column(Integer, name='companyId', nullable=True, comment="公司id")
    customer_id = Column(Integer, name='customer_id', nullable=False, comment="用户id")
    MonitorDate = Column(Date, name='MonitorDate', nullable=False, comment="监控日期")
    keywordShow = Column(String(100), name='keywordShow', nullable=False, comment="展示关键词")
    keyword = Column(String(100), name='keyword', nullable=False, comment="关键词")
    keywordRelation = Column(String(100), name='keywordRelation', nullable=False, comment="子关键词")
    newsId = Column(String(50), name='newsId', nullable=False, comment="新闻的esId")
    title = Column(String(255), name='title', nullable=False, comment="新闻的标题")
    content = Column(Text, name='content', nullable=False, comment="新闻的内容")
    newsSource = Column(String(200), name='newsSource', nullable=True, comment="新闻来源")
    page = Column(SmallInteger, default=0, name='page', nullable=False, comment="页码, 报告有页码, 新闻没有页码")
    type = Column(Integer, name='type', nullable=False, comment="1.新闻 2.报告 3.视频")
    url = Column(String(500), name='url', nullable=True, comment="新闻的链接")
    createdAt = Column(TIMESTAMP, default=datetime.now(), name='createdAt', comment="创建时间")


class TradeControlMPCreate(Base, ModelMixin):
    """中台添加的贸易管制信息"""
    __tablename__ = 'MP_TradeControlMPCreate'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    title = Column(String(255), name='title', nullable=True, comment="文章标题")
    content = Column(Text, name='content', nullable=True, comment="内容")
    public_time = Column(DateTime, name='public_time', nullable=True, comment="文章的发布时间")
    sourceUrl = Column(String(500), name='sourceUrl', nullable=True, comment="原文链接")
    articleSource = Column(String(255), name='articleSource', nullable=True, comment="文章来源")
    type = Column(Integer, name='type', nullable=True, comment="1 新闻 2报告 3视频")
    esId = Column(String(100), name='esId', nullable=True, comment="esId")
    esStatus = Column(Integer, name='esStatus', nullable=True, comment="es的同步状态, 1已同步, 0未同步")
    createdAt = Column(TIMESTAMP, default=datetime.now(), name='createdAt', comment="创建时间")


class CompanyInfo(Base, ModelMixin):
    """企业基本信息"""
    __tablename__ = 'mp_company_info'

    id = Column(Integer, name='id', primary_key=True, comment="流水号ID")
    staff_num_range = Column(String(100), name='staff_num_range', nullable=True, comment="人员规模")
    from_time = Column(Integer, name='from_time', nullable=True, comment="经营开始时间")
    to_time = Column(BigInteger, name='to_time', nullable=True, comment="经营结束时间")
    type = Column(Integer, name='type', nullable=True, comment="法人类型，1 人 2 公司")
    is_microent = Column(Integer, name='is_microent', nullable=True, comment="是否是小微企业 0不是 1是")
    reg_number = Column(String(50), name='reg_number', nullable=True, comment="注册号")
    percentile_score = Column(Integer, name='percentile_score', nullable=True, comment="企业评分")
    reg_capital = Column(String(50), name='reg_capital', nullable=True, comment="注册资本")
    name = Column(String(200), name='name', nullable=True, comment="企业名")
    introduction = Column(String(200), name='introduction', nullable=True, comment="企业简介")
    logo_url = Column(String(200), name='logo_url', nullable=True, comment="企业Logo地址")
    reg_institute = Column(String(200), name='reg_institute', nullable=True, comment="登记机关")
    reg_location = Column(String(200), name='reg_location', nullable=True, comment="注册地址")
    work_location = Column(String(500), name='work_location', nullable=True, comment="主要办公地点")
    industry = Column(String(200), name='industry', nullable=True, comment="行业")
    approved_time = Column(Integer, name='approved_time', nullable=True, comment="核准时间")
    update_times = Column(Integer, name='update_times', nullable=True, comment="更新时间")
    social_staff_num = Column(Integer, name='social_staff_num', nullable=True, comment="参保人数")
    tags = Column(String(200), name='tags', nullable=True, comment="企业标签")
    tax_number = Column(String(200), name='tax_number', nullable=True, comment="纳税人识别")
    business_scope = Column(Text, name='business_scope', nullable=True, comment="经营范围")
    en_name = Column(String(200), name='en_name', nullable=True, comment="英文名")
    alias = Column(String(200), name='alias', nullable=True, comment="简称")
    org_number = Column(String(100), name='org_number', nullable=True, comment="组织机构代码")
    reg_status = Column(String(50), name='reg_status', nullable=True, comment="企业状态（经营状态）")
    check_date = Column(Integer, name='check_date', nullable=True, comment="发照时间")
    estiblish_time = Column(Integer, name='estiblish_time', nullable=True, comment="成立日期（注册日期）")
    isbond = Column(Integer, name='isbond', nullable=True, comment="是否上市（0-未上市，1-上")
    bond_type = Column(String(30), name='bond_type', nullable=True, comment="股票类型")
    bond_num = Column(String(50), name='bond_num', nullable=True, comment="股票号")
    bond_name = Column(String(20), name='bond_name', nullable=True, comment="股票名")
    used_bond_name = Column(String(100), name='used_bond_name', nullable=True, comment="股票曾用名")
    legal_personid = Column(String(50), name='legal_personid', nullable=True, comment="法人id")
    legal_person_name = Column(String(120), name='legal_person_name', nullable=True, comment="法人")
    actual_capital = Column(String(50), name='actual_capital', nullable=True, comment="实收注册资金")
    company_orgtype = Column(String(50), name='company_orgtype', nullable=True, comment="企业类型")
    credit_code = Column(String(50), name='credit_code', nullable=True, comment="统一社会信用代码")
    history_names = Column(String(100), name='history_names', nullable=True, comment="曾用名")
    reg_capital_currency = Column(String(10), name='reg_capital_currency', nullable=True, comment="注册资本币种 人民币 美元 欧元 等")
    actual_capital_currency = Column(String(10), name='actual_capital_currency', nullable=True, comment="实收注册资本币种 人民币 美元 欧元 等")
    email = Column(String(100), name='email', nullable=True, comment="邮箱")
    website_list = Column(String(100), name='website_list', nullable=True, comment="网址")
    phone_number = Column(String(100), name='phone_number', nullable=True, comment="企业联系方式")
    revoke_date = Column(Integer, name='revoke_date', nullable=True, comment="吊销日期")
    revoke_reason = Column(String(100), name='revoke_reason', nullable=True, comment="吊销原因")
    cancel_date = Column(Integer, name='cancel_date', nullable=True, comment="注销日期")
    cancel_reason = Column(String(100), name='cancel_reason', nullable=True, comment="注销原因")
    country = Column(String(100), name='country', nullable=True, comment="国家简称")
    base = Column(String(50), name='base', nullable=True, comment="省份简称")
    city = Column(String(50), name='city', nullable=True, comment="市")
    district = Column(String(50), name='district', nullable=True, comment="区")
    category = Column(String(100), name='category', nullable=True, comment="国民经济行业分类门类")
    category_big = Column(String(100), name='category_big', nullable=True, comment="国民经济行业分类大类")
    category_middle = Column(String(100), name='category_middle', nullable=True, comment="国民经济行业分类中类")
    category_small = Column(String(100), name='category_small', nullable=True, comment="国民经济行业分类小类")
    updated_at = Column(TIMESTAMP, default=datetime.now(), onupdate=datetime.now(), name='updated_at', comment="更新时间")
    created_at = Column(TIMESTAMP, name='created_at', nullable=True, comment="创建时间")
    en_name_alias = Column(String(255), name='en_name_alias', nullable=False, comment="英文名别名")
    districtCode = Column(String(8), name='districtCode', nullable=False, comment="大区代码")
    districtName = Column(String(100), name='districtName', nullable=False, comment="大区名称")
    areaCode = Column(String(10), name='areaCode', nullable=False, comment="地区代码")
    areaName = Column(String(100), name='areaName', nullable=False, comment="地区名称")
    reg_capital_num = Column(BigInteger, name='reg_capital_num', nullable=False, comment="注册资金数（人民币）")
    actual_capital_num = Column(BigInteger, name='actual_capital_num', nullable=False, comment="实收资金数（人民币）")
    tyc_id = Column(BigInteger, name='tyc_id', nullable=False, comment="天眼查的id")
    keyword_condition = Column(String(500), name='keyword_condition', nullable=True, comment="es搜索表达式")
    keywordId = Column(Integer, name='keywordId', nullable=True, comment="舆情关键词表的主键")
    status = Column(Integer, name='status', nullable=False, comment="1正常，0删除")
    securitiesCode = Column(String(20), name='securitiesCode', nullable=True, comment="证券代码")
    listingDate = Column(Date, name='listingDate', nullable=True, comment="上市时间")
    operatingIncome = Column(String(30), name='operatingIncome', nullable=False, comment="营业收入")
    TTM = Column(String(50), name='TTM', nullable=False, comment="归母净利润（TTM）")
    listingBoard = Column(String(50), name='listingBoard', nullable=False, comment="上市板块")
    marketCapitalization = Column(String(50), name='marketCapitalization', nullable=False, comment="总市值")
    companyType = Column(Integer, name='companyType', nullable=True, comment="1 .国内 2.国外")
    sourceFrom = Column(Integer, name='sourceFrom', nullable=True, comment="1.企业库, 2机构库")


class TradeControlCompanyKeywords(Base, ModelMixin):
    """企业设置的关键词表"""
    __tablename__ = 'MP_TradeControlCompanyKeywords'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    companyId = Column(Integer, name='companyId', nullable=True, comment="公司id")
    customer_id = Column(Integer, name='customer_id', nullable=True, comment="用户id")
    keyword = Column(String(32), nullable=True, name='keyword', comment="关键词")
    createdAt = Column(TIMESTAMP, default=datetime.now(), name='createdAt', comment="创建时间")

