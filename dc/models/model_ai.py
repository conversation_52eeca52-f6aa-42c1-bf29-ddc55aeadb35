import json
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete
from sqlalchemy.orm import declarative_base, relationship, backref, Session, DeclarativeMeta
from sqlalchemy.types import CHAR
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import scoped_session
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum
from urllib.parse import quote_plus as urlquote
from dc.exts import db
# 声明ORM的一个基类并建立映射关系
from dc.common.alchemy import AlchemyJsonEncoder, ModelMixin
from dc.models.model_base import Base


# name为在数据库表中的名称
# type为数据类型
# comment为注释
# nullable为不能为空
# default为默认字段
# unique为唯一约束
class News(Base, ModelMixin):
    """资讯表"""
    __tablename__ = 'news'
    news_id = Column(Integer, name='news_id', primary_key=True, comment="自增id")
    category_id = Column(Integer, name='category_id', nullable=True, comment="分类id")
    news_title = Column(String(32), nullable=True, name='news_title', comment="标题")
    subtitle = Column(String(32), nullable=True, name='subtitle', comment="副标题")
    intro = Column(String(32), nullable=True, name='intro', comment="资讯介绍")
    view_num = Column(TINYINT(unsigned=True), default=0, name='view_num', comment="查看数量")
    like_num = Column(TINYINT(unsigned=True), default=0, name='like_num', comment="点赞数量")
    unlike_num = Column(TINYINT(unsigned=True), default=0, name='unlike_num', comment="踩的统计数")
    comment_num = Column(TINYINT(unsigned=True), default=0, name='comment_num', comment="评论数量")
    collect_num = Column(TINYINT(unsigned=True), default=0, name='collect_num', comment="收藏量")


class NewsDetail(Base, ModelMixin):
    """资讯详情表"""
    __tablename__ = 'news_detail'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    news_id = Column(Integer, name='news_id', nullable=True, comment="资讯id")
    news_content = Column(String(32), nullable=True, name='news_content', comment="资讯内容")
    post_ip = Column(String(32), nullable=True, name='post_ip', comment="发布ip")
    page_order = Column(TINYINT(unsigned=True), default=0, name='page_order', comment="分页排序")
    create_time = Column(DateTime, default=datetime.now(), name='create_time', comment="创建时间")
    update_time = Column(DateTime, default=datetime.now(), name='update_time', comment="更新时间")
    # ai_status = Column(TINYINT(unsigned=True), default=0, name='ai_status', comment="AI状态：9未处理 1成功 -1失败")
    # ai_update_time = Column(DateTime, default=datetime.now(), name='ai_update_time', comment="AI更新时间")


class NewsDetail2(Base, ModelMixin):
    """资讯详情表"""
    __tablename__ = 'news_detail_test'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    news_id = Column(Integer, name='news_id', nullable=True, comment="资讯id")
    news_content = Column(String(32), nullable=True, name='news_content', comment="资讯内容")
    news_content2 = Column(String(32), nullable=True, name='news_content2', comment="资讯内容2")
    post_ip = Column(String(32), nullable=True, name='post_ip', comment="发布ip")
    page_order = Column(TINYINT(unsigned=True), default=0, name='page_order', comment="分页排序")
    create_time = Column(DateTime, default=datetime.now(), name='create_time', comment="创建时间")
    update_time = Column(DateTime, default=datetime.now(), name='update_time', comment="更新时间")
    # ai_status = Column(TINYINT(unsigned=True), default=0, name='ai_status', comment="AI状态：9未处理 1成功 -1失败")
    # ai_update_time = Column(DateTime, default=datetime.now(), name='ai_update_time', comment="AI更新时间")


class NewsTag(Base, ModelMixin):
    """DC标签表"""
    __tablename__ = 'DC_YqTag'
    id = Column(Integer, name='id', primary_key=True, comment="自增id")
    tag_name = Column(String(32), nullable=True, name='tag_name', comment="文章id")
    article_id = Column(String(32), nullable=True, name='article_id', comment="标签名称")
    weight = Column(TINYINT(unsigned=True), default=0, name='weight', comment="权重")
    # ai_status = Column(TINYINT(unsigned=True), default=0, name='ai_status', comment="AI状态：9未处理 1成功 -1失败")
    # ai_update_time = Column(DateTime, default=datetime.now(), name='ai_update_time', comment="AI更新时间")
