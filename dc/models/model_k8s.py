from datetime import datetime

from sqlalchemy import Column, Integer, String, DateTime

from dc.common.alchemy import ModelMixin
from dc.models.model_base import Base


class K8sLiveness(Base, ModelMixin):
    """ k8s 探针表 """
    __tablename__ = 'K8S_Liveness'
    id = Column(Integer, name='id', primary_key=True, comment="id")
    hostname = Column(String(200), nullable=True, name='hostname', comment="服务器名称")
    script_name = Column(String(200), nullable=True, name='script_name', comment="脚本名称")
    script_path = Column(String(200), nullable=True, name='script_path', comment="脚本路径")
    process_name = Column(String(200), nullable=True, name='process_name', comment="进程名称")
    group_name = Column(String(200), nullable=True, name='group_name', comment="进程组名称")
    full_process_name = Column(String(200), nullable=True, name='full_process_name', comment="组进程名称")
    host_process_name = Column(String(200), nullable=True, name='host_process_name', comment="服务器进程名称")
    livenessAt = Column(DateTime, default=datetime.now(), name='livenessAt', comment="活跃探针")
    readinessAt = Column(DateTime, default=datetime.now(), name='readinessAt', comment="就绪探针")
    md5 = Column(String(200), nullable=True, name='md5', comment="md5")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now(), name='updatedAt', comment="更新时间")
