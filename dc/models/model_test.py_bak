from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete
from sqlalchemy.orm import declarative_base, relationship, backref, Session
from sqlalchemy.types import CHAR
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import scoped_session
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum
from urllib.parse import quote_plus as urlquote

# 声明ORM的一个基类并建立映射关系
Base = declarative_base()


class SexEnum(IntEnum):
    MAN = 1
    WOMEN = 2


# name为在数据库表中的名称
# type为数据类型
# comment为注释
# nullable为不能为空
# default为默认字段
# unique为唯一约束
class User(Base):
    """用户信息表格"""
    __tablename__ = 'account_user'
    id = Column(Integer, name='id', primary_key=True)
    user_name = Column(String(32), nullable=True, name='user_name', comment="user_name", unique=True)
    password = Column(String(512), nullable=True, name='password', comment="password")
    real_name = Column(String(16), nullable=False, name='real_name', comment="real_name")
    sex = Column(Enum(SexEnum), default=None, name='sex', comment="sex")
    age = Column(TINYINT(unsigned=True), default=0, name='age', comment="age")
    create_at = Column(DateTime, default=datetime.now(), name='create_at', comment="create_at")
    is_valid = Column(Boolean, default=True, name='is_valid', comment="is_valid")
    profile = relationship('UserProfile', backref='user', uselist=False)  # 一对一


class UserAddress(Base):
    """地址信息表格"""
    __tablename__ = 'account_user_address'
    id = Column(Integer, name='id', primary_key=True)
    area = Column(String(256), nullable=False, name='area', comment="area")
    phone_num = Column(CHAR(11), name='phone_num', comment="phone_num")
    remark = Column(String(512), name='remark', comment="remark")
    is_valid = Column(Boolean, default=True, name='is_valid', comment="is_valid")

    create_at = Column(DateTime, default=datetime.now(), name='create_at', comment="create_at")
    # user_id = Column(Integer, ForeignKey(User.id), comment="关联")  # 进行ORM关联一对多
    user_id = Column(Integer, ForeignKey(User.id, ondelete="CASCADE"), comment="关联")  # 进行ORM关联一对多，操作一：指定删除后操作

    # user = relationship("User", backref='address')  # 一对多经典
    # user = relationship("User", backref=backref('address', lazy='dynamic'))  # 可以执行子查询
    user = relationship("User", backref=backref('address', lazy='dynamic', cascade="all,delete"))  # 可以执行子查询 操作二：指定删除后操作


class UserProfile(Base):
    """用户详细信息表格"""
    __tablename__ = 'account_user_profile'
    id = Column(Integer, primary_key=True)
    hobby = Column(String(255), nullable=False, name='hobby', comment="hobby")
    user_id = Column(Integer, ForeignKey(User.id), comment="user_id")
    # user = relationship("User", backref=backref('profile', unelist=False))  # 两种方法


# 同步关系创建/删除数据库
# Base.metadata.create_all(engine)  # 创建表
# Base.metadata.drop_all(engine)  # 删除表