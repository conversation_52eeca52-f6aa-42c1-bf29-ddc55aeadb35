import json
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, SmallInteger, Boolean, ForeignKey, \
    select, func, update, delete, Numeric, Float, Text
from sqlalchemy.orm import declarative_base, relationship, backref, Session, DeclarativeMeta
from sqlalchemy.types import CHAR
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import scoped_session
from sqlalchemy.dialects.mysql import TINYINT
from datetime import datetime
from enum import IntEnum
from urllib.parse import quote_plus as urlquote
from dc.exts import db
# 声明ORM的一个基类并建立映射关系
from dc.common.alchemy import AlchemyJsonEncoder, ModelMixin
from dc.models.model_base import Base


# Base = declarative_base()


# name为在数据库表中的名称
# type为数据类型
# comment为注释
# nullable为不能为空
# default为默认字段
# unique为唯一约束
class DCSiteTask(Base, ModelMixin):
    """采集器任务表"""
    __tablename__ = 'DC_SiteTask'
    id = Column(Integer, name='id', primary_key=True)
    siteListId = Column(Integer, name='siteListId')
    mainTaskId = Column(Integer, name='mainTaskId')
    baseUrl = Column(String(32), nullable=True, name='baseUrl', comment="官网地址")
    url = Column(String(32), nullable=True, name='url', comment="需要采集的url")
    title = Column(String(32), nullable=True, name='title', comment="文章标题")
    urlType = Column(TINYINT(unsigned=True), default=1, name='urlType', comment="url类型, 1页面 2附件")
    taskStatus = Column(TINYINT(unsigned=True), default=0, name='taskStatus', comment="任务状态,  0 未执行, 1 执行完成 , 2执行失败")
    taskAt = Column(DateTime, default=datetime.now(), name='taskAt', comment="执行任务时间")
    analysisStatus = Column(TINYINT(unsigned=True), default=0, name='analysisStatus',
                            comment="分析状态 0 未执行, 1,已执行, 2执行失败")
    httpStatusCode = Column(TINYINT(unsigned=True), default=0, name='httpStatusCode',
                            comment="如果执行, 则返回执行任务后的 status状态")
    mongoCollection = Column(String(32), nullable=True, name='mongoCollection', comment="mongo Collection")
    mongoKey = Column(String(32), nullable=True, name='mongoKey', comment="存入mongodb 的key")
    retryTime = Column(TINYINT(unsigned=True), default=0, name='retryTime', comment="重试次数")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now(), name='updatedAt', comment="更新时间")
    esIndex = Column(String(32), nullable=True, name='esIndex', comment="es-index")
    esId = Column(String(32), nullable=True, name='esId', comment="es->_id")
    ruleId = Column(Integer, name='ruleId', comment='解析规则id')
    publicTime = Column(String, name='publicTime', comment="发布时间")
    coverImg = Column(String, name='coverImg', comment="封面图")
    keyword = Column(String, name='keyword', comment="关键词")
    # siteMainTask = relationship('DCSiteMainTask', backref='siteTask', uselist=False)  # 一对一


class DCSiteTaskHistory(Base, ModelMixin):
    """采集器任务历史表"""
    __tablename__ = 'DC_SiteTaskHistory'
    id = Column(Integer, name='id', primary_key=True)
    taskId = Column(Integer, name='taskId', comment="子任务id")
    siteListId = Column(Integer, name='siteListId')
    mainTaskId = Column(Integer, name='mainTaskId')
    baseUrl = Column(String(32), nullable=True, name='baseUrl', comment="官网地址")
    url = Column(String(32), nullable=True, name='url', comment="需要采集的url")
    title = Column(String(32), nullable=True, name='title', comment="文章标题")
    taskStatus = Column(TINYINT(unsigned=True), default=0, name='taskStatus', comment="任务状态,  0 未执行, 1 执行完成 , 2执行失败")
    taskAt = Column(DateTime, default=datetime.now(), name='taskAt', comment="执行任务时间")
    analysisStatus = Column(TINYINT(unsigned=True), default=0, name='analysisStatus',
                            comment="分析状态 0 未执行, 1,已执行, 2执行失败")
    httpStatusCode = Column(TINYINT(unsigned=True), default=0, name='httpStatusCode',
                            comment="如果执行, 则返回执行任务后的 status状态")
    mongoCollection = Column(String(32), nullable=True, name='mongoCollection', comment="mongo Collection")
    mongoKey = Column(String(32), nullable=True, name='mongoKey', comment="存入mongodb 的key")
    retryTime = Column(TINYINT(unsigned=True), default=0, name='retryTime', comment="重试次数")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now(), name='updatedAt', comment="更新时间")
    esIndex = Column(String(32), nullable=True, name='esIndex', comment="es-index")
    esId = Column(String(32), nullable=True, name='esId', comment="es->_id")
    ruleId = Column(Integer, name='ruleId', comment='解析规则id')
    demo = Column(String(32000), nullable=True, name='demo', comment="备注")
    # siteMainTask = relationship('DCSiteMainTask', backref='siteTask', uselist=False)  # 一对一


class DCSiteList(Base, ModelMixin):
    """数据源列表"""
    __tablename__ = 'DC_SiteList'
    id = Column(Integer, name='id', primary_key=True)
    sourceName = Column(String, name='sourceName', comment="数据源名称")
    sourceMark = Column(String, name='sourceMark', comment="数据源标识")
    siteName = Column(String, name='siteName', comment="网站名称")
    siteUrl = Column(String, name='siteUrl', comment="种子网址")
    level = Column(Integer, name='level', comment="数据源等级（1,2,3,4）")
    sourceCategory = Column(String, name='sourceCategory', comment="数据源分类")
    areaCode = Column(Integer, name='areaCode', comment="地区代码")
    areaName = Column(String, name='areaName', comment="地区名称")
    dcType = Column(Integer, name='dcType', comment="采集类型 1列表 2单页")
    detailUrl = Column(String, name='detailUrl', comment="详情页地址")
    listDcMethod = Column(Integer, name='listDcMethod', comment="列表采集方式 1普通 2高级")
    detailDcMethod = Column(Integer, name='detailDcMethod', comment="详情采集方式 1普通 2高级")
    detailPageCategory = Column(Integer, name='detailPageCategory', comment="详情页类别(1 id自增，2列表页集合型，3自定义)")
    sourceFrequencyType = Column(Integer, name='sourceFrequencyType', comment="采集频次单位  1.分钟 2.小时  3.天")
    sourceFrequency = Column(String, name='sourceFrequency', comment="访问数据源的频次")
    pageFrequencyType = Column(Integer, name='pageFrequencyType', comment="页面采集时间间隔单位（0秒，1分钟，2小时）")
    pageFrequency = Column(String, name='pageFrequency', comment="页面采集时间间隔")
    type = Column(Integer, name='type', comment="类型 :  1. 网站抓取, 2 api")
    listUrl = Column(String, name='listUrl', comment="分页面地址")
    crawlRuleType = Column(Integer, name='crawlRuleType', comment="抽取规则（1xpath,2css,3正则，4jspath）")
    crawlRule = Column(String, name='crawlRule', comment="URL抽取规则详情")
    crawlRuleTtile = Column(String, name='crawlRuleTtile', comment="标题抽取规则详情")
    note = Column(String, name='note', comment="备注")
    listJsPath = Column(String, name='listJsPath', comment="列表的jsPath")
    listPaginateUri = Column(String, name='listPaginateUri', comment="列表分页参数的uri")
    listPagiNateParams = Column(String, name='listPagiNateParams', comment="分页列表的分页参数")
    listPagiNatePageNum = Column(String, name='listPagiNatePageNum', comment="第二页的初始值")
    listPagiNatePageOffset = Column(Integer, name='listPagiNatePageOffset', comment="列表如果有跳页获取 这里设定offset  防止拉取重复数据 ")
    loginUser = Column(String, name='loginUser', comment="需要登录的用户    ")
    loginUserKey = Column(String, name='loginUserKey', comment="登录用户的key as username")
    loginPwd = Column(String, name='loginPwd', comment="登录需要的密码")
    apiMethod = Column(Integer, name='apiMethod', comment="1.get ,2 post")
    apiParams = Column(String, name='apiParams', comment="api的参数")
    apiHeaders = Column(String, name='apiHeaders', comment="api的header")
    loginPwdKey = Column(String, name='loginPwdKey', comment="登录密码的key")
    extra = Column(String, name='extra', comment="极特殊情况采取的处理参数")
    isUsed = Column(Integer, name='isUsed', comment="默认1 可用 2不可用")
    createdAt = Column(String, name='createdAt', comment="")
    updatedAt = Column(String, name='updatedAt', comment="")
    lastExecTime = Column(String, name='lastExecTime', comment="最后一次进入队列时间")
    status = Column(Integer, name='status', comment="1正常，0删除")
    updatedUserId = Column(Integer, name='updatedUserId', comment="修改人ID")
    createUserId = Column(Integer, name='createUserId', comment="创建用户ID")
    nextExecTime = Column(String, name='nextExecTime', comment="下一次进入队列时间")
    firstExecTime = Column(String, name='firstExecTime', comment="首次执行时间")
    specialTreatment = Column(String, name='specialTreatment', comment="特殊处理（1相对路径，2firefox）")
    version = Column(Integer, name='version', comment="版本号")
    versionTime = Column(DateTime, name='versionTime', comment="版本时间")
    MaxDay = Column(Integer, name='MaxDay', comment="最大抓取天数")
    MaxResult = Column(Integer, name='MaxResult', comment="最大结果集数量")
    TimeSelector = Column(String, name='TimeSelector', comment="时间选择器")
    CoverImageSelector = Column(String, name='CoverImageSelector', comment='封面图选择器')
    ListSelector = Column(String, name='ListSelector', comment="捕捉列表选择器")
    isQuickShort = Column(TINYINT, name='isQuickShort', comment="是否需要快照  1 要 2 不要")
    isKeyWord = Column(TINYINT, name='isKeyWord', comment="是否关键词搜索  1 否  2 是")
    isHotModule = Column(TINYINT, name='isHotModule', comment="是否热门  1 是  2 否")
    tags = Column(String, name='tags', comment="标签")


class DCSiteMainTask(Base, ModelMixin):
    """主任务表"""
    __tablename__ = 'DC_SiteMainTask'
    id = Column(Integer, name='id', primary_key=True)
    siteListId = Column(Integer, name='siteListId')
    taskDate = Column(Integer, name='taskDate', default=0)
    url = Column(String(32), nullable=True, name='url', comment="需要采集的url")
    listUrl = Column(String(32), nullable=True, name='listUrl', comment="需要采集的url")
    # httpStatusCode = Column(TINYINT(unsigned=True), default=0, name='httpStatusCode',
    #                         comment="任务状态,  0 未执行, 1 执行完成 , 2执行失败")
    httpStatusCode = Column(Integer, name='httpStatusCode', comment="网站的返回值")
    listUrlCount = Column(Integer, name='listUrlCount', default=0)
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now(), name='updatedAt', comment="更新时间")
    keyWord = Column(String, name='keyWord', comment="关键词")
    status = Column(TINYINT(unsigned=True), default=0, name='status',
                    comment="0,未开始, 1进行中, 2已完成, 3失败")
    # siteTask = relationship("DCSiteTask", backref=backref('siteMainTask', unelist=False))  # 两种方法


class DCSiteCheck(Base, ModelMixin):
    """站点检测表"""
    __tablename__ = 'DC_SiteCheck'
    id = Column(Integer, name='id', primary_key=True)
    siteName = Column(String, name='siteName', comment="站点名称")
    siteMark = Column(String, name='siteMark', comment="站点备注")
    siteUrl = Column(String, name='siteUrl', comment="站点网址")
    siteType = Column(Integer, name='siteType', comment="检测类型 1 页面 2 接口")
    checkMethod = Column(Integer, name='checkMethod', comment="检测方式 1 http code 2 page source 3 json 4 xml 5 text")
    checkRule = Column(String, name='checkRule', comment="检测表达式")
    checkFrequencyType = Column(Integer, name='checkFrequencyType', comment="检测频次单位  1.分钟 2.小时  3.天")
    checkFrequency = Column(String, name='checkFrequency', comment="检测时间间隔")
    extra = Column(String, name='extra', comment="极特殊情况采取的处理参数")
    isUsed = Column(Integer, name='isUsed', comment="默认1 可用 2不可用")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, name='updatedAt', comment="更新时间")
    firstAt = Column(DateTime, name='firstAt', comment="首次执行时间")
    lastAt = Column(DateTime, name='lastAt', comment="上次执行时间")
    nextAt = Column(DateTime, name='nextAt', comment="下次执行时间")
    status = Column(Integer, name='status', comment="1正常，0删除")


class DCGzhArticle(Base, ModelMixin):
    """公众号文章表"""
    __tablename__ = 'DC_GzhArticle'
    id = Column(Integer, name='id', primary_key=True)
    wxId = Column(String, name='wxId', comment="微信号id")
    gzhId = Column(String, name='gzhId', comment="公众号id")
    title = Column(String, name='title', comment="标题")
    url = Column(String, name='url', comment="链接")
    url_md5 = Column(String, name='url_md5', comment="url_md5")
    cover = Column(String, name='cover', comment="图片")
    wx_pub_time = Column(DateTime, name='wx_pub_time', comment="微信创建时间")
    wx_create_time = Column(DateTime, name='wx_create_time', comment="微信更新时间")
    raw = Column(String, name='raw', comment="推送来的原消息")
    isAnalysis = Column(String, default=0, name='isAnalysis', comment="是否已解析")
    analysisQueue = Column(Integer, default=0, name='analysisQueue', comment="分析队列状态：0初始，1队列")
    analysisQueueAt = Column(DateTime, name='analysisQueueAt', comment="分析队列时间")
    analysisResult = Column(String, default=0, name='analysisResult', comment="分析结果")
    analysisAt = Column(DateTime, name='analysisAt', comment="分析时间")
    analysisRetryTimes = Column(Integer, default=0, name='analysisRetryTimes', comment="分析重试次数")
    analysisRetryAt = Column(DateTime, name='analysisRetryAt', comment="分析重试时间")
    esIndex = Column(String, name='esIndex', comment="es index")
    esId = Column(String, name='esId', comment="es key")
    mongoCollection = Column(String, name='mongoCollection', comment="mongo collection")
    mongoKey = Column(String, name='mongoKey', comment="mongo key")
    praiseNum = Column(Integer, name='praiseNum', comment="点赞数")
    readNum = Column(Integer, name='readNum', comment="阅读数")
    dataSource = Column(Integer, default=1, name='dataSource', comment="1.拉取  2.推送")
    readAt = Column(DateTime, name='readAt', comment="阅读数更新时间")
    readStatus = Column(Integer, default=0, name='readStatus', comment="阅读数状态：0未获取，1正常，2失败")
    readQueue = Column(Integer, default=0, name='readQueue', comment="阅读数队列状态：0初始，1队列")
    readQueueAt = Column(DateTime, name='readQueueAt', comment="阅读数队列时间")
    readResult = Column(String, default=0, name='readResult', comment="阅读数结果")
    status = Column(Integer, default=1, name='status', comment="1正常，0删除")
    createAt = Column(DateTime, default=datetime.now(), name='createAt', comment="创建时间")
    # exec_text = Column(String, name='exec_text', comment="执行内容")
    # exec_status = Column(Integer, default=1, name='exec_status', comment="执行状态 0未开始 1成功 2失败")


class DCGzhInfo(Base, ModelMixin):
    """公众号表"""
    __tablename__ = 'DC_GzhInfo'
    id = Column(Integer, name='id', primary_key=True)
    gzhId = Column(String, name='gzhId', comment="公众号id")
    gzhNo = Column(String, name='gzhNo', comment="公众号号码")
    nickName = Column(String, name='nickName', comment="公众号昵称")
    description = Column(String, name='description', comment="描述")
    principalPart = Column(String, name='principalPart', comment="主体")
    remark = Column(String, name='remark', comment="备注")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, name='updatedAt', comment="更新时间")
    status = Column(Integer, name='status', comment="状态：1启用，0已删")
    crawlStatus = Column(Integer, default=1, name='crawlStatus', comment="抓取状态 1.抓取 0.暂停抓取")
    crawlNum = Column(Integer, default=0, name='crawlNum', comment="抓取数量")
    pullStatus = Column(Integer, default=0, name='pullStatus', comment="文章拉取状态 0.未进行 1.成功  2失败")
    pullAt = Column(DateTime, name='pullAt', comment="文章拉取时间")
    pullResult = Column(String, name='pullResult', comment="文章拉取结果")
    isSubscribe = Column(Integer, name='isSubscribe', comment="是否关注了")
    syncStatus = Column(Integer, name='syncStatus', default=0, comment="同步状态： 0 未开始 1 完成 2失败")
    syncAt = Column(DateTime, name='syncAt', comment="同步时间")
    syncResult = Column(String, name='syncResult', comment="同步结果")
    level = Column(TINYINT, name='level', comment='数据源等级（1，2，3，4）')


class DCGzhTag(Base, ModelMixin):
    """公众号标签"""
    __tablename__ = 'DC_GzhTag'
    id = Column(Integer, name='id', primary_key=True)
    gzhId = Column(String, name='gzhId', comment="公众号id")
    tagName = Column(String, name='tagName', comment="标签名")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")


class DCGzhWechat(Base, ModelMixin):
    """公众号微信表"""
    __tablename__ = 'DC_GzhWechat'
    id = Column(Integer, name='id', primary_key=True)
    wxId = Column(String, name='wxId', comment="微信id")
    wxNo = Column(String, name='wxNo', comment="微信号")
    nickName = Column(String, name='nickName', comment="微信昵称")
    password = Column(String, name='password', comment="微信密码")
    isUsed = Column(Integer, name='isUsed', comment="是否使用")
    lastOnlineTime = Column(String, name='lastOnlineTime', comment="上次在线时间")
    machineIp = Column(String, name='machineIp', comment="机器的ip端口")
    machinePid = Column(String, name='machinePid', comment="机器的进程号")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, name='updatedAt', comment="更新时间")
    status = Column(Integer, name='status', comment="状态：1启用，2停用，0已删除")
    wechatType = Column(Integer, default=1, name='wechatType', comment="微信类型：1默认，2推送")


class DCGzhWechatRelation(Base, ModelMixin):
    """公众号微信关联表"""
    __tablename__ = 'DC_GzhWechatRelation'
    id = Column(Integer, name='id', primary_key=True)
    wxId = Column(String, name='wxId', comment="微信id")
    gzhId = Column(String, name='gzhId', comment="公众号id")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")


class DCGzhs(Base, ModelMixin):
    """公众号库表(临时表)"""
    __tablename__ = 'DC_Gzhs'
    id = Column(Integer, name='id', primary_key=True)
    gzhId = Column(String, name='gzhId', comment="公众号id")
    gzhNo = Column(String, name='gzhNo', comment="公众号号码")
    nickName = Column(String, name='nickName', comment="公众号昵称")
    description = Column(String, name='description', comment="描述")
    remark = Column(String, name='remark', comment="备注")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, name='updatedAt', comment="更新时间")
    businessType = Column(Integer, name='businessType',
                          comment="搜索类型：0: 全部, 1: 公众号, 2: 文章, 7: 视频号, 9: 直播, 16384: 新闻, 12582912: 视频, 262208: 小程序, 8192: 微信指数")
    status = Column(Integer, name='status', default=1, comment="状态：1启用，0已删")
    principalPart = Column(String, name='principalPart', comment="企业主体")
    keyword = Column(String, name='keyword', comment="搜索关键字")
    signature = Column(String, name='signature', comment="公众号说明")
    content = Column(String, name='content', comment="公众号详情")
    syncStatus = Column(Integer, name='syncStatus', default=0, comment="同步状态： 0 未开始 1 完成 2失败")
    syncResult = Column(String, name='syncResult', comment="同步结果")


class DCGzhImport(Base, ModelMixin):
    """公众号导入表(临时表)"""
    __tablename__ = 'DC_GzhImport'
    id = Column(Integer, name='id', primary_key=True)
    gzhId = Column(String, name='gzhId', comment="公众号id")
    gzhNo = Column(String, name='gzhNo', comment="公众号号码")
    nickName = Column(String, name='nickName', comment="公众号昵称")
    description = Column(String, name='description', comment="描述")
    remark = Column(String, name='remark', comment="备注")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, name='updatedAt', comment="更新时间")
    businessType = Column(Integer, name='businessType',
                          comment="搜索类型：0: 全部, 1: 公众号, 2: 文章, 7: 视频号, 9: 直播, 16384: 新闻, 12582912: 视频, 262208: 小程序, 8192: 微信指数")
    status = Column(Integer, name='status', default=1, comment="状态：1启用，0已删")
    principalPart = Column(String, name='principalPart', comment="企业主体")
    keyword = Column(String, name='keyword', comment="搜索关键字")
    signature = Column(String, name='signature', comment="公众号说明")
    content = Column(String, name='content', comment="公众号详情")
    syncStatus = Column(Integer, name='syncStatus', default=0, comment="同步状态： 0 未开始 1 完成 2失败")
    syncAt = Column(DateTime, name='syncAt', comment="同步时间")
    syncResult = Column(String, name='syncResult', comment="同步结果")


class IPTest(Base, ModelMixin):
    """ip池速度测试"""
    __tablename__ = 'ip_test'
    id = Column(Integer, name='id', primary_key=True)
    url = Column(String, name='url', comment="url")
    proxy = Column(String, name='proxy', comment="proxy")
    type = Column(String, name='type', comment="type")
    content = Column(String, name='content', comment="content")
    content2 = Column(String, name='content2', comment="content2")
    content3 = Column(String, name='content3', comment="content3")
    exec_time = Column(Integer, name='exec_time', comment="exec_time")
    exec_time2 = Column(Integer, name='exec_time2', comment="exec_time2")
    exec_time3 = Column(Integer, name='exec_time3', comment="exec_time3")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")


class InformationSchemaColumns(Base, ModelMixin):
    """数据字典"""
    __tablename__ = 'columns'
    vid = Column(Integer, primary_key=True)  # just add for sake of this error, dont add in db
    TABLE_CATALOG = Column(String, name='TABLE_CATALOG', comment="")
    TABLE_SCHEMA = Column(String, name='TABLE_SCHEMA', comment="")
    TABLE_NAME = Column(String, name='TABLE_NAME', comment="")
    COLUMN_NAME = Column(String, name='COLUMN_NAME', comment="")
    ORDINAL_POSITION = Column(Integer, name='ORDINAL_POSITION', comment="")
    COLUMN_DEFAULT = Column(String, name='COLUMN_DEFAULT', comment="")
    IS_NULLABLE = Column(String, name='IS_NULLABLE', comment="")
    DATA_TYPE = Column(String, name='DATA_TYPE', comment="")
    CHARACTER_MAXIMUM_LENGTH = Column(Integer, name='CHARACTER_MAXIMUM_LENGTH', comment="")
    CHARACTER_OCTET_LENGTH = Column(Integer, name='CHARACTER_OCTET_LENGTH', comment="")
    NUMERIC_PRECISION = Column(Integer, name='NUMERIC_PRECISION', comment="")
    NUMERIC_SCALE = Column(Integer, name='NUMERIC_SCALE', comment="")
    DATETIME_PRECISION = Column(Integer, name='DATETIME_PRECISION', comment="")
    CHARACTER_SET_NAME = Column(String, name='CHARACTER_SET_NAME', comment="")
    COLLATION_NAME = Column(String, name='COLLATION_NAME', comment="")
    COLUMN_TYPE = Column(String, name='COLUMN_TYPE', comment="")
    COLUMN_KEY = Column(String, name='COLUMN_KEY', comment="")
    EXTRA = Column(String, name='EXTRA', comment="")
    PRIVILEGES = Column(String, name='PRIVILEGES', comment="")
    COLUMN_COMMENT = Column(String, name='COLUMN_COMMENT', comment="")
    GENERATION_EXPRESSION = Column(String, name='GENERATION_EXPRESSION', comment="")


class DCSiteListRule(db.Model, ModelMixin):
    """规则表"""
    __tablename__ = 'DC_SiteListRule'
    id = Column(Integer, name='id', primary_key=True)
    siteListId = Column(Integer, name='siteListId')
    ruleTitle = Column(String, name='ruleTitle', comment="规则名称")
    dataKey = Column(String, name='dataKey', comment="数据存储项")
    comment = Column(String, name='comment', comment="规则描述")
    status = Column(TINYINT(unsigned=True), default=0, name='status',
                    comment="状态,  0 关闭, 1 开启, 2 删除")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now(), name='updatedAt', comment="更新时间")
    updatedUserId = Column(Integer, name='updatedUserId', default=0)
    createUserId = Column(Integer, name='createUserId', default=0)
    # siteTask = relationship("DCSiteTask", backref=backref('siteMainTask', unelist=False))  # 两种方法


class DCSiteListItem(db.Model, ModelMixin):
    """规则数据项表"""
    __tablename__ = 'DC_SiteListItem'
    id = Column(Integer, name='id', primary_key=True)
    siteListId = Column(Integer, name='siteListId', default=0)
    siteRuleId = Column(Integer, name='siteRuleId')
    columnTitle = Column(String, name='columnTitle', comment="数据项名称")
    columnKey = Column(String, name='columnKey', comment="数据项key")
    crawlRuleType = Column(TINYINT(unsigned=True), default=0, name='crawlRuleType',
                           comment="抽取规则 1 xpath, 2 css, 3 正则 4 标记截取")
    crawlRule = Column(String, name='crawlRule', comment="抽取规则详情")
    startFlag = Column(String, name='startFlag', comment="截取开始标记")
    endFlag = Column(String, name='endFlag', comment="截取结束标记")
    columnRegex = Column(String, name='columnRegex', comment="正则表达式")
    columnDefault = Column(String, name='columnDefault', comment="数据项默认值")
    status = Column(TINYINT(unsigned=True), default=0, name='status',
                    comment="状态,  0 启用, 1 暂停, 2 删除")
    createdAt = Column(DateTime, default=datetime.now(), name='createdAt', comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now(), name='updatedAt', comment="更新时间")
    analysisType = Column(TINYINT, default=1, name='analysisType', comment="数爬取类型 1.文本  2.html代码")
    path = Column(String, name='path', comment="路径地址")
    pathLevel = Column(String, name='pathLevel', comment="路径层级")
    # siteTask = relationship("DCSiteTask", backref=backref('siteMainTask', unelist=False))  # 两种方法


class MPCompanyInfo(db.Model, ModelMixin):
    """规则数据项表"""
    __tablename__ = 'mp_company_info'
    id = Column(Integer, name='id', comment="流水号ID", primary_key=True)
    staff_num_range = Column(String, name='staff_num_range', comment="人员规模")
    from_time = Column(Integer, name='from_time', comment="经营开始时间")
    to_time = Column(Integer, name='to_time', comment="经营结束时间")
    type = Column(Integer, name='type', comment="法人类型，1 人 2 公司")
    is_microent = Column(Integer, name='is_microent', comment="是否是小微企业 0不是 1是")
    reg_number = Column(String, name='reg_number', comment="注册号")
    percentile_score = Column(Integer, name='percentile_score', comment="企业评分")
    reg_capital = Column(String, name='reg_capital', comment="注册资本")
    name = Column(String, name='name', comment="企业名")
    introduction = Column(String, name='introduction', comment="企业简介")
    logo_url = Column(String, name='logo_url', comment="企业Logo地址")
    reg_institute = Column(String, name='reg_institute', comment="登记机关")
    reg_location = Column(String, name='reg_location', comment="注册地址")
    industry = Column(String, name='industry', comment="行业")
    approved_time = Column(Integer, name='approved_time', comment="核准时间")
    update_times = Column(Integer, name='update_times', comment="更新时间")
    social_staff_num = Column(Integer, name='social_staff_num', comment="参保人数")
    tags = Column(String, name='tags', comment="企业标签")
    tax_number = Column(String, name='tax_number', comment="纳税人识别")
    business_scope = Column(String, name='business_scope', comment="经营范围")
    en_name = Column(String, name='en_name', comment="英文名")
    alias = Column(String, name='alias', comment="简称")
    org_number = Column(String, name='org_number', comment="组织机构代码")
    reg_status = Column(String, name='reg_status', comment="企业状态（经营状态）")
    check_date = Column(Integer, name='check_date', comment="发照时间")
    estiblish_time = Column(Integer, name='estiblish_time', comment="成立日期（注册日期）")
    isbond = Column(Integer, name='isbond', comment="是否上市（0-未上市，1-上")
    bond_type = Column(String, name='bond_type', comment="股票类型")
    bond_num = Column(String, name='bond_num', comment="股票号")
    bond_name = Column(String, name='bond_name', comment="股票名")
    used_bond_name = Column(String, name='used_bond_name', comment="股票曾用名")
    legal_personid = Column(String, name='legal_personid', comment="法人id")
    legal_person_name = Column(String, name='legal_person_name', comment="法人")
    actual_capital = Column(String, name='actual_capital', comment="实收注册资金")
    company_orgtype = Column(String, name='company_orgtype', comment="企业类型")
    credit_code = Column(String, name='credit_code', comment="统一社会信用代码")
    history_names = Column(String, name='history_names', comment="曾用名")
    reg_capital_currency = Column(String, name='reg_capital_currency', comment="注册资本币种 人民币 美元 欧元 等")
    actual_capital_currency = Column(String, name='actual_capital_currency', comment="实收注册资本币种 人民币 美元 欧元 等")
    email = Column(String, name='email', comment="邮箱")
    website_list = Column(String, name='website_list', comment="网址")
    phone_number = Column(String, name='phone_number', comment="企业联系方式")
    revoke_date = Column(Integer, name='revoke_date', comment="吊销日期")
    revoke_reason = Column(String, name='revoke_reason', comment="吊销原因")
    cancel_date = Column(Integer, name='cancel_date', comment="注销日期")
    cancel_reason = Column(String, name='cancel_reason', comment="注销原因")
    country = Column(String, name='country', comment="国家简称")
    base = Column(String, name='base', comment="省份简称")
    city = Column(String, name='city', comment="市")
    district = Column(String, name='district', comment="区")
    category = Column(String, name='category', comment="国民经济行业分类门类")
    category_big = Column(String, name='category_big', comment="国民经济行业分类大类")
    category_middle = Column(String, name='category_middle', comment="国民经济行业分类中类")
    category_small = Column(String, name='category_small', comment="国民经济行业分类小类")
    updated_at = Column(String, name='updated_at', comment="更新时间")
    created_at = Column(String, name='created_at', comment="创建时间")
    status = Column(TINYINT(unsigned=True), default=1, name='status',
                    comment="状态, 1 有效, 0 无效")


class MPCompanyRecruit(db.Model, ModelMixin):
    """公司招聘信息表-过滤数据"""
    __tablename__ = 'MP_CompanyRecruit'
    id = Column(Integer, name='id', comment="主键", primary_key=True)
    companyId = Column(Integer, name='companyId', comment="公司id")
    jobId = Column(String, name='jobId', comment="职位id")
    jobKind = Column(String, name='jobKind', comment="种类")
    salary = Column(String, name='salary', comment="薪酬")
    requireWorkYears = Column(String, name='requireWorkYears', comment="职位工作年限要求")
    dq = Column(String, name='dq', comment="职位所在地区")
    refreshTime = Column(String, name='refreshTime', comment="发布时间")
    title = Column(String, name='title', comment="职位名称")
    link = Column(String, name='link', comment="职位详情页地址")
    requireEduLevel = Column(String, name='requireEduLevel', comment="学历要求")
    recruiterId = Column(String, name='recruiterId', comment="招聘id")
    recruiterName = Column(String, name='recruiterName', comment="发布人")
    recruiterTitle = Column(String, name='recruiterTitle', comment="发布人职位")
    labels = Column(String, name='labels', comment="岗位标签")
    createAt = Column(String, name='createAt', comment="创建时间")
    recruiterDetails = Column(String, name='recruiterDetails', default="", comment="招聘详情")


class MPCompanyRecruitOriginal(db.Model, ModelMixin):
    """公司招聘信息表-原始数据"""
    __tablename__ = 'MP_CompanyRecruitOriginal'
    id = Column(Integer, name='id', comment="主键", primary_key=True)
    companyId = Column(Integer, name='companyId', comment="公司id")
    jobId = Column(String, name='jobId', comment="职位id")
    jobKind = Column(String, name='jobKind', comment="种类")
    salary = Column(String, name='salary', comment="薪酬")
    requireWorkYears = Column(String, name='requireWorkYears', comment="职位工作年限要求")
    dq = Column(String, name='dq', comment="职位所在地区")
    refreshTime = Column(String, name='refreshTime', comment="发布时间")
    title = Column(String, name='title', comment="职位名称")
    link = Column(String, name='link', comment="职位详情页地址")
    requireEduLevel = Column(String, name='requireEduLevel', comment="学历要求")
    recruiterId = Column(String, name='recruiterId', comment="招聘id")
    recruiterName = Column(String, name='recruiterName', comment="发布人")
    recruiterTitle = Column(String, name='recruiterTitle', comment="发布人职位")
    labels = Column(String, name='labels', comment="岗位标签")
    createAt = Column(String, name='createAt', comment="创建时间")


class MPCompanyRecruitAlias(db.Model, ModelMixin):
    """公司招聘别名表"""
    __tablename__ = 'MP_CompanyRecruitAlias'
    id = Column(Integer, name='id', comment="主键", primary_key=True)
    companyId = Column(Integer, name='companyId', comment="公司id")
    name = Column(String, name='name', comment="公司名称")
    companyAlias = Column(String, name='companyAlias', comment="公司别名")
    status = Column(TINYINT(unsigned=True), default=1, name='status',
                    comment="状态, 1 有效, 2 无效")
    createAt = Column(String, name='createAt', comment="创建时间")
    updateAt = Column(String, name='updateAt', comment="更新时间")


class MPCompanyRecruitStatistics(db.Model, ModelMixin):
    """公司招聘信息表"""
    __tablename__ = 'MP_CompanyRecruitStatistics'
    id = Column(Integer, name='id', comment="主键", primary_key=True)
    companyId = Column(Integer, name='companyId', comment="公司id")
    recruiterNum = Column(Integer, name='recruiterNum', comment="近一年招聘岗位数")
    avgSalary = Column(String, name='avgSalary', comment="平均薪酬")
    jobDistribution = Column(String, name='jobDistribution', comment="岗位分布")
    salaryDistribution = Column(String, name='salaryDistribution', comment="薪资分布")
    educationDistribution = Column(String, name='educationDistribution', comment="学历分布")
    experienceDistribution = Column(String, name='experienceDistribution', comment="经验分布")
    cityDistribution = Column(String, name='cityDistribution', comment="地区分布")
    createAt = Column(String, name='createAt', comment="创建时间")


class MPCompanyAntitrust(db.Model, ModelMixin):
    """反垄断表"""
    __tablename__ = 'MP_CompanyAntitrust'
    id = Column(Integer, name='id', comment="", primary_key=True)
    title = Column(String, name='title', comment="标题")
    pubTime = Column(String, name='pubTime', comment="发布时间")
    source = Column(String, name='source', comment="来源")
    conclusionDate = Column(String, name='conclusionDate', comment="审结时间")
    url = Column(String, name='url', comment="详情链接")
    companyInfo = Column(String, name='companyInfo', comment="公司信息、案件参与者", default="")
    isUnconditional = Column(Integer, name='isUnconditional', comment="是否无条件批准  1.无条件  2.有条件")
    esId = Column(String, name='esId', comment="esId")
    esIndex = Column(String, name='esIndex', comment="esIndex")
    year = Column(String, name='year', comment="年份")
    month = Column(String, name='month', comment="月份")
    createAt = Column(String, name='createAt', comment="创建时间")


class SOYA_User(db.Model, ModelMixin):
    """soya 用户表"""
    __tablename__ = 'SOYA_User'
    id = Column(Integer, name='id', primary_key=True)
    account = Column(String, name="account", comment="SOYA用户账号，唯一")
    email = Column(String, name="email", comment="SOYA用户的email，唯一，发通知和找回密码使用")
    mobile = Column(String, name="mobile", comment="手机号，唯一，发通知和找回密码使用")
    name = Column(String, name="name", comment="姓名")
    nickName = Column(String, name="nickName", comment="昵称")
    avatar = Column(String, name="avatar", comment="头像url")
    introduction = Column(String, name="introduction", comment="简介")
    password = Column(String, name="password", comment="MD5（登录口令）")
    curSessionId = Column(String, name="curSessionId", comment="当前sessionId")
    siteId = Column(Integer, name="siteId", comment="站点id")
    oaRefreshFlag = Column(TINYINT, name="oaRefreshFlag", comment="状态：0-正常，1-需要强制刷新")
    oaUpdatedTime = Column(String, name="oaUpdatedTime", comment="OA用户资料最新更新日期")
    oaDepartments = Column(String, name="oaDepartments", comment="OA用户所属部门列表与身份")
    oaUserId = Column(Integer, name="oaUserId", comment="OA用户ID")
    oaLoginId = Column(String, name="oaLoginId", comment="OA用户名")
    status = Column(TINYINT, name="status", comment="状态：0-已删除，1-正常")
    dateLine = Column(String, name="dateLine", comment="更新时间")
    createdAt = Column(String, name="createdAt", comment="创建时间")
    type = Column(TINYINT, name="type", comment="1后台用户，2前台用户，3前后端")


class MPTycConfig(Base, ModelMixin):
    """天眼查配置表"""
    __tablename__ = 'MP_TycConfig'
    id = Column(Integer, name='id', primary_key=True)
    dailyMaxFee = Column(Integer, name="dailyMaxFee", comment="每日最大金额")
    balanceLessThan = Column(Integer, name="balanceLessThan", comment="余额小于多少报警")
    msgType = Column(Integer, name="msgType", comment="信息类型 : 发送使用")
    createdAt = Column(String, name="createdAt", comment="创建时间")
    updatedAt = Column(String, name="updatedAt", comment="更新时间")


class BossRecruit(db.Model, ModelMixin):
    """boss招聘表"""
    __tablename__ = 'boss_recruit'
    id = Column(Integer, name='id', comment="", primary_key=True)
    companyId = Column(Integer, name='companyId', comment="")
    securityId = Column(String, name='securityId', comment="")
    encryptBossId = Column(String, name='encryptBossId', comment="")
    bossName = Column(String, name='bossName', comment="招聘者名称")
    bossTitle = Column(String, name='bossTitle', comment="招聘者岗位")
    encryptJobId = Column(String, name='encryptJobId', comment="")
    jobName = Column(String, name='jobName', comment="招聘岗位")
    lid = Column(String, name='lid', comment="")
    salaryDesc = Column(String, name='salaryDesc', comment="薪资描述")
    jobLabels = Column(String, name='jobLabels', comment="岗位标签")
    jobValidStatus = Column(String, name='jobValidStatus', comment="岗位是否有效")
    skills = Column(String, name='skills', comment="技能")
    jobExperience = Column(String, name='jobExperience', comment="工作经验")
    jobDegree = Column(String, name='jobDegree', comment="工作学历要求")
    cityName = Column(String, name='cityName', comment="工作城市")
    areaDistrict = Column(String, name='areaDistrict', comment="工作区域")
    businessDistrict = Column(String, name='businessDistrict', comment="工作地点")
    jobType = Column(Integer, name='jobType', comment="岗位类型")
    encryptBrandId = Column(String, name='encryptBrandId', comment="")
    brandName = Column(String, name='brandName', comment="公司名称")
    brandStageName = Column(String, name='brandStageName', comment="公司阶段")
    brandIndustry = Column(String, name='brandIndustry', comment="公司内容")
    brandScaleName = Column(String, name='brandScaleName', comment="公司规模")
    welfareList = Column(String, name='welfareList', comment="福利待遇")
    createAt = Column(String, name='createAt', comment="创建时间")


class DCIntellectualProperty(db.Model, ModelMixin):
    """知识产权过滤表"""
    __tablename__ = 'DC_IntellectualProperty'
    id = Column(Integer, name='id', comment="", primary_key=True)
    siteListId = Column(Integer, name='siteListId', comment="数据源id")
    siteName = Column(String, name='siteName', comment="网站名称")
    siteUrl = Column(String, name='siteUrl', comment="网站地址")
    sourceName = Column(String, name='sourceName', comment="数据源名称")
    keyWords = Column(String, name='keyWords', default='', comment="包含关键词")
    filterWords = Column(String, name='filterWords', default='', comment="过滤关键词")
    tags = Column(String, name='tags', comment="标签")
    remark = Column(String, name='remark', comment="备注")
    createAt = Column(String, name='createAt', comment="创建时间")
    updateAt = Column(String, name='updateAt', comment="编辑时间")


class DCWechatMachine(db.Model, ModelMixin):
    """微信部署表"""
    __tablename__ = 'DC_WechatMachine'
    id = Column(Integer, name='id', primary_key=True)
    machineIp = Column(String, name='machineIp')
    status = Column(Integer, name='status')
    mark = Column(Integer, name='mark')
    createdAt = Column(Integer, name='createdAt', default=datetime.now())
    updatedAt = Column(Integer, name='updatedAt', default=datetime.now())


class DCCalendar(db.Model, ModelMixin):
    """日历表"""
    __tablename__ = 'DC_Calendar'
    id = Column(Integer, name='id', comment="", primary_key=True)
    year = Column(String, name='year', comment="年份")
    month = Column(String, name='month', comment="月份")
    date = Column(String, name='date', comment="日期")
    yearweek = Column(String, name='yearweek', comment="一年第几周")
    yearday = Column(String, name='yearday', comment="一年第几天")
    lunar_year = Column(String, name='lunar_year', comment="农历年份")
    lunar_month = Column(String, name='lunar_month', comment="农历月份")
    lunar_date = Column(String, name='lunar_date', comment="农历日期")
    lunar_yearday = Column(String, name='lunar_yearday', comment="农历一年第几天")
    week = Column(Integer, name='week', comment="周几（一周第几天）")
    weekend = Column(Integer, name='weekend', comment="是否周末： 1 周末  2 非周末")
    workday = Column(Integer, name='workday', comment="是否工作日： 1 工作日  2 非工作日")
    holiday = Column(String, name='holiday', comment="")
    holiday_or = Column(String, name='holiday_or', comment="")
    holiday_overtime = Column(String, name='holiday_overtime', comment="")
    holiday_today = Column(Integer, name='holiday_today', comment="是否节日：1 是  2 否")
    holiday_legal = Column(Integer, name='holiday_legal', comment="是否法定节日： 1 是  2 否")
    holiday_recess = Column(Integer, name='holiday_recess', comment="是否休假： 1 是  2 否")


class DCStockQA(db.Model, ModelMixin):
    """概念股问答"""
    __tablename__ = 'DC_Stock_QA'
    id = Column(Integer, name='id', comment="", primary_key=True)
    source = Column(Integer, name='source', comment="数据来源： 1 深交所互动易  2 全景网关系互动平台  3 上证E互动 ")
    unique_str = Column(String, name='unique_str', comment="数据平台唯一标识")
    title = Column(String, name='title', comment="文章标题")
    question = Column(String, name='question', comment="问题")
    answer = Column(String, name='answer', comment="答案")
    original_question = Column(String, name='original_question', comment="原始问题")
    original_answer = Column(String, name='original_answer', comment="原始答案")
    is_publish = Column(Integer, name='is_publish', default=1, comment="发布状态 1.待发布  2.已发布")
    is_del = Column(Integer, name='is_del', default=1, comment="是否删除  1. 否  2.是")
    stock_name = Column(String, name='stock_name', comment="股票名称")
    stock_code = Column(String, name='stock_code', comment="股票代码")
    answer_date = Column(DateTime, name='answer_date', comment="回答日期")
    capitalization = Column(Integer, name='capitalization', comment="公司市值，单位亿")
    stock_price = Column(Integer, name='stock_price', comment="股价")
    ratio = Column(Integer, name='ratio', comment="涨幅比例")
    publish_uid = Column(Integer, name='publish_uid', comment="发布人")
    create_at = Column(String, name='create_at', comment="创建时间")
    update_at = Column(String, name='update_at', comment="")
    is_pull = Column(Integer, name='is_pull', default=1, comment="是否拉取股票行情  1. 否  2.是")
    summary = Column(String, name='summary', comment="新闻摘要")
    tags = Column(String, name='tags', comment='标签')
    publish_at = Column(String, name='publish_at', default=None, comment="发布时间")
    is_chatgpt = Column(Integer, name='is_chatgpt', default=1, comment="是否成功调用chatgpt 1.未调用  2.已调用")
    unique_question = Column(String, name='unique_question', comment="问题md5唯一标识")


class DCStockInfo(db.Model, ModelMixin):
    """概念股-股票信息"""
    __tablename__ = 'DC_Stock_Info'
    id = Column(Integer, name='id', comment="", primary_key=True)
    stock_code = Column(String, name='stock_code', comment="股票代码")
    stock_company = Column(String, name='stock_company', comment="证券公司标识")
    stock_name = Column(String, name='stock_name', comment="股票名称")
    img_url = Column(String, name='img_url', comment="股票公司对应图片地址")
    is_del = Column(Integer, name='is_del', comment="是否删除：  1.否  2.是")
    create_at = Column(String, name='create_at', comment="创建时间")
    update_at = Column(String, name='update_at', comment="更新时间")


class DCSiteKeyWords(db.Model, ModelMixin):
    """配置关键词表"""
    __tablename__ = 'DC_SiteKeywords'
    id = Column(Integer, name='id', comment="", primary_key=True)
    keyword = Column(String, name='keyword', comment="关键词")
    companyId = Column(Integer, name='companyId', comment="企业id")
    status = Column(TINYINT, name='status', comment="状态 1.有效 0.无效")


class AIWriteArticle(Base, ModelMixin):
    """AI自动撰稿文章任务表"""
    __tablename__ = 'AI_WriteArticle'
    id = Column(Integer, name='id', comment="", primary_key=True)
    subject = Column(String, name='subject', comment="主题")
    title = Column(String, name='title', comment="标题")
    subTitle = Column(String, name='subTitle', comment="子标题")
    intro = Column(String, name='intro', comment="摘要")
    keyword = Column(String, name='keyword', comment="关键字")
    type = Column(Integer, name='type', comment="1普通文章，2深度文章")
    language = Column(Integer, name='language', comment="1中文，2英文")
    sideType = Column(Integer, name='sideType', comment="1中立，2正面，3负面")
    publishData = Column(String, name='publishData', comment="发布数据")
    status = Column(Integer, name='status', comment="0已删除，1排队中，2生成中，3已生成待发布，4发布成功，5执行失败，6发布失败")
    createUserId = Column(Integer, name='createUserId', comment="创建人")
    createdAt = Column(String, name='createdAt', comment="创建时间")
    updatedAt = Column(String, name='updatedAt', comment="更新时间")


class AIWriteSection(Base, ModelMixin):
    """AI自动撰稿文章段落表"""
    __tablename__ = 'AI_WriteSection'
    id = Column(Integer, name='id', comment="", primary_key=True)
    articleId = Column(Integer, name='articleId', comment="文章任务id")
    referenceText = Column(String, name='referenceText', comment="参考文本")
    characterNumMin = Column(Integer, name='characterNumMin', comment="字数下限")
    characterNumMax = Column(Integer, name='characterNumMax', comment="字数上限")
    ask = Column(String, name='ask', comment="要求")
    outline = Column(String, name='outline', comment="提纲")
    result = Column(Integer, name='result', comment="返回结果")
    status = Column(Integer, name='status', comment="0待生成，1已生成")


class AIWriteReference(Base, ModelMixin):
    """AI自动撰稿参考文献表"""
    __tablename__ = 'AI_WriteReference'
    id = Column(Integer, name='id', comment="", primary_key=True)
    articleId = Column(Integer, name='articleId', comment="文章任务id")
    sectionId = Column(Integer, name='sectionId', comment="段落任务id")
    type = Column(Integer, name='type', comment="1本地上传文件，2外部链接")
    address = Column(String, name='address', comment="地址（url或本地路径）")
    extension = Column(String, name='extension', comment="本地文件的扩展类型")
    name = Column(String, name='name', comment="本地文件时，保存文件名称，网址时，保存文章名称")
    important = Column(Integer, name='important', comment="0普通，1重点")
    content = Column(Integer, name='content', comment="解析的文本内容")
    status = Column(Integer, name='status', comment="0未执行，1执行成功，2执行失败")


class AIDailyTemplate(Base, ModelMixin):
    """日刊模板表"""
    __tablename__ = 'AI_DailyTemplate'
    id = Column(Integer, name='id', comment="", primary_key=True)
    templateName = Column(String, name='templateName', comment="模板名称")
    createdUser = Column(Integer, name='createdUser', comment="用户id")
    userSetting = Column(String, name='userSetting', comment="角色设定")
    questionDescription = Column(String, name='questionDescription', comment="描述")
    targetNeed = Column(String, name='targetNeed', comment="目标要求")
    moreDescription = Column(String, name='moreDescription', comment="更多描述")
    status = Column(Integer, name='status', comment="1可用 2不可用")
    createdAt = Column(String, name='createdAt', comment="创建时间")
    updatedAt = Column(String, name='updatedAt', comment="更新时间")


class DCSiteKeywordRelation(Base, ModelMixin):
    """数据源和关键词的关系表"""
    __tablename__ = 'DC_SiteKeywordRelation'
    id = Column(Integer, name='id', comment="", primary_key=True)
    siteId = Column(Integer, name='siteId', comment="数据源id")
    keywordId = Column(Integer, name='keywordId', comment="关键词id")


class DCICWrite(Base, ModelMixin):
    """本土学研内容撰稿"""
    __tablename__ = 'DC_IC_Writing'
    id = Column(Integer, name='id', comment="", primary_key=True)
    title = Column(String, name='title', comment="标题")
    content = Column(String, name='content', comment="正文")
    source = Column(String, name='source', comment="来源")
    type = Column(Integer, name='type', comment="类型 1.网页  2.公众号")
    url = Column(String, name='url', comment="原文链接")
    publicTime = Column(String, name='publicTime', comment="发布时间")
    analysisTime = Column(String, name='analysisTime', comment="爬取时间")
    isPublish = Column(Integer, name='isPublish', comment="是否发布  1.待发布  2.已发布  3.发布失败")
    isRepeat = Column(Integer, name='isRepeat', comment="是否去重  1.未去重  2.去重")
    publishedData = Column(String, name='publishedData', comment="发布数据")
    publishUid = Column(Integer, name='publishUid', comment="发部人id")
    publishAt = Column(String, name='publishAt', comment="发布时间")
    createAt = Column(String, name='createAt', comment="创建时间")
    updateAt = Column(String, name='updateAt', comment="编辑时间")
    deleteAt = Column(String, name='deleteAt', comment='删除时间')


class DCArticle(Base, ModelMixin):
    """舆情热度文章表"""
    __tablename__ = 'DC_Article'
    id = Column(Integer, name='id', primary_key=True)
    baseScore = Column(Integer, default=0, name='baseScore', comment="基础评分")
    articleQualityScore = Column(Integer, default=0, name='articleQualityScore', comment="文章质量得分")
    languageScore = Column(Integer, default=0, name='languageScore', comment="语法得分")
    timeScore = Column(Integer, default=0, name='timeScore', comment="时间评分")
    sourceScore = Column(Integer, default=0, name='sourceScore', comment="来源评分")
    hotWords = Column(String, name='hotWords', comment="逗号分割")
    wordScore = Column(Float, name='wordScore', comment="词频得分")
    esId = Column(String, name='esId', comment="esID")
    newEsId = Column(String, name='newEsId', comment="newEsId")
    articlePublishTime = Column(String, name='articlePublishTime', comment="文章发布时间")
    hotWeight = Column(Float, name='hotWeight', comment="文章权重", default=0)
    hotWeight24h = Column(Float, name='hotWeight24h', comment="24小时文章权重", default=0)
    hotWeightUnique = Column(Float, name='hotWeightUnique', comment="去重权重", default=0)
    type = Column(Integer, default=0, name='type', comment="文章类型")
    createdAt = Column(String, name='createdAt', comment="创建时间", default=datetime.now())
    updatedAt = Column(String, name='updatedAt', comment="编辑时间", default=datetime.now())
    isRepeat = Column(TINYINT, name='isRepeat', comment="文章去重  0未比对，1比对后保留的 ，2比对后删除的")


class DCHotArticleWordsWeight(Base, ModelMixin):
    """舆情热度文章表"""
    __tablename__ = 'DC_HotArticleWordsWeight'
    id = Column(Integer, name='id', primary_key=True)
    word = Column(String, default="", name='word', comment="热词")
    day1Weight = Column(Float, default=0, name='day1Weight', comment="自然日时间戳 % 7  的当前权重")
    day2Weight = Column(Float, default=0, name='day2Weight', comment="自然日时间戳 % 7  的当前权重")
    day3Weight = Column(Float, default=0, name='day3Weight', comment="自然日时间戳 % 7  的当前权重")
    day4Weight = Column(Float, default=0, name='day4Weight', comment="自然日时间戳 % 7  的当前权重")
    day5Weight = Column(Float, default=0, name='day5Weight', comment="自然日时间戳 % 7  的当前权重")
    day6Weight = Column(Float, default=0, name='day6Weight', comment="自然日时间戳 % 7  的当前权重")
    day7Weight = Column(Float, default=0, name='day7Weight', comment="自然日时间戳 % 7  的当前权重")
    weight = Column(Float, name='weight', comment="权重")
    status = Column(Integer, name='status', default=1, comment="审核状态：初始 1  审核通过2  删除-1")
    createAt = Column(String, name='createAt', comment="创建时间", default=datetime.now())
    updateAt = Column(String, name='updateAt', comment="编辑时间", default=datetime.now())
    demo = Column(String, name='demo', comment="备注")


class ELKError(Base, ModelMixin):
    """ELK Errors"""
    __tablename__ = 'ELK_Error'
    id = Column(Integer, name='id', primary_key=True)
    index = Column(String, default="", name='index', comment="索引")
    esid = Column(String, default="", name='esid', comment="es id")
    host = Column(String, default="", name='host', comment="服务器")
    env = Column(String, default="", name='env', comment="环境")
    service = Column(String, default="", name='service', comment="服务")
    log_time = Column(String, default="", name='log_time', comment="采集时间")
    message = Column(String, default="", name='message', comment="消息")
    message_func = Column(String, default=None, name='message_func', comment="函数")
    message_lineno = Column(Integer, default=None, name='message_lineno', comment="行号")
    message_pathname = Column(String, default="", name='message_pathname', comment="路径")
    message_time = Column(String, default="", name='message_time', comment="时间")
    message_levelno = Column(Integer, name='message_levelno', comment="日志级别")
    message_levelname = Column(String, default="", name='message_levelname', comment="日志名称")
    message_process = Column(Integer, name='message_process', comment="进程")
    message_message = Column(String, name='message_message', comment="消息内容")
    error_type = Column(Integer, name='error_type', comment="错误类型")
    createdAt = Column(String, name='createdAt', comment="创建时间", default=datetime.now())
    updatedAt = Column(String, name='updatedAt', comment="编辑时间", default=datetime.now())


class DMDataSourceConfig(Base, ModelMixin):
    """数据源配置表"""
    __tablename__ = 'DM_DataSourceConfig'

    id = Column(Integer, primary_key=True, autoincrement=True, comment="数据源配置标识")
    type = Column(Integer, nullable=False, comment="数据源类型 (1-MySQL, 2-Elasticsearch)")
    name = Column(String(255), nullable=False, comment="数据源名称")
    host = Column(String(255), nullable=False, comment="主机地址")
    port = Column(Integer, nullable=False, comment="端口号")
    username = Column(String(255), nullable=True, comment="用户名")
    password = Column(String(255), nullable=True, comment="密码")
    database = Column(String(255), nullable=True, comment="数据库名称")
    index = Column(String(255), nullable=True, comment="Elasticsearch索引名称")
    createdUserId = Column(Integer, nullable=False, comment="操作人ID")
    createdAt = Column(DateTime, default=datetime.now, comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    status = Column(Integer, nullable=False, default=1, comment="状态（1正常,2删除）")

    # 关联关系：一个数据源可以关联多个报警规则
    rules = relationship('DMAlertRules', primaryjoin='DMDataSourceConfig.id == foreign(DMAlertRules.data_source_id)', back_populates='data_source')


class DMAlertRules(Base, ModelMixin):
    """查询与报警规则表"""
    __tablename__ = 'DM_AlertRules'

    id = Column(Integer, primary_key=True, autoincrement=True, comment="报警规则标识")
    data_source_id = Column(Integer, nullable=False, comment="数据源ID（手动关联到DM_DataSourceConfig表的id字段）")
    query = Column(Text, nullable=False, comment="查询语句")
    threshold = Column(Integer, nullable=False, comment="报警阈值")
    operator = Column(String(10), nullable=False, comment="比较符号 (>, <, =)")
    createdUserId = Column(Integer, nullable=False, comment="操作人ID")
    createdAt = Column(DateTime, default=datetime.now, comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    status = Column(Integer, nullable=False, default=1, comment="状态（1正常,2删除）")

    # 关联关系：一个报警规则属于一个数据源
    data_source = relationship('DMDataSourceConfig', primaryjoin='foreign(DMAlertRules.data_source_id) == DMDataSourceConfig.id', back_populates='rules')
    # 关联关系：一个报警规则可以有多个任务
    tasks = relationship('DMAlertTasks', primaryjoin='DMAlertRules.id == foreign(DMAlertTasks.rule_id)', back_populates='rule')


class DMAlertTasks(Base, ModelMixin):
    """报警任务配置表"""
    __tablename__ = 'DM_AlertTasks'

    id = Column(Integer, primary_key=True, autoincrement=True, comment="报警任务标识")
    rule_id = Column(Integer, nullable=False, comment="报警规则ID（手动关联到DM_AlertRules表的id字段）")
    name = Column(String(255), nullable=False, comment="任务名称")
    description = Column(String(255), nullable=False, comment="任务描述")
    crontab = Column(String(255), nullable=False, comment="Crontab表达式")
    alert_method = Column(String(255), nullable=False, comment="报警方式  (1-微信, 2-钉钉, 3-邮件)")
    wechat_recipients = Column(Text, nullable=True, comment="微信接收人")
    dingtalk_recipients = Column(Text, nullable=True, comment="钉钉接收人")
    email_recipients = Column(Text, nullable=True, comment="邮件接收人")
    createdUserId = Column(Integer, nullable=False, comment="操作人ID")
    createdAt = Column(DateTime, default=datetime.now, comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    status = Column(Integer, nullable=False, default=1, comment="状态（1正常,2删除）")

    # 关联关系：一个任务属于一个报警规则
    rule = relationship('DMAlertRules', primaryjoin='foreign(DMAlertTasks.rule_id) == DMAlertRules.id', back_populates='tasks')
    # 关联关系：1对多
    logs = relationship('DMTaskLogs', primaryjoin='DMAlertTasks.id == foreign(DMTaskLogs.task_id)', back_populates='task')
    alerts = relationship('DMMonitorAlert', primaryjoin='DMAlertTasks.id == foreign(DMMonitorAlert.task_id)', back_populates='task')
    details = relationship('DMMonitorAlertDetail', primaryjoin='DMAlertTasks.id == foreign(DMMonitorAlertDetail.task_id)', back_populates='task')


class DMTaskLogs(Base, ModelMixin):
    """任务日志表"""
    __tablename__ = 'DM_TaskLogs'
    id = Column(Integer, primary_key=True, comment="任务日志标识")
    task_id = Column(Integer, nullable=False, comment="任务ID（手动关联到DM_AlertTasks表的id字段）")
    task_name = Column(String, default="", comment="任务名称")
    execution_duration = Column(Integer, default=None, comment="执行时长（毫秒）")
    status = Column(Integer, default=None, comment="执行状态:1-成功，2-失败，3-执行中")
    result = Column(String, default="", comment="执行结果")
    createdUserId = Column(Integer, default=None, comment="操作人ID")
    createdAt = Column(DateTime, default=datetime.now, comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="编辑时间")
    start_time = Column(DateTime, default=datetime.now, comment="任务开始时间")
    end_time = Column(DateTime, default=datetime.now, comment="任务结束时间")
    alert_message = Column(String, default="", comment="报警信息")

    # 关联关系：一个日志属于一个任务
    task = relationship('DMAlertTasks', primaryjoin='foreign(DMTaskLogs.task_id) == DMAlertTasks.id', back_populates='logs')


class DMMonitorAlert(Base, ModelMixin):
    """监控报警表"""
    __tablename__ = 'DM_MonitorAlert'
    id = Column(Integer, primary_key=True, comment="id")
    alert_type = Column(Integer, default=None, comment="报警类型 1 日志 2数据")
    task_id = Column(Integer, nullable=False, comment="任务ID（手动关联到DM_AlertTasks表的id字段）")
    log_id = Column(Integer, nullable=False, comment="任务日志ID")
    alert_name = Column(String, default="", comment="报警名称")
    execution_duration = Column(Integer, default=None, comment="执行时长（毫秒）")
    status = Column(Integer, default=None, comment="执行状态:1-成功，2-失败，3-执行中")
    result = Column(String, default="", comment="执行结果")
    total = Column(Integer, default=None, comment="总数")
    createdUserId = Column(Integer, default=None, comment="操作人ID")
    createdAt = Column(DateTime, default=datetime.now, comment="创建时间")
    updatedAt = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="编辑时间")
    start_time = Column(DateTime, default=datetime.now, comment="任务开始时间")
    end_time = Column(DateTime, default=datetime.now, comment="任务结束时间")
    alert_message = Column(String, default="", comment="报警信息")

    # 关联关系：一个报警属于一个任务
    task = relationship('DMAlertTasks', primaryjoin='foreign(DMMonitorAlert.task_id) == DMAlertTasks.id', back_populates='alerts')
    # 关联关系：1对多
    details: list = relationship('DMMonitorAlertDetail', primaryjoin='DMMonitorAlert.id == foreign(DMMonitorAlertDetail.alert_id)', back_populates='alert')


class DMMonitorAlertDetail(Base, ModelMixin):
    """监控报警详情表"""
    __tablename__ = 'DM_MonitorAlertDetail'
    id = Column(Integer, primary_key=True, comment="id")
    alert_id = Column(Integer, default=None, comment="报警id")
    task_id = Column(Integer, nullable=False, comment="任务ID（来源于DM_AlertTasks表的id字段）")
    service = Column(String, default="", comment="服务（日志监控）")
    error_type = Column(Integer, default=None, comment="错误类型（日志监控）")
    total = Column(Integer, default=None, comment="统计数量")
    result = Column(String, default="", comment="统计结果")
    updatedAt = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="编辑时间")
    status = Column(Integer, default=1, comment="执行状态:1-正常  2 删除")

    # 关联关系：一个报警属于一个任务
    task = relationship('DMAlertTasks', primaryjoin='foreign(DMMonitorAlertDetail.task_id) == DMAlertTasks.id', back_populates='details')
    alert = relationship('DMMonitorAlert', primaryjoin='foreign(DMMonitorAlertDetail.alert_id) == DMMonitorAlert.id', back_populates='details')
